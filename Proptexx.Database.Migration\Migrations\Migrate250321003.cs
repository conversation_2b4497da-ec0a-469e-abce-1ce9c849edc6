using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations;

[Migration(250321003)]
public class Migrate250321003 : FluentMigrator.Migration
{
    public override void Up()
    {
        // Migrate existing data for new fields
        //Execute.Sql(@"
        //    UPDATE telemetry.api_logs
        //    SET
        //        is_batch = CASE
        //            WHEN endpoint ILIKE '%batch%' THEN TRUE
        //            ELSE FALSE
        //        END,
        //        is_ai_request = CASE
        //            WHEN endpoint ILIKE '/gen/%' OR endpoint ILIKE '/cv/%' THEN TRUE
        //            ELSE FALSE
        //        END,
        //        is_api_request = CASE
        //            WHEN endpoint NOT ILIKE '/gen/%'
        //                 AND endpoint NOT ILIKE '/cv/%'
        //                 AND endpoint NOT ILIKE '%batch%'
        //            THEN TRUE
        //            ELSE FALSE
        //        END,
        //        response_code = COALESCE(
        //            NULLIF((headers::jsonb->>'status')::int, NULL),
        //            200
        //        ),
        //        identifier = (
        //            SELECT jsonb_object_keys(request_body::jsonb->'_')
        //            FROM telemetry.api_logs AS sub
        //            WHERE sub.id = telemetry.api_logs.id
        //            LIMIT 1
        //        )
        //    WHERE request_body IS NOT NULL AND request_body::jsonb ? '_';
        //");

        Execute.Sql(@"CREATE MATERIALIZED VIEW IF NOT EXISTS telemetry.mv_dashboard_summary
                        TABLESPACE pg_default
                        AS
                         SELECT created_at::date AS day,
                            count(*) FILTER (WHERE is_api_request) AS total_api_requests,
                            count(*) FILTER (WHERE is_ai_request) AS total_ai_requests,
                            count(*) FILTER (WHERE is_batch) AS total_batch_requests,
                            round(COALESCE(avg(duration_ms), 0::double precision)::numeric, 1) AS average_request_duration,
                            count(DISTINCT session_id) AS distinct_sessions
                           FROM telemetry.api_logs
                          GROUP BY (created_at::date)
                        WITH NO DATA");

        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_avg_requests_per_day;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_avg_duration;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_avg_sessions_per_day;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_total_requests_by_type;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_batch_request_count;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_models_per_account_id;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_request_count_per_endpoint;");
    }

    public override void Down()
    {
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_dashboard_summary;");
    }
}