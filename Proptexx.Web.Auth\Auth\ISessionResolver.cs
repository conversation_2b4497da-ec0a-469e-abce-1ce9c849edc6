namespace Proptexx.Web.Auth.Auth;

public interface ISessionResolver
{
    Task PersistAsync(string sessionId, TimeSpan maxAge, SessionPayload payload);

    Task<SessionPayload?> RetriveAsync(string sessionId);
    
    Task DeleteAsync(string sessionId);
}

public class SessionPayload
{
    public required string SessionId { get; init; }

    public required Dictionary<string, string> Claims { get; init; } = [];
}