using System.Text.Json;
using Proptexx.Core.Json;
using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class Service : IDbEntity
{
    [IgnoreOnUpdate] public required string Id { get; init; }

    public required string ServiceType { get; set; }

    public required string Title { get; set; }

    public string? Description { get; set; }

    public decimal CreditCost { get; set; }

    [IgnoreOnInsertOrUpdate] public required ServiceSettings Settings { get; set; }

    public string GetDbRef() => "core.service";

    public override string ToString()
        => JsonSerializer.Serialize(this, JsonDefaults.JsonSerializerOptions);
}

public abstract class ServiceSettings
{
    protected ServiceSettings(string type)
    {
        Type = type;
    }

    public string Type { get; }
}

public sealed class WidgetAccessServiceSettings : ServiceSettings
{
    public WidgetAccessServiceSettings() : base("widget-access")
    {
    }
}
public sealed class EcommerceWidgetServiceSettings : ServiceSettings
{
    public EcommerceWidgetServiceSettings() : base("ecommerce-widget")
    {
    }
}
public sealed class RealEstateWidgetServicesSettings : ServiceSettings
{
    public RealEstateWidgetServicesSettings() : base("real-estate-widget")
    {
    }
}


public abstract class ApiServiceSettings : ServiceSettings
{
    protected ApiServiceSettings(string type) : base(type)
    {
    }

    public ApiServiceFlow? Flow { get; set; }

    public Dictionary<string, object> Docs { get; set; } = [];
}

public enum ApiServiceFlow
{
    Async = 0,
    Sync = 1,
    SyncAndAsync = 2
}

public sealed class GeminiModelApiServiceSettings : ApiServiceSettings
{
    public GeminiModelApiServiceSettings() : base("gg-api")
    {
    }

    public string? Prompt { get; init; }
}

public sealed class GenerativeApiServiceSettings : ApiServiceSettings
{
    public GenerativeApiServiceSettings() : base("gen-api")
    {
    }

    public required string Model { get; init; }
}

public sealed class BatchApiServiceSettings : ApiServiceSettings
{
    public BatchApiServiceSettings() : base("batch-api")
    {
    }
}

public sealed class SequentialApiServiceSettings : ApiServiceSettings
{
    public SequentialApiServiceSettings() : base("seq-api")
    {
    }
}

public sealed class FlowApiServiceSettings : ApiServiceSettings
{
    public FlowApiServiceSettings() : base("flow-api")
    {
    }
}
