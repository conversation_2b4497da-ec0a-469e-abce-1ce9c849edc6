using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class WorkspaceServiceBinding : IDbTable
{
    public required Guid WorkspaceId { get; init; }

    public required string ServiceId { get; init; }

    public DateTime? ExpiresAt { get; set; }

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public string GetDbRef() => "core.workspace_service_binding";
}