using System.Text.Json;
using Proptexx.Core.AI;

namespace Proptexx.AI.Services;

public static class JsonDocumentService
{
    public static async Task<JsonDocument> MergeResponsesWithKeyAsync(params ModelResponse[] responses)
    {
        using var stream = new MemoryStream();
        await using var writer = new Utf8JsonWriter(stream);
        
        writer.WriteStartObject();

        foreach (var r in responses)
        {
            var obj = r.Document?.RootElement.EnumerateObject();
            if (obj is null) continue;

            foreach (var prop in obj)
            {
                prop.WriteTo(writer);
            }
        }
        
        writer.WriteEndObject();
        await writer.FlushAsync();
        stream.Position = 0;

        return await JsonDocument.ParseAsync(stream);
    }
    
    public static async Task<JsonDocument> MergeResponsesAsync(IEnumerable<ModelResponse> responses)
    {
        using var stream = new MemoryStream();
        await using var writer = new Utf8JsonWriter(stream);
        var properties = new Dictionary<string, List<JsonElement>>();
        
        writer.WriteStartObject();

        foreach (var r in responses)
        {
            var obj = r.Document?.RootElement.EnumerateObject();
            if (obj is null) continue;

            foreach (var prop in obj)
            {
                if (!properties.TryGetValue(prop.Name, out var values))
                {
                    values = new List<JsonElement>();
                    properties[prop.Name] = values;
                }
                values.Add(prop.Value.Clone());
            }
        }

        foreach (var (key, values) in properties)
        {
            if (values.Count == 1)
            {
                writer.WritePropertyName(key);
                values[0].WriteTo(writer);
            }
            else
            {
                writer.WritePropertyName(key);
                writer.WriteStartArray();
                foreach (var value in values)
                {
                    value.WriteTo(writer);
                }
                writer.WriteEndArray();
            }
        }
        
        writer.WriteEndObject();
        await writer.FlushAsync();
        stream.Position = 0;

        return await JsonDocument.ParseAsync(stream);
    }
}