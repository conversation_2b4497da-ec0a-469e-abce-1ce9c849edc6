using System.Net.Http.Headers;
using Proptexx.Core.Extensions;

namespace Proptexx.Web.Auth.OAuth.Microsoft;

public sealed class MicrosoftOAuthProvider : IOAuthProvider
{
    private readonly MicrosoftOAuthOptions _options;
    private readonly IHttpClientFactory _httpClientFactory;

    public MicrosoftOAuthProvider(IConfiguration configuration, IHttpClientFactory httpClientFactory)
    {
        _options = configuration.GetRequiredSection<MicrosoftOAuthOptions>("microsoft");
        _httpClientFactory = httpClientFactory;
    }

    public Task<string> GenerateAuthLinkAsync(string state)
    {
        var url = $"{_options.AuthorityUri}" +
                  $"?client_id={Uri.EscapeDataString(_options.ClientId)}" +
                  $"&response_type=code" +
                  $"&redirect_uri={Uri.EscapeDataString(_options.RedirectUri)}" +
                  $"&response_mode=query" +
                  $"&scope={Uri.EscapeDataString(_options.Scope)}" +
                  $"&state={Uri.EscapeDataString(state)}";

        return Task.FromResult(url);
    }

    public async Task<TokenResponse> ExchangeCodeForTokenAsync(string code)
    {
        using var httpClient = _httpClientFactory.CreateClient();
        using var tokenRequest = new FormUrlEncodedContent(new[]
        {
            new KeyValuePair<string, string>("client_id", _options.ClientId),
            new KeyValuePair<string, string>("scope", _options.Scope),
            new KeyValuePair<string, string>("code", code),
            new KeyValuePair<string, string>("redirect_uri", _options.RedirectUri),
            new KeyValuePair<string, string>("grant_type", "authorization_code"),
            new KeyValuePair<string, string>("client_secret", _options.ClientSecret)
        });

        var response = await httpClient.PostAsync(_options.AccessTokenUri, tokenRequest);

        if (!response.IsSuccessStatusCode)
        {
            throw new Exception($"Token request failed: {response.StatusCode}");
        }

        var result = await response.Content.ReadFromJsonAsync<TokenResponse>()
                     ?? throw new NullReferenceException("Unable to parse token response");

        return result;
    }

    public async Task<UserInfo> FetchUserInfoAsync(string accessToken)
    {
        using var httpClient = _httpClientFactory.CreateClient();
        var request = new HttpRequestMessage(HttpMethod.Get, "https://graph.microsoft.com/v1.0/me");
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

        var response = await httpClient.SendAsync(request);

        if (!response.IsSuccessStatusCode)
        {
            throw new Exception($"User info request failed: {response.StatusCode}");
        }

        var userInfo = await response.Content.ReadFromJsonAsync<MicrosoftUserInfo>()
                       ?? throw new InvalidOperationException("Unable to deserialize user information");

        return new UserInfo
        {
            Id = userInfo.Id,
            Email = userInfo.GetEmail(),
            Picture = null,
            EmailVerified = true,
            Locale = "en",
            Name = userInfo.DisplayName,
            FamilyName = userInfo.Surname,
            GivenName = userInfo.GivenName
        };
    }
}