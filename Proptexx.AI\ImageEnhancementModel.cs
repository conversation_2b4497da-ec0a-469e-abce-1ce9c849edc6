using System.Text.Json;
using Proptexx.Core.AI;
using Proptexx.Core.Http;
using Proptexx.Core.Services;
using Proptexx.Core.Storage;

namespace Proptexx.AI;

public abstract class ImageEnhancementModel : BaseModel
{
    private readonly IStorageService _storageService;
    private readonly IImageEnhancementClient _imageEnhancementClient;

    protected ImageEnhancementModel(
        IImageEnhancementClient imageEnhancementClient, 
        IStorageService storageService)
    {
        _imageEnhancementClient = imageEnhancementClient;
        _storageService = storageService;
    }

    public override async Task<ModelResponse> InferAsync(ModelContext context)
    {
        if (context.Payload is null)
        {
            throw new NullReferenceException(nameof(context.Payload));
        }

        var imageUrl = PayloadService.GetRequiredString(context.Payload, "imageUrl");

        var ctx = new ImageEnhancementModelContext
        {
            ImageUrl = imageUrl,
            WorkspaceId = context.WorkspaceId,
            ItemId = context.ItemId,
            Payload = context.Payload,
            CancellationToken = context.CancellationToken
        };
        
        var document = await InferAsync(ctx);
        return document;
    }

    public async Task<GenerativeModelResponse> InferAsync(ImageEnhancementModelContext context)
    {
        var modelEndpoint = this.GetModelEndpoint(context.WorkspaceId);
        var payload = this.GetPayload(context);
        var doc = await _imageEnhancementClient.EnhanceAsync(modelEndpoint, payload, context.CancellationToken);

        var base64Image = this.ParseModelResponse(doc, context.ImageUrl, modelEndpoint);
        if (string.IsNullOrWhiteSpace(base64Image))
        {
            throw new Exception("Model image response is null or empty");
        }
        
        const string contentType = "image/jpeg";
        var filename = $"{context.ItemId}_{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}.jpg";
        var url = await _storageService.UploadImageAsync(context.WorkspaceId, filename, base64Image, contentType);

        return new GenerativeModelResponse
        {
            Base64Image = base64Image,
            MimeType = contentType,
            RequestParams = payload,
            OutputImageUrl = url,
            Document = JsonDocument.Parse($$"""{"imageUrl": "{{url}}"}""")
        };
    }

    protected virtual Dictionary<string, object?> GetPayload(ImageEnhancementModelContext context)
    {
        return new Dictionary<string, object?>
        {
            ["image_url"] = context.ImageUrl
        };
    }

    protected virtual string? ParseModelResponse(JsonDocument doc, string inputImageUrl, string modelEndpoint)
    {
        if (doc.RootElement.TryGetProperty("error", out var jsonError))
        {
            throw new ApplicationException($"Model returned error: {jsonError.GetString()}");
        }
        
        if (!(doc.RootElement.TryGetProperty("computer_vision", out var jsonCvResult)
              && jsonCvResult.TryGetProperty("result", out var jsonResult)))
        {
            throw new Exception($"Unable to retrieve result on {inputImageUrl} from {modelEndpoint}");
        }
        
        return jsonResult.GetString();
    }

    protected abstract string GetModelEndpoint(string workspaceId);

    public sealed class Result
    {
        public string? ImageUrl { get; init; }
    }
}

public class ImageEnhancementModelContext : ModelContext
{
    public required string ImageUrl { get; init; }
}

public class ImageEnhancementModelResponse : ModelResponse
{
    public required string Base64Image { get; init; }
    
    public required string MimeType { get; init; }
}
