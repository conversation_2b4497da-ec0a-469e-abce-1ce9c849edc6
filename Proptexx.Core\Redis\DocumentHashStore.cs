using System.Text.Json.Nodes;
using StackExchange.Redis;

namespace Proptexx.Core.Redis;

public enum DocumentItemType
{
    Text = 0,
    Markdown = 1,
    Component = 2
}
public sealed class DocumentHashStore : RedisHashStore<DocumentHashStore.DocumentModel>
{
    public DocumentHashStore(IConnectionMultiplexer connectionMultiplexer) 
        : base(connectionMultiplexer, "documents")
    {
    }

    public static string ParsePath(string site, string language, string path)
    {
        var p = '/' + path.TrimStart('/');
        return $"{site}:{language}:{p}";
        
    }

    protected override string ResolveKey(DocumentModel entry)
    {
        return ParsePath(entry.Site, entry.Language, entry.Path);
    }

    public class DocumentModel
    {
        public required Guid Id { get; init; }

        public required string Site { get; init; }
        
        public required string Path { get; init; }

        public required string Language { get; init; }

        public required DateTime CreatedAt { get; init; }

        public required DateTime PublishedAt { get; init; }

        public required List<DocumentItemModel> Items { get; init; } = [];
        
        public class DocumentItemModel
        {
            public required DocumentItemType Type { get; init; }
            
            public JsonNode? Content { get; init; }
        }
    }
}