namespace Proptexx.Core.Utils;

public static class WebhookHelper
{
    public static async Task OnWidgetSignup(string endpointUrl, string listingUrl, string name, string email, string? phone)
    {
        try
        {
            Console.WriteLine($"OnWidgetSignup (URL:{listingUrl}\nName: {name}\nEmail: {email}\n");

            if (!Uri.TryCreate(endpointUrl, UriKind.Absolute, out _))
            {
                throw new FormatException($"The endpointUrl is not absolute: {endpointUrl}");
            }

            var body = new
            {
                lead_source = "proptexx",
                lead_property = listingUrl,
                name,
                email,
                phone
            };

            var str = await HttpHelper.PostReqAsync(endpointUrl, null, body);
            Console.WriteLine("OnWidgetSignup RESPONSE:");
            Console.WriteLine(str);
        }
        catch (Exception e)
        {
            Console.WriteLine("ERROR");
            Console.WriteLine(e);
        }
    }
}