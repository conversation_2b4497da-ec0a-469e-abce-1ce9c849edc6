using System.Text.Json;
using Npgsql;
using NpgsqlTypes;
using Proptexx.Core.Json;

namespace Proptexx.Core.Postgresql.Mappers;

public sealed class RecordMapper<T> : NpgsqlTypeMapper<T>
{
    private readonly JsonSerializerOptions _jsonSettings = JsonDefaults.JsonSerializerOptions;

    public override T Parse(object value)
    {
        if (value is not string s) throw new NullReferenceException();
        return JsonSerializer.Deserialize<T>(s, _jsonSettings)!;
    }

    protected override void SetValue(NpgsqlParameter parameter, T? value)
    {
        var serialized = JsonSerializer.Serialize(value, _jsonSettings);
        parameter.NpgsqlDbType = NpgsqlDbType.Jsonb;
        parameter.Value = serialized;
    }
}