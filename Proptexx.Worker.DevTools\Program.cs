using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Proptexx.AI;
using Proptexx.Core.Configuration;
using Proptexx.Core.Http;
using Proptexx.Core.Json;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Redis;
using Proptexx.Core.Services;
using Proptexx.Core.Storage;
using Proptexx.Worker.DevTools;

// const string s = """
//                  {
//                    "type": "service_account",
//                    "project_id": "proptexx-prod",
//                    "private_key_id": "a541ca4afc87fc10a0c54a0d861191c931de579c",
//                    "private_key": "-----B<PERSON><PERSON> PRIVATE KEY-----\nMIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQC+Bw56c83cs49l\nIOO7YyWNAjhabl0RvFdvD5HmdFtwP8U7IY60Yy8qUy0jif+Sb7mZD5stb1XHLpwh\nrcIGbWnzp8RGXGsTgnB0RjcUqrnCdYIxpsnM0ibN8TKasSgLAm7YB5DFcE/KultA\n2iOUwWqx16jl+o4CckTIRzcm5fUpcu2XfeWadYFpL8DKKAJmOIBbFbq6ZSbJ8GHH\n0rMjGMixgK+6zoAO2HS8VEPaRiyldG67KSzed7u9I9BBeqRU52XWwbt8hOeij4B7\nJW0E2xoUCE9o2dyLjVn4RWQoxlW05tLGDRsRGLnYMlbTytTnK8XAIU7C8TsGt6sB\navu3ceqPAgMBAAECggEANkDfgliDwupFG3NbcYmqE+s5WaA+1Cx4rg288xXvcUnT\nrsEy9A+JTMU4kvf4Tr+1Npz9CejKjbah/v+T8K/WWylf4VLLg2NvZdMSMMVZicpw\n5GohHWg2if++GepQa2xDZ0wpqiUZsuCn4wkktXjPVH0lDJwPZWVoe+HRPjrTVwSq\nHPKgO5BkWivFoVMK9DE0i4QSf9MM47a1YXkw3lDdV2nl4SN+0uZVJwFTt1JT/nDI\nq5HiyZuaueNoZ/Zguej34v9MPHbdjiAWrCgcpyFqldwodTHCDGVvRGk480c9HCnv\nPvRvmWC6QyRAeSPM3oXzK7jF5bb7eiELaPkP3gNg4QKBgQDbbaZPObAUkMKeMtCi\ns5ZUIqh4vPtyor0tBKJDeewFaolDj2QwLQhyBoej3CwD/elOKsw12Kac/CfHKatU\navij/TqCG5+5hpcOD0DthPzsN3u91GOoATCrW/gOYRO3t49wLmGkNTu/Qo9P9IrR\nj0JdtHoAH+cfd/7N6ypjXdqa8QKBgQDdsvbxkIaZezzo1snKdB6ENjqwjcoRiNo7\nbEnPrDdC7K1U5XgFjqOHxk6CkV9UYneWjQRl1qZ/LSai95CV3WeAdwRMxNNdvFbz\nKewDD24TDhuphKuFFURDwgQebizYT0DtfdRIfml+6qYARaeOhhqcPv0HYBwcOfTP\nUqMlsg3dfwKBgBIwx92/gYpi6c9llQg37RlyehbwS8QYG6i0vqLrY5abW1+/bmXU\nBeQ2CF6cLQXzsbiqMZ2xJTbUbJPFL4/jAlPN9WC924Ls1m7y8sX+BV2rx9VX2x8d\nYiSCjlDa2WCclgaNwch0JUzYvrq5Ihz3TGAqSwbhni5sqzH08eGk8S9xAoGAdKca\ncPkPGetSnWGHRFPdSswODaBN1vQfgGz2WaPQYBKREb68bJAlU+fwn5tGgexd6IXC\n7qMHcJudXNrO5UxejaYSSSK35PaY+dHxMZsI3FXsEEKSza/m/5AzAqAOULNDArPW\n415AvFtThYkhJUlC7zvlf9/+aovEWOKuncMWsJkCgYABzzidm3QLINxITjTxp4WK\nvUTm843ACZq7gYJvsHB32MEh/pyt1S5bBcsJOTCJ76wgreBsMI3RY61X/uN2cqlz\nW2e7M/jSKwVx0TdHjFF8u9CY/WUOm0A5hMqbBu6gsXCJKf3CQ1JbsxL1Ae07oIlZ\nIfFjbN4Jtv1I0Ay0rLhUyg==\n-----END PRIVATE KEY-----\n",
//                    "client_email": "<EMAIL>",
//                    "client_id": "114503005195028509788",
//                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
//                    "token_uri": "https://oauth2.googleapis.com/token",
//                    "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
//                    "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/proptexx-prod%40appspot.gserviceaccount.com",
//                    "universe_domain": "googleapis.com"
//                  }
//                  """;
//
// var b = Encoding.UTF8.GetBytes(s);
// var str = Convert.ToBase64String(b);
// Console.WriteLine(str);

var builder = Host.CreateApplicationBuilder(args);
builder.AddProptexxConfiguration();
builder.AddProptexxLogging();
builder.Services.AddSingleton<IStorageService, AzureStorageService>();
builder.Services.AddSingleton<WorkspaceDataHashStore>();
builder.Services.AddJsonConverters();
// builder.Services.AddPostgresql(pgsqlProdStr);
// builder.Services.AddRedis(builder.Configuration);

builder.Services.AddProptexxHttpClients();
builder.Services.AddModels();

var services = builder.Services.BuildServiceProvider();
var cancellationToken = CancellationToken.None;

await DownloadBatchFiles.ExecuteAsync(services, cancellationToken);

// const string imageUrl = "https://img.adamscameron.com/Homes/Images/Listings/*********/4/875a9c41c7c55410d96f16050a901505/Photo.jpg";
// var imageAssessmentClient = services.GetRequiredService<IImageAssessmentClient>();
//
// for (var i = 0; i < 50; i++)
// {
//     var assessment = await imageAssessmentClient.InspectImageAsync(imageUrl);
//     var mimeType = assessment.MimeType;
//     Console.WriteLine(mimeType);
//     
// }
// await TestVirtualRefurnishing.ExecuteAsync(services, cancellationToken);

// await LatestUsageOfWidget.ExecuteAsync(services, cancellationToken);
// await ApiKeyTester.ExecuteAsync();
// await CompletedBatchRerunner.ExecuteAsync(services, cancellationToken);
// await PreProcessingTester.FromFiles(services, cancellationToken);

// -----------------------------------------------------
// widget_cache  strings to hash, but need to wait for prod server upgrade to redis 7.4 for HEXPIRE support in redis
// -----------------------------------------------------

// var redis = services.GetRequiredService<IConnectionMultiplexer>();
// var server = redis.GetServer(redis.GetEndPoints().First());
// var database = redis.GetDatabase();
//
// const string pattern = "widget_cache:*";
// var keys = server.Keys(pattern: pattern, pageSize: 500);
// var keyList = keys.ToArray();
// var imageUrls = await database.StringGetAsync(keyList);
//
// var items = new List<HashEntry>();
// for (var i = 0; i < keyList.Length; i++)
// {
//     var redisKey = keyList[i];
//     // var imageUrl = await database.StringGetAsync(redisKey);
//     var imageUrl = imageUrls.GetValue(i)?.ToString();
//
//     if (string.IsNullOrWhiteSpace(imageUrl))
//     {
//         throw new NullReferenceException(nameof(imageUrl));
//     }
//     
//     var key = redisKey.ToString().Split(':')[1];
//     Console.WriteLine($"Key {key}: {imageUrl}");
//
//     var value = JsonSerializer.Serialize(new RedisGenerativeCacheItem
//     {
//         ImageUrl = imageUrl,
//         CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
//     });
//
//     var entry = new HashEntry(key, value);
//     items.Add(entry);
//     // await database.HashFieldExpireAsync("widget_generative_cache", [key], TimeSpan.FromDays(40));
// }
//
// await database.HashSetAsync("widget_generative_cache", items.ToArray());
//
//
// public class RedisGenerativeCacheItem
// {
//     public required string ImageUrl { get; init; }
//     
//     public long CreatedAt { get; init; }
// }
