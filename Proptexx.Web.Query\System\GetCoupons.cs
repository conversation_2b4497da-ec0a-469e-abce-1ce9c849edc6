using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.System;

public sealed class GetCoupons : IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        context.User.EnsureRootAccess();
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var result = await npgsql.QueryAsync<CouponModel>(CouponModel.Sql, new { });
        return result;
    }

    private sealed class CouponModel
    {
        public Guid Id { get; init; }
        
        public required string Code { get; init; }
        
        public int DiscountType { get; init; }

        public int DiscountValue { get; init; }

        public string? Currency { get; init; }
        
        public DateTime CreatedAt { get; init; }

        public static string Sql => @"
            select c.id,
                   c.code,
                   c.discount_type,
                   c.discount_value::int as discount_value,
                   c.created_at,
                   cpb.product_id,
                   p.title as product_title,
                   p.currency
            from core.coupon c
            left outer join core.coupon_product_binding cpb on c.id = cpb.coupon_id
            left outer join core.product p on cpb.product_id = p.id
            order by c.created_at desc;
        ";
    }
}