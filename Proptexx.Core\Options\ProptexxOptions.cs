using System.Text;
using Microsoft.AspNetCore.Http;
using Microsoft.IdentityModel.Tokens;

namespace Proptexx.Core.Options;

public sealed class ProptexxOptions
{
    public required string SecurityKey { get; init; }

    public required string Issuer { get; init; }
    
    public required string Audience { get; init; }
    
    public int AccessTokenTtl { get; init; } = 10;

    public string ClientIdentifier { get; init; } = "clientId";

    public string CallerIdentifier { get; init; } = "$accountId";

    public CookieSettings? Cookie { get; init; }
    
    public required string AccountUrl { get; init; }
    
    public required string DocsUrl { get; init; }

    public SymmetricSecurityKey GetSecurityKey()
    {
        return new SymmetricSecurityKey(Encoding.UTF8.GetBytes(SecurityKey));
    }
}

public sealed class CookieSettings
{
    public string SessionRefreshToken { get; init; } = "$refreshToken";

    public required string Domain { get; init; }

    public double MaxAgeMinutes { get; init; } = TimeSpan.FromDays(1).TotalMinutes;

    public bool HttpOnly { get; init; } = true;

    public bool Secure { get; init; } = true;

    public string Path { get; init; } = "/";

    public string SameSite { get; init; } = "none";

    public CookieOptions GetCookieOptions(TimeSpan maxAge)
    {
        return new CookieOptions
        {
            Path = this.Path,
            HttpOnly = this.HttpOnly,
            Secure = this.Secure,
            SameSite = this.SameSite.Equals("none", StringComparison.InvariantCultureIgnoreCase)
                ? SameSiteMode.None
                : SameSiteMode.Strict,
            Domain = this.Domain,
            MaxAge = maxAge
        };
    }
}