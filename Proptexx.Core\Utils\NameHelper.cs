namespace Proptexx.Core.Utils;

public static class NameHelper
{
    public static (string firstName, string lastName) ParseFullName(string fullName)
    {
        string firstName, lastName;
        var ar = fullName.Split(' ');
        if (ar.Length > 1)
        {
            firstName = string.Join(' ', ar.Take(ar.Length - 1));
            lastName = ar[^1];
        }
        else
        {
            firstName = fullName;
            lastName = string.Empty;
        }

        return (firstName, lastName);
    }

    public static string ParseWorkspaceTitle(string founderName, string? workspaceName)
    {
        return string.IsNullOrWhiteSpace(workspaceName) ? $"{founderName}'s space" : workspaceName;
    }
}