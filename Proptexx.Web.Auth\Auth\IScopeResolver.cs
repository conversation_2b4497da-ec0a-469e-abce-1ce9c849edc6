using System.Collections.ObjectModel;
using System.Security.Claims;
using System.Text.Json;
using Proptexx.Core.Json;

namespace Proptexx.Web.Auth.Auth;

public interface IScopeResolver
{
    Task ResolveAsync(ScopeContext context, CancellationToken cancellationToken);
}

public interface IScopeResolver<in TPayload> : IScopeResolver where TPayload : class
{
    Task IScopeResolver.ResolveAsync(ScopeContext context, CancellationToken cancellationToken)
    {
        if (context.Payload is null) throw new NullReferenceException(nameof(context.Payload));

        var payload = context.Payload.Deserialize<TPayload>(JsonDefaults.JsonSerializerOptions)
                      ?? throw new NullReferenceException(nameof(context.Payload));

        return this.ResolveAsync(context, payload, cancellationToken);
    }

    Task ResolveAsync(ScopeContext context, TPayload payload, CancellationToken cancellationToken);
}

public sealed class ScopeContext
{
    private readonly Dictionary<string, string> _claims;
    private readonly Dictionary<string, object> _data = [];

    public ScopeContext(ClaimsPrincipal identity, JsonDocument? payload)
    {
        var dictionary = identity.Claims.ToDictionary(x => x.Type, x => x.Value);
        _claims = new Dictionary<string, string>(dictionary, StringComparer.InvariantCulture);
        this.Identity = identity;
        this.Payload = payload;
    }

    public ClaimsPrincipal Identity { get; }

    public JsonDocument? Payload { get; }

    public void AddClaim(string key, string value)
    {
        if (_claims.ContainsKey(key))
        {
            _claims[key] = value;
            return;
        }

        _claims.TryAdd(key, value);
    }

    public void RemoveClaim(string key)
    {
        if (!_claims.ContainsKey(key)) return;
        _claims.Remove(key);
    }

    public void AddData(string key, object value)
    {
        if (key.StartsWith('$')) return;
        _data.Add(key, value);
    }

    public ReadOnlyDictionary<string, string> GetClaims() => _claims.AsReadOnly();

    public ReadOnlyDictionary<string, object> GetData() => _data.AsReadOnly();
}
