﻿namespace Proptexx.Core.Constants
{
    public class OutsetaEvents
    {
        // --- General Activity Types ---
        public const string Custom = "Custom";
        public const string Note = "Note";
        public const string Email = "Email";
        public const string PhoneCall = "PhoneCall";
        public const string Meeting = "Meeting";
        public const string Chat = "Chat";

        // --- Account Related Events ---
        public const string AccountCreated = "AccountCreated";
        public const string AccountUpdated = "AccountUpdated";
        public const string AccountAddPerson = "AccountAddPerson";
        public const string AccountRemovePerson = "AccountRemovePerson";
        public const string AccountPrimaryPersonUpdated = "AccountPrimaryPersonUpdated";
        public const string AccountStageUpdated = "AccountStageUpdated";
        public const string AccountDeleted = "AccountDeleted";
        public const string AccountBillingInformationRemoved = "AccountBillingInformationRemoved";
        public const string AccountBillingInformationRequested = "AccountBillingInformationRequested";
        public const string AccountBillingInformationUpdated = "AccountBillingInformationUpdated";
        public const string AccountBillingInvoiceEmailSent = "AccountBillingInvoiceEmailSent";
        public const string AccountBillingInvoiceCreated = "AccountBillingInvoiceCreated";
        public const string AccountBillingInvoiceDeleted = "AccountBillingInvoiceDeleted";
        public const string AccountPaidSubscriptionCreated = "AccountPaidSubscriptionCreated";
        public const string AccountSubscriptionAddOnsChanged = "AccountSubscriptionAddOnsChanged";
        public const string AccountSubscriptionCancellationRequested = "AccountSubscriptionCancellationRequested";
        public const string AccountSubscriptionPlanUpdated = "AccountSubscriptionPlanUpdated";
        public const string AccountSubscriptionPaymentCollected = "AccountSubscriptionPaymentCollected";
        public const string AccountSubscriptionPaymentDeclined = "AccountSubscriptionPaymentDeclined";
        public const string AccountSubscriptionRenewalExtended = "AccountSubscriptionRenewalExtended";
        public const string AccountSubscriptionStarted = "AccountSubscriptionStarted";

        // --- Person Related Events ---
        public const string PersonCreated = "PersonCreated";
        public const string PersonUpdated = "PersonUpdated";
        public const string PersonDeleted = "PersonDeleted";
        public const string PersonLogin = "PersonLogin";
        public const string PersonListSubscribed = "PersonListSubscribed";
        public const string PersonListConfirmed = "PersonListConfirmed";
        public const string PersonListUnsubscribed = "PersonListUnsubscribed";
        public const string PersonSegmentAdded = "PersonSegmentAdded";
        public const string PersonSegmentRemoved = "PersonSegmentRemoved";
        public const string PersonEmailOpened = "PersonEmailOpened";
        public const string PersonEmailClicked = "PersonEmailClicked";
        public const string PersonEmailBounce = "PersonEmailBounce";
        public const string PersonEmailSpam = "PersonEmailSpam";
        public const string PersonEmailSubscribed = "PersonEmailSubscribed";
        public const string PersonEmailUnsubscribed = "PersonEmailUnsubscribed";
        public const string PersonSupportTicketCreated = "PersonSupportTicketCreated";
        public const string PersonSupportTicketUpdated = "PersonSupportTicketUpdated";
        public const string PersonSupportTicketClosed = "PersonSupportTicketClosed";
        public const string PersonLeadFormSubmitted = "PersonLeadFormSubmitted";

        // --- Deal Related Events ---
        public const string DealCreated = "DealCreated";
        public const string DealUpdated = "DealUpdated";
        public const string DealAddPerson = "DealAddPerson";
        public const string DealAddAccount = "DealAddAccount";
        public const string DealDeleted = "DealDeleted";
        public const string DealDueDate = "DealDueDate";

        // --- Plan & Add-On Related Events ---
        public const string PlanCreated = "PlanCreated";
        public const string PlanUpdated = "PlanUpdated";
        public const string AddOnCreated = "AddOnCreated";
        public const string AddOnUpdated = "AddOnUpdated";
    }
}
