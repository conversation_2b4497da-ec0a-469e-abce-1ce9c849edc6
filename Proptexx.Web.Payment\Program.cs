using Proptexx.Stripe;
using Proptexx.Web;
using Proptexx.Web.Payment;

var builder = WebApplication.CreateBuilder(args);
builder.AddProptexxWeb();
builder.RegisterStripeApiKey();
builder.Services.AddScoped<StripeCallback>();

var app = builder.Build();
app.UseRouting();
app.MapHealthChecks("/health");
app.UseCors();
app.MapGet("/", () => "Proptexx");
app.MapPost("/stripe", (StripeCallback handler, HttpContext httpContext) => handler.InvokeAsync(httpContext));
app.Run();
