using System.Text.Json;
using Npgsql;
using Proptexx.Core.Entities;
using Proptexx.Core.Postgresql;

namespace Proptexx.Core.Stores;

public static class BatchStore
{
    public static async Task<IList<BatchResultModel>> GetBatchResults(NpgsqlConnection npgsql, Guid[] batchIds)
    {
        var param = new { _batch_ids = batchIds };
        var items = await npgsql
            .ListQueryAsync<BatchResultModel>(BatchResultModel.Sql, param);

        return items;
    }
}

public sealed class BatchResultModel
{
    public Guid BatchId { get; init; }

    public Guid TaskId { get; init; }

    public DateTime? BatchCreatedAt { get; init; }

    public DateTime? ResultCompletedAt { get; init; }

    public required string Model { get; init; }
    
    public required JsonDocument Config { get; init; }

    public DateTime? StartedAt { get; init; }

    public int Status { get; init; }

    public JsonDocument? Result { get; init; }
    
    public string? ErrorMessage { get; init; }

    public TimeSpan? TaskDuration { get; init; }

    public static string Sql => @"
        select distinct on (t.id)
            b.id as batch_id,
            b.created_at as batch_created_at,
            r.completed_at as result_completed_at,
            t.id as task_id,
            t.model,
            t.config,
            r.started_at,
            coalesce(r.status, 0) as status,
            r.output as result,
            r.error_message,
            (select r2.completed_at - r.started_at
            from batching.result r2
            where r2.task_id = t.id
            order by r2.completed_at desc
            limit 1) as task_duration
        from batching.batch b
        join batching.task t on b.id = t.batch_id
        left join batching.result r on t.id = r.task_id
        where (:_batch_ids is not null and b.id = any(:_batch_ids))
        order by t.id, r.started_at desc;
    ";

    public static string GetStatusText(BatchResultStatus status)
    {
        return status switch
        {
            BatchResultStatus.Error => "ERROR",
            BatchResultStatus.Processing => "PROCESSING",
            BatchResultStatus.Success => "OK",
            _ => "PENDING"
        };
    }

    public static string GetStatusText(int status) => GetStatusText((BatchResultStatus)status);
}
