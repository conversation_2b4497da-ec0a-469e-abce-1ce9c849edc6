using Dapper;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Attributes;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Account;

public sealed class GetProfile : IQuery
{
    [GuidNotEmpty] public Guid? Id { get; init; }

    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var accountId = Id ?? Guid.Parse(context.User.GetCallerId());
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var profile = await npgsql.QueryFirstOrDefaultAsync<ProfileModel>(ProfileModel.Sql, new { _account_id = accountId });
        return profile;
    }
    
    private class ProfileModel
    {
        public required Guid Id { get; init; }
        
        public required string FirstName { get; init; }
        
        public required string FamilyName { get; init; }
        
        public DateTime? DateOfBirth { get; init; }
        
        public int? Gender { get; init; }
        
        public DateTime CreatedAt { get; init; }

        public static string Sql => @"
            select a.id,
                   a.first_name,
                   a.family_name,
                   a.date_of_birth,
                   a.gender,
                   a.created_at,
                   (select data.data_value from core.account_data data where a.id = data.account_id and data.data_key = 'google_id') as google_id,
                   (select data.data_value from core.account_data data where a.id = data.account_id and data.data_key = 'microsoft_id') as microsoft_id
            from core.account a
            where a.id = :_account_id;
        ";
    }
}