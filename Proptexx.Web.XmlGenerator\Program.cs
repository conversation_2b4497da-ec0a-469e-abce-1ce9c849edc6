using Proptexx.Core.Storage;
using Proptexx.Web;
using Proptexx.Web.XmlGenerator;

var builder = WebApplication.CreateBuilder(args);
builder.AddProptexxWeb();
builder.Services.AddScoped<ExcelHandler>();
builder.Services.AddSingleton<IStorageService, AzureStorageService>();

var app = builder.Build();
app.UseProptexxWeb();
app.MapGet("/", () => "Proptexx");
app.MapPost("/excel/{batchId:guid}", (ExcelHandler handler, HttpContext context) => handler.InvokeAsync(context));

app.Run();