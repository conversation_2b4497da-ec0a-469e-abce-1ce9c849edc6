### Authenticate
# @name authen
POST https://auth.staging.proptexx.com/_auth
Content-Type: application/json
Authorization: Api<PERSON>ey MDc4MTYzZDUtYjM2NS00MTFlLThlNDItZDRkYTMwNjhkYzQ5

{}

### Extract token using correct syntax for property with $ sign
@accessToken = {{authen.response.body.$.$accessToken}}

### Call Model with accessToken from previous request
POST https://api.staging.proptexx.com/flow/virtual-staging-or-refurnishing
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "imageUrl": "https://cdn-bnokp.nitrocdn.com/QNoeDwCprhACHQcnEmHgXDhDpbEOlRHH/assets/images/optimized/rev-d674785/www.decorilla.com/online-decorating/wp-content/uploads/2021/12/How-to-decorate-a-small-living-room-by-Decorilla-designer-Mlade<PERSON>-C.jpg"
}

### Success
POST https://api.staging.proptexx.com/flow/virtual-staging-or-refurnishing
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "imageUrl": "https://cdn-bnokp.nitrocdn.com/QNoeDwCprhACHQcnEmHgXDhDpbEOlRHH/assets/images/optimized/rev-d674785/www.decorilla.com/online-decorating/wp-content/uploads/2021/12/How-to-decorate-a-small-living-room-by-Decorilla-designer-Mladen-C.jpg",
  "room_Type": "Living rOOm",
  "architecture_style": "modErn"
}

### Fail
POST https://api.staging.proptexx.com/flow/virtual-staging-or-refurnishing
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "imageUrl": "https://cdn-bnokp.nitrocdn.com/QNoeDwCprhACHQcnEmHgXDhDpbEOlRHH/assets/images/optimized/rev-d674785/www.decorilla.com/online-decorating/wp-content/uploads/2021/12/How-to-decorate-a-small-living-room-by-Decorilla-designer-Mladen-C.jpg",
  "room_Type": "DiniNg rOOm",
  "architecture_style": "modErn"
}

### IS24
POST https://api.staging.proptexx.com/flow/virtual-staging-or-refurnishing-is
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "imageUrl": "https://static.proptexx.com/appartments/listing1/livingroom.jpg"
}