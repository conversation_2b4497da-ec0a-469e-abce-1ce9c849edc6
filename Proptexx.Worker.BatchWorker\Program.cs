using Proptexx.AI;
using Proptexx.Worker;
using Proptexx.Worker.BatchWorker;
using Proptexx.Core.Services;
using Proptexx.Core.BigQuery;

var builder = Host.CreateApplicationBuilder(args);
builder.AddProptexxWorker();
builder.Services.AddModels();
builder.Services.AddSingleton<BatchDataService>();
builder.Services.AddSingleton<BigQueryBatchExporter>();
builder.Services.AddHostedService<BatchWorkerService>();

var app = builder.Build();
app.Run();