using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Attributes;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Workspace;

public sealed class GetOrders : IQuery
{
    [GuidNotEmpty] public Guid? WorkspaceId { get; init; }
    
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var workspaceId = WorkspaceId ?? context.User.GetWorkspaceGuid();
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var enumerable = await npgsql.QueryAsync<PurchaseModel>(PurchaseModel.Sql, new { _workspace_id = workspaceId });
        return enumerable;
    }

    private sealed class PurchaseModel
    {
        public required Guid OrderLineId { get; init; }
        
        public required Guid OrderId { get; init; }
        
        public required Guid ProductId { get; init; }
        
        public int OrderNbr { get; init; }
        
        public string? PaymentLink { get; init; }
        
        public required string ProductTitle { get; init; }
        
        public required decimal PriceAmount { get; init; }
        
        public required string Currency { get; init; }
        
        public string? ReferencePerson { get; init; }
        
        public DateTime? PaidAt { get; init; }
        
        public DateTime CreatedAt { get; init; }
        
        public static string Sql => @"
            select ol.id as order_line_id,
                   ol.order_id,
                   ol.product_id,
                   o.order_nbr,
                   o.payment_link,
                   p.title as product_title,
                   p.price_amount,
                   p.currency,
                   o.reference_person,
                   o.paid_at,
                   o.created_at
            from core.order_line ol
            join core.order o on o.id = ol.order_id
            join core.product p on ol.product_id = p.id
            where ol.workspace_id = :_workspace_id
            order by o.created_at desc;
        ";
    }
}
