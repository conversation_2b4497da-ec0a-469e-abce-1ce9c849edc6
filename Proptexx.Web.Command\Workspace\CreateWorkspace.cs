using System.ComponentModel.DataAnnotations;
using Dapper;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Utils;

namespace Proptexx.Web.Command.Workspace;

public sealed class CreateWorkspace : ICommand
{
    [Required, MinLength(4)]
    public required string Name { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        var accountId = Guid.Parse(context.User.GetCallerId());

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        await using var trx = await npgsql.BeginTransactionAsync(context.CancellationToken);

        try
        {
            var founder = await npgsql.QueryFirstAsync<Core.Entities.Account>("select a.* from core.account a where a.id = :_account_id", new { _account_id = accountId });
            var workspace = await OnboardHelper.CreateWorkspace(npgsql, founder, this.Name, null, null);
            await trx.CommitAsync(context.CancellationToken);
            context.AddData("workspaceId", workspace.Id);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            await trx.RollbackAsync(context.CancellationToken);
            throw;
        }
    }
}
