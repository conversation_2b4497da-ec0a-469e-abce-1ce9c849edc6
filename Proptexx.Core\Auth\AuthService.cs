using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.IdentityModel.Tokens;
using Proptexx.Core.Options;

namespace Proptexx.Core.Auth;

public static class AuthService
{
    private static string[] _reservedClaims = ["aud", "iss"];
    private static JwtSecurityTokenHandler _tokenHandler;

    static AuthService()
    {
        _tokenHandler = new JwtSecurityTokenHandler();
        _tokenHandler.InboundClaimTypeMap.Clear();
        _tokenHandler.OutboundClaimTypeMap.Clear();
    }

    public static string GenerateAccessToken(string issuer, string audience, SymmetricSecurityKey securityKey, int accessTokenTtl, Dictionary<string, string> claims)
    {
        var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

        var filteredClaims = claims
            .Where(kvp => !_reservedClaims.Contains(kvp.Key))
            .Select(kvp => new Claim(kvp.Key, kvp.Value))
            .ToList();

        var token = new JwtSecurityToken(
            issuer: issuer,
            audience: audience,
            claims: filteredClaims,
            expires: DateTime.UtcNow.AddMinutes(accessTokenTtl),
            signingCredentials: credentials);

        return _tokenHandler.WriteToken(token);
    }

    public static IEnumerable<Claim> ClaimsFromToken(ProptexxOptions options, string token)
    {
        var parameters = GetTokenValidationParams(options);

        try
        {
            var principal = _tokenHandler.ValidateToken(token, parameters, out var validatedToken);
            return principal.Claims.Where(x => !_reservedClaims.Contains(x.Type));
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }

        return [];
    }

    public static TokenValidationParameters GetTokenValidationParams(ProptexxOptions options)
    {
        var securityKey = options.GetSecurityKey();
        return new TokenValidationParameters
        {
            ValidIssuer = options.Issuer,
            ValidAudience = options.Audience,
            IssuerSigningKey = securityKey,
            ValidateIssuerSigningKey = true,
            ValidateLifetime = true,
            ValidateAudience = true,
            ValidateIssuer = true,
        };
    }
}