using Dapper;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;
using System.ComponentModel.DataAnnotations;

namespace Proptexx.Web.Query.Dashboard;

public sealed class GetWidgetClients : BaseFilter, IQuery
{
    /// <summary>
    /// Optional workspace ID filter. If not provided, returns all widget clients for the user's accessible workspaces.
    /// </summary>
    public Guid? WorkspaceId { get; init; }

    /// <summary>
    /// Optional account ID filter. If not provided, returns all widget clients.
    /// </summary>
    public Guid? AccountId { get; init; }

    /// <summary>
    /// Optional plan ID filter. If not provided, returns all widget clients.
    /// </summary>
    public string? PlanId { get; init; }

    /// <summary>
    /// Optional domain filter for searching widget clients by domain.
    /// </summary>
    public string? Domain { get; init; }

    /// <summary>
    /// Page number for pagination (1-based). Default is 1.
    /// </summary>
    public int Page { get; init; } = 1;

    /// <summary>
    /// Page size for pagination. Default is 50, maximum is 100.
    /// </summary>
    [Range(1, 100)]
    public int PageSize { get; init; } = 50;

    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);

        var offset = (Page - 1) * PageSize;

        var parameters = new
        {
            WorkspaceId,
            AccountId,
            PlanId,
            Domain = Domain != null ? $"%{Domain}%" : null,
            Limit = PageSize,
            Offset = offset,
            Ids = this.Ids // From BaseFilter
        };

        // Get total count for pagination
        var totalCount = await npgsql.QuerySingleAsync<int>(CountSql, parameters);

        // Get widget clients data
        var widgetClients = await npgsql.QueryAsync<WidgetClientDetailDto>(Sql, parameters);

        return new
        {
            Data = widgetClients,
            Pagination = new
            {
                Page,
                PageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling((double)totalCount / PageSize)
            }
        };
    }

    public static string Sql => @"
        SELECT 
            -- All Widget Client fields
            wc.api_key AS ApiKey,
            wc.domain AS Domain,
            wc.allowed_paths AS AllowedPaths,
            wc.renders_used AS RendersUsed,
            wc.quota AS Quota,
            wc.outseta_id AS OutsetaId,
            wc.workspace_id AS WorkspaceId,
            wc.account_id AS AccountId,
            
            -- Workspace basic information
            w.name AS WorkspaceName,
            w.title AS WorkspaceTitle,
            
            -- Account basic information
            a.first_name AS AccountFirstName,
            a.family_name AS AccountLastName,
            CONCAT_WS(' ', a.first_name, a.family_name) AS AccountFullName,
            ae.email AS AccountEmail,
            
            -- Subscription plan information
            sp.name AS PlanName
            
        FROM core.widget_clients wc
        LEFT JOIN core.workspace w ON wc.workspace_id = w.id
        LEFT JOIN core.account a ON wc.account_id = a.id
        LEFT JOIN (
            SELECT DISTINCT ON (account_id) 
                account_id, email 
            FROM core.account_email 
            ORDER BY account_id, verified_at DESC NULLS LAST, created_at ASC
        ) ae ON a.id = ae.account_id
        LEFT JOIN core.subscription_plan sp ON wc.plan_id = sp.uid
        ORDER BY wc.api_key
        LIMIT @Limit OFFSET @Offset;
    ";

    public static string CountSql => @"
        SELECT COUNT(*)
        FROM core.widget_clients wc
        LEFT JOIN core.workspace w ON wc.workspace_id = w.id
        LEFT JOIN core.account a ON wc.account_id = a.id
        LEFT JOIN core.subscription_plan sp ON wc.plan_id = sp.uid
        WHERE 1=1
            AND (@WorkspaceId IS NULL OR wc.workspace_id = @WorkspaceId)
            AND (@AccountId IS NULL OR wc.account_id = @AccountId)
            AND (@PlanId IS NULL OR wc.plan_id = @PlanId)
            AND (@Domain IS NULL OR wc.domain ILIKE @Domain)
            AND (NULLIF(@Ids, ARRAY[]::UUID[]) IS NULL OR wc.workspace_id = ANY(@Ids));
    ";
}

public sealed class WidgetClientDetailDto
{
    // All Widget Client fields from database
    public required string ApiKey { get; init; }
    public required string Domain { get; init; }
    public required string AllowedPaths { get; init; }
    public required int RendersUsed { get; init; }
    public required int Quota { get; init; }
    public string? OutsetaId { get; init; }
    public Guid? WorkspaceId { get; init; }
    public Guid? AccountId { get; init; }

    // Workspace basic information
    public string? WorkspaceName { get; init; }
    public string? WorkspaceTitle { get; init; }

    // Account basic information
    public string? AccountFirstName { get; init; }
    public string? AccountLastName { get; init; }
    public string? AccountFullName { get; init; }
    public string? AccountEmail { get; init; }

    // Subscription plan information
    public string? PlanName { get; init; }

} 