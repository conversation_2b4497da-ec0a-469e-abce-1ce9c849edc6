namespace Proptexx.Core.Entities;

public class BatchEntry
{
    public required string ImageUrl { get; set; }

    public string? OriginalUrl { get; set; }

    public WidgetBatchPrediction? Prediction { get; set; }

    public string? ProcessingError { get; set; }

    public string? MimeType { get; set; }
    
    public int? InferenceNbr { get; set; }
}

public class WidgetBatchPrediction
{
    public int CvModel { get; set; }

    public string? Type { get; set; }

    public bool? IsEmptyRoom { get; set; }
    
    public string? ErrorMessage { get; set; }

    public bool IsSuccess { get; set; }
    
    public string? RoomType { get; set; }
}