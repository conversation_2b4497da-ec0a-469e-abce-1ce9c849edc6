using Proptexx.Core.DTOs;

namespace Proptexx.Core;

public class WidgetOptions
{
    public string? Locale { get; init; }
    
    public string? ImgUrlPattern { get; init; }

    public string? Position { get; init; }

    public string? CustomTrigger { get; init; }
    
    public bool? IsOpen { get; init; }

    public bool? SkipAuth { get; init; }

    public string? LogoUrl { get; init; }
    
    public string? TriggerUrl { get; init; }
    
    public string? PrimaryColor { get; init; }
    
    public string? LightColor { get; init; }
    
    public string? DarkColor { get; init; }
    
    public int Offset { get; init; }
    
    public int Elevation { get; init; }
    
    public bool UseAds { get; init; }

    public bool? CustomizeWarning { get; init; }

    public bool? SingleImage { get; init; }
    public string? Layout { get; init; }
    public string? BackgroundColor { get; init; }
    
    public bool HideSidebar { get; init; }

    public string? CustomizeLogoUrl { get; init; }
    public string? OptLogoUrl { get; init; }
    public int? ScratchedImageMinWidth { get; set; }
    public int? ScratchedImageMinHeight { get; set; }
    public int? ScratchedImageMinAspectRatio { get; set; }
    public int? ScratchedImageMaxAspectRatio { get; set; }
    public long? ScratchedImageMinFileSizeInBytes { get; set; }
    public string? ScratchedImageAllowedFormats { get; set; }
    public string? ScratchedImageExcludedKeywords { get; set; }

    public string? ScopeSelector { get; set; }

}