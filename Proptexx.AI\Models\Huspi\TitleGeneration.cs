using System.Text.Json.Serialization;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Http;
using Proptexx.Core.Services;
using StackExchange.Redis;

namespace Proptexx.AI.Models.Huspi;

public sealed class TitleGeneration(
    IComputerVisionClient computerVisionClient,
    IImageAssessmentClient imageAssessmentClient,
    IConnectionMultiplexer connectionMultiplexer,
    ILoggerFactory loggerFactory)
    : BaseComputerVisionModel(
        "TitleGeneration",
        computerVisionClient,
        imageAssessmentClient,
        connectionMultiplexer,
        loggerFactory)
{
    public sealed class Result
    {
        [JsonPropertyName("title")]
        public string? Title { get; init; }
    }
}
