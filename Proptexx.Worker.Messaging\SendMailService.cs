using System.Net.Mail;
using Npgsql;
using Proptexx.Core.Postgresql;

namespace Proptexx.Worker.Messaging;

public sealed class SendMailService : BackgroundService
{
    private const int FormatRetryDelaySeconds = 20;
    private const int FormatRetryMaxSeconds = 60;

    private readonly ILogger<SendMailService> _logger;
    private readonly NpgsqlDataSource _npgsql;

    public SendMailService(ILogger<SendMailService> logger, NpgsqlDataSource npgsql)
    {
        _logger = logger;
        _npgsql = npgsql;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var retryDelaySeconds = FormatRetryDelaySeconds;
        while (!stoppingToken.IsCancellationRequested)
        {
            NpgsqlTransaction? transaction = null;
            MessageModel? current = null;
        
            try
            {
                await using var connection = await _npgsql.OpenConnectionAsync(stoppingToken);
                transaction = await connection.BeginTransactionAsync(stoppingToken);
        
                const string fetchEmailsQuery = @"
                    select mr.id, mr.recipient, m.subject, m.content , m.is_rich
                    from core.message_recipient mr 
                    join core.message m on mr.message_id = m.id
                    where mr.message_type = 1 and mr.status = 0
                    for update skip locked";
        
                var messageList = await connection.ListQueryAsync<MessageModel>(fetchEmailsQuery);
                foreach (var message in messageList)
                {
                    try
                    {
                        current = message;
                        await SendEmailAsync(message.Recipient, message.Subject, message.Content, message.IsRich, stoppingToken);
                        await UpdateEmailStatus(connection, message.Id, 1, null);
                        _logger.LogInformation("Message {MessageRecipientId} is sent to {Recipient}", message.Id, message.Recipient);
                    }
                    catch (Exception ex)
                    {
                        await UpdateEmailStatus(connection, message.Id, -1, ex.Message);
                        _logger.LogError(ex, "Inner exception on {MessageRecipientId} to {Recipient}", message.Id, message.Recipient);
                    }
                }
        
                await transaction.CommitAsync(stoppingToken);
                retryDelaySeconds = FormatRetryDelaySeconds;
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Outer exception on {MessageRecipientId} to {Recipient}", current?.Id, current?.Recipient);
        
                if (transaction is not null)
                {
                    await transaction.RollbackAsync(stoppingToken);
                }
        
                retryDelaySeconds = Math.Min(retryDelaySeconds * 2, FormatRetryMaxSeconds);
            }
            finally
            {
                current = null;

                if (transaction is not null)
                {
                    await transaction.DisposeAsync();
                }
                
                await Task.Delay(TimeSpan.FromSeconds(retryDelaySeconds), stoppingToken);
            }
        }
    }

    private static Task SendEmailAsync(string recipientEmail, string? subject, string? body, bool isHtml, CancellationToken cancellationToken) 
        => SendEmailAsync(new[] { recipientEmail }, subject, body, isHtml, cancellationToken);

    private static async Task SendEmailAsync(IEnumerable<string> recipientEmails, string? subject, string? body, bool isHtml, CancellationToken cancellationToken)
    {
        var email = new MailMessage
        {
            From = new MailAddress("<EMAIL>", "Proptexx"),
            Subject = subject,
            Body = body,
            IsBodyHtml = isHtml
        };

        foreach (var re in recipientEmails)
        {
            email.To.Add(re);
        }

        using var smtpClient = new SmtpClient("smtp-relay.gmail.com", 587);
        smtpClient.EnableSsl = true;

        await smtpClient.SendMailAsync(email, cancellationToken);
    }

    private static async Task UpdateEmailStatus(NpgsqlConnection connection, Guid id, int status, string? response)
    {
        const string updateStatusQuery = @"
                update core.message_recipient 
                set status = :_status, response = :_response, processed_at = current_timestamp
                where id = :_id";

        var updateCmd = new NpgsqlCommand(updateStatusQuery, connection);
        updateCmd.Parameters.AddWithValue(":_status", status);
        updateCmd.Parameters.AddWithValue(":_response", (object?)response ?? DBNull.Value);
        updateCmd.Parameters.AddWithValue(":_id", id);
        await updateCmd.ExecuteNonQueryAsync();
    }
    
    public class MessageModel
    {
        public Guid Id { get; init; }
    
        public required string Recipient { get; init; }
    
        public required string Subject { get; init; }
    
        public string? Content { get; init; }

        public bool IsRich { get; init; }
    }
}
