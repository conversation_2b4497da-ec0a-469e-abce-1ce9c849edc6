using System.Data;
using Dapper;
using StackExchange.Redis;

namespace Proptexx.Worker.DataSync;

public static class EmailSync
{
    internal static async Task SyncAsync(IDbConnection conn, IDatabase redis, CancellationToken stoppingToken)
    {
        try
        {
            const string keyPrefix = "email_details";
            const string trackingKey = $"{keyPrefix}_keys";

            var enumerable = await conn.QueryAsync<EmailModel>(EmailModel.Sql);

            var keys = redis.SetMembers(trackingKey);
            var batch = redis.CreateBatch();

            foreach (var k in keys)
            {
                var key = k.ToString();
                _ = batch.KeyDeleteAsync($"{keyPrefix}:{key}");
            }

            foreach (var item in enumerable)
            {
                var key = item.Key.Trim().ToLowerInvariant();
                HashEntry[] fields = [
                    new HashEntry("accountId", item.AccountId.ToString()),
                    new HashEntry("firstName", item.FirstName),
                    new HashEntry("familyName", item.FamilyName),
                    new HashEntry("verifiedAt", item.VerifiedAt?.ToString("O") ?? RedisValue.EmptyString),
                    new HashEntry("cancelledAt", item.CancelledAt?.ToString("O")?? RedisValue.EmptyString),
                    // new HashEntry("hash", item.Hash), 
                    // new HashEntry("salt", item.Salt)
                ];

                _ = batch.HashSetAsync($"{keyPrefix}:{key}", fields);
                _ = batch.SetAddAsync(trackingKey, key);
            }

            batch.Execute();
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }
    }
    
    public class EmailModel
    {
        internal static string Sql = @" 
            select ae.email as key,
                   ae.account_id,
                   ae.verified_at,
                   a.first_name,
                   a.family_name,
                   a.cancelled_at
            from core.account_email ae
            join core.account a on ae.account_id = a.id
            left outer join core.account_secret acs on a.id = acs.account_id
        ";

        public required string Key { get; init; }

        public Guid AccountId { get; init; }

        public DateTime? VerifiedAt { get; init; }

        public required string FirstName { get; init; }

        public required string FamilyName { get; init; }

        public DateTime? CancelledAt { get; init; }
    }
}