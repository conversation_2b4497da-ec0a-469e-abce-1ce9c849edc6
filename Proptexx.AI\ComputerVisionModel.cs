using System.Text.Json;
using Google.Cloud.AIPlatform.V1;
using Google.Protobuf;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Proptexx.Core.AI;
using Proptexx.Core.Http;
using Proptexx.Core.Json;
using Proptexx.Core.Services;
using StackExchange.Redis;

namespace Proptexx.AI;

public abstract class ComputerVisionModel<T> : BaseModel<T>, IModel where T : class
{
    private readonly ILogger _logger;
    private readonly PredictionServiceClient _serviceClient;
    private readonly IImageAssessmentClient _imageAssessmentService;
    private readonly IConfiguration _configuration;
    private readonly IDatabase _redis;
    private readonly string _cacheKey;
    private readonly string _verifyKey;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromHours(24);

    protected ComputerVisionModel(
        PredictionServiceClient serviceClient,
        IConfiguration configuration, 
        IConnectionMultiplexer connectionMultiplexer, 
        ILoggerFactory loggerFactory,
        IImageAssessmentClient imageAssessmentService)
    {
        var implName = GetType().FullName!;
        _logger = loggerFactory.CreateLogger(implName);
        _serviceClient = serviceClient;
        _configuration = configuration;
        _redis = connectionMultiplexer.GetDatabase();
        _imageAssessmentService = imageAssessmentService;
        _cacheKey = $"model:{implName}";
        _verifyKey = $"model_verify:{implName}";
    }

    protected abstract Task<string?> ResolvePromptAsync();

    public override async Task<ModelResponse> InferAsync(ModelContext context)
    {
        if (context.Payload is null)
        {
            throw new ApplicationException("Payload is empty");
        }

        var imageUrl = PayloadService.GetRequiredString(context.Payload, "imageUrl");
        
        var cached = await CheckCacheAsync(imageUrl);
        if (cached is not null) return cached;

        var assessment = await _imageAssessmentService.InspectImageAsync(imageUrl, context.CancellationToken);
        var response = await InferAsync(assessment, false);
        return response;
    }

    public Task<ModelResponse> InferAsync(ImageAssessment assessment, bool checkCache = true)
    {
        return this.InferAsync(assessment.ImageUrl, assessment.Base64String, assessment.MimeType, checkCache);
    }

    public async Task<ModelResponse> InferAsync(string imageUrl, string base64Image, string mimeType, bool checkCache = true)
    {
        ModelResponse? cached = null;

        if (checkCache)
        {
            cached = await CheckCacheAsync(imageUrl);
            if (cached is not null) return cached;
        }

        var projectId = _configuration.GetValue<string>("GoogleG:ProjectId", "proptexx-dev")!;
        var location = _configuration.GetValue<string>("GoogleG:Location", "europe-west3")!;
        var publisher = _configuration.GetValue<string>("GoogleG:Publisher", "google")!;
        var model = _configuration.GetValue<string>("GoogleG:Model", "gemini-1.5-flash-001")!;
        var content = new Content { Role = "USER" };
        var modelName = $"projects/{projectId}/locations/{location}/publishers/{publisher}/models/{model}";
        
        content.Parts.Add(new Part { InlineData = new Blob { MimeType = mimeType, Data = ByteString.FromBase64(base64Image) } });

        var prompt = await ResolvePromptAsync();
        if (!string.IsNullOrWhiteSpace(prompt))
        {
            content.Parts.Add(new Part { Text = prompt });
        }

        var generateContentRequest = new GenerateContentRequest
        {
            Model = modelName,
            GenerationConfig = new (){
                Temperature = 0.0f,
                TopP = 0.95f,
                Seed = 45654,
                ResponseSchema = this.GetResponseSchema(),
                ResponseMimeType = "application/json"
            },
            Contents = { content },
            SafetySettings =
            {
                new SafetySetting { Category = HarmCategory.HateSpeech, Threshold = SafetySetting.Types.HarmBlockThreshold.BlockMediumAndAbove },
                new SafetySetting { Category = HarmCategory.DangerousContent, Threshold = SafetySetting.Types.HarmBlockThreshold.BlockMediumAndAbove },
                new SafetySetting { Category = HarmCategory.SexuallyExplicit, Threshold = SafetySetting.Types.HarmBlockThreshold.BlockMediumAndAbove },
                new SafetySetting { Category = HarmCategory.Harassment, Threshold = SafetySetting.Types.HarmBlockThreshold.BlockMediumAndAbove }
            }
        };

        GenerateContentResponse? gResponse = null;
        try
        {
            gResponse = await _serviceClient.GenerateContentAsync(generateContentRequest);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }

        JsonDocument? doc;
        if (gResponse != null &&
            gResponse.Candidates.Count > 0 &&
            gResponse.Candidates[0].Content.Parts.Count > 0 &&
            !string.IsNullOrWhiteSpace(gResponse.Candidates[0].Content.Parts[0].Text))
        {
            var text = gResponse.Candidates[0].Content.Parts[0].Text;
            text = text.Replace("```json", "").Replace("```", "").Trim();
            doc = this.ParseResultToJsonDocument(text);
        }
        else
        {
            throw new ApplicationException("Unable to parse computer vision output");
        }

        var response = new ModelResponse
        {
            Document = doc
        };

        PersistToCache(doc, imageUrl);

        if (cached is not null)
        {
            await VerifyCacheResultAsync(imageUrl, cached, response);
        }

        return response;
    }

    private async Task<ModelResponse?> CheckCacheAsync(string imageUrl)
    {
        ModelResponse? result = null;
        try
        {
            var value = await _redis.HashGetAsync(_cacheKey, imageUrl);
            if (!value.HasValue) return null;

            result = new ModelResponse
            {
                Document = JsonDocument.Parse(value.ToString())
            };
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in ComputerVisionModel.CheckCacheAsync");
        }

        return result;
    }

    private void PersistToCache(JsonDocument? document, string imageUrl)
    {
        try
        {
            if (document is null) return;
            var str = JsonSerializer.Serialize(document, JsonDefaults.JsonSerializerOptions);
            var batch = _redis.CreateBatch();
            _ = batch.HashSetAsync(_cacheKey, imageUrl, str);
            _ = batch.HashFieldExpireAsync(_cacheKey, [imageUrl], _cacheExpiry);
            batch.Execute();
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in ComputerVisionModel.PersistToCache");
            Console.WriteLine(e);
        }
    }

    private async Task VerifyCacheResultAsync(string imageUrl, ModelResponse cached, ModelResponse generated)
    {
        string? result = null;

        try
        {
            if (cached.Document is null || generated.Document is null) return;

            var cachedStr = JsonSerializer.Serialize(cached.Document.RootElement);
            var generatedStr = JsonSerializer.Serialize(generated.Document.RootElement);

            if (cachedStr != generatedStr)
            {
                result = $$"""
                           {
                            "cached": {{cachedStr}},
                            "generated": {{generatedStr}}
                           }
                           """;
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in ComputerVisionModel.VerifyCacheResultAsync #1");
            result = e.Message;
        }

        try
        {
            if (!string.IsNullOrWhiteSpace(result))
            {
                await _redis.HashSetAsync(_verifyKey, imageUrl, result);
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in ComputerVisionModel.VerifyCacheResultAsync #2");
        }
    }

    protected virtual JsonDocument ParseResultToJsonDocument(string text) => JsonDocument.Parse(text);

    public virtual OpenApiSchema? GetResponseSchema() => null;
}
