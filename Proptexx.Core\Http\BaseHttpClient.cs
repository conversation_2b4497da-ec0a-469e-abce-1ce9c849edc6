using System.Collections.Concurrent;
using System.Text.Json;
using Microsoft.Extensions.Logging;

namespace Proptexx.Core.Http;

public abstract class BaseHttpClient
{
    private const string ProxyEndpoint = "https://imgproxy.proptexx.com/?imageUrl=";
    private const int MaxTrackedDomains = 500;

    protected static readonly string[] AcceptedMimeTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
    private static readonly ConcurrentDictionary<string, byte> DomainsUsingProxy = new();

    private readonly HttpClient _httpClient;
    private readonly ILogger _logger;

    protected BaseHttpClient(
        HttpClient httpClient,
        ILogger logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    protected async Task<byte[]> GetAsByteArrayAsync(string url, bool useFallback, CancellationToken cancellationToken)
    {
        using var response = await SendAsync(HttpMethod.Get, url, new Dictionary<string, string>(), null, useFallback, cancellationToken);
        var contentType = response.Content.Headers.ContentType?.MediaType;
        if (contentType is null || !AcceptedMimeTypes.Contains(contentType, StringComparer.OrdinalIgnoreCase))
            throw new ApplicationException($"Unsupported content type: {contentType}");

        return await response.Content.ReadAsByteArrayAsync(cancellationToken);
    }

    protected async Task<string> GetAsStringAsync(string url, bool useFallback, CancellationToken cancellationToken)
    {
        using var response = await SendAsync(HttpMethod.Get, url, new Dictionary<string, string>(), null, useFallback, cancellationToken);
        var contentType = response.Content.Headers.ContentType?.MediaType;
        if (contentType is null || !AcceptedMimeTypes.Contains(contentType, StringComparer.OrdinalIgnoreCase))
            throw new ApplicationException($"Unsupported content type: {contentType}");

        return await response.Content.ReadAsStringAsync(cancellationToken);
    }

    protected async Task<JsonDocument> PostJsonAsync(string url, HttpContent content, CancellationToken cancellationToken, Dictionary<string, string>? headers = null)
    {
        using var response = await SendAsync(HttpMethod.Post, url, headers ?? new Dictionary<string, string>(), content, false, cancellationToken);
        await using var str = await response.Content.ReadAsStreamAsync(cancellationToken);
        var doc = await JsonDocument.ParseAsync(str, cancellationToken: cancellationToken);
        return doc;
    }

    private async Task<HttpResponseMessage> SendAsync(
        HttpMethod method,
        string originalUrl,
        IDictionary<string, string> headers,
        HttpContent? content = null,
        bool useFallback = false,
        CancellationToken cancellationToken = default)
    {
        if (!Uri.TryCreate(originalUrl, UriKind.Absolute, out var uri))
            throw new ApplicationException($"URL is not absolute: {originalUrl}");

        var allowFallback = useFallback && method == HttpMethod.Get;
        
        if (allowFallback && DomainsUsingProxy.ContainsKey(uri.Host))
        {
            _logger.LogInformation("Domain {Host} previously failed, using proxy directly for {Url}", uri.Host, originalUrl);
            return await SendThroughProxyAsync(uri, content, cancellationToken);
        }

        var response = await SendRequestAsync(method, uri, headers, content, cancellationToken);
        var statusCode = (int)response.StatusCode;

        if (allowFallback
            && statusCode is >= 400 and < 500
            && statusCode != 404
            && IsFallbackMimeType(response.Content.Headers.ContentType?.MediaType))
        {
            _logger.LogWarning("Primary request returned forbidden or unexpected content type for {Url}. Attempting fallback proxy...", originalUrl);
            return await SendThroughProxyAsync(uri, content, cancellationToken);
        }
        
        ValidateResponse(response, uri);
        return response;
    }
    
    private static bool IsFallbackMimeType(string? mediaType)
    {
        return mediaType != null &&
               (mediaType.Equals("text/html", StringComparison.OrdinalIgnoreCase) ||
                mediaType.Equals("text/plain", StringComparison.OrdinalIgnoreCase));
    }
    
    private async Task<HttpResponseMessage> SendThroughProxyAsync(
        Uri uri,
        HttpContent? content,
        CancellationToken cancellationToken)
    {
        try
        {
            var fallbackHeaders = new Dictionary<string, string>
            {
                ["X-API-KEY"] = "9YuOdjgFFCYFrdCZ206DwyK82Yk3mHwxvlDRfGCUwrk="
            };

            var proxyUrl = new Uri(ProxyEndpoint + Uri.EscapeDataString(uri.AbsoluteUri));
            var fallbackResponse = await SendRequestAsync(HttpMethod.Get, proxyUrl, fallbackHeaders, content, cancellationToken);
            ValidateResponse(fallbackResponse, uri);

            if (!string.IsNullOrWhiteSpace(uri.Host))
            {
                TrackFallbackDomain(uri.Host);
            }

            return fallbackResponse;
        }
        catch (Exception proxyEx)
        {
            throw new Exception($"Proxy request failed for: {uri.AbsoluteUri}. Proxy: {proxyEx.Message}", proxyEx);
        }
    }

    private async Task<HttpResponseMessage> SendRequestAsync(
        HttpMethod method,
        Uri url,
        IDictionary<string, string> headers,
        HttpContent? content,
        CancellationToken cancellationToken)
    {
        using var request = new HttpRequestMessage(method, url);

        if (content is not null)
        {
            request.Content = content;
        }

        foreach (var kv in headers)
        {
            request.Headers.TryAddWithoutValidation(kv.Key, kv.Value);
        }

        var response = await _httpClient.SendAsync(
            request, HttpCompletionOption.ResponseHeadersRead, cancellationToken);

        return response;
    }

    private void ValidateResponse(HttpResponseMessage response, Uri uri)
    {
        if (response.IsSuccessStatusCode) return;
        var statusCode = (int)response.StatusCode;
        _logger.LogWarning("Non-success response: {StatusCode} from {Url}", statusCode, uri);
        throw new ApplicationException($"Request failed with status code {statusCode}");
    }

    private static void TrackFallbackDomain(string domain)
    {
        if (string.IsNullOrWhiteSpace(domain)) return;

        if (DomainsUsingProxy.Count >= MaxTrackedDomains)
        {
            var oldest = DomainsUsingProxy.Keys.FirstOrDefault();
            if (oldest != null)
            {
                DomainsUsingProxy.TryRemove(oldest, out _);
            }
        }

        DomainsUsingProxy.TryAdd(domain, 0);
    }

    public IReadOnlyCollection<string> GetFallbackDomains()
    {
        return DomainsUsingProxy.Keys.ToList();
    }
}
