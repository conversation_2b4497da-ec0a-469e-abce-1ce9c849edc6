using System.ComponentModel.DataAnnotations;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Stores;

namespace Proptexx.Web.Query.Account;

public sealed class EmailExists : IQuery
{
    [Required, EmailAddress]
    public required string Email { get; init; }

    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        await using var conn = await context.OpenNpgsqlAsync(context.CancellationToken);
        var exists = await conn.Account().EmailExists(this.Email);
        return exists;
        // var redis = context.GetService<IConnectionMultiplexer>().GetDatabase();
        // var accountStore = new EmailHashStore(redis);
        // var accountId = await accountStore.EmailExistsAsync(this.Username);
    }
}