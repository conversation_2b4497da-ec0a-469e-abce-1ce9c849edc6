using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class Client : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; init; } = Guid.NewGuid();

    public Guid? WorkspaceId { get; init; }

    public required string Name { get; init; }
    
    public string? Description { get; set; }

    public bool? IssueToken { get; set; }

    public string? Scopes { get; set; }

    public int SessionLifespan { get; set; } = 10;

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public DateTime? UpdatedAt { get; } = null!;

    public DateTime? CancelledAt { get; private set; }

    public string? CancellationReason { get; private set; }

    public string GetDbRef() => "core.client";

    public Client SetCancellation(DateTime cancelledAt, string cancellationReason)
    {
        this.CancelledAt = cancelledAt;
        this.CancellationReason = cancellationReason;
        return this;
    }

    public Client RevokeCancellation()
    {
        this.CancelledAt = null;
        this.CancellationReason = null;
        return this;
    }
}