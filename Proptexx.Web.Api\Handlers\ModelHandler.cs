using System.Text.Json;
using System.Text.Json.Serialization;
using Npgsql;
using Proptexx.AI.Services;
using Proptexx.Core;
using Proptexx.Core.Entities;
using Proptexx.Core.Json;
using Proptexx.Core.Postgresql.Builder;
using Proptexx.Web.Api.Helpers;
using Proptexx.Web.Api.Responses;
using StackExchange.Redis;

namespace Proptexx.Web.Api.Handlers;

public sealed class ModelHandler
{
    private readonly ILogger<ModelHandler> _logger;
    private readonly NpgsqlDataSource _dataSource;
    private readonly IDatabase _redis;

    public ModelHandler(
        ILogger<ModelHandler> logger,
        NpgsqlDataSource dataSource,
        IConnectionMultiplexer connectionMultiplexer)
    {
        _logger = logger;
        _dataSource = dataSource;
        _redis = connectionMultiplexer.GetDatabase();
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var id = Guid.NewGuid();
        var response = new ModelApiResponse();

        try
        {
            var modelName = BatchHelper.ParseModelName(context);
            var workspaceId = context.User.GetWorkspaceGuid();
            var workspaceName = context.User.TryGetWorkspaceName() ?? workspaceId.ToString();
            var callbackUrl = BatchHelper.GetCallbackUrl(context);
            var doc = await JsonDocument.ParseAsync(context.Request.Body);
            var batchTask = BatchHelper.ParsePayloadForBatchItem(id, modelName, doc.RootElement);
            var isSync = string.IsNullOrWhiteSpace(callbackUrl);

            var batch = new Batch
            {
                Id = id,
                WorkspaceId = workspaceId,
                AccountId = context.User.TryGetCallerGuid(),
                CallbackUrl = callbackUrl,
                IsSync = isSync
            };
            
            // var meta = new BatchRequestMeta
            // {
            //     BatchId = batch.Id,
            //     Language = context.Request.Headers.AcceptLanguage,
            //     Referrer = context.Request.Headers.Referer,
            //     ClientIp = context.Connection.RemoteIpAddress?.ToString(),
            //     CorrelationId = context.TraceIdentifier,
            //     UserAgent = context.Request.Headers.UserAgent
            // };

            await using var npgsql = await _dataSource.OpenConnectionAsync(context.RequestAborted);
            await BatchHelper.PersistBatchAsync(_logger, npgsql, batch, [batchTask], context.RequestAborted);

            if (isSync)
            {
                var batchResult = await MlHelper.ProcessItemAsync(context.RequestServices, _logger, 
                    workspaceId, batchTask.Id, batchTask.Model, batchTask.Config, context.RequestAborted);

                await npgsql.InsertAsync(batchResult);

                if (!string.IsNullOrWhiteSpace(batchResult.ErrorMessage))
                {
                    throw new ApplicationException(batchResult.ErrorMessage);
                }
                
                response.Success(id, batchResult.Output);
            }
            else
            {
                await BatchHelper.PushToRedisAsync(
                    _redis, workspaceName, batch, [batchTask], null);

                response.Success(id);
            }
            
            context.Response.StatusCode = StatusCodes.Status200OK;
        }
        catch (ApplicationException e)
        {
            context.Response.StatusCode = StatusCodes.Status400BadRequest;
            response.Fail(e.Message);
        }
        catch (UnauthorizedAccessException e)
        {
            context.Response.StatusCode = StatusCodes.Status401Unauthorized;
            response.Fail(e.Message);
        }
        catch (JsonException)
        {
            context.Response.StatusCode = StatusCodes.Status400BadRequest;
            response.Fail("Unable to parse the request payload");
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            context.Response.StatusCode = StatusCodes.Status500InternalServerError;
            response.Fail("The operation exited with an error. We have been notified and are looking into it");
        }
        finally
        {
            await context.Response.WriteAsJsonAsync(response, JsonDefaults.JsonSerializerOptions);
        }
    }
}

public sealed class ModelApiResponse : ApiResponse
{
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public JsonDocument? Result { get; private set; }

    public void Success(Guid id, JsonDocument? result)
    {
        base.Success(id);
        this.Result = result;
    }
}