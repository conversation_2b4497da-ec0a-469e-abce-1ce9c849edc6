using System.Text.Json;
using Proptexx.Core.Json;
using Proptexx.Web.Auth.OAuth;
using StackExchange.Redis;

namespace Proptexx.Web.Auth.Handlers;

public sealed class OAuthLinkHandler
{
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly IOAuthProviderFactory _providerFactory;

    public OAuthLinkHandler(
        ILogger<OAuthLinkHandler> logger,
        IConnectionMultiplexer connectionMultiplexer,
        IOAuthProviderFactory providerFactory)
    {
        _connectionMultiplexer = connectionMultiplexer;
        _providerFactory = providerFactory;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var payload = await JsonSerializer.DeserializeAsync<OAuthLinkPayload>(
            context.Request.Body, JsonDefaults.CompactOptions);

        if (string.IsNullOrWhiteSpace(payload?.Provider))
        {
            throw new ApplicationException("Bad request");
        }

        var provider = _providerFactory.GetProvider(payload.Provider);

        var state = Guid.NewGuid().ToString();
        var authUrl = await provider.GenerateAuthLinkAsync(state);

        var redis = _connectionMultiplexer.GetDatabase();
        var stateValue = payload.ToString();
        await redis.HashSetAsync("oauth_states", state, stateValue);
        await redis.HashFieldExpireAsync("oauth_states", [state], TimeSpan.FromMinutes(5));

        context.Response.ContentType = "application/json";
        await context.Response.WriteAsJsonAsync(new { url = authUrl });
    }
}

internal class OAuthLinkPayload
{
    public required string State { get; init; }
        
    public required string Provider { get; init; }
        
    public string? Type { get; init; }

    public string? RedirectUrl { get; init; }

    public override string ToString()
    {
        return JsonSerializer.Serialize(this, JsonDefaults.CompactOptions);
    }
}
