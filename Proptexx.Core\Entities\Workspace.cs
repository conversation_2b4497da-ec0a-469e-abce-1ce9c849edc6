using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class Workspace : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; } = Guid.NewGuid();

    public Guid? ParentId { get; set; }

    public required Guid? ClusterTplId { get; init; }

    public Guid? FounderId { get; init; }

    public required string Name { get; set; }
    
    public required string Title { get; set; }

    public bool IsTestMode { get; set; } = false;

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public DateTime? UpdatedAt { get; } = null!;
    
    public string GetDbRef() => "core.workspace";
}