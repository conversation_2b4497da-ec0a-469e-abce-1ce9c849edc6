﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Proptexx.Core.DTOs.Email
{
    public class EmailResult
    {
        public bool IsSuccess { get; set; }
        public string? ErrorMessage { get; set; }
        public string? MessageId { get; set; }
        public DateTime SentAt { get; set; }

        public static EmailResult Success(string? messageId = null) => new()
        {
            IsSuccess = true,
            MessageId = messageId,
            SentAt = DateTime.UtcNow
        };

        public static EmailResult Failure(string error) => new()
        {
            IsSuccess = false,
            ErrorMessage = error,
            SentAt = DateTime.UtcNow
        };
    }
}
