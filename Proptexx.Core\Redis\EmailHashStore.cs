using StackExchange.Redis;

namespace Proptexx.Core.Redis;

public sealed class EmailHashStore : RedisHashStore<EmailHashStore.EmailModel>
{
    public EmailHashStore(IConnectionMultiplexer connectionMultiplexer) 
        : base(connectionMultiplexer, "emails")
    {
    }

    protected override string ResolveKey(EmailModel entry) => entry.Email;
    
    public class EmailModel
    {
        public required string Email { get; init; }
        
        public Guid AccountId { get; init; }

        public DateTime? VerifiedAt { get; init; }
    }
}