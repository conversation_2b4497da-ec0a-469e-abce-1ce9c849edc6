using Microsoft.Extensions.Logging;
using Proptexx.Core.Entities;
using Proptexx.Core.DTOs;
using Dapper;
using Npgsql;
using Proptexx.Core.Constants;

namespace Proptexx.Core.Services;

/// <summary>
/// Service for managing subscriptions using Dapper
/// Implements ISubscriptionService interface
/// </summary>
public class SubscriptionService : ISubscriptionService
{
    private readonly ILogger<SubscriptionService> _logger;
    private readonly NpgsqlDataSource _dataSource;

    public SubscriptionService(ILogger<SubscriptionService> logger, NpgsqlDataSource dataSource)
    {
        _logger = logger;
        _dataSource = dataSource;
    }

    /// <summary>
    /// Create a new subscription
    /// </summary>
    public async Task<ClientSubscription> CreateAsync(ClientSubscription subscription, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Creating new subscription for workspace {ClientSecretId}", subscription.ClientSecretId);
            
            subscription.CreatedAt = DateTime.UtcNow;
            subscription.UpdatedAt = DateTime.UtcNow;
            

            const string insertSql = @"
                INSERT INTO core.client_subscription 
                (id, client_secret_id, is_demo, type, quota, total_usage, status, 
                 created_at, updated_at, notes, plan_id, domain, allowed_paths, outseta_account_id)
                VALUES 
                (@Id, @ClientSecretId, @IsDemo, @Type, @Quota, @TotalUsage, @Status,
                 @CreatedAt, @UpdatedAt, @Notes, @PlanId, @Domain, @AllowedPaths, @OutsetaAccountId)
                RETURNING *";

            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var parameters = new DynamicParameters(subscription);
            var inserted = await connection.QuerySingleAsync<ClientSubscription>(insertSql, parameters);
            
            _logger.LogInformation("Successfully created subscription {SubscriptionId}", inserted.Id);
            return inserted;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating subscription for workspace {ClientSecretId}", subscription.ClientSecretId);
            throw;
        }
    }

    /// <summary>
    /// Get subscription by API key
    /// </summary>
    public async Task<ClientSubscription?> GetByApiKeyAsync(string apiKey, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting subscription by API key: {ApiKey}", apiKey);
            
            // First get client_secret_id from API key, then get subscription
            var clientSecretId = await GetClientSecretIdFromApiKeyAsync(apiKey, cancellationToken);
            if (clientSecretId == null)
            {
                return null;
            }
            
            const string selectSql = "SELECT * FROM core.client_subscription WHERE client_secret_id = @ClientSecretId";
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var subscription = await connection.QuerySingleOrDefaultAsync<ClientSubscription>(selectSql, new { ClientSecretId = clientSecretId });
            
            return subscription;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscription by API key: {ApiKey}", apiKey);
            throw;
        }
    }

    /// <summary>
    /// Get subscription by ID
    /// </summary>
    public async Task<ClientSubscription?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting subscription by ID: {SubscriptionId}", id);
            
            const string selectSql = "SELECT * FROM core.client_subscription WHERE id = @Id";
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var subscription = await connection.QuerySingleOrDefaultAsync<ClientSubscription>(selectSql, new { Id = id });
            
            return subscription;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscription by ID: {SubscriptionId}", id);
            throw;
        }
    }

    /// <summary>
    /// Get all subscriptions with filtering and pagination
    /// </summary>
    public async Task<(IEnumerable<ClientSubscription> Subscriptions, int TotalCount)> GetAllAsync(
        string? status = null,
        string? subscriptionType = null,
        Guid? clientSecretId = null,
        int page = 1,
        int pageSize = 50,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting subscriptions with filters - Status: {Status}, Type: {Type}, ClientSecretId: {ClientSecretId}, Page: {Page}, PageSize: {PageSize}",
                status, subscriptionType, clientSecretId, page, pageSize);

            var whereConditions = new List<string>();
            var parameters = new DynamicParameters();

            // Build WHERE clause dynamically
            if (!string.IsNullOrEmpty(status))
            {
                whereConditions.Add("status = @Status");
                parameters.Add("@Status", status);
            }

            if (!string.IsNullOrEmpty(subscriptionType))
            {
                whereConditions.Add("type = @Type");
                parameters.Add("@Type", subscriptionType);
            }

            if (clientSecretId.HasValue)
            {
                whereConditions.Add("client_secret_id = @ClientSecretId");
                parameters.Add("@ClientSecretId", clientSecretId.Value);
            }

            var whereClause = whereConditions.Count > 0 ? $"WHERE {string.Join(" AND ", whereConditions)}" : "";

            // Get total count
            var countSql = $"SELECT COUNT(*) FROM core.client_subscription {whereClause}";
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var totalCount = await connection.ExecuteScalarAsync<int>(countSql, parameters);

            // Get paginated results
            var offset = (page - 1) * pageSize;
            var selectSql = $@"
                SELECT * FROM core.client_subscription 
                {whereClause}
                ORDER BY created_at DESC 
                LIMIT @PageSize OFFSET @Offset";

            parameters.Add("@PageSize", pageSize);
            parameters.Add("@Offset", offset);

            var subscriptions = await connection.QueryAsync<ClientSubscription>(selectSql, parameters);

            _logger.LogDebug("Retrieved {Count} subscriptions out of {TotalCount} total", subscriptions.Count(), totalCount);

            return (subscriptions, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscriptions with filters");
            throw;
        }
    }

    /// <summary>
    /// Update an existing subscription
    /// </summary>
    public async Task<bool> UpdateAsync(ClientSubscription subscription, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Updating subscription {SubscriptionId}", subscription.Id);
            
            subscription.UpdatedAt = DateTime.UtcNow;
            
            const string updateSql = @"
                UPDATE core.client_subscription 
                SET is_demo = @IsDemo, type = @Type, quota = @Quota, total_usage = @TotalUsage, 
                    status = @Status, updated_at = @UpdatedAt, notes = @Notes, plan_id = @PlanId,
                    domain = @Domain, allowed_paths = @AllowedPaths, outseta_account_id = @OutsetaAccountId
                WHERE id = @Id";

            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var rowsAffected = await connection.ExecuteAsync(updateSql, subscription);
            
            _logger.LogInformation("Successfully updated subscription {SubscriptionId}", subscription.Id);
            return rowsAffected > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating subscription {SubscriptionId}", subscription.Id);
            throw;
        }
    }

    /// <summary>
    /// Delete a subscription
    /// </summary>
    public async Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Deleting subscription {SubscriptionId}", id);
            
            const string deleteSql = "DELETE FROM core.client_subscription WHERE id = @Id";
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var rowsAffected = await connection.ExecuteAsync(deleteSql, new { Id = id });
            
            if (rowsAffected == 0)
            {
                _logger.LogWarning("Subscription {SubscriptionId} not found for deletion", id);
                return false;
            }
            
            _logger.LogInformation("Successfully deleted subscription {SubscriptionId}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting subscription {SubscriptionId}", id);
            throw;
        }
    }

    /// <summary>
    /// Check if API key exists
    /// </summary>
    public async Task<bool> ApiKeyExistsAsync(string apiKey, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Checking if API key exists: {ApiKey}", apiKey);
            
            // Check if API key exists in client_secret table
            const string selectSql = "SELECT COUNT(*) FROM core.client_secret WHERE api_key = @ApiKey";
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var count = await connection.ExecuteScalarAsync<int>(selectSql, new { ApiKey = apiKey });
            
            return count > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if API key exists: {ApiKey}", apiKey);
            throw;
        }
    }

    public async Task<ClientSubscription?> CreatePayAsYouGoSubscriptionAsync(
        string apiKey, 
        string? clientName = null, 
        string? contactName = null, 
        string? contactEmail = null, 
        List<string>? productServices = null, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Get client_secret_id from API key
            var clientSecretId = await GetClientSecretIdFromApiKeyAsync(apiKey, cancellationToken);
            if (clientSecretId == null)
            {
                _logger.LogError("API key not found: {ApiKey}", apiKey);
                return null;
            }

            var subscription = new ClientSubscription
            {
                Id = Guid.NewGuid(),
                ClientSecretId = clientSecretId.Value,
                IsDemo = false, 
                Type = "PayAsYouGo",
                Quota = 0,
                TotalUsage = 0,
                Status = SubscriptionConstants.Status.Active,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Notes = "Created as PayAsYouGo subscription"
            };
            return await CreateAsync(subscription, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating PayAsYouGo subscription for API key: {ApiKey}", apiKey);
            throw;
        }
    }

    public async Task<bool> ApiKeyExistsInSubscriptionAsync(string apiKey, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get client_secret_id from API key, then check if subscription exists
            var clientSecretId = await GetClientSecretIdFromApiKeyAsync(apiKey, cancellationToken);
            if (clientSecretId == null)
            {
                return false;
            }

            const string selectSql = "SELECT COUNT(*) FROM core.client_subscription WHERE client_secret_id = @ClientSecretId";
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var count = await connection.ExecuteScalarAsync<int>(selectSql, new { ClientSecretId = clientSecretId });
            return count > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if API key exists in subscription: {ApiKey}", apiKey);
            throw;
        }
    }
     
    /// <summary>
    /// Increment usage for both client_secret and client_subscription by client_secret_id
    /// </summary>
    public async Task<bool> IncrementUsageAsync(Guid clientSecretId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Incrementing usage for client_secret_id: {ClientSecretId}", clientSecretId);
            
            const string sql = "SELECT core.increment_usage(@ClientSecretId)";
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            await connection.ExecuteAsync(sql, new { ClientSecretId = clientSecretId });
            
            _logger.LogDebug("Successfully incremented usage for client_secret_id: {ClientSecretId}", clientSecretId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing usage for client_secret_id: {ClientSecretId}", clientSecretId);
            return false;
        }
    }

    /// <summary>
    /// Increment usage for both client_secret and client_subscription by API key
    /// </summary>
    public async Task<bool> IncrementUsageByApiKeyAsync(string apiKey, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Incrementing usage for API key: {ApiKey}", apiKey);
            
            const string sql = "SELECT core.increment_usage_by_api_key(@ApiKey)";
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            await connection.ExecuteAsync(sql, new { ApiKey = apiKey });
            
            _logger.LogDebug("Successfully incremented usage for API key: {ApiKey}", apiKey);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing usage for API key: {ApiKey}", apiKey);
            return false;
        }
    }

    /// <summary>
    /// Increment usage for client_secret only by client_secret_id
    /// </summary>
    public async Task<bool> IncrementClientSecretUsageAsync(Guid clientSecretId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Incrementing client_secret usage for client_secret_id: {ClientSecretId}", clientSecretId);
            
            const string sql = "SELECT core.increment_client_secret_usage(@ClientSecretId)";
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            await connection.ExecuteAsync(sql, new { ClientSecretId = clientSecretId });
            
            _logger.LogDebug("Successfully incremented client_secret usage for client_secret_id: {ClientSecretId}", clientSecretId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing client_secret usage for client_secret_id: {ClientSecretId}", clientSecretId);
            return false;
        }
    }

    /// <summary>
    /// Increment usage for client_secret only by API key
    /// </summary>
    public async Task<bool> IncrementClientSecretUsageByApiKeyAsync(string apiKey, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Incrementing client_secret usage for API key: {ApiKey}", apiKey);
            
            const string sql = "SELECT core.increment_client_secret_usage_by_api_key(@ApiKey)";
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            await connection.ExecuteAsync(sql, new { ApiKey = apiKey });
            
            _logger.LogDebug("Successfully incremented client_secret usage for API key: {ApiKey}", apiKey);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing client_secret usage for API key: {ApiKey}", apiKey);
            return false;
        }
    }

    /// <summary>
    /// Increment usage for subscription only by client_secret_id
    /// </summary>
    public async Task<bool> IncrementSubscriptionUsageAsync(Guid clientSecretId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Incrementing subscription usage for client_secret_id: {ClientSecretId}", clientSecretId);
            
            const string sql = "SELECT core.increment_subscription_usage(@ClientSecretId)";
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            await connection.ExecuteAsync(sql, new { ClientSecretId = clientSecretId });
            
            _logger.LogDebug("Successfully incremented subscription usage for client_secret_id: {ClientSecretId}", clientSecretId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing subscription usage for client_secret_id: {ClientSecretId}", clientSecretId);
            return false;
        }
    }

    /// <summary>
    /// Increment usage for subscription only by API key
    /// </summary>
    public async Task<bool> IncrementSubscriptionUsageByApiKeyAsync(string apiKey, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Incrementing subscription usage for API key: {ApiKey}", apiKey);
            
            const string sql = "SELECT core.increment_subscription_usage_by_api_key(@ApiKey)";
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            await connection.ExecuteAsync(sql, new { ApiKey = apiKey });
            
            _logger.LogDebug("Successfully incremented subscription usage for API key: {ApiKey}", apiKey);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing subscription usage for API key: {ApiKey}", apiKey);
            return false;
        }
    }

    /// <summary>
    /// Get total usage by client_id
    /// </summary>
    public async Task<ClientUsageSummary?> GetClientTotalUsageAsync(Guid clientId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting total usage for client_id: {ClientId}", clientId);
            
            const string sql = "SELECT * FROM core.get_client_total_usage(@ClientId)";
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var result = await connection.QuerySingleOrDefaultAsync<ClientUsageSummary>(sql, new { ClientId = clientId });
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting total usage for client_id: {ClientId}", clientId);
            return null;
        }
    }

    /// <summary>
    /// Get total usage by client_id as JSON
    /// </summary>
    public async Task<string> GetClientTotalUsageJsonAsync(Guid clientId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting total usage JSON for client_id: {ClientId}", clientId);
            
            const string sql = "SELECT core.get_client_total_usage_json(@ClientId)";
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var result = await connection.ExecuteScalarAsync<string>(sql, new { ClientId = clientId });
            
            return result ?? "{}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting total usage JSON for client_id: {ClientId}", clientId);
            return "{}";
        }
    }

    /// <summary>
    /// Get all clients usage summary
    /// </summary>
    public async Task<IEnumerable<ClientUsageSummary>> GetAllClientsUsageAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting all clients usage summary");
            
            const string sql = "SELECT * FROM core.get_all_clients_usage()";
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var results = await connection.QueryAsync<ClientUsageSummary>(sql);
            
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all clients usage summary");
            return Enumerable.Empty<ClientUsageSummary>();
        }
    }

    /// <summary>
    /// Get top clients by usage
    /// </summary>
    public async Task<IEnumerable<ClientUsageSummary>> GetTopClientsByUsageAsync(int limit = 10, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting top {Limit} clients by usage", limit);
            
            const string sql = "SELECT * FROM core.get_top_clients_by_usage(@Limit)";
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var results = await connection.QueryAsync<ClientUsageSummary>(sql, new { Limit = limit });
            
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting top clients by usage");
            return Enumerable.Empty<ClientUsageSummary>();
        }
    }

    /// <summary>
    /// Generate API key from client_secret_id
    /// </summary>
    public async Task<string?> GetApiKeyFromClientSecretIdAsync(Guid clientSecretId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Generating API key for client_secret_id: {ClientSecretId}", clientSecretId);
            
            const string sql = "SELECT core.get_api_key_from_client_secret_id(@ClientSecretId)";
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var result = await connection.ExecuteScalarAsync<string>(sql, new { ClientSecretId = clientSecretId });
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating API key for client_secret_id: {ClientSecretId}", clientSecretId);
            return null;
        }
    }

    /// <summary>
    /// Get client_secret_id from API key using ApiKeyService
    /// </summary>
    public async Task<Guid?> GetClientSecretIdFromApiKeyAsync(string apiKey, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting client_secret_id for API key: {ApiKey}", apiKey);
            
            // Use ApiKeyService.ParseApiKey instead of database function for better performance
            var secretIdString = ApiKeyService.ParseApiKey(apiKey);
            if (string.IsNullOrWhiteSpace(secretIdString))
            {
                _logger.LogWarning("Unable to parse API key: {ApiKey}", apiKey);
                return null;
            }
            
            if (Guid.TryParse(secretIdString, out var secretId))
            {
                return secretId;
            }
            
            _logger.LogWarning("Invalid secret ID format from API key: {ApiKey}, SecretId: {SecretId}", apiKey, secretIdString);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting client_secret_id for API key: {ApiKey}", apiKey);
            return null;
        }
    }

    /// <summary>
    /// Get client usage summary from view
    /// </summary>
    public async Task<IEnumerable<ClientUsageSummary>> GetClientUsageSummaryFromViewAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting client usage summary from view");
            
            const string sql = "SELECT * FROM core.client_usage_summary ORDER BY total_usage DESC";
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var results = await connection.QueryAsync<ClientUsageSummary>(sql);
            
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting client usage summary from view");
            return Enumerable.Empty<ClientUsageSummary>();
        }
    }

    /// <summary>
    /// Get client usage summary by client_id from view
    /// </summary>
    public async Task<ClientUsageSummary?> GetClientUsageSummaryByClientIdAsync(Guid clientId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting client usage summary for client_id: {ClientId}", clientId);
            
            const string sql = "SELECT * FROM core.client_usage_summary WHERE client_id = @ClientId";
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var result = await connection.QuerySingleOrDefaultAsync<ClientUsageSummary>(sql, new { ClientId = clientId });
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting client usage summary for client_id: {ClientId}", clientId);
            return null;
        }
    }

    /// <summary>
    /// Check usage for Period subscription type
    /// </summary>
    public async Task<UsageCheckResult> CheckPeriodUsageAsync(string apiKey, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Checking Period usage for API key: {ApiKey}", apiKey);
            
            // Get client_secret_id from API key using ApiKeyService
            var clientSecretId = await GetClientSecretIdFromApiKeyAsync(apiKey, cancellationToken);
            if (clientSecretId == null)
            {
                return new UsageCheckResult
                {
                    IsValid = false,
                    Reason = "Invalid API key",
                    ErrorCode = UsageCheckErrorCode.ValidationError,
                    Type = "Period",
                    Quota = 0,
                    TotalUsage = 0,
                    RemainingQuota = 0,
                    UsagePercentage = 0,
                    IsExpired = false,
                    DaysUntilExpiry = null
                };
            }
            
            const string sql = @"
                SELECT 
                    cs.type,
                    cs.quota,
                    cs.total_usage,
                    cs.status,
                    cs.client_secret_id,
                    client_secret.expired_at
                FROM core.client_subscription cs
                JOIN core.client_secret ON cs.client_secret_id = client_secret.id
                WHERE cs.client_secret_id = @ClientSecretId
                AND cs.type = 'Period'
                AND cs.status = 'Active'";
            
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var result = await connection.QuerySingleOrDefaultAsync<dynamic>(sql, new { ClientSecretId = clientSecretId });
            
            if (result == null)
            {
                return new UsageCheckResult
                {
                    IsValid = false,
                    Reason = "Period subscription not found or inactive",
                    ErrorCode = UsageCheckErrorCode.SubscriptionNotFound,
                    Type = "Period",
                    Quota = 0,
                    TotalUsage = 0,
                    RemainingQuota = 0,
                    UsagePercentage = 0,
                    IsExpired = false,
                    DaysUntilExpiry = null
                };
            }

            var quota = (long)result.quota;
            var totalUsage = (long)result.total_usage;
            var remainingQuota = quota - totalUsage;
            var usagePercentage = quota > 0 ? (double)totalUsage / quota * 100 : 0;
            var expiredAt = result.expired_at as DateTime?;
            var isExpired = expiredAt.HasValue && expiredAt.Value < DateTime.UtcNow;
            var daysUntilExpiry = expiredAt.HasValue ? (int?)(expiredAt.Value - DateTime.UtcNow).TotalDays : null;

            return new UsageCheckResult
            {
                IsValid = !isExpired && remainingQuota > 0,
                Reason = isExpired ? "Subscription expired" : (remainingQuota <= 0 ? "Quota exceeded" : "Valid"),
                ErrorCode = isExpired ? UsageCheckErrorCode.SubscriptionExpired : 
                           (remainingQuota <= 0 ? UsageCheckErrorCode.QuotaExceeded : UsageCheckErrorCode.None),
                Type = "Period",
                Quota = quota,
                TotalUsage = totalUsage,
                RemainingQuota = remainingQuota,
                UsagePercentage = usagePercentage,
                IsExpired = isExpired,
                DaysUntilExpiry = daysUntilExpiry
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking Period usage for API key: {ApiKey}", apiKey);
            return new UsageCheckResult
            {
                IsValid = false,
                Reason = "Error checking usage",
                ErrorCode = UsageCheckErrorCode.ValidationError,
                Type = "Period",
                Quota = 0,
                TotalUsage = 0,
                RemainingQuota = 0,
                UsagePercentage = 0,
                IsExpired = false,
                DaysUntilExpiry = null
            };
        }
    }

    /// <summary>
    /// Check usage for OneTime subscription type
    /// </summary>
    public async Task<UsageCheckResult> CheckOneTimeUsageAsync(string apiKey, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Checking OneTime usage for API key: {ApiKey}", apiKey);
            
            // Get client_secret_id from API key using ApiKeyService
            var clientSecretId = await GetClientSecretIdFromApiKeyAsync(apiKey, cancellationToken);
            if (clientSecretId == null)
            {
                return new UsageCheckResult
                {
                    IsValid = false,
                    Reason = "Invalid API key",
                    ErrorCode = UsageCheckErrorCode.ValidationError,
                    Type = "OneTime",
                    Quota = 0,
                    TotalUsage = 0,
                    RemainingQuota = 0,
                    UsagePercentage = 0,
                    IsExpired = false,
                    DaysUntilExpiry = null
                };
            }
            
            const string sql = @"
                SELECT 
                    cs.type,
                    cs.quota,
                    cs.total_usage,
                    cs.status,
                    cs.client_secret_id,
                    client_secret.expired_at
                FROM core.client_subscription cs
                JOIN core.client_secret ON cs.client_secret_id = client_secret.id
                WHERE cs.client_secret_id = @ClientSecretId
                AND cs.type = 'OneTime'
                AND cs.status = 'Active'";
            
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var result = await connection.QuerySingleOrDefaultAsync<dynamic>(sql, new { ClientSecretId = clientSecretId });
            
            if (result == null)
            {
                return new UsageCheckResult
                {
                    IsValid = false,
                    Reason = "OneTime subscription not found or inactive",
                    ErrorCode = UsageCheckErrorCode.SubscriptionNotFound,
                    Type = "OneTime",
                    Quota = 0,
                    TotalUsage = 0,
                    RemainingQuota = 0,
                    UsagePercentage = 0,
                    IsExpired = false,
                    DaysUntilExpiry = null
                };
            }

            var quota = (long)result.quota;
            var totalUsage = (long)result.total_usage;
            var remainingQuota = quota - totalUsage;
            var usagePercentage = quota > 0 ? (double)totalUsage / quota * 100 : 0;
            var expiredAt = result.expired_at as DateTime?;
            var isExpired = expiredAt.HasValue && expiredAt.Value < DateTime.UtcNow;
            var daysUntilExpiry = expiredAt.HasValue ? (int?)(expiredAt.Value - DateTime.UtcNow).TotalDays : null;

            return new UsageCheckResult
            {
                IsValid = !isExpired && remainingQuota > 0,
                Reason = isExpired ? "Subscription expired" : (remainingQuota <= 0 ? "Quota exceeded" : "Valid"),
                ErrorCode = isExpired ? UsageCheckErrorCode.SubscriptionExpired : 
                           (remainingQuota <= 0 ? UsageCheckErrorCode.QuotaExceeded : UsageCheckErrorCode.None),
                Type = "OneTime",
                Quota = quota,
                TotalUsage = totalUsage,
                RemainingQuota = remainingQuota,
                UsagePercentage = usagePercentage,
                IsExpired = isExpired,
                DaysUntilExpiry = daysUntilExpiry
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking OneTime usage for API key: {ApiKey}", apiKey);
            return new UsageCheckResult
            {
                IsValid = false,
                Reason = "Error checking usage",
                ErrorCode = UsageCheckErrorCode.ValidationError,
                Type = "OneTime",
                Quota = 0,
                TotalUsage = 0,
                RemainingQuota = 0,
                UsagePercentage = 0,
                IsExpired = false,
                DaysUntilExpiry = null
            };
        }
    }

    /// <summary>
    /// Check usage for PayAsYouGo subscription type
    /// </summary>
    public async Task<UsageCheckResult> CheckPayAsYouGoUsageAsync(string apiKey, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Checking PayAsYouGo usage for API key: {ApiKey}", apiKey);
            
            // Get client_secret_id from API key using ApiKeyService
            var clientSecretId = await GetClientSecretIdFromApiKeyAsync(apiKey, cancellationToken);
            if (clientSecretId == null)
            {
                return new UsageCheckResult
                {
                    IsValid = false,
                    Reason = "Invalid API key",
                    ErrorCode = UsageCheckErrorCode.ValidationError,
                    Type = "PayAsYouGo",
                    Quota = 0,
                    TotalUsage = 0,
                    RemainingQuota = 0,
                    UsagePercentage = 0,
                    IsExpired = false,
                    DaysUntilExpiry = null
                };
            }
            
            const string sql = @"
                SELECT 
                    cs.type,
                    cs.quota,
                    cs.total_usage,
                    cs.status,
                    cs.client_secret_id,
                    client_secret.expired_at
                FROM core.client_subscription cs
                JOIN core.client_secret ON cs.client_secret_id = client_secret.id
                WHERE cs.client_secret_id = @ClientSecretId
                AND cs.type = 'PayAsYouGo'
                AND cs.status = 'Active'";
            
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var result = await connection.QuerySingleOrDefaultAsync<dynamic>(sql, new { ClientSecretId = clientSecretId });
            
            if (result == null)
            {
                return new UsageCheckResult
                {
                    IsValid = false,
                    Reason = "PayAsYouGo subscription not found or inactive",
                    ErrorCode = UsageCheckErrorCode.SubscriptionNotFound,
                    Type = "PayAsYouGo",
                    Quota = 0,
                    TotalUsage = 0,
                    RemainingQuota = 0,
                    UsagePercentage = 0,
                    IsExpired = false,
                    DaysUntilExpiry = null
                };
            }

            var quota = (long)result.quota;
            var totalUsage = (long)result.total_usage;
            var remainingQuota = quota > 0 ? quota - totalUsage : long.MaxValue; // PayAsYouGo has unlimited quota if quota is 0
            var usagePercentage = quota > 0 ? (double)totalUsage / quota * 100 : 0;
            var expiredAt = result.expired_at as DateTime?;
            var isExpired = expiredAt.HasValue && expiredAt.Value < DateTime.UtcNow;
            var daysUntilExpiry = expiredAt.HasValue ? (int?)(expiredAt.Value - DateTime.UtcNow).TotalDays : null;

            return new UsageCheckResult
            {
                IsValid = !isExpired && (quota == 0 || remainingQuota > 0), // PayAsYouGo is valid if quota is 0 (unlimited) or has remaining quota
                Reason = isExpired ? "Subscription expired" : (quota > 0 && remainingQuota <= 0 ? "Quota exceeded" : "Valid"),
                ErrorCode = isExpired ? UsageCheckErrorCode.SubscriptionExpired : 
                           (quota > 0 && remainingQuota <= 0 ? UsageCheckErrorCode.QuotaExceeded : UsageCheckErrorCode.None),
                Type = "PayAsYouGo",
                Quota = quota,
                TotalUsage = totalUsage,
                RemainingQuota = remainingQuota,
                UsagePercentage = usagePercentage,
                IsExpired = isExpired,
                DaysUntilExpiry = daysUntilExpiry
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking PayAsYouGo usage for API key: {ApiKey}", apiKey);
            return new UsageCheckResult
            {
                IsValid = false,
                Reason = "Error checking usage",
                ErrorCode = UsageCheckErrorCode.ValidationError,
                Type = "PayAsYouGo",
                Quota = 0,
                TotalUsage = 0,
                RemainingQuota = 0,
                UsagePercentage = 0,
                IsExpired = false,
                DaysUntilExpiry = null
            };
        }
    }

    /// <summary>
    /// Check usage for any subscription type (auto-detect)
    /// </summary>
    public async Task<UsageCheckResult> CheckUsageAsync(string apiKey, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Checking usage for API key: {ApiKey}", apiKey);
            
            // Get client_secret_id from API key using ApiKeyService
            var clientSecretId = await GetClientSecretIdFromApiKeyAsync(apiKey, cancellationToken);
            if (clientSecretId == null)
            {
                return new UsageCheckResult
                {
                    IsValid = false,
                    Reason = "Invalid API key",
                    ErrorCode = UsageCheckErrorCode.ValidationError,
                    Type = "Unknown",
                    Quota = 0,
                    TotalUsage = 0,
                    RemainingQuota = 0,
                    UsagePercentage = 0,
                    IsExpired = false,
                    DaysUntilExpiry = null
                };
            }
            
            const string sql = @"
                SELECT 
                    cs.type,
                    cs.quota,
                    cs.total_usage,
                    cs.status,
                    cs.client_secret_id,
                    client_secret.expired_at
                FROM core.client_subscription cs
                JOIN core.client_secret ON cs.client_secret_id = client_secret.id
                WHERE cs.client_secret_id = @ClientSecretId
                AND cs.status = 'Active'";
            
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var result = await connection.QuerySingleOrDefaultAsync<dynamic>(sql, new { ClientSecretId = clientSecretId });
            
            if (result == null)
            {
                return new UsageCheckResult
                {
                    IsValid = false,
                    Reason = "Subscription not found or inactive",
                    ErrorCode = UsageCheckErrorCode.SubscriptionNotFound,
                    Type = "Unknown",
                    Quota = 0,
                    TotalUsage = 0,
                    RemainingQuota = 0,
                    UsagePercentage = 0,
                    IsExpired = false,
                    DaysUntilExpiry = null
                };
            }

            var type = (string)result.type;
            var quota = (long)result.quota;
            var totalUsage = (long)result.total_usage;
            var remainingQuota = quota > 0 ? quota - totalUsage : long.MaxValue;
            var usagePercentage = quota > 0 ? (double)totalUsage / quota * 100 : 0;
            var expiredAt = result.expired_at as DateTime?;
            var isExpired = expiredAt.HasValue && expiredAt.Value < DateTime.UtcNow;
            var daysUntilExpiry = expiredAt.HasValue ? (int?)(expiredAt.Value - DateTime.UtcNow).TotalDays : null;

            // Determine validity based on subscription type
            bool isValid;
            string reason;
            UsageCheckErrorCode errorCode;

            if (isExpired)
            {
                isValid = false;
                reason = "Subscription expired";
                errorCode = UsageCheckErrorCode.SubscriptionExpired;
            }
            else if (type == "PayAsYouGo")
            {
                isValid = quota == 0 || remainingQuota > 0; // PayAsYouGo is valid if quota is 0 (unlimited) or has remaining quota
                reason = quota > 0 && remainingQuota <= 0 ? "Quota exceeded" : "Valid";
                errorCode = quota > 0 && remainingQuota <= 0 ? UsageCheckErrorCode.QuotaExceeded : UsageCheckErrorCode.None;
            }
            else // Period or OneTime
            {
                isValid = remainingQuota > 0;
                reason = remainingQuota <= 0 ? "Quota exceeded" : "Valid";
                errorCode = remainingQuota <= 0 ? UsageCheckErrorCode.QuotaExceeded : UsageCheckErrorCode.None;
            }

            return new UsageCheckResult
            {
                IsValid = isValid,
                Reason = reason,
                ErrorCode = errorCode,
                Type = type,
                Quota = quota,
                TotalUsage = totalUsage,
                RemainingQuota = remainingQuota,
                UsagePercentage = usagePercentage,
                IsExpired = isExpired,
                DaysUntilExpiry = daysUntilExpiry
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking usage for API key: {ApiKey}", apiKey);
            return new UsageCheckResult
            {
                IsValid = false,
                Reason = "Error checking usage",
                ErrorCode = UsageCheckErrorCode.ValidationError,
                Type = "Unknown",
                Quota = 0,
                TotalUsage = 0,
                RemainingQuota = 0,
                UsagePercentage = 0,
                IsExpired = false,
                DaysUntilExpiry = null
            };
        }
    }

    /// <summary>
    /// Check usage by client_secret_id for any subscription type
    /// </summary>
    public async Task<UsageCheckResult> CheckUsageByClientSecretIdAsync(Guid clientSecretId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Checking usage for client_secret_id: {ClientSecretId}", clientSecretId);
            
            const string sql = @"
                SELECT 
                    cs.type,
                    cs.quota,
                    cs.total_usage,
                    cs.status,
                    cs.client_secret_id,
                    client_secret.expired_at
                FROM core.client_subscription cs
                JOIN core.client_secret ON cs.client_secret_id = client_secret.id
                WHERE cs.client_secret_id = @ClientSecretId
                AND cs.status = 'Active'";
            
            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var result = await connection.QuerySingleOrDefaultAsync<dynamic>(sql, new { ClientSecretId = clientSecretId });
            
            if (result == null)
            {
                return new UsageCheckResult
                {
                    IsValid = false,
                    Reason = "Subscription not found or inactive",
                    ErrorCode = UsageCheckErrorCode.SubscriptionNotFound,
                    Type = "Unknown",
                    Quota = 0,
                    TotalUsage = 0,
                    RemainingQuota = 0,
                    UsagePercentage = 0,
                    IsExpired = false,
                    DaysUntilExpiry = null
                };
            }

            var type = (string)result.type;
            var quota = (long)result.quota;
            var totalUsage = (long)result.total_usage;
            var remainingQuota = quota > 0 ? quota - totalUsage : long.MaxValue;
            var usagePercentage = quota > 0 ? (double)totalUsage / quota * 100 : 0;
            var expiredAt = result.expired_at as DateTime?;
            var isExpired = expiredAt.HasValue && expiredAt.Value < DateTime.UtcNow;
            var daysUntilExpiry = expiredAt.HasValue ? (int?)(expiredAt.Value - DateTime.UtcNow).TotalDays : null;

            // Determine validity based on subscription type
            bool isValid;
            string reason;
            UsageCheckErrorCode errorCode;

            if (isExpired)
            {
                isValid = false;
                reason = "Subscription expired";
                errorCode = UsageCheckErrorCode.SubscriptionExpired;
            }
            else if (type == "PayAsYouGo")
            {
                isValid = quota == 0 || remainingQuota > 0;
                reason = quota > 0 && remainingQuota <= 0 ? "Quota exceeded" : "Valid";
                errorCode = quota > 0 && remainingQuota <= 0 ? UsageCheckErrorCode.QuotaExceeded : UsageCheckErrorCode.None;
            }
            else // Period or OneTime
            {
                isValid = remainingQuota > 0;
                reason = remainingQuota <= 0 ? "Quota exceeded" : "Valid";
                errorCode = remainingQuota <= 0 ? UsageCheckErrorCode.QuotaExceeded : UsageCheckErrorCode.None;
            }

            return new UsageCheckResult
            {
                IsValid = isValid,
                Reason = reason,
                ErrorCode = errorCode,
                Type = type,
                Quota = quota,
                TotalUsage = totalUsage,
                RemainingQuota = remainingQuota,
                UsagePercentage = usagePercentage,
                IsExpired = isExpired,
                DaysUntilExpiry = daysUntilExpiry
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking usage for client_secret_id: {ClientSecretId}", clientSecretId);
            return new UsageCheckResult
            {
                IsValid = false,
                Reason = "Error checking usage",
                ErrorCode = UsageCheckErrorCode.ValidationError,
                Type = "Unknown",
                Quota = 0,
                TotalUsage = 0,
                RemainingQuota = 0,
                UsagePercentage = 0,
                IsExpired = false,
                DaysUntilExpiry = null
            };
        }
    }

    /// <summary>
    /// Ensure a PayAsYouGo subscription exists for the given API key. If not, create it.
    /// </summary>
    public async Task<ClientSubscription?> EnsureClientSubscriptionExists(
        string apiKey,
        string? clientName = null,
        string? contactName = null,
        string? contactEmail = null,
        List<string>? productServices = null,
        CancellationToken cancellationToken = default)
    {
        // Get client_secret_id from API key
        var clientSecretId = await GetClientSecretIdFromApiKeyAsync(apiKey, cancellationToken);
        if (clientSecretId == null)
        {
            _logger.LogError("API key not found: {ApiKey}", apiKey);
            return null;
        }

        // Check if subscription already exists
        const string selectSql = "SELECT * FROM core.client_subscription WHERE client_secret_id = @ClientSecretId";
        await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
        var existing = await connection.QuerySingleOrDefaultAsync<ClientSubscription>(selectSql, new { ClientSecretId = clientSecretId });
        if (existing != null)
        {
            _logger.LogInformation("Subscription already exists for client_secret_id: {ClientSecretId}", clientSecretId);
            return existing;
        }

        // Create new PayAsYouGo subscription
        var subscription = new ClientSubscription
        {
            Id = Guid.NewGuid(),
            ClientSecretId = clientSecretId.Value,
            IsDemo = false,
            Type = "PayAsYouGo",
            Quota = 0,
            TotalUsage = 0,
            Status = SubscriptionConstants.Status.Active,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            Notes = "Auto-created as PayAsYouGo subscription",
            PlanId = null,
            Domain = string.Empty,
            AllowedPaths = "[]",
            OutsetaAccountId = null
        };
        return await CreateAsync(subscription, cancellationToken);
    }
}