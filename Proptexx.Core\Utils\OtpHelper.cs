using Proptexx.Core.Services;
using StackExchange.Redis;

namespace Proptexx.Core.Utils;

public static class OtpHelper
{
    public static async Task<string> Create(ProptexxContext context, string identifier)
    {
        var database = context.GetService<IConnectionMultiplexer>().GetDatabase();
        var otp = PasswordService.GeneratePassword(5);
        var otpService = new RedisOtpService(database);
        await otpService.SetOtpAsync(identifier, otp);
        return otp;
    }
}