using System.ComponentModel.DataAnnotations;
using Proptexx.AI.Widget;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Services;

namespace Proptexx.Web.Command.Widget;

public sealed class SubmitImages : ICommand
{
    [Required] public required string Url { get; init; }

    public List<string> Images { get; init; } = [];

    public bool ForceReload { get; init; }
    
    public async Task ExecuteAsync(CommandContext context)
    {
        var batchId = Guid.NewGuid();
        var workspaceName = context.User.GetWorkspaceName();
        var widgetService = context.GetService<WidgetService>();

        var entry = await widgetService
            .ProcessImages(workspaceName, this.Url, this.Images, context.CancellationToken);

        var images = entry.Images;
        foreach (var image in images) image.Exception = null;
        var status = BatchService.GetStatus(images);
        var entries = images.Where(x => x.Status == 2).ToList();
        var batch = new { batchId, status, entries, images };
        context.AddData("batch", batch);
    }
}
