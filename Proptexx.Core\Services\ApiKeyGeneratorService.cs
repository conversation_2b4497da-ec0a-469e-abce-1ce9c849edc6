namespace Proptexx.Core.Services;

/// <summary>
/// Service for generating API keys using existing ApiKeyService logic
/// Implements Single Responsibility Principle
/// </summary>
public class ApiKeyGeneratorService : IApiKeyGeneratorService
{
    public string GenerateApiKey(Guid secretId)
    {
        return ApiKeyService.CreateApiKey(secretId);
    }

    public string GenerateApiKey(string secretId)
    {
        return ApiKeyService.CreateApiKey(secretId);
    }

    public string GenerateApiKey(Guid clientId, Guid secretId)
    {
        return ApiKeyService.CreateApiKey(clientId, secretId);
    }

    public bool IsValidFormat(string apiKey)
    {
        if (string.IsNullOrWhiteSpace(apiKey))
            return false;

        try
        {
            // Try to parse the API key using existing logic
            var secretId = ApiKeyService.ParseApiKey(apiKey);
            return !string.IsNullOrWhiteSpace(secretId);
        }
        catch
        {
            return false;
        }
    }

    public string? ParseApiKey(string apiKey)
    {
        try
        {
            return ApiKeyService.ParseApiKey(apiKey);
        }
        catch
        {
            return null;
        }
    }
} 