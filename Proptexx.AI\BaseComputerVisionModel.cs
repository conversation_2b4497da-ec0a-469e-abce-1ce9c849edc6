using System.Text.Json;
using Microsoft.Extensions.Logging;
using Proptexx.Core.AI;
using Proptexx.Core.Http;
using Proptexx.Core.Json;
using Proptexx.Core.Services;
using StackExchange.Redis;

namespace Proptexx.AI;

public abstract class BaseComputerVisionModel : BaseModel
{
    private readonly string _modelName;
    private readonly IDatabase _redis;
    private readonly ILogger _logger;
    private readonly string _cacheKey;
    private readonly string _verifyKey;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromHours(24);
    private readonly IComputerVisionClient _computerVisionClient;
    private readonly IImageAssessmentClient _imageAssessmentClient;

    protected BaseComputerVisionModel(string modelName, IComputerVisionClient computerVisionClient, IImageAssessmentClient imageAssessmentClient, IConnectionMultiplexer connectionMultiplexer, ILoggerFactory loggerFactory)
    {
        var implName = GetType().FullName!;
        _modelName = modelName;
        _computerVisionClient = computerVisionClient;
        _imageAssessmentClient = imageAssessmentClient;
        _redis = connectionMultiplexer.GetDatabase();
        _logger = loggerFactory.CreateLogger(modelName);
        _cacheKey = $"model:{implName}";
        _verifyKey = $"model_verify:{implName}";
    }

    public override async Task<ModelResponse> InferAsync(ModelContext context)
    {
        if (context.Payload is null)
        {
            throw new NullReferenceException(nameof(context.Payload));
        }

        var imageUrl = PayloadService.GetRequiredString(context.Payload, "imageUrl");

        var cached = await CheckCacheAsync(imageUrl);
        if (cached is not null) return cached;

        var assessment = await _imageAssessmentClient.InspectImageAsync(imageUrl, context.CancellationToken);
        var response = await InferAsync(context.WorkspaceId, assessment, context.CancellationToken, false);
        return response;
    }

    public Task<ModelResponse> InferAsync(string workspaceId, ImageAssessment assessment, CancellationToken cancellationToken, bool checkCache = true)
    {
        return this.InferAsync(workspaceId, assessment.ImageUrl, assessment.Base64String, assessment.MimeType, cancellationToken, checkCache);
    }

    public async Task<ModelResponse> InferAsync(string workspaceId, string imageUrl, string? base64Image, string mimeType, CancellationToken cancellationToken, bool checkCache = true)
    {
        ModelResponse? cached = null;

        if (checkCache)
        {
            cached = await CheckCacheAsync(imageUrl);
            if (cached is not null) return cached;
        }

        var modelEndpoint = this.GetModelEndpoint(workspaceId);
        var doc = await _computerVisionClient.SendAsync(modelEndpoint, _modelName, imageUrl, base64Image, mimeType, cancellationToken);

        var result = new ModelResponse
        {
            Document = this.ParseModelResponse(doc, imageUrl, modelEndpoint)
        };

        PersistToCache(imageUrl, result.Document);

        if (cached is not null)
        {
            await VerifyCacheResultAsync(imageUrl, cached, result);
        }

        return result;
    }

    protected virtual JsonDocument? ParseModelResponse(JsonDocument doc, string inputImageUrl, string modelEndpoint)
    {
        return doc;
    }

    protected virtual string GetModelEndpoint(string workspaceId)
        => "https://cv.vlm.proptexx.ai/info/";

    private async Task<ModelResponse?> CheckCacheAsync(string imageUrl)
    {
        ModelResponse? result = null;
        try
        {
            var value = await _redis.HashGetAsync(_cacheKey, imageUrl);
            if (!value.HasValue) return null;

            result = new ModelResponse
            {
                Document = JsonDocument.Parse(value.ToString())
            };
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in ComputerVisionModel.CheckCacheAsync");
        }

        return result;
    }

    private void PersistToCache(string imageUrl, JsonDocument? document)
    {
        try
        {
            if (document is null) return;
            var str = JsonSerializer.Serialize(document, JsonDefaults.JsonSerializerOptions);
            var batch = _redis.CreateBatch();
            _ = batch.HashSetAsync(_cacheKey, imageUrl, str);
            _ = batch.HashFieldExpireAsync(_cacheKey, [imageUrl], _cacheExpiry);
            batch.Execute();
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in ComputerVisionModel.PersistToCache");
            Console.WriteLine(e);
        }
    }

    private async Task VerifyCacheResultAsync(string imageUrl, ModelResponse cached, ModelResponse generated)
    {
        string? result = null;

        try
        {
            if (cached.Document is null || generated.Document is null) return;

            var cachedStr = JsonSerializer.Serialize(cached.Document.RootElement);
            var generatedStr = JsonSerializer.Serialize(generated.Document.RootElement);

            if (cachedStr != generatedStr)
            {
                result = $$"""
                           {
                            "cached": {{cachedStr}},
                            "generated": {{generatedStr}}
                           }
                           """;
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in ComputerVisionModel.VerifyCacheResultAsync #1");
            result = e.Message;
        }

        try
        {
            if (!string.IsNullOrWhiteSpace(result))
            {
                await _redis.HashSetAsync(_verifyKey, imageUrl, result);
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in ComputerVisionModel.VerifyCacheResultAsync #2");
        }
    }
}
