using Proptexx.Web;
using Proptexx.Web.SiteProxy;

var builder = WebApplication.CreateBuilder(args)
    .AddProptexxWeb();

builder.Services.AddScoped<WebProxyHandler>();

var app = builder.Build();
app.MapGet("/", () => "Proptexx");
app.MapGet("/health", () => "OK");
app.MapGet("/proxy", (WebProxyHandler handler, HttpContext context) => handler.InvokeAsync(context));
app.MapGet("/scrape-images-by-url", (WebScrapeHandler handler, HttpContext context) => handler.InvokeAsync(context));
app.Run();