
using Proptexx.Web;
using Proptexx.Web.ImageProxy;

var builder = WebApplication.CreateBuilder(args)
    .AddProptexxWeb();

// Add CORS policy to allow any origin
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy
            .AllowAnyOrigin()
            .AllowAnyHeader()
            .AllowAnyMethod();
    });
});

builder.Services.AddSingleton<PlaywrightBrowser>();
builder.Services.AddSingleton<ImageProxyHandler>();

var app = builder.Build();

// Use CORS middleware
app.UseCors();

app.MapGet("/", () => "Proptexx | Image Proxy");
app.MapGet("/health", () => "OK");
app.MapGet("/proxy", (ImageProxyHandler handler, HttpContext context) => handler.InvokeAsync(context));

var browser = app.Services.GetRequiredService<PlaywrightBrowser>();
await browser.InitializeAsync();
app.Run();

