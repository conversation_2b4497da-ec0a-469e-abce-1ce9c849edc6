using Proptexx.Core.Entities;

namespace Proptexx.Core.Json.Converters;

public sealed class ProductConfigJsonConverter : DiscriminatorJsonConverter<ProductConfig>
{
    public ProductConfigJsonConverter() : base("configType", false, Types)
    {
    }

    private static readonly Dictionary<string, Type> Types = new()
    {
        ["widget-access"] = typeof(ProductWidgetAccessConfig),
        ["ecommerce-widget"] = typeof(ProductWidgetAccessConfig),
        ["real-estate-widget"] = typeof(ProductRealEstateWidgetConfig),
    };

}