using Microsoft.Extensions.Logging;
using Npgsql;
using Proptexx.Core.Constants;

namespace Proptexx.Core.Services;

/// <summary>
/// Interface for subscription classification service
/// </summary>
public interface ISubscriptionClassificationService
{
    /// <summary>
    /// Classify ecommerce widget type based on workspace analysis
    /// </summary>
    Task<string> ClassifyEcommerceWidgetTypeAsync(Guid workspaceId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Classify product services based on workspace analysis
    /// Returns a list of product services that the workspace should have access to
    /// </summary>
    Task<List<string>> ClassifyProductServicesAsync(Guid workspaceId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all available product services
    /// </summary>
    string[] GetAvailableProductServices();
} 