using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class Message : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; } = Guid.NewGuid();
    
    public Guid? ParentId { get; set; }
    
    public Guid? SenderId { get; init; }
    
    public required string Subject { get; init; }

    public string? Content { get; init; }

    public bool IsRich { get; init; }

    public bool IsHidden { get; init; }

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public DateTime? ScheduledAt { get; private set; }

    public DateTime? ProcessedAt { get; private set; }

    public string GetDbRef() => "core.message";

    public Message ScheduleForSend(DateTime? dateTime = null)
    {
        var val = dateTime ?? DateTime.UtcNow;

        // Ensure the DateTime is in UTC
        if (val.Kind != DateTimeKind.Utc)
        {
            val = val.ToUniversalTime();
        }

        if (val < DateTime.UtcNow)
        {
            throw new FormatException("Message has to be scheduled for now or in the future");
        }

        this.ProcessedAt = val;
        return this;
    }

    public Message Processing()
    {
        this.ProcessedAt = DateTime.UtcNow;
        return this;
    }
}