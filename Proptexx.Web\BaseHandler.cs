using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Json;
using Proptexx.Web.Extensions;

namespace Proptexx.Web;

public abstract class BaseHandler
{
    protected BaseHandler(ILogger logger)
    {
        Logger = logger;
    }

    protected ILogger Logger { get; }

    protected abstract Task HandleAsync(HttpContext httpContext);

    public async Task InvokeAsync(HttpContext httpContext)
    {
        try
        {
            await this.HandleAsync(httpContext);
        }
        catch (ApplicationException e)
        {
            Logger.LogError(e, "Application Exception");
            httpContext.Response.SetStatus(StatusCodes.Status400BadRequest);
        }
        catch (Exception e)
        {
            Logger.LogError(e, "System Exception");
            httpContext.Response.SetStatus(StatusCodes.Status500InternalServerError);
        }
    }
    
    protected static async Task LogAsync(string method, string route, HttpRequest request)
    {
        // Log the method and route
        Console.WriteLine($"{method} request on {route}");
    
        // Log the query string
        Console.WriteLine($"Query String: {request.QueryString}");
    
        // Log headers
        Console.WriteLine("Headers:");
        foreach (var header in request.Headers)
        {
            Console.WriteLine($"{header.Key}: {header.Value}");
        }

        // Log the body if it's available and not empty
        if (request.ContentLength > 0 && request.Body.CanRead)
        {
            // Enable seeking on the request body stream if it's not already enabled
            request.EnableBuffering();

            // Read the request body
            using var reader = new StreamReader(request.Body, leaveOpen: true);
            var body = await reader.ReadToEndAsync();
            Console.WriteLine("Body:");
            Console.WriteLine(body);

            // Reset the request body stream position so it can be read again by the next middleware/controller
            request.Body.Position = 0;
        }

        // Log other relevant metadata
        Console.WriteLine($"Host: {request.Host}");
        Console.WriteLine($"Protocol: {request.Protocol}");
        Console.WriteLine($"Path: {request.Path}");
        Console.WriteLine($"PathBase: {request.PathBase}");
        Console.WriteLine($"Is HTTPS: {request.IsHttps}");
    }
}

public abstract class BaseHandler<T> : BaseHandler
{
    protected BaseHandler(ILogger logger) : base(logger)
    {
    }

    protected override async Task HandleAsync(HttpContext httpContext)
    {
        if (!httpContext.Request.Cookies.TryGetValue("sessionId", out var sessionId) || !Guid.TryParse(sessionId, out var sessionGuid))
        {
            throw new ApplicationException("Invalid session");
        }

        var payload = await JsonSerializer.DeserializeAsync<T>(httpContext.Request.Body, JsonDefaults.CompactOptions) 
                      ?? throw new ApplicationException("Unable to process payload");

        await this.HandleAsync(httpContext, sessionGuid, payload);
    }

    protected abstract Task HandleAsync(HttpContext httpContext, Guid sessionId, T payload);
}
