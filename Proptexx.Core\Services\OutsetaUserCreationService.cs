using Dapper;
using Microsoft.Extensions.Logging;
using Npgsql;
using Proptexx.Core.Constants;
using Proptexx.Core.DTOs;
using Proptexx.Core.DTOs.Email;
using Proptexx.Core.Entities;
using Proptexx.Core.Interface;
using Proptexx.Core.Messaging;
using Proptexx.Core.Stores;
using Proptexx.Core.Utils;
using System.Text.Json;

namespace Proptexx.Core.Services;

public class OutsetaUserCreationService(
        ILogger<OutsetaUserCreationService> logger,
        NpgsqlConnection connection,
        NpgsqlDataSource npgsqlDataSource,
        ISubscriptionClassificationService subscriptionClassificationService,
        ISubscriptionService subscriptionService) : IOutsetaUserCreationService
{
    // Widget client data is now stored in api_key_clients table
 
    public async Task<string> ProcessUserRegistrationOutSetaAsync(OutsetaAccountData registrationData)
    {
        await using var connection = await npgsqlDataSource.OpenConnectionAsync();
        await using var transaction = await connection.BeginTransactionAsync();

        try
        {
            // Create registration data
            var data = new OutsetaRegistrationData
            {
                RegistrationData = registrationData
            };

            // Step 1: Create Account and Workspace
            await CreateAccountWorkspaceAsync(data);

            // Step 2: Create Client and API Key
            await CreateClientApiKeyAsync(data);

            // Step 3: Enable Services
            await EnableServiceAsync(data);

            // Step 4: Add Domain Whitelisting (if store URL provided)
            if (!string.IsNullOrWhiteSpace(registrationData.StoreUrl))
            {
                await AddDomainWhiteListingAsync(data);
            }
            // Step 5: Register API key in unified system with auto-classified type (includes widget data)
            await RegisterApiKeyInUnifiedSystemAsync(data);

            // Step 7: Send Welcome Email
            await SendWelcomeEmailAsync(data);

            await transaction.CommitAsync();

            logger.LogInformation("Successfully processed Outseta registration for {Email}, WorkspaceId: {WorkspaceId}",
                registrationData.Email, data.WorkspaceId);

            return data.ApiKey ?? throw new InvalidOperationException("API key was not created");
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            logger.LogError(ex, "Failed to process Outseta registration for {Email}", registrationData.Email);
            throw;
        }
    }
    public async Task AddDomainWhiteListingAsync(OutsetaRegistrationData context)
    {
        try
        {
            var storeUrl = context.RegistrationData.StoreUrl;
            if (string.IsNullOrWhiteSpace(storeUrl))
            {
                logger.LogInformation("No store URL provided, skipping domain whitelisting");
                return;
            }

            if (context.WorkspaceId == null)
            {
                throw new InvalidOperationException("Workspace ID is required but not found in context");
            }
            var workspaceId = context.WorkspaceId.Value;

            // Normalize the store URL - remove protocol if present to get clean hostname
            var hostname = storeUrl.StartsWith("http://") ? storeUrl.Substring(7) :
                          storeUrl.StartsWith("https://") ? storeUrl.Substring(8) :
                          storeUrl;

            // Remove trailing slash if present
            hostname = hostname.TrimEnd('/');

            // Create domain entries (hostname without protocol, input_value with full URL)
            var domainEntries = new List<(string hostname, string inputValue)>();

            // Add both www and non-www versions
            if (!hostname.StartsWith("www."))
            {
                domainEntries.Add((hostname, $"https://{hostname}"));
                domainEntries.Add(($"www.{hostname}", $"https://www.{hostname}"));
            }
            else
            {
                domainEntries.Add((hostname, $"https://{hostname}"));
                domainEntries.Add((hostname.Substring(4), $"https://{hostname.Substring(4)}"));
            }

            foreach (var (hostName, inputValue) in domainEntries)
            {
                // Check if domain already exists for this workspace
                var existingCount = await connection.QuerySingleAsync<int>(@"
                    SELECT COUNT(*) FROM core.domain 
                    WHERE workspace_id = @workspaceId AND hostname = @hostname",
                    new { workspaceId, hostname = hostName });

                if (existingCount == 0)
                {
                    await connection.ExecuteAsync(@"
                        INSERT INTO core.domain (workspace_id, hostname, path, input_value, match_strategy)
                        VALUES (@workspaceId, @hostname, @path, @inputValue, @matchStrategy)",
                        new
                        {
                            workspaceId,
                            hostname = hostName,
                            path = "/",
                            inputValue,
                            matchStrategy = 0
                        });
                }
            }

            logger.LogInformation("Added domain whitelisting for workspace {WorkspaceId}: {Domains}",
                workspaceId, string.Join(", ", domainEntries.Select(x => x.hostname)));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to add domain whitelisting for {StoreUrl}", context.RegistrationData.StoreUrl);
            throw;
        }
    }

    public async Task CreateAccountWorkspaceAsync(OutsetaRegistrationData data)
    {
        try
        {
            var registrationData = data.RegistrationData;

            // Step 1: Check if account already exists
            var existingAccount = await connection.Account().GetAccountIdByEmailAsync(registrationData.Email);
            if (existingAccount != null)
            {
                logger.LogInformation("Account already exists for email: {Email}", registrationData.Email);
                throw new InvalidOperationException($"Account already exists for email: {registrationData.Email}");
            }

            // Step 2: Create account using existing OnboardHelper
            var password = PasswordService.GeneratePassword(8);
            var account = await OnboardHelper.OnboardAccount(connection, registrationData.FullName,
                registrationData.Email, true, password, registrationData.Phone, true);

            // Step 3: Create workspace using existing OnboardHelper
            var workspace = await OnboardHelper.CreateWorkspace(connection, account,
                registrationData.CompanyName, null, null);

            // Update context with created entities
            data.AccountId = account.Id;
            data.WorkspaceId = workspace.Id;

            logger.LogInformation("Created account {AccountId} and workspace {WorkspaceId} for {Email}",
                account.Id, workspace.Id, registrationData.Email);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to create account and workspace for {Email}", data.RegistrationData.Email);
            throw;
        }
    }

    public async Task CreateClientApiKeyAsync(OutsetaRegistrationData context)
    {
        try
        {
            if (context.WorkspaceId == null)
            {
                throw new InvalidOperationException("Workspace ID is required but not found in context");
            }

            var workspaceId = context.WorkspaceId.Value;

            // Create client for widget access
            const string clientName = "widget";
            const string scopes = "widget login otp";
            var clientId = await connection.Client().Create(workspaceId, clientName, scopes, false);
            var clientSecret = await connection.Client().AddSecret(clientId);

            // Generate API key using existing service
            var apiKey = ApiKeyService.CreateApiKey(clientSecret.Id);

            // Update context with API key
            context.ApiKey = apiKey;

            logger.LogInformation("Created client {ClientId} and API key for workspace {WorkspaceId}",
                clientId, workspaceId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to create client and API key for workspace {WorkspaceId}", context.WorkspaceId);
            throw;
        }
    }

    public async Task EnableServiceAsync(OutsetaRegistrationData context)
    {
        try
        {
            if (context.WorkspaceId == null)
            {
                throw new InvalidOperationException("Workspace ID is required but not found in context");
            }

            var workspaceId = context.WorkspaceId.Value;

            // Enable widget-access service
            var requestedServices = new List<RequestedService>
            {
                new() { WorkspaceId = workspaceId, ServiceId = "widget-access", ExpiresAt = null }
            };

            await OrderManager.EnableServicesAsync(connection, workspaceId, requestedServices);

            logger.LogInformation("Enabled widget-access service for workspace {WorkspaceId}", workspaceId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to enable services for workspace {WorkspaceId}", context.WorkspaceId);
            throw;
        }
    }

    // Widget client data is now handled in RegisterApiKeyInUnifiedSystemAsync

    private static string? GetDomainFromStoreUrl(string? storeUrl)
    {
        if (string.IsNullOrWhiteSpace(storeUrl))
            return null;

        try
        {
            // Remove protocol prefix
            var url = storeUrl;
            if (url.StartsWith("https://", StringComparison.OrdinalIgnoreCase))
                url = url[8..];
            else if (url.StartsWith("http://", StringComparison.OrdinalIgnoreCase))
                url = url[7..];

            // Remove trailing slash and extract hostname
            url = url.TrimEnd('/');
            var slashIndex = url.IndexOf('/');

            return slashIndex > 0 ? url[..slashIndex] : url;
        }
        catch (Exception)
        {
            return null;
        }
    }

    private static int GetQuotaForPlanId(string? planId)
    {
        if (string.IsNullOrWhiteSpace(planId))
            return 10;

        if (SubscriptionPlans.Plans.TryGetValue(planId, out var plan))
        {
            return plan.Quota > 0 ? plan.Quota : 10;
        }

        return 10; // Default quota if plan ID not found
    }

    // Outseta account checking is now handled in api_key_clients table

    public async Task RegisterApiKeyInUnifiedSystemAsync(OutsetaRegistrationData context)
    {
        try
        {
            if (context.WorkspaceId == null || string.IsNullOrWhiteSpace(context.ApiKey))
            {
                logger.LogWarning("Cannot register API key in unified system: missing workspace ID or API key");
                return;
            }

            // Classify API key type using workspace analysis
            var productService = await subscriptionClassificationService.ClassifyEcommerceWidgetTypeAsync(
                context.WorkspaceId.Value);

            // Use injected registration service

            // Check if already registered
            //if (await apiKeyRegistrationService.IsApiKeyRegisteredAsync(context.ApiKey, CancellationToken.None))
            //{
            //    logger.LogInformation("API key already registered in unified system for workspace {WorkspaceId}", context.WorkspaceId);
            //    return;
            //}

            // Parse user name
            var fullName = context.RegistrationData.FullName?.Split(' ', 2) ?? Array.Empty<string>();
            var firstName = fullName.Length > 0 ? fullName[0] : context.RegistrationData.Email.Split('@')[0];
            var lastName = fullName.Length > 1 ? fullName[1] : "";

            // Register in unified system with widget data
            //var apiKeyClient = await apiKeyRegistrationService.RegisterApiKeyAsync(
            //    apiKey: context.ApiKey,
            //    productService: productService,
            //    clientName: context.RegistrationData.CompanyName ?? $"{firstName}'s Workspace",
            //    contactName: $"{firstName} {lastName}".Trim(),
            //    contactEmail: context.RegistrationData.Email,
            //    environment: "Prod",
            //    subscriptionType: "Monthly", // Default to Monthly for Outseta registrations
            //    rateLimitMonthly: GetQuotaForPlanId(context.RegistrationData.PlanUid),
            //    workspaceId: context.WorkspaceId,
            //    accountId: context.AccountId,
            //    planId: context.RegistrationData.PlanUid,
            //    domain: GetDomainFromStoreUrl(context.RegistrationData.StoreUrl),
            //    allowedPaths: ["/"],
            //    outsetaId: context.RegistrationData.AccountUid,
            //    notes: $"Auto-registered from Outseta webhook. Outseta ID: {context.RegistrationData.AccountUid}",
            //    cancellationToken: CancellationToken.None);

            //logger.LogInformation("Successfully registered API key in unified system as {ProductService} for workspace {WorkspaceId}, outseta ID {OutsetaId}",
            //    productService, context.WorkspaceId, context.RegistrationData.AccountUid);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to register API key in unified system for workspace {WorkspaceId}", context.WorkspaceId);
            // Don't throw - this is not critical for the main flow
        }
    }

    public async Task SendWelcomeEmailAsync(OutsetaRegistrationData context)
    {
        try
        {
            // Parse Outseta data
            var fullName = context.RegistrationData.FullName?.Split(' ', 2) ?? Array.Empty<string>();
            var firstName = fullName.Length > 0 ? fullName[0] : context.RegistrationData.Email.Split('@')[0];
            var lastName = fullName.Length > 1 ? fullName[1] : "";

            var emailData = new OutSetaEmailData
            {
                Email = context.RegistrationData.Email,
                FirstName = firstName,
                LastName = lastName,
                WorkspaceName = context.RegistrationData.CompanyName ?? $"{firstName}'s Workspace",
                WorkspaceId = context.WorkspaceId?.ToString() ?? "",
                ApiKey = context.ApiKey ?? ""
            };

            // Generate template using existing EmailTemplate
            var (subject, body) = EmailTemplate.OnOutsetaAccountRegistration(emailData);

            // Queue email using existing infrastructure
            await QueueEmailAsync(connection, emailData.Email, subject, body);

            logger.LogInformation("Outseta welcome email queued for {Email} with workspace {WorkspaceId}",
                emailData.Email, context.WorkspaceId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to queue Outseta welcome email for {Email}",
                context.RegistrationData.Email);
            throw;
        }
    }

    private async Task QueueEmailAsync(NpgsqlConnection connection, string recipient, string subject, string body)
    {
        // Insert message into existing queue system
        var messageId = await connection.QuerySingleAsync<Guid>("""
            INSERT INTO core.message (subject, content, is_rich, created_at) 
            VALUES (@subject, @body, @isRich, @createdAt)
            RETURNING id
            """, new { subject, body, isRich = true, createdAt = DateTime.UtcNow });

        // Insert recipient into queue
        await connection.ExecuteAsync("""
            INSERT INTO core.message_recipient (message_id, recipient, message_type, status, created_at)
            VALUES (@messageId, @recipient, @messageType, @status, @createdAt)
            """, new
        {
            messageId,
            recipient,
            messageType = 1,
            status = 0,
            createdAt = DateTime.UtcNow
        });

        logger.LogInformation("Email queued: MessageId={MessageId}, Recipient={Recipient}", messageId, recipient);
    }
}
