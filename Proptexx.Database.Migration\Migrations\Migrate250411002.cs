using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations;

[Migration(250411002)]
public class Migrate250411002 : FluentMigrator.Migration
{
    public override void Up()
    {
        // Drop existing views if they exist.
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_widget_metrics_summary CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_widget_metrics_today CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_widget_metrics_past CASCADE;");

        // This helper function creates the widget metrics views (today or past) by providing a date condition.
        string CreateMetricsView(string viewName, string dateCondition) => $@"
            CREATE MATERIALIZED VIEW {viewName} AS
            WITH
            -- Base events from the logs that apply to widgets.
            base AS (
                SELECT
                    workspace_id,
                    created_at::date AS day,
                    endpoint,
                    identifier,
                    client_version,
                    response_code,
                    session_id,
                    ip,
                    headers,
                    created_at,
                    request_body
                FROM telemetry.api_logs
                WHERE {dateCondition}
                  AND is_widget = true
            ),
            -- Signin events (only API calls with endpoint '/_auth' and response code 200).
            signins AS (
                SELECT workspace_id, day, COUNT(*) AS total_signins
                FROM base
                WHERE endpoint = '/_auth'
                  AND response_code = 200
                GROUP BY workspace_id, day
            ),
            -- Renders: For events identified as widget renders:
            renders AS (
                SELECT workspace_id, day,
                       COUNT(*) AS total_renders,
                       /* Count distinct render users by using session_id when available or ip if null */
                       COUNT(DISTINCT COALESCE(session_id::text, ip)) AS distinct_render_users,
                       /* Average renders per user = total renders divided by distinct user count (with a 0 check) */
                       ROUND(COUNT(*)::numeric / NULLIF(COUNT(DISTINCT COALESCE(session_id::text, ip)), 0), 1) AS average_renders_per_user
                FROM base
                WHERE identifier = 'widget.indoorStagingOrRefurnishing'
                GROUP BY workspace_id, day
            ),
            -- Engagement: Calculate average user engagement time taking into account a 10 minute inactivity threshold.
            -- Step 1: Define a unified session identifier (using session_id or ip)
            base_sessions AS (
                SELECT
                    workspace_id,
                    COALESCE(session_id::text, ip) AS session_identifier,
                    created_at,
                    created_at::date AS day
                FROM base
                WHERE identifier = 'widget.indoorStagingOrRefurnishing'
            ),
            -- Step 2: Order events per session and fetch the previous event time.
            ordered_sessions AS (
                SELECT
                    workspace_id,
                    session_identifier,
                    day,
                    created_at,
                    LAG(created_at) OVER (
                        PARTITION BY workspace_id, day, session_identifier
                        ORDER BY created_at
                    ) AS prev_created_at
                FROM base_sessions
            ),
            -- Step 3: Mark events as starting a new session chunk if the gap is more than 10 minutes (600 seconds) or if it is the first event.
            marked_sessions AS (
                SELECT 
                    *,
                    CASE 
                        WHEN prev_created_at IS NULL OR EXTRACT(EPOCH FROM (created_at - prev_created_at)) > 600
                        THEN 1 
                        ELSE 0 
                    END AS new_chunk
                FROM ordered_sessions
            ),
            -- Step 4: For each session identifier, do a running sum to group events into session chunks.
            grouped_sessions AS (
                SELECT 
                    workspace_id,
                    session_identifier,
                    day,
                    created_at,
                    SUM(new_chunk) OVER (
                        PARTITION BY workspace_id, day, session_identifier 
                        ORDER BY created_at
                    ) AS chunk_id
                FROM marked_sessions
            ),
            -- Step 5: For each chunk, calculate the session duration (difference between first and last event)
            -- and cap the duration at 30 minutes.
            session_chunks AS (
                SELECT 
                    workspace_id, 
                    day, 
                    session_identifier, 
                    chunk_id,
                    MIN(created_at) AS start_time,
                    MAX(created_at) AS end_time,
                    LEAST(EXTRACT(EPOCH FROM (MAX(created_at) - MIN(created_at)))/60, 30) AS eng_time
                FROM grouped_sessions
                GROUP BY workspace_id, day, session_identifier, chunk_id
            ),
            -- Step 6: Average the per-chunk engagement times per workspace and day.
            engagement AS (
                SELECT workspace_id, day, AVG(eng_time) AS average_engagement_time
                FROM session_chunks
                GROUP BY workspace_id, day
            ),
            -- Top room type by frequency.
            top_room AS (
                SELECT DISTINCT ON (workspace_id, day)
                       workspace_id, day,
                       (request_body::json -> 'payload' ->> 'roomType') AS top_room_type
                FROM base
                WHERE identifier = 'widget.indoorStagingOrRefurnishing'
                  AND request_body IS NOT NULL
                  AND (request_body::json -> 'payload' ->> 'roomType') IS NOT NULL
                GROUP BY workspace_id, day, request_body
                ORDER BY workspace_id, day,
                         COUNT(*) OVER (PARTITION BY workspace_id, day, (request_body::json -> 'payload' ->> 'roomType')) DESC
            ),
            -- Top architecture style by frequency.
            top_style AS (
                SELECT DISTINCT ON (workspace_id, day)
                       workspace_id, day,
                       (request_body::json -> 'payload' ->> 'architectureStyle') AS top_style
                FROM base
                WHERE identifier = 'widget.indoorStagingOrRefurnishing'
                  AND request_body IS NOT NULL
                  AND (request_body::json -> 'payload' ->> 'architectureStyle') IS NOT NULL
                GROUP BY workspace_id, day, request_body
                ORDER BY workspace_id, day,
                         COUNT(*) OVER (PARTITION BY workspace_id, day, (request_body::json -> 'payload' ->> 'architectureStyle')) DESC
            ),
            -- Unique combinations of workspace and day.
            distinct_days AS (
                SELECT DISTINCT workspace_id, day FROM base
            )
            -- Final select: join the results from all the CTEs.
            SELECT
                d.workspace_id,
                d.day,
                COALESCE(s.total_signins, 0) AS total_signins,
                COALESCE(r.total_renders, 0) AS total_renders,
                COALESCE(r.average_renders_per_user, 0) AS average_renders_per_user,
                COALESCE(e.average_engagement_time, 0) AS average_engagement_time,
                COALESCE(tr.top_room_type, '') AS top_room_type,
                COALESCE(ts.top_style, '') AS top_style,
                COALESCE(r.distinct_render_users, 0) AS distinct_render_users
            FROM distinct_days d
            LEFT JOIN signins s ON s.workspace_id = d.workspace_id AND s.day = d.day
            LEFT JOIN renders r ON r.workspace_id = d.workspace_id AND r.day = d.day
            LEFT JOIN engagement e ON e.workspace_id = d.workspace_id AND e.day = d.day
            LEFT JOIN top_room tr ON tr.workspace_id = d.workspace_id AND tr.day = d.day
            LEFT JOIN top_style ts ON ts.workspace_id = d.workspace_id AND ts.day = d.day
            WITH NO DATA;
        ";

        // Create today's view and add its unique index.
        Execute.Sql(CreateMetricsView("telemetry.mv_widget_metrics_today", "created_at::date = current_date"));
        Execute.Sql("CREATE UNIQUE INDEX idx_mv_widget_metrics_today_unique ON telemetry.mv_widget_metrics_today(workspace_id, day);");

        // Create past view and add its unique index.
        Execute.Sql(CreateMetricsView("telemetry.mv_widget_metrics_past", "created_at::date < current_date"));
        Execute.Sql("CREATE UNIQUE INDEX idx_mv_widget_metrics_past_unique ON telemetry.mv_widget_metrics_past(workspace_id, day);");

        //Drop the materialized view if it already exists
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_widget_metrics_summary;");

        //Create the materialized view with aggregated calculations
        Execute.Sql($@"CREATE MATERIALIZED VIEW telemetry.mv_widget_metrics_summary AS
        SELECT
            day,
            workspace_id,
            SUM(total_signins) AS total_signins,
            SUM(total_renders) AS total_renders,
            AVG(average_engagement_time) AS average_engagement_time,
            ROUND(SUM(total_renders)::NUMERIC / NULLIF(SUM(distinct_render_users), 0), 1) AS average_renders_per_user,
            MAX(top_room_type) AS top_room_type,
            MAX(top_style) AS top_style
        FROM (
                SELECT * FROM telemetry.mv_widget_metrics_today
                UNION ALL
                SELECT * FROM telemetry.mv_widget_metrics_past
            ) AS combined_metrics
        GROUP BY day, workspace_id
        WITH NO DATA;;");

        //Create an index to improve performance on key lookup columns
        Execute.Sql($@"CREATE INDEX idx_mv_widget_metrics_summary_workspace_day
            ON telemetry.mv_widget_metrics_summary(workspace_id, day);    ");

        //fixing existing queries to add workspaceId filter

        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_dashboard_summary CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_dashboard_summary_past CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_dashboard_summary_today CASCADE;");

        Execute.Sql(@"CREATE MATERIALIZED VIEW IF NOT EXISTS telemetry.mv_dashboard_summary_today
                        AS
                        SELECT workspace_id,
                               created_at::date AS day,
                               COUNT(*) FILTER (WHERE is_api_request) AS total_api_requests,
                               COUNT(*) FILTER (WHERE is_ai_request) AS total_ai_requests,
                               COUNT(*) FILTER (WHERE is_batch) AS total_batch_requests,
                               ROUND(COALESCE(AVG(duration_ms), 0::double precision)::numeric, 1) AS average_request_duration,
                               COUNT(DISTINCT session_id) AS distinct_sessions
                        FROM telemetry.api_logs
                        WHERE created_at >= date_trunc('day', now() AT TIME ZONE 'UTC') -- Midnight UTC
                          AND created_at < now() AT TIME ZONE 'UTC'  -- Current time UTC
                        GROUP BY workspace_id, created_at::date
                        WITH NO DATA;");

        Execute.Sql(@"CREATE MATERIALIZED VIEW IF NOT EXISTS telemetry.mv_dashboard_summary_past
                        AS
                        SELECT workspace_id,
                               created_at::date AS day,
                               COUNT(*) FILTER (WHERE is_api_request) AS total_api_requests,
                               COUNT(*) FILTER (WHERE is_ai_request) AS total_ai_requests,
                               COUNT(*) FILTER (WHERE is_batch) AS total_batch_requests,
                               ROUND(COALESCE(AVG(duration_ms), 0::double precision)::numeric, 1) AS average_request_duration,
                               COUNT(DISTINCT session_id) AS distinct_sessions
                        FROM telemetry.api_logs
                        WHERE created_at < date_trunc('day', now() AT TIME ZONE 'UTC') -- Before Midnight UTC
                        GROUP BY workspace_id, created_at::date
                        WITH NO DATA;");

        Execute.Sql(@"CREATE MATERIALIZED VIEW IF NOT EXISTS telemetry.mv_dashboard_summary
                        AS
                        SELECT workspace_id,
                               day,
                               SUM(total_api_requests) AS total_api_requests,
                               SUM(total_ai_requests) AS total_ai_requests,
                               SUM(total_batch_requests) AS total_batch_requests,
                               ROUND(COALESCE(AVG(average_request_duration), 0::double precision)::numeric, 1) AS average_request_duration,
                               SUM(distinct_sessions) AS distinct_sessions
                        FROM (
                            SELECT * FROM telemetry.mv_dashboard_summary_today
                            UNION ALL
                            SELECT * FROM telemetry.mv_dashboard_summary_past
                        ) AS combined
                        GROUP BY workspace_id, day
                        WITH NO DATA;");

        Execute.Sql(@"CREATE INDEX IF NOT EXISTS idx_mv_dashboard_today_day 
                        ON telemetry.mv_dashboard_summary_today (workspace_id, day);

                      CREATE INDEX IF NOT EXISTS idx_mv_dashboard_today_total_requests 
                        ON telemetry.mv_dashboard_summary_today (total_api_requests, total_ai_requests, total_batch_requests);");

        Execute.Sql(@"CREATE INDEX IF NOT EXISTS idx_mv_dashboard_past_day 
                        ON telemetry.mv_dashboard_summary_past (workspace_id, day);

                      CREATE INDEX IF NOT EXISTS idx_mv_dashboard_past_total_requests 
                        ON telemetry.mv_dashboard_summary_past (total_api_requests, total_ai_requests, total_batch_requests);");

        Execute.Sql(@"CREATE INDEX IF NOT EXISTS idx_mv_dashboard_combined_day 
                        ON telemetry.mv_dashboard_summary (workspace_id, day);

                      CREATE INDEX IF NOT EXISTS idx_mv_dashboard_combined_total_requests 
                        ON telemetry.mv_dashboard_summary (total_api_requests, total_ai_requests, total_batch_requests);");
    }
    public override void Down()
    {
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_widget_metrics_summary CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_widget_metrics_today CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_widget_metrics_past CASCADE;");
    }
}