using Proptexx.Core.Services;
using Proptexx.Web.Auth.Auth;
using StackExchange.Redis;

namespace Proptexx.Web.Auth.Resolvers;

public sealed class StoreResolver : IScopeResolver
{
    private readonly IDatabase _redis;

    public StoreResolver(IConnectionMultiplexer connectionMultiplexer)
    {
        _redis = connectionMultiplexer.GetDatabase();
    }
    
    public async Task ResolveAsync(ScopeContext context, CancellationToken cancellationToken)
    {
        if (context.Payload is null) throw new BadHttpRequestException("Payload is null");
        var sessionId = PayloadService.GetOptionalString(context.Payload, "sessionId");
        if (string.IsNullOrWhiteSpace(sessionId)) return;

        var session = await _redis.HashGetAsync("sessions", sessionId);
    }
}