namespace Proptexx.Web.Auth.Auth;

public interface IScopeResolverFactory
{
    IScopeResolver? GetResolver(string scope);
}

public sealed class ScopeResolverFactory : IScopeResolverFactory
{
    private readonly IServiceProvider _services;

    public ScopeResolverFactory(IServiceProvider services)
    {
        _services = services;
    }

    public IScopeResolver? GetResolver(string scope)
    {
        return _services.GetKeyedService<IScopeResolver>(scope);
    }
}