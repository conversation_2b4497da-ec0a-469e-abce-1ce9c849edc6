using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class AccountSecret : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; } = Guid.NewGuid();

    public required Guid AccountId { get; init; }

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public DateTime? ExpiredAt { get; private set; }

    public required string Hash { get; init; }

    public required string Salt { get; init; }

    public string GetDbRef() => "core.account_secret";

    public AccountSecret SetExpired(DateTime expiredAt)
    {
        this.ExpiredAt = expiredAt;
        return this;
    }

    public AccountSecret RevokeExpired()
    {
        this.ExpiredAt = null;
        return this;
    }
}