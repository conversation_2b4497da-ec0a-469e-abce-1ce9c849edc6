using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class CouponProductBinding : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; init; } = Guid.NewGuid();

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public Guid CouponId { get; init; }
    
    public Guid? ProductId { get; init; }

    public DateTime? ValidFrom { get; init; }

    public DateTime? ValidTo { get; init; }

    public Guid CreatedBy { get; init; }

    public string GetDbRef() => "core.coupon_product_binding";
}