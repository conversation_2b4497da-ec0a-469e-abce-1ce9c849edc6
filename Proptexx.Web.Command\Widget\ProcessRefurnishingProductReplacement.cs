using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using Proptexx.AI;
using Proptexx.AI.Models.Flow;
using Proptexx.Core;
using Proptexx.Core.AI;
using Proptexx.Core.Attributes;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Resilience;
using Proptexx.Core.Services;
using StackExchange.Redis;
using static Proptexx.Core.Constants.WidgetConstants;

namespace Proptexx.Web.Command.Widget;

[RequiredService(ServiceNames.EcommerceWidget, ServiceNames.WidgetAccess, ErrorMessage = "Access denied: E-commerce widget or widget access service required")]
public sealed class ProcessRefurnishingProductReplacement : ICommand
{
    private static readonly Random _rnd = new();

    private const string CacheHashKey = "widget_generative_cache";

    [Required]
    public required string RoomImage { get; init; }
    
    [Required]
    public required string ProductImageUrl { get; init; }
    
    [Required]
    public required string ProductName { get; init; }

    public string ProductDescription { get; init; } = string.Empty;

    public int? Seed { get; init; }
 
    public async Task ExecuteAsync(CommandContext context)
    {
        var logger = context.GetService<ILogger<ProcessRefurnishingProductReplacement>>();

        try
        {
            var workspaceId = context.User.GetWorkspaceId();
            var redis = context.GetService<IConnectionMultiplexer>().GetDatabase();

            var cacheKey = EncodingService.UniqueKey(this.RoomImage, this.ProductImageUrl, this.ProductName, this.ProductDescription, this.Seed ?? _rnd.Next(10000, 99999));
            var cachedImageUrl = await CheckCacheAsync(redis, cacheKey);
            if (!string.IsNullOrWhiteSpace(cachedImageUrl))
            {
                context.AddData("responseUrl", cachedImageUrl);
                return;
            }

            var model = context.Services.GetModel<ProductReplacementInRoom>();

            var payload = JsonSerializer.SerializeToDocument(new
            {
                this.RoomImage,
                this.ProductImageUrl,
                this.ProductName,
                this.ProductDescription,
                this.Seed
            });

            var response = await PollyRetryExtensions.GetDefaultRetryPolicy(logger, $"ProcessRefurnishingProductReplacement - ProcessRefurnishingProductReplacement").ExecuteAsync(() =>
            {
                return model.InferAsync(new ModelContext
                {
                    Payload = payload,
                    ItemId = Guid.NewGuid().ToString(),
                    WorkspaceId = workspaceId.ToString(),
                    CancellationToken = context.CancellationToken
                });
            });

            if (response.Document is null)
            {
                throw new ApplicationException("Model returned empty result");
            }
            var imageUrl = PayloadService.GetRequiredString(response.Document, "imageUrl");
            context.AddData("responseUrl", imageUrl);

            var redisValue = JsonSerializer.Serialize(new GenerativeImageCacheItem
            {
                ImageUrl = imageUrl,
                CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            });

            await redis.HashSetAsync(CacheHashKey, cacheKey, redisValue);
        }
        catch (ApplicationException e)
        {
            throw new CommandException(e.Message, e);
        }
        catch (Exception e)
        {
            logger.LogError(e, "Unable to generate output");
            throw new CommandException("Unable to generate output");
        }
    }
    private static async Task<string?> CheckCacheAsync(IDatabaseAsync redis, string key)
    {
        var redisValue = await redis.HashGetAsync(CacheHashKey, key);

        if (!redisValue.HasValue) return null;

        var str = redisValue.ToString();
        var obj = JsonSerializer.Deserialize<GenerativeImageCacheItem>(str);
        return obj?.ImageUrl;
    }
}

