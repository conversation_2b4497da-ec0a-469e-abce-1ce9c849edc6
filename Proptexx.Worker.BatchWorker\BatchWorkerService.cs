using System.Text;
using System.Text.Json;
using Npgsql;
using NpgsqlTypes;
using Proptexx.AI.Services;
using Proptexx.Core.Entities;
using Proptexx.Core.Json;
using Proptexx.Core.Redis;
using Proptexx.Core.Services;
using Proptexx.Core.BigQuery;
using StackExchange.Redis;

namespace Proptexx.Worker.BatchWorker;

internal class BatchWorkerService : BackgroundService
{
    private readonly IServiceProvider _services;
    private readonly ILogger<BatchWorkerService> _logger;
    private readonly NpgsqlDataSource _dataSource;
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly IConfiguration _configuration;
    private readonly BatchDataService _batchDataService;
    private readonly BigQueryBatchExporter _bigQueryExporter;
    private LoadedLuaScript? _loaded;

    public BatchWorkerService(
        IServiceProvider services,
        IConfiguration configuration,
        ILoggerFactory loggerFactory, 
        NpgsqlDataSource dataSource,
        IConnectionMultiplexer connectionMultiplexer,
        BatchDataService batchDataService,
        BigQueryBatchExporter bigQueryExporter)
    {
        _services = services;
        _configuration = configuration;
        _logger = loggerFactory.CreateLogger<BatchWorkerService>();
        _dataSource = dataSource;
        _connectionMultiplexer = connectionMultiplexer;
        _batchDataService = batchDataService;
        _bigQueryExporter = bigQueryExporter;
    }

    protected override async Task ExecuteAsync(CancellationToken cancellationToken)
    {
        var batchPopCount = _configuration.GetValue("popCount", 30);
        var parallelRuns = _configuration.GetValue("parallelRuns", 10);
        var intervalMs = _configuration.GetValue("intervalMs", 500);

        _logger.LogInformation(
            "Starting batch worker with {BatchPopCount} pop count, {ParallelRuns} parallel runs, {IntervalMs} interval",
            batchPopCount, parallelRuns, intervalMs);

        var redis = _connectionMultiplexer.GetDatabase();
        var endpoint = _connectionMultiplexer.GetEndPoints().First();
        var server = _connectionMultiplexer.GetServer(endpoint);
        _loaded = await RedisLuaScripts.PullScript.LoadAsync(server);

        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var entries = await FetchEntriesAsync(redis, _loaded.Hash, batchPopCount, cancellationToken);
                if (entries.Count <= 0) continue;

                var semaphore = new SemaphoreSlim(parallelRuns);
                var enumerableTasks = entries.Select(async item =>
                {
                    await semaphore.WaitAsync(cancellationToken);

                    BatchResult batchResult;
                    BatchCallbackPayload? callbackResult = null;

                    try
                    {
                        batchResult = await MlHelper.ProcessItemAsync(
                            _services, _logger, item.WorkspaceId, item.Task.Id, item.Task.Model, item.Task.Config, cancellationToken);

                        if (!string.IsNullOrWhiteSpace(item.CallbackUrl))
                        {
                            callbackResult = await CreateCallbackAsync(_logger, redis, item);
                        }

                        _logger.LogInformation("Task completed: {TaskId}", item.Task.Id);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing item {ItemId}", item.Task.Id);

                        batchResult = new BatchResult
                        {
                            Id = Guid.NewGuid(),
                            TaskId = item.Task.Id,
                            StartedAt = DateTime.UtcNow,
                            Status = BatchResultStatus.Error,
                            CompletedAt = DateTime.UtcNow,
                            ErrorMessage = "Failed to process item. See logs for details.",
                            Exception = ex.Message,
                        };
                    }
                    finally
                    {
                        semaphore.Release();
                    }

                    return (batchResult, callbackResult);
                });

                var taskResults = await Task.WhenAll(enumerableTasks);
                await ProcessResultsAsync(_logger, _dataSource, redis, taskResults, cancellationToken);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "System Exception");
                await Task.Delay(intervalMs * 10, cancellationToken);
            }
            finally
            {
                await Task.Delay(intervalMs, cancellationToken);
            }
        }
    }

    private static async Task<BatchCallbackPayload?> CreateCallbackAsync(ILogger<BatchWorkerService> logger, IDatabase redis, RedisTaskMessage taskMessage)
    {
        BatchCallbackPayload? result = null;
        var batchId = taskMessage.Task.BatchId.ToString();

        try
        {
            if (string.IsNullOrWhiteSpace(taskMessage.CallbackUrl)) return null;

            var numLeft = await redis.HashIncrementAsync("batch_callback_tracker", batchId, -1);

            if (numLeft <= 0)
            {
                result = new BatchCallbackPayload
                {
                    BatchId = taskMessage.Task.BatchId,
                    CallbackUrl = taskMessage.CallbackUrl
                };
            }
        }
        catch (Exception e)
        {
            var taskId = taskMessage.Task.Id.ToString();
            logger.LogError(e, "Error checking callback for task {TaskId} in batch {BatchId}", taskId, batchId);
        }

        return result;
    }

    private async Task ProcessResultsAsync(ILogger<BatchWorkerService> logger, NpgsqlDataSource dataSource, IDatabase redis, (BatchResult batchResult, BatchCallbackPayload? callbackResult)[] taskResults, CancellationToken cancellationToken)
    {
        List<BatchResult> batchResults = [];
        List<BatchCallbackPayload> callbackResults = [];
        var batchesProcessed = new HashSet<string>();

        foreach (var (batchResult, callbackResult) in taskResults)
        {
            batchResults.Add(batchResult);

            if (callbackResult is not null)
            {
                callbackResults.Add(callbackResult);
            }
        }

        // Store results and immediately export to BigQuery (same as old PostgreSQL behavior)
        foreach (var result in batchResults)
        {
            // Store task result in Redis for BigQuery export
            await _batchDataService.StoreTaskResultAsync(result.TaskId.ToString(), new
            {
                id = result.Id.ToString(),
                task_id = result.TaskId.ToString(),
                status = (int)result.Status,
                output = result.Output,
                error_message = result.ErrorMessage,
                started_at = result.StartedAt,
                completed_at = result.CompletedAt,
                exception = result.Exception
            });
            
            // Get batch ID from task metadata for this result
            var batchId = await GetBatchIdForTaskAsync(redis, result.TaskId.ToString());
            if (!string.IsNullOrEmpty(batchId))
            {
                batchesProcessed.Add(batchId);
            }
        }

        // Immediately export ALL results to BigQuery (replaces old PostgreSQL persistence)
        // This ensures data is persisted regardless of webhook success/failure or callback existence
        foreach (var batchId in batchesProcessed)
        {
            _logger.LogInformation("Exporting batch {BatchId} to BigQuery (immediate persistence)", batchId);
            try
            {
                await _bigQueryExporter.ExportBatchDataAsync(Guid.Parse(batchId), redis, cancellationToken);
                _logger.LogInformation("Successfully exported batch {BatchId} to BigQuery", batchId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to export batch {BatchId} to BigQuery", batchId);
                // Continue processing even if BigQuery export fails
            }
        }

        if (callbackResults.Count > 0)
        {
            await SendCallbacksAsync(logger, redis, callbackResults);
        }
    }

    private static async Task SendCallbacksAsync(ILogger<BatchWorkerService> logger, IDatabase redis, List<BatchCallbackPayload> callbackResults)
    {
        try
        {
            List<Task> tasks = [];
            var batch = redis.CreateBatch();

            foreach (var callbackResult in callbackResults)
            {
                var batchId = callbackResult.BatchId.ToString();

                try
                {
                    var callbackResultStr = JsonSerializer.Serialize(callbackResult);

                    var pushTask = batch.ListLeftPushAsync("batch_callbacks", callbackResultStr);
                    tasks.Add(pushTask);

                    var deleteTask = batch.HashDeleteAsync("batch_callback_tracker", batchId);
                    tasks.Add(deleteTask);
                }
                catch (Exception e)
                {
                    logger.LogError(e, "Error placing callback for batch {BatchId}", batchId);
                }
            }

            batch.Execute();
            await Task.WhenAll(tasks);
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error sending callbacks to redis");
        }
    }

    private static async Task<IList<RedisTaskMessage>> FetchEntriesAsync(IDatabase redis, byte[] loadedHash, int batchPopCount, CancellationToken cancellationToken)
    {
        var now = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        var redisResult = await redis.ScriptEvaluateAsync(loadedHash, [], [
            batchPopCount.ToString(),
            now.ToString(),
        ]);

        var redisResults = (RedisResult[])redisResult!;
        return redisResults.Select(DeserializeRedisValue).ToList();
    }

    private static RedisTaskMessage DeserializeRedisValue(RedisResult item)
    {
        if (item.IsNull)
        {
            throw new NullReferenceException(
                $"The RedisValue is null or empty. This should not happen; Investigate!");
        }

        var value = item.ToString();

        if (string.IsNullOrWhiteSpace(value))
        {
            throw new NullReferenceException(
                $"The item from redis is null or whitespace. This should not happen; Investigate!");
        }

        var obj = JsonSerializer.Deserialize<RedisTaskMessage>(value, JsonDefaults.CompactOptions);
        if (obj is null)
        {
            throw new NullReferenceException(
                $"The object returned by JsonSerializer.Deserialize is null. This should not happen; Investigate!");
        }

        return obj;
    }

    private static async Task PersistResultAsync(ILogger logger, NpgsqlDataSource dataSource, List<BatchResult> batchResults, CancellationToken cancellationToken)
    {
        try
        {
            if (batchResults.Count == 0) return;

            var sqlBuilder = new StringBuilder();
            sqlBuilder.Append($@"
                INSERT INTO batching.result (task_id, status, output, error_message, started_at, completed_at, request_params, response_params, exception) 
                VALUES (@TaskId{0}, @Status{0}, @Output{0}, @ErrorMessage{0}, @StartedAt{0}, @CompletedAt{0}, @RequestParams{0}, @ResponseParams{0}, @Exception{0})");

            for (var i = 1; i < batchResults.Count; i++)
            {
                sqlBuilder.Append($",(@TaskId{i}, @Status{i}, @Output{i}, @ErrorMessage{i}, @StartedAt{i}, @CompletedAt{i}, @RequestParams{i}, @ResponseParams{i}, @Exception{i})");
            }

            await using var npgsql = await dataSource.OpenConnectionAsync(cancellationToken);
            await using var cmd = new NpgsqlCommand(sqlBuilder.ToString(), npgsql);
            for (var i = 0; i < batchResults.Count; i++)
            {
                cmd.Parameters.AddWithValue($"TaskId{i}", NpgsqlDbType.Uuid, batchResults[i].TaskId);
                
                cmd.Parameters.AddWithValue($"Status{i}", NpgsqlDbType.Integer, (int)batchResults[i].Status);
                
                cmd.Parameters.AddWithValue($"Output{i}", NpgsqlDbType.Jsonb, batchResults[i].Output ?? (object)DBNull.Value);

                cmd.Parameters.AddWithValue($"ErrorMessage{i}", NpgsqlDbType.Text, batchResults[i].ErrorMessage ?? (object)DBNull.Value);

                cmd.Parameters.AddWithValue($"StartedAt{i}", NpgsqlDbType.TimestampTz, batchResults[i].StartedAt);
                
                cmd.Parameters.AddWithValue($"CompletedAt{i}", NpgsqlDbType.TimestampTz, batchResults[i].CompletedAt ?? (object)DBNull.Value);

                cmd.Parameters.AddWithValue($"RequestParams{i}", NpgsqlDbType.Jsonb,
                    batchResults[i].RequestParams ?? (object)DBNull.Value);
                
                cmd.Parameters.AddWithValue($"ResponseParams{i}", NpgsqlDbType.Jsonb,
                    batchResults[i].ResponseParams ?? (object)DBNull.Value);
                
                cmd.Parameters.AddWithValue($"Exception{i}", NpgsqlDbType.Text,
                    batchResults[i].Exception ?? (object)DBNull.Value);
            }

            var numItems = await cmd.ExecuteNonQueryAsync(cancellationToken);
            logger.LogInformation("Persisted {NumItems} to postgres", numItems);
        }
        catch (Exception e)
        {
            logger.LogError(e, "Exception while persisting result");
            throw;
        }
    }

    private async Task<string?> GetBatchIdForTaskAsync(IDatabase redis, string taskId)
    {
        try
        {
            // Get task metadata which contains the batch ID
            var taskMetadataJson = await redis.StringGetAsync($"task_metadata:{taskId}");
            if (taskMetadataJson.HasValue)
            {
                var taskMetadata = JsonSerializer.Deserialize<JsonDocument>(taskMetadataJson!, JsonDefaults.CompactOptions);
                if (taskMetadata != null && taskMetadata.RootElement.TryGetProperty("batch_id", out var batchIdProperty))
                {
                    return batchIdProperty.GetString();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get batch ID for task {TaskId}", taskId);
        }
        
        return null;
    }
}