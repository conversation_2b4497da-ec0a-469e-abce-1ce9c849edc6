namespace Proptexx.Core.DTOs;

/// <summary>
/// Enhanced validation result with subscription and expiry information
/// </summary>
public class EnhancedValidationResult
{
    public bool IsValid { get; set; }
    public string? Reason { get; set; }
    public RequestValidationErrorCode ErrorCode { get; set; }
    public double UsagePercentage { get; set; }
    public long RemainingQuota { get; set; }
    public object? Subscription { get; set; }
    public string? SubscriptionStatus { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public int? DaysUntilExpiry { get; set; }

    public static EnhancedValidationResult Invalid(
        string reason, 
        RequestValidationErrorCode errorCode,
        double usagePercentage = 0,
        long remainingQuota = 0,
        string? subscriptionStatus = null,
        DateTime? expiresAt = null,
        int? daysUntilExpiry = null)
    {
        return new EnhancedValidationResult
        {
            IsValid = false,
            Reason = reason,
            ErrorCode = errorCode,
            UsagePercentage = usagePercentage,
            RemainingQuota = remainingQuota,
            SubscriptionStatus = subscriptionStatus,
            ExpiresAt = expiresAt,
            DaysUntilExpiry = daysUntilExpiry
        };
    }

    public static EnhancedValidationResult Valid(
        object? subscription = null,
        double usagePercentage = 0,
        long remainingQuota = 0,
        string? subscriptionStatus = null,
        DateTime? expiresAt = null,
        int? daysUntilExpiry = null)
    {
        return new EnhancedValidationResult
        {
            IsValid = true,
            Subscription = subscription,
            UsagePercentage = usagePercentage,
            RemainingQuota = remainingQuota,
            SubscriptionStatus = subscriptionStatus,
            ExpiresAt = expiresAt,
            DaysUntilExpiry = daysUntilExpiry
        };
    }
}

/// <summary>
/// Represents the classification of a request
/// </summary>
public class RequestClassification
{
    public RequestClassification(string type, string subType, string? identifier)
    {
        Type = type;
        Target = subType;
        Identifier = identifier;
    }
    
    public string Type { get; }
    public string Target { get; }
    public string? Identifier { get; }
}

/// <summary>
/// Quota validation result for specific request types
/// </summary>
public class QuotaValidationResult
{
    public bool IsValid { get; set; }
    public string? Reason { get; set; }
    public RequestValidationErrorCode ErrorCode { get; set; }
    public double UsagePercentage { get; set; }
    public long RemainingQuota { get; set; }

    public static QuotaValidationResult Valid(double usagePercentage, long remainingQuota)
    {
        return new QuotaValidationResult
        {
            IsValid = true,
            UsagePercentage = usagePercentage,
            RemainingQuota = remainingQuota
        };
    }
}

/// <summary>
/// Expiry information for subscriptions
/// </summary>
public class ExpiryInfo
{
    public bool IsExpired { get; set; }
    public string? ExpiryReason { get; set; }
    public DateTime? EffectiveExpiryDate { get; set; }
    public int? DaysUntilExpiry { get; set; }
}

/// <summary>
/// Extended error codes for enhanced validation
/// </summary>
public enum RequestValidationErrorCode
{
    InvalidApiKey = 1001,
    SubscriptionNotFound = 1002,
    SubscriptionExpired = 1003,
    ApiKeyExpired = 1004,
    QuotaExceeded = 1005,
    QuotaNearLimit = 1006,
    ValidationError = 1007
} 

/// <summary>
/// Comprehensive subscription information
/// </summary>
public class SubscriptionInfo
{
    public string? Type { get; set; }
    public string? Status { get; set; }
    public long Quota { get; set; }
    public long TotalUsage { get; set; }
    public bool IsDemo { get; set; }
    public string? PlanId { get; set; }
    public DateTime? ExpiresAt { get; set; }
    
    // Widget-specific properties
    public int? WidgetQuota { get; set; }
    public int? WidgetRendersUsed { get; set; }
    public DateTime? WidgetExpiresAt { get; set; }
} 