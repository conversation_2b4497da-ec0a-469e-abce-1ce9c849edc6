<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <Authors>Proptexx</Authors>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <LangVersion>12</LangVersion>
        <OutputType>Exe</OutputType>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\Proptexx.AI\Proptexx.AI.csproj" />
      <ProjectReference Include="..\Proptexx.Worker\Proptexx.Worker.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="AWSSDK.EC2" Version="3.7.437.4" />
        <PackageReference Include="DotnetGeminiSDK" Version="1.0.14" />
        <PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
    </ItemGroup>
</Project>
