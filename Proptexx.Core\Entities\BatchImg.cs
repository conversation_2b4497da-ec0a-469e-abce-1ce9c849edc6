using Proptexx.Core.Services;

namespace Proptexx.Core.Entities;

public sealed class BatchImg : IBatchImageStatus
{
    public required string Id { get; init; }
    
    public required string ImageUrl { get; init; }

    public DateTime CreatedAt { get; init; } = DateTime.UtcNow;
    
    public string? OriginalUrl { get; set; }

    public int Status { get; set; }

    public string? SceneType { get; set; }

    public string? RoomType { get; set; }

    public bool? IsEmptyRoom { get; set; }

    public List<ListingWorkspace> Listings { get; set; } = [];

    public DateTime? ProcessedAt { get; set; }
    
    public long? ProcessTime { get; set; }

    public string? Error { get; set; }

    public Dictionary<string, float>? InputRoomTypesScore { get; set; }
    
    public string? MimeType { get; set; }
    
    public string? ClaimedId { get; set; }
    
    public int Width { get; set; }
    
    public int Height { get; set; }

    public void SetSuccess()
    {
        this.Status = 2;
        this.Error = null;
    }
    
    public void SetError(string errorMessage)
    {
        this.Status = -1;
        this.Error = errorMessage;
    }
}

public class ListingWorkspace
{
    public required string WorkspaceId { get; init; }

    public required string ListingUrl { get; init; }
}