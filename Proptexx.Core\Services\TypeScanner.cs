using System.Reflection;

namespace Proptexx.Core.Services;

internal static class TypeScanner
{
    internal static Dictionary<string, Type> Scan<T>(Dictionary<string, List<Descriptor>> descriptors)
    {
        var result = new Dictionary<string, Type>();

        foreach (var (identifier, dList) in descriptors)
        {
            var items = dList
                .SelectMany(d => DescriptorToTypes<T>(identifier, d))
                .ToDictionary(x => x.name, x => x.type);

            foreach (var item in items)
            {
                var key = item.Key.ToUpperInvariant();

                if (result.ContainsKey(key))
                {
                    throw new Exception($"The key '{key}' is already defined on the T registry {typeof(T).Name}");
                }
                
                result.Add(key, item.Value);
            }
        }

        return result;
    }

    private static IEnumerable<(string name, Type type)> DescriptorToTypes<T>(string identifier, Descriptor descriptor)
    {
        Func<Type, bool> predicate = t => t is
        {
            IsClass: true,
            IsAbstract: false,
            IsGenericType: false,
            IsValueType: false
        };

        if (!string.IsNullOrWhiteSpace(descriptor.AssemblyPath))
        {
            predicate = t => t is
            {
                IsClass: true,
                IsAbstract: false,
                IsGenericType: false,
                IsValueType: false
            } && OnWhenRecursive(t, descriptor);
        }

        var result = descriptor.Assembly
            .GetTypes()
            .Where(t => typeof(T).IsAssignableFrom(t))
            .Where(predicate)
            .Select(t => (ResolveName(identifier, descriptor, t), t));

        return result;
    }

    private static string ResolveName(string identifier, Descriptor descriptor, Type t)
    {
        var fullName = t.FullName;
        if (string.IsNullOrWhiteSpace(fullName))
        {
            throw new NullReferenceException($"Unable to read the full type name of {t}");
        }

        var assemblyName = t.Assembly.GetName().Name;
        if (string.IsNullOrWhiteSpace(assemblyName))
        {
            throw new NullReferenceException($"Unable to read the full assembly name of {t}");
        }

        var prefix = JoinStringAndStripEmpty(assemblyName, descriptor.AssemblyPath);

        if (!fullName.StartsWith(prefix))
        {
            throw new Exception($"Unable to resolve name of type {t}");
        }

        var pLen = prefix.Length + 1;
        var result = JoinStringAndStripEmpty(identifier, fullName[pLen..]);
        return result;
    }

    private static string JoinStringAndStripEmpty(params string?[] input) 
        => string.Join('.', input.Where(x => !string.IsNullOrWhiteSpace(x)));

    private static bool OnWhenRecursive(Type type, Descriptor descriptor)
    {
        if (string.IsNullOrWhiteSpace(descriptor.AssemblyPath)) return false;
        if (string.IsNullOrWhiteSpace(type.Namespace)) return false;
    
        var assemblyName = type.Assembly.GetName().Name;
        if (string.IsNullOrWhiteSpace(assemblyName)) return false;

        var path = $"{assemblyName}.{descriptor.AssemblyPath}";
        var result = descriptor.SearchRecursive
            ? type.Namespace.StartsWith(path)
            : type.Namespace.Equals(path);
    
        return result;
    }

    internal sealed class Descriptor
    {
        public required Assembly Assembly { get; init; }

        public string? AssemblyPath { get; init; }

        public bool SearchRecursive { get; init; }
    }
}