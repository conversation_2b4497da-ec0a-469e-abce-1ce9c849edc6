using System.Text.Json.Serialization;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Http;
using Proptexx.Core.Services;
using StackExchange.Redis;

namespace Proptexx.AI.Models.CV;

public sealed class FloorPlanAnalysis(
    IComputerVisionClient computerVisionClient,
    IImageAssessmentClient imageAssessmentClient,
    IConnectionMultiplexer connectionMultiplexer,
    ILoggerFactory loggerFactory)
    : BaseComputerVisionModel(
        "FloorplanAnalyzer",
        computerVisionClient,
        imageAssessmentClient,
        connectionMultiplexer,
        loggerFactory)
{
    public sealed class Result
    {
        [JsonPropertyName("living_room")]
        public List<string>? LivingRoom { get; init; }
        
        [JsonPropertyName("living_room_count")]
        public int? LivingRoomCount { get; init; }
        
        [JsonPropertyName("kitchen")]
        public List<string>? Kitchen { get; init; }
        
        [JsonPropertyName("kitchen_count")]
        public int? KitchenCount { get; init; }
        
        [JsonPropertyName("bedroom")]
        public List<string>? Bedroom { get; init; }
        
        [JsonPropertyName("bedroom_count")]
        public int? BedroomCount { get; init; }
        
        [JsonPropertyName("bathroom")]
        public List<string>? Bathroom { get; init; }
        
        [JsonPropertyName("bathroom_count")]
        public int? BathroomCount { get; init; }
        
        [JsonPropertyName("attic")]
        public List<string>? Attic { get; init; }
        
        [JsonPropertyName("attic_count")]
        public int? AtticCount { get; init; }
        
        [JsonPropertyName("cellar")]
        public List<string>? Cellar { get; init; }
        
        [JsonPropertyName("cellar_count")]
        public int? CellarCount { get; init; }
        
        [JsonPropertyName("storage_room")]
        public List<string>? StorageRoom { get; init; }
        
        [JsonPropertyName("storage_room_count")]
        public int? StorageRoomCount { get; init; }
        
        [JsonPropertyName("wash_place")]
        public List<string>? WashPlace { get; init; }
        
        [JsonPropertyName("wash_place_count")]
        public int? WashPlaceCount { get; init; }
        
        [JsonPropertyName("garage")]
        public List<string>? Garage { get; init; }
        
        [JsonPropertyName("garage_count")]
        public int? GarageCount { get; init; }
        
        [JsonPropertyName("parking_garage")]
        public List<string>? ParkingGarage { get; init; }
        
        [JsonPropertyName("parking_garage_count")]
        public int? ParkingGarageCount { get; init; }
    }
} 