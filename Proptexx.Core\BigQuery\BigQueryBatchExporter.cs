using System.Text.Json;
using Google.Cloud.BigQuery.V2;
using Google.Apis.Auth.OAuth2;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Entities;
using Proptexx.Core.Json;
using StackExchange.Redis;

namespace Proptexx.Core.BigQuery;

public class BigQueryBatchExporter
{
    private readonly BigQueryClient _bigQueryClient;
    private readonly ILogger<BigQueryBatchExporter> _logger;
    private readonly string _projectId;
    private readonly string _datasetId;

    public BigQueryBatchExporter(IConfiguration configuration, ILogger<BigQueryBatchExporter> logger)
    {
        _logger = logger;

        // Read from configuration, same pattern as other settings
        _projectId = configuration.GetValue<string>("BIGQUERY_PROJECT_ID")
            ?? throw new ArgumentNullException("BIGQUERY_PROJECT_ID configuration is required");

        _datasetId = configuration.GetValue<string>("BIGQUERY_DATASET_ID")
            ?? throw new ArgumentNullException("BIGQUERY_DATASET_ID configuration is required");

        var credentialsJson = configuration.GetValue<string>("BIGQUERY_CREDENTIALS_JSON");

        var defaultLocation = configuration.GetValue<string>("BIGQUERY_DEFAULT_LOCATION") ?? "europe-west1";

        if (!string.IsNullOrEmpty(credentialsJson))
        {
            _bigQueryClient = BigQueryClient.Create(_projectId, GoogleCredential.FromJson(credentialsJson));
            _logger.LogInformation("Using BIGQUERY_CREDENTIALS_JSON for BigQuery authentication");
        }
        else
        {
            // Fall back to Application Default Credentials (ADC)
            _bigQueryClient = BigQueryClient.Create(_projectId);
            _logger.LogInformation("Using Application Default Credentials for BigQuery");
        }
        _bigQueryClient.WithDefaultLocation(defaultLocation);

        _logger.LogInformation("BigQuery client initialized for project {ProjectId}, dataset {DatasetId}", _projectId, _datasetId);
    }

    public async Task ExportBatchDataAsync(Guid batchId, IDatabase redis, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting BigQuery export for batch {BatchId}", batchId);

            // Ensure BigQuery tables exist before attempting to use them
            await BigQuerySchemas.EnsureTablesExistAsync(_bigQueryClient, _datasetId, _logger);

            // Get batch data from Redis
            var batchData = await GetBatchDataFromRedisAsync(batchId, redis);
            if (batchData == null)
            {
                _logger.LogWarning("No batch data found in Redis for batch {BatchId}", batchId);
                return;
            }

            // Export batch metadata (only once)
            if (batchData.Batch != null)
            {
                await ExportBatchMetadataAsync(batchData.Batch, cancellationToken);
            }

            // Export all available tasks and results (incremental export)
            await ExportBatchTasksAsync(batchData.Tasks, cancellationToken);
            await ExportBatchResultsAsync(batchData.Results, cancellationToken);

            _logger.LogInformation("Successfully exported batch {BatchId} data to BigQuery ({TaskCount} tasks, {ResultCount} results)",
                batchId, batchData.Tasks.Count, batchData.Results.Count);

            // Clean up Redis data only if batch is complete
            var isComplete = await IsBatchCompleteInRedisAsync(batchId, redis);
            if (isComplete)
            {
                await CleanupRedisDataAsync(batchId, redis);
                _logger.LogInformation("Batch {BatchId} is complete, cleaned up Redis data", batchId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export batch {BatchId} to BigQuery", batchId);
            throw;
        }
    }

    private async Task<BatchDataContainer?> GetBatchDataFromRedisAsync(Guid batchId, IDatabase redis)
    {
        // Get batch metadata
        var batchMetadataJson = await redis.StringGetAsync($"batch_metadata:{batchId}");
        if (!batchMetadataJson.HasValue)
        {
            return null;
        }

        var batchMetadata = JsonSerializer.Deserialize<JsonDocument>(batchMetadataJson!, JsonDefaults.JsonSerializerOptions);

        // Convert metadata to Batch entity
        var batch = new Batch
        {
            WorkspaceId = Guid.Parse(batchMetadata!.RootElement.GetProperty("workspace_id").GetString()!),
            AccountId = batchMetadata.RootElement.TryGetProperty("account_id", out var accountId) && !accountId.ValueKind.Equals(JsonValueKind.Null)
                ? Guid.Parse(accountId.GetString()!) : null,
            Session = batchMetadata.RootElement.TryGetProperty("session", out var session) && !session.ValueKind.Equals(JsonValueKind.Null)
                ? session.GetString() : null,
            CallbackUrl = batchMetadata.RootElement.TryGetProperty("callback_url", out var callbackUrl) && !callbackUrl.ValueKind.Equals(JsonValueKind.Null)
                ? callbackUrl.GetString() : null,
            IsSync = batchMetadata.RootElement.GetProperty("is_sync").GetBoolean()
        };

        // Manually set the Id
        var batchIdProperty = typeof(Batch).GetProperty("Id")!;
        batchIdProperty.SetValue(batch, batchId);

        // Get task IDs for this batch
        var taskIds = await redis.SetMembersAsync($"batch_tasks:{batchId}");
        var tasks = new List<BatchTask>();
        var results = new List<BatchResult>();

        foreach (var taskId in taskIds)
        {
            var taskIdString = taskId.ToString();

            // Get task metadata
            var taskMetadataJson = await redis.StringGetAsync($"task_metadata:{taskIdString}");
            if (taskMetadataJson.HasValue)
            {
                var taskMetadata = JsonSerializer.Deserialize<JsonDocument>(taskMetadataJson!, JsonDefaults.JsonSerializerOptions);
                if (taskMetadata != null)
                {
                    var task = new BatchTask
                    {
                        Id = Guid.Parse(taskIdString),
                        BatchId = batchId,
                        Model = taskMetadata.RootElement.GetProperty("model").GetString()!,
                        Config = JsonSerializer.Deserialize<JsonDocument>(taskMetadata.RootElement.GetProperty("config").GetRawText())!
                    };
                    tasks.Add(task);
                }
            }

            // Get task result
            var resultJson = await redis.StringGetAsync($"task_result:{taskIdString}");
            if (resultJson.HasValue)
            {
                var resultData = JsonSerializer.Deserialize<JsonDocument>(resultJson!, JsonDefaults.JsonSerializerOptions);
                if (resultData != null)
                {
                    var startedAt = resultData.RootElement.TryGetProperty("started_at", out var startedAtProp) && !startedAtProp.ValueKind.Equals(JsonValueKind.Null)
                        ? startedAtProp.GetDateTime() : DateTime.UtcNow;

                    var result = new BatchResult
                    {
                        Id = Guid.Parse(resultData.RootElement.GetProperty("id").GetString()!),
                        TaskId = Guid.Parse(taskIdString),
                        StartedAt = startedAt
                    };

                    // Set mutable properties
                    result.Status = (BatchResultStatus)resultData.RootElement.GetProperty("status").GetInt32();
                    result.Output = resultData.RootElement.TryGetProperty("output", out var output) && !output.ValueKind.Equals(JsonValueKind.Null)
                        ? JsonSerializer.Deserialize<JsonDocument>(output.GetRawText()) : null;
                    result.ErrorMessage = resultData.RootElement.TryGetProperty("error_message", out var errorMsg) && !errorMsg.ValueKind.Equals(JsonValueKind.Null)
                        ? errorMsg.GetString() : null;
                    result.CompletedAt = resultData.RootElement.TryGetProperty("completed_at", out var completedAt) && !completedAt.ValueKind.Equals(JsonValueKind.Null)
                        ? completedAt.GetDateTime() : null;
                    result.Exception = resultData.RootElement.TryGetProperty("exception", out var exception) && !exception.ValueKind.Equals(JsonValueKind.Null)
                        ? exception.GetString() : null;

                    results.Add(result);
                }
            }
        }

        return new BatchDataContainer
        {
            Batch = batch,
            Tasks = tasks,
            Results = results
        };
    }

    private async Task<bool> IsBatchCompleteInRedisAsync(Guid batchId, IDatabase redis)
    {
        var taskIds = await redis.SetMembersAsync($"batch_tasks:{batchId}");

        foreach (var taskId in taskIds)
        {
            var hasResult = await redis.KeyExistsAsync($"task_result:{taskId}");
            if (!hasResult)
            {
                return false;
            }
        }

        return true; // All tasks have results
    }

    private async Task ExportBatchMetadataAsync(Batch batch, CancellationToken cancellationToken)
    {
        var dataset = await _bigQueryClient.GetDatasetAsync(_datasetId, cancellationToken: cancellationToken);
        var table = await dataset.GetTableAsync("batches", cancellationToken: cancellationToken);

        var row = new BigQueryInsertRow
        {
            ["id"] = batch.Id.ToString(),
            ["workspace_id"] = batch.WorkspaceId.ToString(),
            ["account_id"] = batch.AccountId?.ToString(),
            ["session"] = batch.Session,
            ["created_at"] = batch.CreatedAt,
            ["callback_url"] = batch.CallbackUrl,
            ["is_sync"] = batch.IsSync
        };

        await table.InsertRowAsync(row, cancellationToken: cancellationToken);
    }

    private async Task ExportBatchTasksAsync(List<BatchTask> tasks, CancellationToken cancellationToken)
    {
        if (!tasks.Any()) return;

        var dataset = await _bigQueryClient.GetDatasetAsync(_datasetId, cancellationToken: cancellationToken);
        var table = await dataset.GetTableAsync("batch_tasks", cancellationToken: cancellationToken);

        var rows = tasks.Select(task => new BigQueryInsertRow
        {
            ["id"] = task.Id.ToString(),
            ["batch_id"] = task.BatchId.ToString(),
            ["created_at"] = task.CreatedAt,
            ["model"] = task.Model,
            ["config"] = task.Config.RootElement.GetRawText()
        }).ToList();

        await table.InsertRowsAsync(rows, cancellationToken: cancellationToken);
    }

    private async Task ExportBatchResultsAsync(List<BatchResult> results, CancellationToken cancellationToken)
    {
        if (!results.Any()) return;

        var dataset = await _bigQueryClient.GetDatasetAsync(_datasetId, cancellationToken: cancellationToken);
        var table = await dataset.GetTableAsync("batch_results", cancellationToken: cancellationToken);

        var rows = results.Select(result => new BigQueryInsertRow
        {
            ["id"] = result.Id.ToString(),
            ["task_id"] = result.TaskId.ToString(),
            ["status"] = (int)result.Status,
            ["output"] = result.Output?.RootElement.GetRawText(),
            ["error_message"] = result.ErrorMessage,
            ["started_at"] = result.StartedAt,
            ["completed_at"] = result.CompletedAt,
            ["exception"] = result.Exception
        }).ToList();

        await table.InsertRowsAsync(rows, cancellationToken: cancellationToken);
    }

    private async Task CleanupRedisDataAsync(Guid batchId, IDatabase redis)
    {
        var tasks = new List<Task>();

        // Get all task IDs first
        var taskIds = await redis.SetMembersAsync($"batch_tasks:{batchId}");

        // Delete batch metadata
        tasks.Add(redis.KeyDeleteAsync($"batch_metadata:{batchId}"));

        // Delete all task metadata and results
        foreach (var taskId in taskIds)
        {
            tasks.Add(redis.KeyDeleteAsync($"task_metadata:{taskId}"));
            tasks.Add(redis.KeyDeleteAsync($"task_result:{taskId}"));
        }

        // Delete the batch tasks set
        tasks.Add(redis.KeyDeleteAsync($"batch_tasks:{batchId}"));

        await Task.WhenAll(tasks);
        _logger.LogDebug("Cleaned up Redis data for batch {BatchId}", batchId);
    }

    private class BatchDataContainer
    {
        public required Batch Batch { get; init; }
        public required List<BatchTask> Tasks { get; init; }
        public required List<BatchResult> Results { get; init; }
    }
}
