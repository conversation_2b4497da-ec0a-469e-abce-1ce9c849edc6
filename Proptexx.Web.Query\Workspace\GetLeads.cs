using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Workspace;

public sealed class GetLeads : IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var workspaceId = context.User.GetWorkspaceGuid();
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var result = await npgsql.QueryAsync<LeadModel>(LeadModel.Sql, new { _workspace_id = workspaceId });
        return result;
    }

    public class LeadModel
    {
        public required Guid Id { get; init; }
    
        public required string FirstName { get; init; }
    
        public required string FamilyName { get; init; }
    
        public required string Emails { get; init; }
        
        public DateTime? MemberSince { get; init; }
        
        public static string Sql => @"
            select a.id,
                   a.first_name,
                   a.family_name,
                   cab.verified_at as member_since,
                   string_agg(ae.email, ',') as emails
            from core.account a
            join core.account_email ae on a.id = ae.account_id
            join core.cluster_account_binding cab on a.id = cab.account_id and cab.verified_at <= current_timestamp
            join core.cluster c on cab.cluster_id = c.id and lower(c.name) = 'lead' and c.workspace_id = :_workspace_id
            group by a.id, a.first_name, a.family_name, cab.verified_at
            order by a.first_name, a.family_name;
        ";
    }
}
