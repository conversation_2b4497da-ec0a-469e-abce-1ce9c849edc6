using System.Data;
using Dapper;
using StackExchange.Redis;

namespace Proptexx.Worker.DataSync;

public static class PhoneSync
{
    internal static async Task SyncAsync(IDbConnection conn, IDatabase redis, CancellationToken stoppingToken)
    {
        try
        {
            const string keyPrefix = "phone_details";
            const string trackingKey = $"{keyPrefix}_keys";

            var enumerable = await conn.QueryAsync<PhoneModel>(PhoneModel.Sql);

            var keys = redis.SetMembers(trackingKey);
            var batch = redis.CreateBatch();

            foreach (var k in keys)
            {
                var key = k.ToString();
                _ = batch.KeyDeleteAsync($"{keyPrefix}:{key}");
            }

            foreach (var item in enumerable)
            {
                var key = item.Key.Trim();
                HashEntry[] fields = [
                    new HashEntry("accountId", item.AccountId.ToString()),
                    new HashEntry("firstName", item.FirstName),
                    new HashEntry("familyName", item.FamilyName),
                    new HashEntry("verifiedAt", item.VerifiedAt?.ToString("O") ?? RedisValue.EmptyString),
                    new HashEntry("cancelledAt", item.CancelledAt?.ToString("O") ?? RedisValue.EmptyString)
                ];

                _ = batch.HashSetAsync($"{keyPrefix}:{key}", fields);
                _ = batch.SetAddAsync(trackingKey, key);
            }

            batch.Execute();
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }
    }

    public class PhoneModel
    {
        internal static string Sql = @" 
        select ap.number as key,
               ap.account_id,
               ap.verified_at,
               a.first_name,
               a.family_name,
               a.cancelled_at
        from core.account_phone ap
        join core.account a on ap.account_id = a.id
    ";

        public required string Key { get; init; }

        public Guid AccountId { get; init; }

        public DateTime? VerifiedAt { get; init; }

        public required string FirstName { get; init; }

        public required string FamilyName { get; init; }

        public DateTime? CancelledAt { get; init; }
    }
}