using Npgsql;
using Proptexx.Core.Messaging;
using Proptexx.Core.Stores;

namespace Proptexx.Core.Utils;

public static class MessageHelper
{
    public static async Task OnWidgetSignupToLead(NpgsqlConnection npgsql, string email, string otp)
    {
        var (subject, body) = EmailTemplate.OnWidgetSignupToLead(otp);
        var opt = new MessageCreationOptions()
            .AddRecipient(new EmailRecipientOption(email, null))
            .SetBody(body, true);

        await MessageService.CreateAndSendAsync(npgsql, subject, opt);
    }

    public static async Task OnWidgetSignupToAdmin(
        NpgsqlConnection npgsql, 
        Guid workspaceId, 
        string? listingUrl,
        string leadName, 
        string leadEmail,
        string? widgetSignupEmailReceiver)
    {
        var (subject, body) = EmailTemplate
            .OnWidgetSignupToAdmin(listingUrl, leadName, leadEmail);

        var opt = new MessageCreationOptions();

        if (widgetSignupEmailReceiver is null)
        {
            var admins = await npgsql.Workspace().GetAdmins(workspaceId);
            if (admins.Count <= 0) return;

            foreach (var admin in admins)
            {
                opt.AddRecipient(new EmailRecipientOption(admin.Email, admin.AccountId));
            }
        }
        else
        {
            var receivers = widgetSignupEmailReceiver.Contains(';')
                ? widgetSignupEmailReceiver.Split(';')
                : [widgetSignupEmailReceiver];

            foreach (var r in receivers)
            {
                opt.AddRecipient(new EmailRecipientOption(r, null));
            }
        }
        
        opt.SetBody(body, true);
        await MessageService.CreateAndSendAsync(npgsql, subject, opt);
    }

    public static async Task OnPortalOnboarding(NpgsqlConnection npgsql, string email, string fullName)
    {
        var (subject, body) = EmailTemplate.OnPortalAccountRegistration(fullName);
        var opt = new MessageCreationOptions()
            .AddRecipient(new EmailRecipientOption(email, null))
            .SetBody(body, true);

        await MessageService.CreateAndSendAsync(npgsql, subject, opt);
    }

    public static async Task SendOtp(NpgsqlConnection npgsql, string email, string otp)
    {
        var (subject, body) = EmailTemplate.OnOtpRequested(otp);
        var opt = new MessageCreationOptions()
            .AddRecipient(new EmailRecipientOption(email, null))
            .SetBody(body, true);

        await MessageService.CreateAndSendAsync(npgsql, subject, opt);
    }
}