﻿using Dapper;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Dashboard;

public sealed class GetChartComboOptionsV2 : IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var accountId = Guid.Parse(context.User.GetCallerId());
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);

        var accounts = await npgsql.QueryAsync<TreeOptionsDto>(SqlAccount, new { _account_id = accountId });
        var workspaces = await npgsql.QueryAsync<TreeOptionsDto>(SqlWorkspace, new { _account_id = accountId });
        var clients = await npgsql.QueryAsync<TreeOptionsDto>(SqlClient, new { _account_id = accountId });

        var workspaceMap = workspaces.ToDictionary(w => w.Id);
        foreach (var workspace in workspaces)
        {
            if (workspace.ParentId != null && workspaceMap.ContainsKey(workspace.ParentId.Value))
            {
                workspaceMap[workspace.ParentId.Value].Children.Add(workspace);
            }
        }

        foreach (var client in clients)
        {
            if (workspaceMap.ContainsKey(client.ParentId.Value))
            {
                workspaceMap[client.ParentId.Value].Children.Add(client);
            }
        }

        var topLevelWorkspaces = workspaceMap.Values.Where(w => w.ParentId == null).ToList();

        var account = accounts.FirstOrDefault();
        if (account != null)
        {
            account.Children.AddRange(topLevelWorkspaces);
            return account;
        }

        return topLevelWorkspaces;
    }

    public static string SqlAccount => @"
        SELECT id, concat_ws(' ', first_name, family_name) AS name, NULL AS parent_id, 'account' AS type 
        FROM core.account 
        WHERE id = :_account_id;
    ";

    public static string SqlWorkspace => @"
        SELECT id, name, parent_id, 'workspace' AS type 
        FROM core.workspace;
    ";

    public static string SqlClient => @"
        SELECT id, name, workspace_id AS parent_id, 'client' AS type 
        FROM core.client;
    ";
}

public class TreeOptionsDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public Guid? ParentId { get; set; }
    public string Type { get; set; } = string.Empty;
    public List<TreeOptionsDto> Children { get; set; } = new();
}
