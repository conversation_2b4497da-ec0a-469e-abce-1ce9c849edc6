using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations;

[Migration(250321001)]
public class Migrate250321001 : FluentMigrator.Migration
{
    private const string FilePath = "base-20250321.sql";
    private readonly string[] _schemas = ["core", "crm", "batching", "widget", "telemetry"];
    
    public override void Up()
    {
        // If schemas exist, just return
        var schemaExists = _schemas.Any(s =>
        {
            return Schema.Schema(s).Exists();
        });

        if (schemaExists) return;

        // Applyign base script
        this.Execute.Script(FilePath);
    }

    public override void Down()
    {
    }
}