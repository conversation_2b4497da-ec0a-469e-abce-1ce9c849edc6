using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class Domain : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; init; } = Guid.NewGuid();
    
    public required Guid WorkspaceId { get; init; }
    
    public required string Hostname { get; set; }

    public string? Path { get; set; }
    
    public string? QueryParams { get; set; }

    public string? InputValue { get; set; }

    public MatchStrategy MatchStrategy { get; set; }

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public DateTime? UpdatedAt { get; set; }

    public string GetDbRef() => "core.domain";
}

public enum MatchStrategy
{
    Start = 0,
    Exact = 1,
    Regex = 2
}