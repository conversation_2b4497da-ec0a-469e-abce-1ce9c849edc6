using System.ComponentModel.DataAnnotations;
using Dapper;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Attributes;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Constants;

namespace Proptexx.Web.Command.Workspace;

public sealed class RevokeService : ICommand
{
    [Required, GuidNotEmpty]
    public required Guid WorkspaceId { get; init; }

    [Required]
    public required string ServiceId { get; init; }

    public string? ClientSecretId { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        await using var trx = await npgsql.BeginTransactionAsync(context.CancellationToken);

        try
        {
            await npgsql.ExecuteAsync(@"
                delete from core.workspace_service_binding wsb 
                where wsb.workspace_id = :_workspace_id and wsb.service_id = :_service_id
            ", new { _workspace_id = WorkspaceId, _service_id = ServiceId });

            string? clientName = ServiceId == WidgetConstants.ServiceNames.WidgetAccess
                ? "widget"
                : ServiceId;

            if (clientName != null)
            {
                if (!string.IsNullOrEmpty(ClientSecretId))
                {
                    await npgsql.ExecuteAsync(@"
                        delete from core.client_subscription s
                        where s.client_secret_id = :_client_secret_id
                    ", new { _client_secret_id = Guid.Parse(ClientSecretId) });
                }
                await npgsql.ExecuteAsync(@"
                    delete from core.client c
                    where c.workspace_id = :_workspace_id and c.name = :_client_name
                ", new { _workspace_id = WorkspaceId, _client_name = clientName });
            }

            await trx.CommitAsync(context.CancellationToken);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            await trx.RollbackAsync(context.CancellationToken);
            throw;
        }
    }
}