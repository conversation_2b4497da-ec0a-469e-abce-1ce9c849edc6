import subprocess
import sys
import time
import argparse
from os import path

# Define a function to handle command-line arguments
def parse_args():
    parser = argparse.ArgumentParser(description="Container Build Script")

    # Add arguments for container builder and container registry
    parser.add_argument(
        "--registry",
        required=True,
        help="Specify the container registry"
    )

    parser.add_argument(
        "--program",
        default="docker",
        help="Specify 'docker' or 'podman' as the container build program (default: 'docker')"
    )

    parser.add_argument(
        "--tags",
        default="latest",
        help="Specify the git commit ID"
    )

    return parser.parse_args()

args = parse_args()
tags = args.tags.split(',')

RED = "\033[31m"
GREEN = "\033[32m"
RESET = "\033[0m"
ITEMS = [
    "proptexx.server.sdk",
    "proptexx.web.auth",
    "proptexx.web.api",
    "proptexx.web.command",
    "proptexx.web.partner",
    "proptexx.web.payment",
    "proptexx.web.query",
    "proptexx.web.webhook",
    "proptexx.worker.batchworker",
    "proptexx.worker.batchreporter",
    "proptexx.worker.datasync",
    "proptexx.worker.messaging",
    "proptexx.worker.telemetry",
    "proptexx.database.migration"
]

def push_item(image_name, index, total):
    print(f"Pushing {image_name} ({index}/{total})")

    for tag in tags:
        command = [args.program, "push", "--tls-verify=false", f"{image_name}:{tag}"]
        result = subprocess.run(command, capture_output=False, text=False)

        if (result.returncode == 0):
            print(f"{GREEN}Successfully pushed {image_name}:{tag} ({index}/{total}){RESET}\n")
        else:
            print(f"{RED}Failed to push {image_name}:{tag} ({index}/{total}){RESET}\n")
            sys.exit(1)

    return 1

if (__name__ == "__main__"):
    try:
        start_time = time.time()
        num_success = 0

        for index, item in enumerate(ITEMS, start=1):
            num_success += push_item(f"{args.registry}/{item}",
                                     index, len(ITEMS))

        end_time = time.time()
        total_time = end_time - start_time
        all_success = num_success == len(ITEMS)

        print(
            f"{GREEN if all_success else RED}Pushed {num_success} / {len(ITEMS)} in {total_time}{RESET}")

    except KeyboardInterrupt:
        print(f"\n{RED}Script interrupted by user{RESET}")
        sys.exit(1)
