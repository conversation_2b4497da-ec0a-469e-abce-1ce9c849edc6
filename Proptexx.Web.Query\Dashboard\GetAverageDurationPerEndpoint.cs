﻿using Dapper;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Auth;

namespace Proptexx.Web.Query.Dashboard;

public sealed class GetAverageDurationPerEndpoint : IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var accountId = Guid.Parse(context.User.GetCallerId()); // Get the accountId from caller
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);

        var result = await npgsql.QueryAsync<DurationModel>(Sql, new { account_id = accountId });
        return result;
    }

    public sealed class DurationModel
    {
        public required string Endpoint { get; init; }
        public double AverageDuration { get; init; }
    }

    public static string Sql => @"
    WITH Extracted AS (
        SELECT 
            CASE 
            -- Extract 'identifier' when endpoint is '/_query'
            WHEN endpoint = '/_query' AND request_body LIKE '%identifier%' 
            THEN split_part(jsonb_extract_path_text(request_body::jsonb, '_', 'identifier'), '.', 2)

            -- Remove '/cv/' prefix when endpoint starts with '/cv/'
            WHEN endpoint LIKE '/cv/%' 
            THEN RIGHT(endpoint, LENGTH(endpoint) - 4)

            -- Keep all other endpoints as they are
            ELSE endpoint 
        END AS full_identifier,
            duration_ms
        FROM telemetry.api_logs
        WHERE account_id = @account_id
        and request_body NOT LIKE '%dashboard.%'
    )
    SELECT 
        full_identifier AS endpoint, 
        ROUND(AVG(duration_ms), 2) AS AverageDuration
    FROM Extracted
    WHERE full_identifier IS NOT NULL 
    GROUP BY endpoint
    ORDER BY AverageDuration DESC;
";


}
