using System.Text.Json.Serialization;

// using SharpCompress.Common;

namespace Proptexx.Web.Auth.OAuth;

public sealed class UserInfo
{
    public required string Id { get; init; }

    public string? Name { get; init; }

    public string? GivenName { get; init; }

    public string? FamilyName { get; init; }

    public string? Picture { get; init; }

    public string? Email { get; init; }

    public bool? EmailVerified { get; init; }

    public string? Locale { get; init; }
}

public sealed class MicrosoftUserInfo
{
    [JsonPropertyName("id")]
    public required string Id { get; init; }

    [JsonPropertyName("displayName")]
    public string? DisplayName { get; init; }

    [JsonPropertyName("givenName")]
    public string? GivenName { get; init; }

    [JsonPropertyName("surname")]
    public string? Surname { get; init; }

    [JsonPropertyName("userPrincipalName")]
    public string? UserPrincipalName { get; init; }

    [JsonPropertyName("mail")]
    public string? Mail { get; init; }

    [JsonPropertyName("jobTitle")]
    public string? JobTitle { get; init; }

    [JsonPropertyName("mobilePhone")]
    public bool? MobilePhone { get; init; }

    [JsonPropertyName("officeLocation")]
    public string? OfficeLocation { get; init; }

    [JsonPropertyName("preferredLanguage")]
    public string? PreferredLanguage { get; init; }

    public string GetEmail()
    {
        var result = this.Mail ?? this.UserPrincipalName;
        if (string.IsNullOrWhiteSpace(result) || !result.Contains('@'))
        {
            throw new FormatException($"Invalid email address {result}");
        }

        return result;
    }
}

/*
     "businessPhones": [
       "40077335"
     ],
     "mobilePhone": null,
     "officeLocation": null,
     "preferredLanguage": null,
     "userPrincipalName": "<EMAIL>",
 */