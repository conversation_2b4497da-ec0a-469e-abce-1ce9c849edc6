using System.Text.Json;
using System.Text.Json.Serialization;
using Npgsql;
using Proptexx.AI;
using Proptexx.Core;
using Proptexx.Core.Entities;
using Proptexx.Core.Json;
using Proptexx.Core.Services;
using Proptexx.Web.Api.Helpers;
using Proptexx.Web.Api.Responses;
using StackExchange.Redis;

namespace Proptexx.Web.Api.Handlers;

public sealed class BatchHandler
{
    private readonly NpgsqlDataSource _dataSource;
    private readonly IServiceProvider _services;
    private readonly ILogger<BatchHandler> _logger;
    private readonly IDatabase _redis;
    private readonly BatchDataService _batchDataService;

    public BatchHandler(
        IConnectionMultiplexer connectionMultiplexer,
        IServiceProvider services,
        ILogger<BatchHandler> logger,
        NpgsqlDataSource dataSource,
        BatchDataService batchDataService)
    {
        _dataSource = dataSource;
        _services = services;
        _logger = logger;
        _redis = connectionMultiplexer.GetDatabase();
        _batchDataService = batchDataService;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var batchId = Guid.NewGuid();
        var response = new BatchApiResponse();

        try
        {
            var workspaceId = context.User.GetWorkspaceGuid();
            var workspaceName = context.User.TryGetWorkspaceName() ?? workspaceId.ToString();
            var doc = await JsonDocument.ParseAsync(context.Request.Body);
            var items = PayloadService.GetRequiredArray(doc, "items", 0);
            var session = PayloadService.GetOptionalString(doc, "session");

            var i = 0;
            var batchTasks = new List<BatchTask>();
            foreach (var element in items)
            {
                var batchTask = BatchHelper.ParseBatchPayloadForBatchItem(batchId, element, i);

                if (!_services.HasModel(batchTask.Model))
                {
                    var similarModels = ModelFactory.GetModelsContaining(batchTask.Model.Split('/').LastOrDefault() ?? "").Take(5).ToList();
                    var suggestion = similarModels.Any() 
                        ? $" Similar models: [{string.Join(", ", similarModels)}]"
                        : "";
                    
                    // Provide specific guidance for common RoomType issues
                    var guidance = similarModels.Any()
                        ? " If one of the above models is correct, please update the 'model' field to that exact name."
                        : " Please verify the 'model' field matches one of the available model names.";
                    
                    throw new ApplicationException($"The requested model '{batchTask.Model}' is not accessible - model not found in registry.{suggestion}{guidance}");
                }

                batchTasks.Add(batchTask);
                i++;
            }

            var batch = new Batch
            {
                Id = batchId,
                WorkspaceId = workspaceId,
                AccountId = context.User.TryGetCallerGuid(),
                Session = session,
                CallbackUrl = BatchHelper.GetCallbackUrl(context),
                IsSync = false
            };

            // Store batch metadata in Redis for BigQuery export
            await _batchDataService.StoreBatchMetadataAsync(batchId.ToString(), new
            {
                id = batch.Id.ToString(),
                workspace_id = batch.WorkspaceId.ToString(),
                account_id = batch.AccountId?.ToString(),
                session = batch.Session,
                created_at = DateTime.UtcNow,
                callback_url = batch.CallbackUrl,
                is_sync = batch.IsSync
            });

            // Store task metadata in Redis for BigQuery export
            foreach (var batchTask in batchTasks)
            {
                await _batchDataService.StoreTaskMetadataAsync(batchTask.Id.ToString(), new
                {
                    id = batchTask.Id.ToString(),
                    batch_id = batchTask.BatchId.ToString(),
                    created_at = DateTime.UtcNow,
                    model = batchTask.Model,
                    config = batchTask.Config
                });

                // Track this task as part of the batch
                await _batchDataService.AddTaskToBatchAsync(batchId.ToString(), batchTask.Id.ToString());
            }
            
            await BatchHelper.PushToRedisAsync(_redis, workspaceName, batch, batchTasks, null);

            context.Response.StatusCode = StatusCodes.Status200OK;
            context.Items.Add("batchId", batchId);

            response.Success(batchId);
        }
        catch (ApplicationException e)
        {
            context.Response.StatusCode = StatusCodes.Status400BadRequest;
            response.Fail(e.Message);
        }
        catch (UnauthorizedAccessException e)
        {
            context.Response.StatusCode = StatusCodes.Status401Unauthorized;
            response.Fail(e.Message);
        }
        catch (JsonException)
        {
            context.Response.StatusCode = StatusCodes.Status400BadRequest;
            response.Fail("Unable to parse the request payload");
        }
        catch (Exception e)
        {
            _logger.LogError(e, "System exception in {HandlerName}", nameof(BatchHandler));
            context.Response.StatusCode = StatusCodes.Status500InternalServerError;
            response.Fail("The operation exited with an error. We have been notified and are looking into it");
        }
        finally
        {
            await context.Response.WriteAsJsonAsync(response, JsonDefaults.JsonSerializerOptions);
        }
    }
}

public sealed class BatchApiResponse : ApiResponse
{
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? Label { get; init; }
}