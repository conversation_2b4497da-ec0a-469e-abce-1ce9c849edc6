using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations;

[Migration(250403001)]
public class Migrate250403001 : FluentMigrator.Migration
{
    public override void Up()
    {
        // Drop existing materialized views if they exist
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_minute_today CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_minute_past CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_hourly_today CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_hourly_past CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_daily_today CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_daily_past CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_monthly_today CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_monthly_past CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_minute_summary CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_hourly_summary CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_daily_summary CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_monthly_summary CASCADE;");

        // Create views for minute, hourly, daily, and monthly time frames
        CreateTimeFrameView("minute", "date_trunc('minute', created_at)");
        CreateTimeFrameView("hourly", "date_trunc('hour', created_at)");
        CreateTimeFrameView("daily", "date_trunc('day', created_at)");
        CreateTimeFrameView("monthly", "date_trunc('month', created_at)");
    }

    private void CreateTimeFrameView(string granularity, string truncExpression)
    {
        // Create Today View
        Execute.Sql($@"
            CREATE MATERIALIZED VIEW telemetry.mv_api_requests_timeline_{granularity}_today AS
            SELECT 
                workspace_id,
                {truncExpression} AS time_unit,
                COUNT(*) AS total_requests,
                SUM(CASE WHEN response_code >= 400 THEN 1 ELSE 0 END) AS failed_requests
            FROM telemetry.api_logs
            WHERE created_at >= date_trunc('day', now()) -- Today�s data only
            GROUP BY workspace_id, time_unit
            WITH NO DATA;
        ");
        Execute.Sql($@"CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_api_requests_timeline_{granularity}_today 
                      ON telemetry.mv_api_requests_timeline_{granularity}_today (workspace_id, time_unit);");

        // Create Old Data View
        Execute.Sql($@"
            CREATE MATERIALIZED VIEW telemetry.mv_api_requests_timeline_{granularity}_past AS
            SELECT 
                workspace_id,
                {truncExpression} AS time_unit,
                COUNT(*) AS total_requests,
                SUM(CASE WHEN response_code >= 400 THEN 1 ELSE 0 END) AS failed_requests
            FROM telemetry.api_logs
            WHERE created_at < date_trunc('day', now()) -- Before today�s data
            GROUP BY workspace_id, time_unit
            WITH NO DATA;
        ");
        Execute.Sql($@"CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_api_requests_timeline_{granularity}_past 
                      ON telemetry.mv_api_requests_timeline_{granularity}_past (workspace_id, time_unit);");

        // Create Summary View for each time frame
        Execute.Sql($@"
            CREATE MATERIALIZED VIEW telemetry.mv_api_requests_timeline_{granularity}_summary AS
            SELECT 
                workspace_id,
                time_unit,
                SUM(total_requests) AS total_requests,
                SUM(failed_requests) AS failed_requests
            FROM (
                SELECT * FROM telemetry.mv_api_requests_timeline_{granularity}_today
                UNION ALL
                SELECT * FROM telemetry.mv_api_requests_timeline_{granularity}_past
            ) AS combined
            GROUP BY workspace_id, time_unit
            WITH NO DATA;
        ");
        Execute.Sql($@"CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_api_requests_timeline_{granularity}_summary 
                      ON telemetry.mv_api_requests_timeline_{granularity}_summary (workspace_id, time_unit);");
    }

    public override void Down()
    {
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_minute_today CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_minute_past CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_hourly_today CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_hourly_past CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_daily_today CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_daily_past CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_monthly_today CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_monthly_past CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_minute_summary CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_hourly_summary CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_daily_summary CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_timeline_monthly_summary CASCADE;");
    }
}