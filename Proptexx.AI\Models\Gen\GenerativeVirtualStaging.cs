using System.Net.Http.Json;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Proptexx.Core.Http;
using Proptexx.Core.Redis;
using Proptexx.Core.Storage;

namespace Proptexx.AI.Models.Gen;

public sealed class GenerativeVirtualStaging : GenerativeModel
{
    private readonly IHostEnvironment? _env;
    private readonly IStorageService _storageService;
    private readonly string _modelEndpoint;

    public GenerativeVirtualStaging(IGenerativeClient generativeClient,
        IStorageService storageService,
        IConfiguration configuration,
        WorkspaceDataHashStore workspaceDataHashStore,
        IHostEnvironment? env = null)
        : base(generativeClient, storageService)
    {
        _env = env;
        _storageService = storageService;

        _modelEndpoint = configuration.GetValue<string>("gen_staging_refurnishing_endpoint")
                         ?? throw new NullReferenceException("gen_staging_refurnishing_endpoint");
    }

    protected override string GetEndpointConfig()
    {
        return "https://containers.datacrunch.io/proptexxstaging/generative-virtual-staging/predict";
        // return _modelEndpoint;
    }

    public override async Task<GenerativeModelResponse> InferAsync(GenerativeModelContext context)
    {
        if (_env != null)
        {
            // not IS24

            if (context.Payload is null)
            {
                throw new NullReferenceException(nameof(context.Payload));
            }

            var payload = new
            {
                image = context.Base64Image,
                architecture_style = context.ArchitectureStyle,
                room_type = context.RoomType,
                scene_type = "indoor",
            };

            using var httpClient = new HttpClient();
            var response = await httpClient.PostAsJsonAsync(
                _modelEndpoint,
                payload,
                context.CancellationToken);

            response.EnsureSuccessStatusCode();
            var json = await response.Content.ReadAsStringAsync();
            var doc = JsonDocument.Parse(json);

            string base64Image = string.Empty;
            if (doc.RootElement.TryGetProperty("data", out var dataElement) &&
                dataElement.TryGetProperty("image", out var imageElement) &&
                imageElement.ValueKind == JsonValueKind.String)
            {
                base64Image = imageElement.GetString() ?? string.Empty;
            }
            const string contentType = "image/jpeg";
            var filename = $"{context.ItemId}_{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}.jpg";
            var imgUrl = await _storageService.UploadImageAsync(context.WorkspaceId, filename, base64Image, contentType);

            return new GenerativeModelResponse
            {
                RequestParams = new Dictionary<string, object?>
                {
                    ["image"] = context.Base64Image,
                    ["architecture_style"] = context.ArchitectureStyle,
                    ["room_type"] = context.RoomType,
                    ["scene_type"] = "indoor"
                },
                Base64Image = base64Image,
                MimeType = contentType,
                OutputImageUrl = imgUrl,
                Document = JsonDocument.Parse($$"""{"imageUrl": "{{imgUrl}}"}""")
            };
        }
        else
        {
            // IS24
            
            return await base.InferAsync(context);
        }
    }
}