using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations
{
    [Migration(250725001)]
    public class ImageScratchingRules : FluentMigrator.Migration
    {
        public override void Up()
        {
            Alter.Table("widget")
                .InSchema("core")
                .AddColumn("scratched_image_min_width").AsInt32().Nullable()
                .AddColumn("scratched_image_min_height").AsInt32().Nullable()
                .AddColumn("scratched_image_min_aspect_ratio").AsInt32().Nullable()
                .AddColumn("scratched_image_max_aspect_ratio").AsInt32().Nullable()
                .AddColumn("scratched_image_min_file_size_in_bytes").AsInt64().Nullable()
                .AddColumn("scratched_image_allowed_formats").AsString().Nullable()
                .AddColumn("scratched_image_excluded_keywords").AsString().Nullable();
        }

        public override void Down()
        {
            Delete.Column("scratched_image_min_width").FromTable("widget").InSchema("core");
            Delete.Column("scratched_image_min_height").FromTable("widget").InSchema("core");
            Delete.Column("scratched_image_min_aspect_ratio").FromTable("widget").InSchema("core");
            Delete.Column("scratched_image_max_aspect_ratio").FromTable("widget").InSchema("core");
            Delete.Column("scratched_image_min_file_size_in_bytes").FromTable("widget").InSchema("core");
            Delete.Column("scratched_image_allowed_formats").FromTable("widget").InSchema("core");
            Delete.Column("scratched_image_excluded_keywords").FromTable("widget").InSchema("core");
        }
    }
} 