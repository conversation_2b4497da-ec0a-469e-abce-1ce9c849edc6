﻿using System.ComponentModel.DataAnnotations;
using Dapper;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Dashboard;

public sealed class GetRequestsTimeline : BaseFilter, IQuery
{
    [Required]
    public required string Granularity { get; init; }

    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);

        var result = await npgsql.QueryAsync<TelemetryPoint>(Sql, GetParameters());
        return result;     
    }

    public string Sql => Granularity switch
    {
        "minute" => BaseQuery("minute"),
        "hourly" => BaseQuery("hourly"),
        "daily" => BaseQuery("daily"),
        "monthly" => BaseQuery("monthly"),
        _ => BaseQuery("daily") // Default to daily if invalid value
    };

    private static string BaseQuery(string granularity) => $@"
        SELECT 
            EXTRACT(EPOCH FROM time_unit) * 1000 AS timestamp, 
            SUM(total_requests) AS total_requests,
            SUM(failed_requests) AS failed_requests
        FROM telemetry.mv_api_requests_timeline_{granularity}_summary
        WHERE 
            time_unit BETWEEN @startDate::timestamp AND @endDate::timestamp
            AND (NULLIF(@ids, ARRAY[]::UUID[]) IS NULL OR workspace_id = ANY(@ids))
        GROUP BY time_unit
        ORDER BY time_unit ASC;
    ";
}

public class TelemetryPoint
{
    public long Timestamp { get; set; } // Time in milliseconds
    public int TotalRequests { get; set; }
    public int FailedRequests { get; set; }
}
