using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;

namespace Proptexx.Core.Extensions;

public static class ConfigurationExtensions
{
    public static T GetRequiredKey<T>(
        this IConfiguration configuration,
        string key)
    {
        var result = configuration.GetValue<T>(key);
        return result ?? throw new NullReferenceException($"GetRequiredKey {key}");
    }

    public static T GetRequiredSection<T>(
        this IConfiguration configuration,
        string key)
    {
        var result = configuration.GetSection<T>(key)
            ?? throw new NullReferenceException($"GetRequiredSection {key}");

        return result;
    }

    public static T? GetSection<T>(
        this IConfiguration configuration,
        string key)
    {
        return configuration.GetRequiredSection(key).Get<T>();
    }
}