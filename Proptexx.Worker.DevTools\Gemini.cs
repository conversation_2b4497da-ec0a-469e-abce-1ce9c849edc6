namespace Proptexx.Worker.DevTools;

public static class Gemini
{
    public static Task ExecuteAsync()
    {
        return Task.CompletedTask;
        // static async Task<string> QueryGeminiApiAsync()
        // {
        //     var client = new HttpClient();
        //     var baseUri = new Uri("https://generativelanguage.googleapis.com");
        //     var uri = new Uri(baseUri, "/v1/models/gemini-1.5-flash-001:generateContent?key=AIzaSyD1-3b-wKR5iawcIwVTqOm89liX907t9iY");
        //
        //     var payload = new GeminiRequest();
        //     var gContent = new GeminiContent("user");
        //
        //     gContent.Parts.Add(new Dictionary<string, JsonNode?>
        //     {
        //         ["text"] = "Analyze the following images and determine if they are indoor or outdoor? Additionally, identify if they contain furniture. Return the result parts as JSON object only, not prefixed with 'json'-string, in the specified TypeScript structure. On success: { imageUrl: string, isIndoor: boolean; hasFurniture: boolean | null; isSuccess: true; }, On error: { imageUrl: string; errorMessage: string; isSuccess: false; }"
        //     });
        //
        //     gContent.Parts.Add(new Dictionary<string, JsonNode?>
        //     {
        //         ["inlineData"] = JsonSerializer.SerializeToNode(new {
        //             mimeType = "image/jpeg",
        //             data = "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"
        //         })
        //     });
        //
        //     payload.Contents.Add(gContent);
        //
        //     var json = JsonSerializer.Serialize(payload, JsonDefaults.JsonSerializerOptions);
        //     var content = new StringContent(json, Encoding.UTF8, "application/json");
        //
        //     using var responseMessage = await client.PostAsync(uri, content);
        //     var str = await responseMessage.Content.ReadAsStringAsync();
        //     return str;
        // }
        //
        // public class GeminiRequest
        // {
        //     public List<GeminiContent> Contents { get; init; } = [];
        // }
        //
        // public class GeminiResponse
        // {
        //     public List<GeminiResponseCandidate> Candidates { get; init; } = [];
        // }
        //
        // public class GeminiResponseCandidate
        // {
        //     public required GeminiContent Content { get; init; }
        //     
        //     public required string FinishReason { get; init; }
        //     
        //     public int Index { get; init; }
        //
        //     public List<object>? SafetyRatings { get; init; }
        // }
        //
        // public class GeminiContent
        // {
        //     public GeminiContent(string role)
        //     {
        //         this.Role = role;
        //     }
        //
        //     public string Role { get; init; }
        //
        //     public List<Dictionary<string, JsonNode?>> Parts { get; set; } = [];
        // }
        //
        // public class GeminiResponsePartWidget
        // {
        //     public required string ImageUrl { get; init; }
        //
        //     public bool IsSuccess { get; init; }
        //
        //     public bool? IsIndoor { get; init; }
        //     
        //     public bool? HasFurniture { get; init; }
        //     
        //     public string? ErrorMessage { get; init; }
        // }


        // var passwd = "YzAxYjVmOWUtZDVkZC00OTg3LThiMzctMGE2MjhhZmNkNWYzfGExNGE1M2U4LTJlOTktNDVlMi05ZjM3LWRkMTAwNTEyOGYxZQ";
        // var (hash, salt) = PasswordService.GenerateSecret("ProptexxP51!");
        // Console.WriteLine($"Hash: {hash} - Salt: {salt}");

        // Create client api key for EXIT
        // var clientId = Guid.Parse("b34ea94e-806e-4746-aa05-ab4b7b25a705");
        // var secretId = Guid.Parse("0426dfb9-400b-4c9f-aa88-f29709688a08");
        // var apiKey = ApiKeyService.CreateApiKey(clientId, secretId);
        // Console.WriteLine($"ApiKey: {apiKey}");
        // var z = ApiKeyService.ParseApiKey(
        //     "NmJjYWIzYTktYWZhMy00ZDdhLWIwNjQtYzAzZjFiOGVkOWNlfDAxMDc1ZDA5LTJiODgtNGYzNS1iZTE2LTg1ZjdjODYxNmI2MQ");

        // Migrate data from old to new database
        // using var pgOld = new OldConnection();
        // using var pgNew = new NewConnection();
        //
        // Console.WriteLine("Connected to both databases");
        //
        // var (users, userData) = pgOld.GetData();
        // Console.WriteLine($"Total Users entries {users.Count}");
        // Console.WriteLine($"Total Userdata entries: {userData.Count}");
        //
        // pgNew.Insert(users, userData);
        //
        // Console.WriteLine("DONE");

    }
}