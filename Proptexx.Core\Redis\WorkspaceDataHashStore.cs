using StackExchange.Redis;

namespace Proptexx.Core.Redis;

public sealed class WorkspaceDataHashStore : RedisHashStore<WorkspaceDataHashStore.WorkspaceDataModel>
{
    public WorkspaceDataHashStore(IConnectionMultiplexer connectionMultiplexer) 
        : base(connectionMultiplexer, "workspace_data")
    {
    }

    protected override string ResolveKey(WorkspaceDataModel entry) => entry.Id.ToString();

    public async Task<(string? endpoint, string? watermark)> GetGenerativeEndpointConfigAsync(Guid workspaceId, string model)
    {
        (string?, string?) result = (null, null);
        var entry = await GetEntryAsync(workspaceId.ToString());
        if (entry is null) return result;
        entry.Data.TryGetValue($"{model}:endpoint", out var endpoint);
        entry.Data.TryGetValue($"{model}:watermark", out var watermark);
        return (endpoint, watermark);
    }

    public class WorkspaceDataModel
    {
        public required Guid Id { get; init; }

        public Dictionary<string, string> Data { get; init; } = new();
    }
}