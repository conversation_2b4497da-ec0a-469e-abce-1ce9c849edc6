using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Attributes;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Workspace;

public sealed class GetServices : IQuery
{
    [GuidNotEmpty] public Guid? WorkspaceId { get; init; }
    
    public string? ServiceType { get; init; }
    
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var workspaceId = WorkspaceId ?? context.User.GetWorkspaceGuid();

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var enumerable = await npgsql.QueryAsync<ServiceModel>(ServiceModel.Sql, new { _workspace_id = workspaceId, _service_type = this.ServiceType });
        return enumerable;
    }

    public sealed class ServiceModel
    {
        public required string Id { get; init; }
        
        public required string Title { get; init; }
        
        public string? Description { get; init; }
        
        public DateTime HadSince { get; init; }
        
        public DateTime? ExpiresAt { get; init; }

        public static string Sql => @"
            select s.id,
                   s.title,
                   s.description,
                   wsb.created_at as had_since,
                   wsb.expires_at as expires_at
            from core.service s
            join core.workspace_service_binding wsb on s.id = wsb.service_id
            where wsb.workspace_id = :_workspace_id 
              and (wsb.expires_at is null or wsb.expires_at > current_timestamp) and (:_service_type is null or lower(s.service_type) = lower(:_service_type))
            order by s.title;
        ";
    }
}