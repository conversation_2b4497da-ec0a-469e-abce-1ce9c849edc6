using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;
using Proptexx.Core.AI;
using Proptexx.Core.Http;
using Proptexx.Core.Services;

namespace Proptexx.AI;

public abstract class TextModel<T> : BaseModel<T>, IModel where T : class
{
    private readonly ITextualClient _textualClient;

    protected TextModel(ITextualClient textualClient)
    {
        _textualClient = textualClient;
    }
    
    public override async Task<ModelResponse> InferAsync(ModelContext context)
    {
        if (context.Payload is null)
        {
            throw new ApplicationException("Payload is empty");
        }
        
        var message = PayloadService.GetRequiredString(context.Payload, "message");
        return await InferAsync(message, context.CancellationToken);
    }

    public async Task<ModelResponse> InferAsync(string message, CancellationToken cancellationToken)
    {
        const string apiKey = "AIzaSyD1-3b-wKR5iawcIwVTqOm89liX907t9iY";
        var baseUri = new Uri("https://generativelanguage.googleapis.com");
        const string path = $"/v1/models/gemini-1.5-flash-001:generateContent";
        var queryString = BuildQueryString(new Dictionary<string, string>
        {
            { "key", apiKey }
        });

        var prompt = await ResolvePromptAsync(message);
        
        var c = new CvContent("user");
        c.AddText(prompt);
        
        var uri = new Uri(baseUri, $"{path}?{queryString}");
        var value = new
        {
            contents = new[] { c },
            generationConfig = new
            {
                maxOutputTokens = 100,
                temperature = 0.2,
                topP = 0.95
            },
            safetySettings = new[]
            {
                new { category = "HARM_CATEGORY_HATE_SPEECH", threshold = "BLOCK_MEDIUM_AND_ABOVE" },
                new { category = "HARM_CATEGORY_DANGEROUS_CONTENT", threshold = "BLOCK_MEDIUM_AND_ABOVE" },
                new { category = "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold = "BLOCK_MEDIUM_AND_ABOVE" },
                new { category = "HARM_CATEGORY_HARASSMENT", threshold = "BLOCK_MEDIUM_AND_ABOVE" }
            }
        };

        var document = await _textualClient.SendRequest(uri.AbsoluteUri, value, cancellationToken);
        
        return new ModelResponse
        {
            Document = ExtractTextToDocument(document)
        };
    }
    
    private static JsonDocument? ExtractTextToDocument(JsonDocument document)
    {
        var root = document.RootElement;

        if (root.TryGetProperty("candidates", out var candidatesElement) &&
            candidatesElement.GetArrayLength() > 0 &&
            candidatesElement[0].TryGetProperty("content", out var contentElement) &&
            contentElement.TryGetProperty("parts", out var partsElement) &&
            partsElement.GetArrayLength() > 0 &&
            partsElement[0].TryGetProperty("text", out var textElement))
        {
            var text = textElement.GetString();
            if (!string.IsNullOrWhiteSpace(text))
            {
                return JsonDocument.Parse(text);
            }
        }

        return null;
    }

    private class CvContent
    {
        public CvContent(string role)
        {
            this.Role = role;
        }

        public string Role { get; init; }

        public List<Dictionary<string, JsonNode?>> Parts { get; } = [];

        public void AddText(string text)
        {
            Parts.Add(new Dictionary<string, JsonNode?>
            {
                ["text"] = text
            });
        }

        public void AddInlineData(string data, string mimeType)
        {
            Parts.Add(new Dictionary<string, JsonNode?>
            {
                ["inlineData"] = JsonSerializer.SerializeToNode(new { data, mimeType })
            });
        }

        internal class Response
        {
            [JsonPropertyName("candidates")] public List<Candidate>? Candidates { get; set; } = null!;

            [JsonPropertyName("usageMetadata")]
            public UsageMetadata? UsageMetadata { get; set; }

            public JsonDocument ToJsonDocument()
            {
                var str = JsonSerializer.Serialize(this);
                return JsonDocument.Parse(str);
            }
        }

        internal class Candidate
        {
            [JsonPropertyName("content")]
            public Content? Content { get; set; }

            [JsonPropertyName("finishReason")]
            public string? FinishReason { get; set; }

            [JsonPropertyName("index")]
            public int? Index { get; set; }

            [JsonPropertyName("safetyRatings")]
            public List<SafetyRating>? SafetyRatings { get; set; }
        }

        internal class Content
        {
            [JsonPropertyName("parts")]
            public List<Part>? Parts { get; set; }

            [JsonPropertyName("role")]
            public string? Role { get; set; }
        }

        internal class Part
        {
            [JsonPropertyName("text")]
            public string? Text { get; set; }
        }

        internal class SafetyRating
        {
            [JsonPropertyName("category")]
            public string? Category { get; set; }

            [JsonPropertyName("probability")]
            public string? Probability { get; set; }
        }

        internal class UsageMetadata
        {
            [JsonPropertyName("promptTokenCount")]
            public int? PromptTokenCount { get; set; }

            [JsonPropertyName("candidatesTokenCount")]
            public int? CandidatesTokenCount { get; set; }

            [JsonPropertyName("totalTokenCount")]
            public int? TotalTokenCount { get; set; }
        }
    }

    public abstract Task<string> ResolvePromptAsync(string message);
}