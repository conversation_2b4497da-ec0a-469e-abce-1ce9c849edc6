namespace Proptexx.Core;

public static class BatchIdService
{
    public static string CreateBatchId(Guid workspaceId, string identifier)
    {
        if (workspaceId.Equals(Guid.Empty)) throw new NullReferenceException($"{nameof(CreateBatchId)}: workspaceId is Guid.Empty");
        if (string.IsNullOrWhiteSpace(identifier)) throw new NullReferenceException($"{nameof(CreateBatchId)}: identifier is null or empty");
        return EncodingService.Encode(workspaceId.ToString(), identifier);
    }
}