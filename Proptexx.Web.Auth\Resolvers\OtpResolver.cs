using System.Security.Authentication;
using Dapper;
using Npgsql;
using Proptexx.Core;
using Proptexx.Web.Auth.Auth;
using StackExchange.Redis;

namespace Proptexx.Web.Auth.Resolvers;

public sealed class OtpResolver : IScopeResolver<OtpPayload>
{
    private readonly NpgsqlDataSource _dataSource;
    private readonly IConnectionMultiplexer _connectionMultiplexer;

    public OtpResolver(NpgsqlDataSource dataSource, IConnectionMultiplexer connectionMultiplexer)
    {
        _dataSource = dataSource;
        _connectionMultiplexer = connectionMultiplexer;
    }

    public async Task ResolveAsync(ScopeContext context, OtpPayload payload, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(payload.Username) || string.IsNullOrWhiteSpace(payload.Password))
        {
            throw new AuthException("OTP is required");
        }

        Guid? accountId = null;
        string? emailAddress = null;

        if (Guid.TryParse(payload.Username, out var tempRef))
        {
            accountId = tempRef;
        }
        else
        {
            emailAddress = payload.Username;
        }

        var database = _connectionMultiplexer.GetDatabase();
        var otpService = new RedisOtpService(database);
        var otp = await otpService.GetOtpAsync(payload.Username);

        if (!string.IsNullOrWhiteSpace(otp) && otp.Equals(payload.Password))
        {
            await using var npgsql = await _dataSource.OpenConnectionAsync(cancellationToken);
            await using var trx = await npgsql.BeginTransactionAsync(cancellationToken);

            try
            {
                await npgsql.ExecuteAsync(@"
                    update core.account_email
                        set verified_at = coalesce(verified_at, current_timestamp)
                        where (:_account_id is not null or :_email is not null) and (account_id = :_account_id or email = :_email);
                ", new { _account_id = accountId, _email = emailAddress });

                var data = await npgsql.QueryFirstOrDefaultAsync<UpdateEmailModel>(UpdateEmailModel.Sql, new { _account_id = accountId, _email = emailAddress });
                if (data is not null)
                {
                    await LoginHelper.SetAccountClaimsAsync(context, data.AccountId, $"{data.FirstName} {data.FamilyName}", emailAddress);
                    await LoginHelper.SetWorkspaceClaimsAsync(context, npgsql, data.AccountId);
                    await LoginHelper.SetSystemClaimsAsync(context, data.IsRoot);
                }

                await trx.CommitAsync(cancellationToken);
            }
            catch (Exception)
            {
                await trx.RollbackAsync(cancellationToken);
                throw;
            }
        }
        else
        {
            throw new AuthenticationException("Invalid OTP");
        }
    }
}

public class UpdateEmailModel
{
    public Guid AccountId { get; init; }

    public required string FirstName { get; init; }
    
    public required string FamilyName { get; init; }
    
    public bool IsRoot { get; init; }

    public static string Sql = @"
        select a.id as account_id,
               a.first_name,
               a.family_name,
               a.is_root
        from core.account a
        join core.account_email ae on a.id = ae.account_id
        where (:_account_id is not null or :_email is not null) and (account_id = :_account_id or email = :_email);
    ";
}

public class OtpPayload
{
    public string? Username { get; init; }
    
    public string? Password { get; init; }
}