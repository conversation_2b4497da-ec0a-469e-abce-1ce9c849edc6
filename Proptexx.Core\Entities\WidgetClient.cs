﻿namespace Proptexx.Core.Entities
{
    public class WidgetClient
    {
        // Primary Key
        public string ApiKey { get; set; } = null!;
        
        // Core WidgetClient properties
        public string Domain { get; set; } = null!;
        public List<string> AllowedPaths { get; set; } = [];
        public int RendersUsed { get; set; } = 0;
        public int Quota { get; set; } = 0;
        public string? OutsetaId { get; set; }
        
        // Foreign Keys
        public Guid? WorkspaceId { get; set; }
        public Guid? AccountId { get; set; }
        public string? PlanId { get; set; }
        
        // Date fields
        public DateTime? StartDate { get; set; }
        public DateTime? ExpiredDate { get; set; }
        
        // Navigation Properties
        public Workspace? Workspace { get; set; }
        public Account? Account { get; set; }
        public SubscriptionPlan? SubscriptionPlan { get; set; }
        
        // Computed Properties
        public double UsagePercentage => Quota > 0 ? (double)RendersUsed / Quota * 100 : 0;
        public bool IsExpired => ExpiredDate.HasValue && ExpiredDate.Value < DateTime.UtcNow;
        public bool IsQuotaExceeded => RendersUsed >= Quota;
        public int RemainingQuota => Math.Max(0, Quota - RendersUsed);
        public bool IsActive => !IsExpired && !IsQuotaExceeded;
    }
}
