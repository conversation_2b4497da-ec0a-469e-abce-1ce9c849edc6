
### Authenticate
POST https://auth.dev.local/_auth
Content-Type: application/json
Authorization: Api<PERSON>ey ZTQwYzhmOTEtZjQ2Ni00YzFkLTlkYzctYmJmOGY2MjAyMjE4

{}

> {% client.global.set("accessToken", response.body.$accessToken) %}

### Call Status API with the response id from previous call to get status of batch. Also using the accessToken from first request

GET https://api.dev.local/status/4e2fa793-1e0b-4ed8-b515-14f1dfe3ffdb
Content-Type: application/json
Authorization: Bearer {{accessToken}}
