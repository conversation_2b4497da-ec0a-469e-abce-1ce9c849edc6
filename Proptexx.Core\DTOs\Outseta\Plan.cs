using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Proptexx.Core.DTOs.Outseta // Changed namespace
{
    public class Plan
    {
        public string? Name { get; set; }
        public string? Description { get; set; }
        public PlanFamily? PlanFamily { get; set; }
        public int AccountRegistrationMode { get; set; }
        public bool IsQuantityEditable { get; set; }
        public int MinimumQuantity { get; set; }
        public decimal MonthlyRate { get; set; }
        public decimal AnnualRate { get; set; }
        public decimal QuarterlyRate { get; set; }
        public decimal OneTimeRate { get; set; }
        public decimal SetupFee { get; set; }
        public bool SkipSetupFeeOnPlanChange { get; set; }
        public bool IsTaxable { get; set; }
        public bool IsActive { get; set; }
        public bool IsPerUser { get; set; }
        public bool RequirePaymentInformation { get; set; }
        public int TrialPeriodDays { get; set; }
        public int ExpiresAfterMonths { get; set; }
        public string? UnitOfMeasure { get; set; }
        public List<object>? PlanAddOns { get; set; } 
        public List<object>? ContentGroups { get; set; } 
        public string? Uid { get; set; }
        [JsonPropertyName("_objectType")]
        public string? ObjectType { get; set; }
        public DateTime Created { get; set; }
        public DateTime Updated { get; set; }
    }
}
