local totalToTake = tonumber(ARGV[1])
local now = tonumber(ARGV[2])
local results = {}
local unpack = unpack or table.unpack

if not totalToTake or not now then
    return results
end

local workspaces = redis.call('ZRANGEBYSCORE', 'batch_workspaces', '-inf', now)
local workspaceCount = #workspaces
if workspaceCount <= 0 then
    return results
end

local rateLimits = {}
local rawLimits = redis.call('HMGET', 'batch_rate_limit', unpack(workspaces))

for i, ws in ipairs(workspaces) do
    local limit = tonumber(rawLimits[i]) or 100
    rateLimits[ws] = limit
end

local workspaceIndex = 1
local loopAttempts = 0
local maxAttempts = totalToTake + 2

while #results < totalToTake and loopAttempts < maxAttempts do
    loopAttempts = loopAttempts + 1

    local ws = tostring(workspaces[workspaceIndex])
    local limit = rateLimits[ws] or 100
    local queueKey = 'batch:' .. ws

    local msg = redis.call('RPOP', queueKey)

    if msg then
        local usage = redis.call('HINCRBY', 'batch_usage_counts', ws, 1)
        local remaining = limit - usage

        if remaining >= 0 then
            table.insert(results, msg)

            if usage == 1 then
                redis.call('HEXPIRE', 'batch_usage_counts', 60, 'FIELDS', 1, ws)
            end

        else
            redis.call('ZADD', 'batch_workspaces', now + 60, ws)
            redis.call('HINCRBY', 'batch_usage_counts', ws, -1)
            redis.call('LPUSH', queueKey, msg)
        end
    end

    workspaceIndex = workspaceIndex + 1
    if workspaceIndex > workspaceCount then
        workspaceIndex = 1
    end
end

return results
