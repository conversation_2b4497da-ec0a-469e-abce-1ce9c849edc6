[{"Name": "Portal", "Projects": [{"Path": "Proptexx.Web.Auth\\Proptexx.Web.Auth.csproj", "Action": "Start"}, {"Path": "Proptexx.Web.Command\\Proptexx.Web.Command.csproj", "Action": "Start"}, {"Path": "Proptexx.Web.Query\\Proptexx.Web.Query.csproj", "Action": "Start"}, {"Path": "Proptexx.Worker.Telemetry\\Proptexx.Worker.Telemetry.csproj", "Action": "Start"}, {"Path": "Proptexx.Web.Webhook\\Proptexx.Web.Webhook.csproj", "Action": "Start"}, {"Path": "Proptexx.Worker.DataSync\\Proptexx.Worker.DataSync.csproj", "Action": "Start"}]}, {"Name": "API", "Projects": [{"Path": "Proptexx.Web.Auth\\Proptexx.Web.Auth.csproj", "Action": "Start"}, {"Path": "Proptexx.Web.Command\\Proptexx.Web.Command.csproj", "Action": "Start"}, {"Path": "Proptexx.Web.Query\\Proptexx.Web.Query.csproj", "Action": "Start"}, {"Path": "Proptexx.Worker.Telemetry\\Proptexx.Worker.Telemetry.csproj", "Action": "Start"}, {"Path": "Proptexx.Web.Api\\Proptexx.Web.Api.csproj", "Action": "Start"}]}, {"Name": "Widget)", "Projects": [{"Path": "Proptexx.Web.Auth\\Proptexx.Web.Auth.csproj", "Action": "Start"}, {"Path": "Proptexx.Web.Command\\Proptexx.Web.Command.csproj", "Action": "Start"}, {"Path": "Proptexx.Web.Query\\Proptexx.Web.Query.csproj", "Action": "Start"}, {"Path": "Proptexx.Worker.Telemetry\\Proptexx.Worker.Telemetry.csproj", "Action": "Start"}, {"Path": "Proptexx.Web.Api\\Proptexx.Web.Api.csproj", "Action": "Start"}]}]