using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.System;

public sealed class GetServices : IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        context.User.EnsureRootAccess();

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var result = await npgsql.QueryAsync<ServiceModel>(ServiceModel.Sql);
        return result;
    }

    private sealed class ServiceModel
    {
        public required string Id { get; init; }
        
        public required string Title { get; init; }
        
        public string? Description { get; init; }
        
        public required string ServiceType { get; init; }

        public decimal CreditCost { get; init; }
        
        public static string Sql => @"
            select s.id,
                   s.title,
                   s.description,
                   s.service_type,
                   s.credit_cost
            from core.service s
            order by s.title
        ";
    }
}