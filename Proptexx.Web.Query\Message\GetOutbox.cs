using Dapper;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Message;

public sealed class GetOutbox : IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var accountId = Guid.Parse(context.User.GetCallerId());

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);

        const string sql = @"
            select m.id,
                   m.subject,
                   m.content,
                   m.created_at,
                   max(m.created_at) as created_at,
                   string_agg(mr.recipient, ',') as recipients
            from core.message m
            left outer join core.message_recipient mr on m.id = mr.message_id
            where m.sender_id = :_account_id
            group by m.id
            order by max(m.created_at) desc;
        ";

        var param = new { _account_id = accountId };
        var result = await npgsql.QueryAsync<OutboxModel>(sql, param);
        return result;
    }
    
    public class OutboxModel
    {
        public required Guid Id { get; init; }
        
        public required string Subject { get; init; }
        
        public string? Content { get; init; }
        
        public DateTime CreatedAt { get; init; }
        
        public string? Recipients { get; init; }
    }
}