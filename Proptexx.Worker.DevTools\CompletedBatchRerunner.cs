using System.Text.Json;
using Dapper;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Npgsql;
using Proptexx.Core.Json;
using Proptexx.Core.Redis;
using StackExchange.Redis;

namespace Proptexx.Worker.DevTools;

public static class CompletedBatchRerunner
{
    public static async Task ExecuteAsync(ServiceProvider services, CancellationToken cancellationToken)
    {
        var loggerFactory = services.GetRequiredService<ILoggerFactory>();
        var logger = loggerFactory.CreateLogger(nameof(CompletedBatchRerunner));
        var dataSource = services.GetRequiredService<NpgsqlDataSource>();
        var redis = services.GetRequiredService<IConnectionMultiplexer>();
        var server = redis.GetServer("************", 6379);
        var db = redis.GetDatabase();
        // var keys = server.Keys(pattern: "batch:*:remaining_tasks").ToArray();
        // var values = db.StringGet(keys);

        // var guids = new List<Guid>();
        // for (var i = 0; i < keys.Length; i++)
        // {
        //     var k = keys[i];
        //     var v = values[i];
        //     if (v == 1)
        //     {
        //         var x = k.ToString().Split(':');
        //         guids.Add(Guid.Parse(x[1]));
        //     }
        // }

        List<string> ids =
        [
            "145ce580-ace9-42be-9a6c-dabf2ee8b127",
            "72d90d6d-3e29-4d5a-894d-909c1619d014",
            "1520967d-2d89-4186-a4d1-e43483d93a9e",
            "6d3c6e2c-e662-4727-a205-9118c1e495f4",
            "f8b57ba2-8eef-48b9-85cb-238ca463ede5",
            "959ff44d-1d53-4551-bb78-1c1dddcdf205",
            "d3e16582-6e1f-4632-9046-57c5a1b0d6ee",
            "7bd26772-77b6-4de8-8182-80bfcf4b3640",
            "c63baa93-8376-4f6c-854f-aa59bb698ffc",
            "0d5b9e85-2470-4489-927f-f36cb88d4dd2",
            "9ccc0964-d9f2-437d-af83-5da8b9030a01",
            "fe1eaeeb-4810-4832-90b5-dc45c0030a13",
            "5621586f-f8fa-4265-9307-0d2e2dacdd5b",
            "9025fbe8-bced-4460-9519-3894eacdd3ca",
            "b2b2cc86-9077-4153-85e2-4e28d3eebe65",
            "54d252f1-f8e2-472a-918f-248dc4ba8546",
            "fd276e8f-df99-43dc-b2b6-4b2831a58f54",
            "f53f3ca6-ddcc-4f5a-8212-05dff228d9f9",
            "d4e25194-0a7f-4fae-b3a4-95fbfcab45b1",
            "81021e3d-753a-489c-a488-ceee038fcb1d",
            "378ea925-e148-47a9-a8de-d576021893b7",
            "12b0c309-ac7e-49b0-a257-638d70156d76",
            "ceaa826b-fde5-48e7-9712-874ca10b365d",
            "4bf85e21-097d-4103-b256-82790998fed4",
            "2c1256af-f6a6-4bc5-a85b-08f60b357d1d",
            "f186dd44-2b22-4779-8ab5-018a6bde6c24",
            "ebab7cb9-239b-427a-80c9-4c7328d380d3",
            "7f973e6a-f5c5-4a67-8f31-f197b3f63256",
            "9f41f9a8-f1b4-4d38-ab3b-ddae58ebbcc8",
            "1d4ab756-c848-4e8f-9f02-538b6d1d9958",
            "e790e975-9c8c-4b58-aff8-219b875494e8",
            "8edd7e8d-1e62-43a7-8db5-78082e078cd4",
            "92969609-929c-41dc-b426-ca645c595845",
            "b96ee14c-6d66-4bcd-a8df-2af7d53b5f3f",
            "5b76797b-584d-48a8-b7d0-a9571b8ab017",
            "2512e5bb-ffa7-4447-a4f8-6619147f7ff7",
            "ca5b9f79-4c0a-4a91-9bd7-59cfe5e68d25",
            "7fdb0eca-e68b-489b-b7f8-cc3d67447df6",
            "c739da3e-e109-442c-9e72-c6d2d2992735",
            "a9eaf648-6bf3-4b69-8c0c-3fd043ca62ba",
            "3b67a8e3-385b-47de-b1d8-0fee67538041",
            "041724d1-c3ba-45e3-9d32-1b9077a09a45",
            "f84fb176-c18f-417c-ad7b-bb761c8bfa38",
            "1744de4c-5d9a-446a-9548-25794b10ed62",
            "d4d57592-7c44-4ca9-8942-2a3b9f0a1334",
            "b4615f72-eb4a-4511-89e9-5f4d0460f6d5",
            "35480c7a-cf74-4e10-a5a5-9ecdc6f0cc40",
            "165a956b-f9b6-4dac-b0eb-a7d53976e53b",
            "6697c03a-d0c8-4e70-9b18-55df97e18baa",
            "818dbb4a-17c9-4737-a434-34d7a5b2765b",
            "19bbfecc-221b-49a4-be7f-8a3d6a2dec07",
            "6dc80f06-c233-4e41-8ebf-474f07e54303",
            "e250aea4-4775-4c7c-8e13-4d7f732542d9",
            "9c4ea47c-70da-44fe-bf9c-67deb78e0275",
            "1a8164f6-50ae-4c47-8b65-eece3066cbb3",
            "8d8856d4-ec6d-4d81-82fc-679d035bd7a0",
            "ed7d2a40-cf6a-4068-93ec-3dc7455fad17",
            "eae63196-48cf-4e9c-9aff-c3a3502da535",
            "ceab6fe2-8e5e-440e-bf13-2e46100aa8bd",
            "d062bb9d-ecdb-4f35-bdcd-3de4860440b3",
            "42f86d84-5312-4f7e-b656-2b51988f0531",
            "f3671c94-3f1d-42be-8bf8-f6e51e3a8e69",
            "39afcc21-4c08-4d43-b0e7-40757b48b9dd",
            "db3942c2-4e59-4536-a0cf-1fa4847fa9e3",
            "f8248cdf-e933-4b19-a2e3-4fcea93adbd7",
            "3a635e09-04d6-47ba-a90b-507a03281f25",
            "1a7f1894-7ee3-4c98-b0de-f0b203bd0428",
            "9a6bd094-8d6f-4e57-9d1c-de94c252eb67",
            "d7b75d3d-433d-48ce-817d-7648b97db5dd",
            "9442d289-d7ca-41da-a0b4-e562a9e2769f",
            "453cea1d-0b74-4bd5-b5e0-36d9eaa26ede",
            "1a3cde46-7ece-48a5-9093-25ff6669f97e",
            "2ae4515d-4b1d-49c2-bbc1-ac5b1e3670b7",
            "4c7e8507-7f2a-4235-91c2-0a218d970c68",
            "c17791e2-14dc-4af7-a59d-a3e1c1cbc7e7",
            "47172b8b-f8ad-4943-80a2-a6fa325548bd",
            "27f9b48e-2498-43cb-90a7-681d03981bbe",
            "3a462ba2-3cf0-4fce-a961-3250b16d731f",
            "8b436ba1-c338-42dd-83c3-b5317e4df364",
            "67342d92-f293-4022-a9d6-8dcc8eecc4ad",
            "4ddf90e6-79bc-4a08-a863-3b15d1795ba3",
            "a75f0e02-9db2-41d6-ae7d-41fa245b97bd",
            "d5c80feb-317a-4c3a-a36e-3ab3e426b936",
            "0e42128d-db91-4d39-a2fe-86ddd92f857a",
            "07ac0bf4-a154-452e-b35d-34bfbbe9223c",
            "33b2b710-ae68-4e3a-8b87-716a115efb8e",
            "3cc7c244-e511-44e5-bb3c-0dc7fd5a44f7",
            "613dccdf-63d9-4b3f-9a67-15fa581866aa",
            "7f22a680-c7a4-4a3c-9aec-2d1d42f990a6",
            "e3ab6efc-7fd0-416c-83f0-683944a2b70e",
            "dd9add1a-68c0-471d-b067-a80fc032c39e",
            "41f4fd74-42f9-4f46-8454-a50f5e2aea59",
            "dee05277-cb4c-4740-bec8-16477b74f864",
            "af683d77-a091-4f41-b2f5-2c1724b247c1",
            "9a0588d9-c6a4-4007-ac9b-a76d37202c30",
            "db83a78c-3729-43d3-a88c-a067b38d4ce7",
            "1370b52a-e7a5-4d09-892a-51fda67799ee",
            "dde1398d-07f2-4567-9b38-912bced3e38a",
            "090ecae3-3d5e-4ca5-8552-560012849d27",
            "bc067251-4410-48f9-aa80-0f0223fa8a2f",
            "a12a9b18-e4fb-4752-92a3-4f5779b806c6",
            "1beadc2d-b1c5-468a-bbae-ab5f87c46eca",
            "b2fb42a1-b582-4525-a5ad-4aa696c553ff",
            "59cd24e6-2b41-49d1-93c6-19762b456c04",
            "ccd451ce-9b17-422d-ae11-f86055c77b07",
            "c4514f42-2324-4582-bde5-30d68f942cd1",
            "2d21f8ad-b570-4ff3-a760-891bfe08e973",
            "2d6f4ac3-de60-40a6-886b-06f38fdcb2b0",
            "c2aa9aab-98c5-4497-9845-c1932a750a15",
            "8883b506-fd08-404c-8809-f74cc7cb135d",
            "afe13d46-a095-473c-a0d6-1ca79ef46d58",
            "0048060e-01f5-4f92-b7c7-2c627e232d4c",
            "893810ad-c2bd-4af7-8224-98736559cd1f",
            "4a4dd48d-0435-4731-9336-627f8a91897a",
            "d7798eba-67c1-4bfe-8ca6-f5de290c1605",
            "b5ad3e59-52e9-4de6-8a33-ff174ae0f5d7",
            "30e092e6-6c84-475f-89e6-d90231f0aabc",
            "b012b5a9-14ab-4afa-859c-cdc1176286f9",
            "c7a6ebbf-d054-4f1c-be18-24b400eea8bf",
            "cd0eb318-f6f0-4c35-9834-ea6bdb2fcc20",
            "2eb312be-f67d-4450-a109-2c9371c6398c",
            "948c2c22-f398-46c1-816e-8cc9c611eeb2",
            "897997d0-da27-485a-9f55-51f010cc7939",
            "fef8782f-4ec2-4404-9739-31d6103da5a0",
            "5f50c5d8-951d-4723-8da4-ac4f8458dcd8",
            "9025240b-58bc-4772-be0f-73f9319053b8",
            "13fc66fe-49ee-443c-9d17-124d1c4d5ae5",
            "5b82430b-4c69-4ed0-819d-6e276c4bf525",
            "4cf43e9b-47b9-4ecf-8d90-40c3ab3cec02",
            "72059021-2d95-49f0-b9a1-39e96c826425",
            "42560765-97bc-4493-a051-0c1625ea9e26",
            "d9521b67-68fe-4b9f-a5dd-1d082ca3a323",
            "0d0d46af-498f-4059-85a5-84733f1b3d83",
            "954c573f-1fee-4970-b774-c0cae524c59a",
            "1441ab57-94c0-4bd6-ba51-a9524d26ec61",
            "378ada03-517f-4ddb-9448-3542edae5c80",
            "b94c9038-a96d-4c57-8d19-3b5626a182c5",
            "58b3f52e-1a76-487e-8379-0cc0eec661c7",
            "f19fa576-8ad8-4830-a668-740656888c14",
            "1b13c86c-421c-4553-a318-5ee37d1e42bc",
            "062f0ff7-77d6-410b-babc-90a7a23a40b1",
            "5fc9e1ce-da63-45ec-9973-f0183ed31499",
            "0bc3ff9c-a4ca-43cd-8ab2-07db37b2dfc2",
            "f1fe187f-3362-4424-9104-a124012684fe",
            "2a6ac25e-ae60-4f51-b9b1-55da125c7a24",
            "7885e5cd-2f75-4f90-8410-b6102f4917a9",
            "8d4e33ee-4cff-45d9-adef-a6b4470c9514",
            "e897dcfa-eaff-454e-9df6-75cf78beca9d",
            "68a802b5-8270-47a3-baa6-d7a5ddb70d6e",
            "1fc749ee-c037-49f7-9b3d-68fe26efdc34",
            "b2766f79-b911-44ac-aa24-f053db5bd6c7",
            "7352c11e-b841-4556-8ad2-390a55b73f62",
            "233eb5ab-837f-4ff6-97c9-393bd3e0c53c",
            "396a730e-d289-47df-9c6a-5329685a43bd",
            "e57f4723-1a4d-4296-acee-7dea0877f05e",
            "a110e408-e3f5-4760-9052-bcaaea02531d",
            "d1f2b8be-75a1-4619-ba70-df1a3b411f7e",
            "ddba6731-26b6-4843-a466-824b2b2673ca",
            "aa73e612-c0c2-4d0f-ac99-cdc334ade573",
            "061e4c73-de0c-49d2-bc7a-eaaac9f847de",
            "a5443aa5-f157-47ba-b9f1-fdf7cee622b9",
            "8b8a99da-b855-4510-8b9f-d12f67f1a770",
            "2177dacd-6e80-45b5-9027-71d06b65dc49",
            "4e016469-65f9-45d1-b33e-93acc6b263d7",
            "79c02ba6-11c4-4d9d-b251-87627ddbbb70",
            "c425c402-fc3b-447b-8c93-92a40e3fb1c5",
            "8ad04ed0-eea5-4347-abcc-90c99c8efe4f",
            "3d25b4c5-3be0-4dc9-ae3d-83dddfb5b456",
            "38029b9b-9620-4dde-86fd-6b0cafa771ae",
            "382c5a23-f851-44ae-a49c-994c9da9ca75",
            "080062b9-4b00-4b34-88c3-6af08ba9cfa1",
            "86651760-b34f-4482-8e2a-cecad032a9c2",
            "989ae68f-52ee-4c43-8075-4dc54055db7a",
            "290c3369-9f8b-4fb4-a3cd-20a33c8c846e",
            "05f3da75-99ac-4532-a12b-cb68e6624f5b",
            "4e971c9c-0d46-48a6-a180-a8f6519581e5",
            "bc5600a6-c071-4cdf-bf66-016f4fd411e1",
            "d8b94f79-6bb8-4e3f-8a12-1c53bea19414",
            "6e10fe98-f6f1-4b79-bc9c-da8784e1c349",
            "be2131a1-c971-4a47-82d1-82222b6def40",
            "606ddd18-e0cf-45bf-87bf-95254d929b74",
            "2a537414-32f9-4c40-9ce8-678cf7ceaf3c",
            "e994fef2-23c2-440a-884f-736639f36e97",
            "2df736ab-090f-4ba8-b25d-77a142be9c0d",
            "d17ea4b1-5450-48b2-bce4-3c9db6b262fc",
            "71982075-01fd-435d-bed0-fd58599b8fd7",
            "1dcaa177-9c27-459c-b6c2-e56ad7c884ad",
            "14532e66-43e4-4ed7-bc93-810359034efb",
            "68e0811c-9832-46a1-90f2-6809f1e339dc",
            "01a04728-2ad9-4907-ab90-8bcefe30287d",
            "2b6c8394-29f2-4b55-96a1-6c813c23f329",
            "45f2c432-4e4e-4e04-a312-3e9922f29364",
            "e55d64b7-8bc1-450c-ad0d-0e278ae8795b",
            "b169fea9-5b18-45f6-84ba-07dc3be9cfc3",
            "9f59a85d-dd08-440e-b976-2c61dd32360b",
            "9758bf25-ecca-4d9c-83f7-bb8e840a06b2",
            "8979889f-a7a3-4e9f-84f6-adddc0577d41",
            "263eff72-e956-4d72-94c7-013a99d3b88c",
            "8066448c-6bf8-44d0-8920-48265560e123",
            "cc6396e2-ca8b-405b-94c8-77f01e586ab3",
            "6a281f4e-b8dd-4b22-9f06-c8952e2a6fae",
            "72066ed2-9279-4a99-99a9-1beea562e429",
            "706bb13f-226e-440c-90c7-ea924c38cf36",
            "254d0dfa-eb0c-45cf-a446-26e0b6f6f125",
            "bc97be15-a3c7-402b-bdd6-f4962d3ad7a7",
            "a69eaca2-9466-4951-a2dc-0c4edfa02892",
            "adac39c8-c457-4d5f-907c-c432b5662040",
            "622fe1f0-af9d-494d-9f08-540fb5fd6207",
            "22f0ca9b-c8ab-452d-bb0a-c992e9791533",
            "25f64c6a-c6e3-476e-aa80-afd416288c7a",
            "1aa5b4a4-58b9-4a39-8d36-6b7c4417dd12",
            "e2d1671e-3127-4947-8c97-6c46a11a7ff6",
            "a9e5a699-9f95-43bb-a192-375cded9ea7a",
            "0a66e141-401c-4f0e-8de4-15e4db2b8184",
            "0c236aa5-e3f6-4e9f-8ce6-81f0f054b74d",
            "b9dfee6d-7adb-413e-8096-fdc79f575dc5",
            "f8b995ec-e243-4080-952a-496aa2b40e60",
            "0f586466-dafb-46f1-a540-715519c60383",
            "74448ae8-9df1-4c4c-bee9-89748b9ffa16",
            "8bebf0d3-bf4c-4a8d-ab70-bba256fc4520",
            "3b8aaca0-b233-4741-8eb6-c5f5659de5c7",
            "c1fccebb-ac66-4189-95e3-14d0db8ed09a",
            "f63e86d4-039d-416a-9caa-114c55bc6e71",
            "8c20dea8-7c8a-48f8-92cf-df16d34855dc",
            "4d2588fc-5041-433b-856d-8832499f6ebf",
            "a5934e43-2ece-4d57-bd43-5900354edf2e",
            "f98a430d-b33d-4ff8-910c-7f36270c32e4",
            "e56ebca8-8484-4315-98fc-bc2bc5e00410",
            "7d2ff0fb-f32b-4202-8fc3-fc19ac5eb277",
            "46041b9d-5cf1-4944-bb3c-d18a2a2c686f",
            "09b03394-289d-474a-8877-83793e7f6796",
            "a5f16790-76e2-436b-b154-c89d4500c459",
            "6c0d534e-cb78-4bca-82d9-c580b5b0e68b",
            "864aa14e-35eb-4288-b048-d1cec2bd3450",
            "adfb34c5-db38-406d-b479-d95f6c3517b2",
            "54ce8a26-b973-4f28-92f7-147211a453b4",
            "04008444-2b87-484b-aec4-bffe9ef8d50f",
            "fcb2b7c7-d9d1-4eb8-b876-c55253a7ce69",
            "d4c88831-3a6f-44f4-9292-7d18cf73f827",
            "02d41111-ae7c-4c34-8ec2-2a660f5aa492",
            "f4d07de9-47ba-4c24-ac95-33b75cdcccc5",
            "7627ba65-6b96-4f38-96ef-99dcb7c6e86c",
            "d49dd3fe-9466-4da1-a83e-0b5909509829",
            "0e389cb2-d50d-4a50-aca2-13f375a5fef1",
            "07acef23-177e-4d41-8636-b96319d3a205",
            "0168d0d7-d59b-4d0a-a28d-d161c721a3fe",
            "94d258bc-5726-4c23-87f4-a2ef68d33ea7",
            "e0610719-e06b-41ff-8296-5c19ad092d5b",
            "a0e2b126-b875-4827-8db0-a4a52cd5c442",
            "2cdf57ef-d87b-4199-8e0b-4a97d39f84f9",
            "bde9a830-f134-4a94-8ac0-98e6df1e203b",
            "82168fe9-8157-4d48-9884-ec08ac1b8cb3",
            "1c32598b-07b0-40b0-8b21-a11f866b7e25",
            "761feab5-bbc3-465d-ab0f-6ca524e61113",
            "9b04608d-603f-41b0-9bff-88c1a109a079",
            "280f9341-fbb9-4a0e-be19-15204c9f46bc",
            "4ca4178c-01ca-4460-a2dc-633787dde926",
            "d318df36-e275-48b1-9db2-915fdbddf602",
            "e07c1e2b-2a92-48c0-a4b4-4a66f3fa0e37",
            "81efa1fa-5634-48b4-bfbc-ee81817c1507",
            "367432ce-f0ae-4533-b840-00c1d6eefd78",
            "c14f5511-66ad-4210-b85f-3b5981d29b0a",
            "1aff7f52-e73f-445f-ad5c-706df269b559",
            "a36c1e70-6d3b-485b-96fd-2dc4bdb9c802",
            "f7fc5f41-beb8-4d08-88f4-d652e75bd5a8",
            "d951f91a-8c73-4b91-b99e-b8b19920105f",
            "4fd6a88c-f59d-4a26-8c9e-1dc35693fff2",
            "1974a93b-9d61-4a78-9466-673248ca7b4b",
            "385d47b4-4423-4d90-8994-76f242c10c19",
            "a72631b2-2ace-4846-b9f7-57d3c556ffc6",
            "f35cf2ea-8128-4920-89a9-ddc9fdb08e3c",
            "e86707e6-e9c1-44a5-b20a-6b046410c9f5",
            "f9a9bc91-5045-47c3-8b3d-8fcb7b413758",
            "be6ce68b-091e-451a-8369-de635647b9a5",
            "5a9147ca-c2ae-458f-9184-d212a8c6b6b2",
            "e67e4571-72bc-4720-ad71-350dc47061f3",
            "1f53eda0-20fe-4674-b7f3-e34f1c679b1f",
            "3a4a5fef-d08f-4256-9cd9-8f031e17f1e2",
            "f5f415c9-1c0e-40aa-bc8a-0e23f166a285",
            "ad174238-90d3-4ea2-8808-d418ea7668f6",
            "a0e783f8-cab4-4b2a-b0b5-91a310115a80",
            "51d3e2b0-5cc3-46fa-93e2-71d614a0772b",
            "30cec660-cf74-4375-9d23-ff3d1e9d25ce",
            "50a376b7-c204-4717-b650-66525001b04b",
            "aecdaca8-46fc-4f15-b9f9-be310539a487",
            "4f36d235-60f2-48a4-a03a-38fed44e62b9",
            "7d4f60b1-1497-41f2-96d3-901825201dc5",
            "42f44cf5-8724-4028-ae0e-5b7cd823f779",
            "3fb99f23-d662-4f43-8ba2-9213239e969f",
            "409c3663-e4de-42c5-a90b-57e16dcad721",
            "4a0f7cb5-c7eb-4835-a479-0bb658be2076",
            "b9092a18-0396-4b4e-8f2f-02ef0f1eafdd",
            "0e2719e4-54d1-4892-90d6-201a36a9eacb",
            "2bfe7824-51c6-4a66-bc77-185a75f0685e",
            "6d354cf8-898b-45b4-9ba8-f8604eb9fd7c",
            "b889e0b7-ea48-43d0-84f5-3864a325baff",
            "f619c2c0-8c34-4d06-990f-0c04792d8f70",
            "d83e7c6a-b1f5-4819-9060-ca98b6cccc6b",
            "6482824f-e9f1-41b7-bb81-06a63dc18ba0",
            "e575a3d3-bf77-42b1-904a-4873b21da5dd",
            "7f7d4956-4209-4a51-a476-8e21c5414034",
            "21ba6166-2345-4496-b847-98dbecdaafef",
            "6449ced4-b920-4b97-84db-20472e9e351f",
            "dd045e99-7d4f-4bdf-98af-1a43db0b0948",
            "8e102b5c-2837-4672-ab76-0275b67623e9",
            "ed02d26e-266f-4430-bd2f-51d51929f13f",
            "aa54a897-a6d8-4b07-babd-ea31a69ce2ba",
            "0cb78cdc-99a7-4131-b055-22da7f125532",
            "8cfb35cc-a4a1-4ded-a831-0d65ba79371b",
            "97e86943-581b-4d64-ad73-7f43d12d8a3f",
            "934bba8d-39b3-411f-8a9c-f2dc7c964a19",
            "3fc426c8-716b-43cd-bff1-44841f2112af",
            "23ca787b-6b1e-443a-b784-75277d320fd7",
            "9293e9fd-82b0-4666-9ad4-937cedcb8784",
            "419f0605-c1ed-4579-a8d0-cc1ce4f6a234",
            "1784af8c-670e-4832-a584-8dd134dcc4a2",
            "84575179-735e-4662-b41e-182d620c8577",
            "883b2067-0b27-4677-a62e-a6ac7cbdbb6f",
            "35381a1e-7a3b-4806-ba4f-34aa21808ccd",
            "d46b55fd-8b5d-4ea4-9b2e-854123e3797e",
            "f25d1aef-cbdc-4178-b369-c84255956d29",
            "257c0b3d-6e18-43a9-aa01-0a51e50e6caa",
            "0966bde0-d2f8-4305-b316-d1b6707e78a0",
            "45240a71-7fd7-4c74-8853-f693e3a87934",
            "55b25eff-0c2b-4ad0-bcd2-5f915532615a",
            "f79d6c47-592d-4701-afec-ed2e85e09b62",
            "fae3d6b7-047c-4a6b-a8a3-3375a6f9176f",
            "89c51dcd-4bf5-4781-8b76-0f05f2b95b73",
            "4817436e-f049-430e-a5f8-b896c1bbbdc5",
            "087fff99-ed58-4c73-8705-c58caf8e4910",
            "c104f13b-fa56-43a1-82f6-a77f97dd5b3f",
            "61e889de-25d1-4b68-9ea9-6f093a0f01ff",
            "c10b9d83-26dc-49ba-b460-262c3bd5a2c3",
            "7706fa02-1722-442b-aa05-10dc6dca7fba",
            "0ee67e87-49ed-4ab7-acf6-44b5cd96c569",
            "65d54765-8df1-4c92-83d9-a5e3fdb24700",
            "2696586d-95f8-4476-94e8-2cd18293ce85",
            "d301341f-156a-43bb-81e7-86042f516620",
            "7d086892-3f5e-497b-bed0-3893004ee14d",
            "9274ff02-4e21-4043-91f2-2d9b669d274b",
            "44e5db9f-6e5e-466f-be7e-e9ec49a262d8",
            "1e49b3e8-6dc5-49e0-9d1c-e55285fbe992",
            "d48db7b7-3330-4fde-8171-c528bbccf808",
            "2e472f9a-af51-4835-8629-d36f01b2e913",
            "36b3fd26-a7b7-4e9f-bead-319ac71b4ef1",
            "89b90618-1811-4321-b7fb-72052c8c67d1",
            "384180b4-75cc-4bef-8471-635d8b909f32",
            "7d8a9758-38cd-4722-83ec-5f849936d2bb",
            "a91db67d-9f84-49d7-9269-9979cf7bb2f5",
            "44b14971-d896-4816-b59f-45e0af254c8b",
            "1ed61893-79e9-4354-bd5f-a566a304b167",
            "0bbf1028-f2a1-49df-8e9a-60da236eba3e",
            "3d37249e-72f5-4de8-bcb4-35faa0932447",
            "77cf74cb-6ba9-44f1-996b-f3b8b3684342",
            "acfaf71b-ab26-4c53-a09b-61f8798eeeaa",
            "6871dc7b-11bc-4dc9-8210-40620f0923d6",
            "5a45c504-febc-4ea0-a841-253db7d713ca",
            "1b66476d-e80c-466a-8783-38ab007d5638",
            "ab6d4a82-eea4-42a0-b312-b354d01a6aad",
            "c9b63fc8-5454-4660-ba15-66685496a478",
            "f84c4426-e9c2-4661-82db-bb8e53b1e00f",
            "9131fc8b-6998-4b2a-bf11-5e154ca2e8ff",
            "be93bb21-1dcb-4881-8325-1b21dee8cb00",
            "6b48908c-e449-4813-8f50-efcf57dd4d58",
            "9de49ba6-2e34-47c8-a35b-460ad63e1d6e",
            "71f67033-0486-40d1-8ba1-bc37f9002641",
            "d29d3bfb-f10d-434c-9d37-8fc21cdaf658",
            "8682b596-0acb-4213-844c-a6a36f82ef27",
            "0cbb9e3e-106b-4bf8-a3ba-84e8b6e1a3b3",
            "86c239b4-ee07-4677-ae03-71a6fd6e993c",
            "7871f61f-e251-48cb-ae59-b69f66ea0ac5",
            "895eaee8-a048-4c0a-a7f0-549685c491d2",
            "89e22c78-6a7d-4cea-b51f-d68a4178c5c4",
            "558f0908-0771-4344-801f-5a5744ddae0f",
            "e0e394d5-4ba1-4862-be1b-6940b66a80cd",
            "230c3e1d-8191-4a78-9de8-cc4e064f9027",
            "43b32b3e-7a9e-48ad-8292-11986cb7d329",
            "4fc47ed1-f125-4d33-af17-c38b56a98766",
            "2600975d-b550-45fe-92fa-741692ff6c79",
            "8dfdc074-c49b-4791-96aa-5fc3e082bba4",
            "482b5cc0-0a4e-435b-b81b-8199fb7b1cd1",
            "6d3facc1-0400-4394-b2ab-7d6eaafed797",
            "19cf256e-dc94-437f-82a2-c21890e172b5",
            "b7c51986-e915-487f-81e6-253b54171a3c",
            "7d68e819-3f58-4cfd-879b-7e76924f3bb5",
            "c87a3c2c-f700-43e7-b26f-af56762df6c5",
            "6b21d7aa-c3cd-4d31-bff6-72b2495e0e6d",
            "72873823-5801-42d9-b241-e20d30e89bf9",
            "b5b732d2-7bae-479d-ab45-c315125d96fc",
            "b6520cbb-524d-44cf-9a94-884363c7518d",
            "40559c59-e37e-4790-a535-4715da6ad9cc",
            "4dcd27a9-7d2c-483a-88e7-2b9a10063630",
            "c5c621d1-31d0-4544-b1d8-6398c497cdf8",
            "1e4aafce-d95c-4bb5-aecf-d906931448a9",
            "82b71c5b-ffce-43e8-a115-ad4b6ae3cb5c",
            "efc4846e-9cf3-481b-890e-22db51200c0b",
            "44c64fb3-732f-47c7-84bd-e4f97d7c16c5",
            "d09a0915-6a5a-4b1a-9222-6d2f5ba938e8",
            "ac041637-e3fe-478d-9bb9-7b584f7faea3",
            "1eba2736-ba08-45be-b083-537c1c15e957",
            "f40e04b8-8913-475c-a81e-c9947ba7edc8",
            "95079c99-dcaa-43e2-be2f-093a53b966ba",
            "b6d488c8-efcb-461f-a7a9-834c5036b610",
            "4bcfe364-41c8-4cd7-a7dd-88ff23fbddaa",
            "585be5db-e38b-48f3-9f7b-c9224057c97d",
            "39334353-9c81-42b3-a91c-723a213f7268",
            "9657ad09-2763-4488-aaf9-a946741b42b6",
            "54751d33-e4da-41ec-92bd-2753689cd60e",
            "1665bd7c-c0c2-499b-9bdd-a818ad619d1b",
            "f91648c2-5790-446f-bc35-1f3fa8cb56f9",
            "cf1d3ce0-2ac3-4674-a8dd-67eb069b9ef7",
            "360e58d3-b85d-4640-b47e-b28ed849caf6",
            "50ad9f67-c795-4619-8476-d8b29274e746",
            "1b16cdb0-c1b9-4ece-abb4-8ee73b203ce9",
            "8c3be5c4-f927-40b9-b675-a7f5c9238866",
            "36d726ef-6986-4606-8406-a883f68cbec8",
            "e8e203f3-1363-4cb6-9e4c-1fac68047310",
            "6038bf90-cec7-4778-a74d-07b06e606abc",
            "bae2abba-eb62-4377-a667-4e3e4f227f58",
            "a8520b42-208e-4336-9a06-c3cb392dfbd2",
            "05d9f71a-c9be-424b-bf71-a2534cb67502",
            "2cdd27cd-37d0-4c5a-804d-c6af1fd22058",
            "f3c9d1c9-dba4-4dad-bf44-11383065d295",
            "476e15d9-3a0b-4af1-a60a-e6ff40f20a10",
            "baf08fe2-f748-4ffa-b487-a23691cdc783",
            "6acdd949-be59-49b7-99e1-d2bfc1dd2333",
            "c8c30700-369b-450e-8f21-7d5be6ea24fa",
            "39cf52a5-bceb-4312-b243-897810542927",
            "19d86f6d-9720-408d-8142-821e8525a287",
            "b6fde5d4-5e63-40d5-b442-b3acffff1031",
            "f1da522e-c8ef-4d89-94d7-b627da8843dc",
            "e3d7559e-4d43-4039-b383-277cd00689e6",
            "7fa59d96-5a41-47c7-b79a-53bb13a791d1",
            "a7f633f7-2f9c-4a3d-9a9c-c8b46bacf126",
            "a252f6f6-e19a-4c08-8219-e871a065642c",
            "29f67097-469f-4e24-b464-fdc86c838dcd",
            "bc794126-4618-4408-a8cc-97fcae115530",
            "24f3d920-42ea-4dbc-941e-315d6f92d998",
            "152c9769-ce7e-4163-ac48-34b399cda214",
            "82cce438-e57a-4c3a-9b8d-adb6ae1d5311",
            "1e7c09ea-9245-4921-831d-c929e15e2fd8",
            "671f2359-37c0-485c-a4e9-3e278c0ebe2f",
            "d708c3d1-7320-465c-89d6-e25648da48a5",
            "45d69447-ca58-4e5d-b241-54c2951c7391",
            "50887948-6d41-4876-8a70-9503f00400b0",
            "63489f91-1ca8-438d-b177-e679d40d6432",
            "2e818820-aa8f-4375-824f-324c0944ab08",
            "5bd00907-3e04-4033-bed3-2d037bd876d4",
            "7b62ccb7-2b74-4521-a136-05545765f4f8",
            "06326196-fc9b-4677-9201-2c7f22167c83",
            "114a6ca3-c6fd-49b4-bade-212c440d0d63",
            "fdd171f3-573f-46e1-b2b4-5a450b328f2a",
            "8ba780ad-90b5-4464-b963-f1120fa53b7f",
            "13bcaaca-cd58-498c-8322-5d4d8ed0e073",
            "72c31711-a796-4d3c-84c0-ea9297787dbc",
            "15564223-3e59-46e3-9f9c-097b45df738f",
            "01048102-2c1e-4a53-b7b8-ca597671eded",
            "65e3c5c1-8bc4-453e-8214-c6d64af0ba9a",
            "17b05ed2-caf3-478d-9bad-c4ecc9c2b39a",
            "6176e15b-2a75-4f4d-a272-5323fd20959f",
            "f5aed68b-2470-4c6d-b19b-b4ac5a60a937",
            "868dcc7c-5f52-4505-bfe7-459194b3276d",
            "4f754575-1c55-4dcc-8494-eba3f8c504d0",
            "a16235fe-a57d-4e68-b67c-ff850d8f2dd9",
            "a60d50f0-dac2-47df-b84a-9ca436619c91",
            "b5d8e3e1-69da-4d69-bd9c-ed976aa31cf8",
            "f21561a9-b2b6-435a-8ccc-3fce91b8b882",
            "eaacac8b-92a8-4b65-98c8-a14a6b590490",
            "48c358df-0efe-4408-8bb0-bbdf1e92aa1c",
            "cf28f76f-58dc-4860-9cd5-cc98137a24f2",
            "28f5865a-d19e-4930-a54e-f4d4a573db1d",
            "410db70e-da57-44aa-9a32-7f7e3b2e3093",
            "dec9f704-f5ba-4e57-b384-b23832ff863a",
            "297498e6-bab1-49db-b42a-ef9626bc55d7",
            "bfcca6cd-c68c-4f83-99b5-12c4322f774c",
            "e43d0387-38aa-44d2-8e82-236e0b0e27e8",
            "0152c4ad-b759-434d-9539-bd7a7b110fd4",
            "c1514c94-8418-4604-a27a-3f5f96abd46b",
            "910586d4-afda-4329-a479-7b826fa4c711",
            "0ea6c72b-5200-4561-9dcc-fb4b96ea07ef",
            "97fffbae-b0df-4935-9cd9-4669739bdcee",
            "3b44e65a-aa86-4a08-aaa1-4cf3ccb1043f",
            "2de45e67-d279-4d2b-b447-4f9a2d0a6237",
            "67db2f81-3920-482c-a5de-acb795883e2a",
            "c4acb954-f521-45c3-a781-365431e3da3c",
            "72125e31-86e7-46d0-a577-6714be117507",
            "c85f12c5-e157-4ee9-8363-a5e03caf680a",
            "8f6dce03-e70e-46fe-ad72-53c51c54164f",
            "e1a9033a-e2f8-4430-a2ac-8d82ba7d800d",
            "499df600-6924-4845-9f5f-a8a3d1ff86a9",
            "72522492-aa7f-473b-80ae-19cc797b667f",
            "bbc43066-7047-4200-b124-7fcdf7281d98",
            "25d485cd-6c62-44e5-94e4-91b34ce80b77",
            "393b4e5a-5da0-46c7-a323-cc5f33133101",
            "775a30f4-e964-48a9-bd0b-773773218ff0",
            "8a1bb022-fa44-4c3b-a8ef-85322fa0f371",
            "3567fbf0-8705-4139-b4da-ee93f44b623f",
            "ef82929f-8679-4320-9d75-d9e0d81556b9",
            "ac46ee23-4fec-457c-a881-65ca0a200dc5",
            "ce7916fd-4fb3-4527-92cf-c7057a5c5a87",
            "d6359494-26a5-40ae-801c-7b4e203c41f0",
            "9759171c-b436-4cf3-a7db-679ef5634576",
            "f19049db-2621-43fd-8b3c-176404d7c43d",
            "1e3acb79-695f-4e1d-aa05-76c841d37dba",
            "0aae7167-01ba-472a-8e5f-dd6341e80811",
            "957bb936-3f66-452b-a102-97edf14202fc",
            "8b1b88e5-e00a-43b1-bd84-92b42448d84d",
            "bdfe4b7a-0e78-4901-972a-a136e61edbc1",
            "57bef3ee-10cb-452e-8aab-30166a11ca57",
            "9e45093f-61a0-4d59-84e1-6f799867aa5c",
            "ffc606a4-49f9-47db-b5ad-09f757b2f887",
            "3fffdd5d-65c9-4432-acc3-be0e2eb6fbd7",
            "c7618817-512c-4e7c-8fdd-bf8365e50253",
            "85dd7602-b3c7-4695-9ddf-650e85f9a898",
            "bbc6dc30-0c6b-4848-a739-9f59189c3758",
            "1f3ab7e1-5eae-4df5-a3fd-ceb54c4a523f",
            "8d19cfeb-cf19-408b-91e1-8220dcaaae4f",
            "e8df8523-ff6f-4e50-a813-026ce69dfd36",
            "504a2d08-baff-472f-b64a-e1df8ab49e22",
            "c6434665-0834-43af-9dd4-655150c7fce6",
            "a399c2a0-a375-4d27-99bf-ad4eec2f8350",
            "0153622a-682f-49dd-9ecc-9c811d653a04",
            "c42180b0-0a1c-435a-89cd-e3ae5f50d55c",
            "de488acf-10c0-4335-8a6e-0c90b03e591d",
            "683b2759-fa4b-4e36-8d7a-521b17bcc961",
            "2bd87f12-9fdc-496b-aa93-1a3cc57a9493",
            "10655fd5-a1f3-4c67-ab1d-5cc0884f331d",
            "9937e046-9104-494c-b29d-4d2333d92867",
            "df1d30a6-261f-4313-b8ff-9cf34fddb355",
            "f5a7cc95-5ad9-4a38-81d5-d04b3c3c9122",
            "6512a6af-a76b-41d7-8475-489c09c68e59",
            "11821bf6-7532-4fb4-bf4e-b0b0ec3c1f88",
            "3fc1f601-2b6b-4654-8432-a9c877e38803",
            "bd303a09-1f99-4188-8d16-18f69906e4aa",
            "0a043abc-21aa-4aed-89da-e6b80de4e6c7",
            "104e10ad-86c6-48eb-9489-ee8570ee0de9",
            "a34aab9a-17a5-4983-be16-f1241425b44d",
            "5c9eb158-f89f-44d8-bc0b-fcecd9782571",
            "f37b6057-0f30-42ee-9dff-cb254b7aa6a6",
            "6dd64c6a-b8a9-4987-bbf6-760fe82b2358",
            "378268de-5219-4e17-b436-beed31e0ee2c",
            "ab1c450b-b1b8-4a89-8768-18002704de18",
            "a939a494-c9bd-47cd-8857-a5d98a7177f8",
            "8679c9a8-d73c-4d9f-bddf-934237949ef7",
            "530aaaa8-4dac-406d-8f6b-f0e9c14392ec",
            "c0971d64-fa0a-4804-b7e3-8ffe0c1980c1",
            "b3c9be6c-14f8-44a1-9cd7-d1f9aaab5ad2",
            "6bb9f109-05df-4921-a2f7-a336a2f61e36",
            "a4ed338a-7989-4495-9096-eff25c211ec6",
            "7850dbcd-dd38-4ae8-a59f-85c3ad7d5de5",
            "95df8544-fe20-4d10-b63d-328f110e8d38",
            "577a487f-1e3f-4b8e-aa49-a46c752f1b47",
            "515688c9-cdf7-4896-9256-aaf934fe921f",
            "64cc70af-361f-40ae-939e-b5ebe448a76c",
            "df35b9fd-7ab7-438b-b640-6f3da0b9e35c",
            "26933b67-af7d-4d27-9519-fde84bedc179",
            "86515e01-6458-4f27-ba5d-b466af9447aa",
            "f0f361f0-2c2d-4fc7-9e37-ea7beae05df4",
            "3085802e-2ccc-479b-9da3-f1c9484f75bf",
            "17637271-94e3-4048-96e6-9af4577570aa",
            "c8e8dfd7-cc8a-4888-994b-b693ceecd6ab",
            "58a47894-77a0-4cfd-99aa-16251d06f980",
            "ec2f6f71-5834-4eee-a988-9d8296f0e9a3",
            "9adade77-8fd1-45c1-b4fa-823a336c4a42",
            "a94f64e2-b1f4-4829-89b8-0f25f4f1cac3",
            "cf2aa624-52ec-4f7d-8c48-a75d4008e310",
            "f63ba27f-af15-4679-8474-48e8b70ad778",
            "b981ec30-3124-4fc3-b801-de832a7c8f91",
            "1adc1b47-d621-46f4-884d-2a797087f4bf",
            "5e9b0270-27aa-4c24-ae72-854548cd5f86",
            "1dcdb910-6266-446c-bdb9-8088d07f7fb5",
            "5f3d8d74-38fc-4e8a-a09a-af8764aac84e",
            "b5ba4f24-924f-43dc-91e8-0a8283edd0cb",
            "5f6277a2-8104-4a34-87f2-630cbb448227",
            "a8aadf11-2031-4d3c-8175-8033c1ac7847",
            "909852df-49db-40c5-b6d7-aefcf73a9fb0",
            "3358e549-f1f8-4094-91e0-d50584cddf02",
            "e7e63fe4-e5f2-4b93-b50d-30586b51a402",
            "59bb479b-11bf-4895-9c10-cae5b10cd804",
            "6e469145-bd9d-47c0-88fa-4b4d65eb4de0",
            "559a7b75-5585-40e9-851b-7dc57615d9cb",
            "118b897e-9255-42f7-a986-71c3247dc95e",
            "a0d203e7-fc61-4642-adf4-ac4e6d327e93",
            "fb2933b4-cd34-459f-a810-4739863cd633",
            "071db3a1-8909-4062-b56e-61fe6bd61d9d",
            "e1684663-e481-4420-9fae-1efe9c5dfb36",
            "9d2d0401-0853-4c70-9010-cb7ec3299d78",
            "dba30e37-1915-4f9e-aa91-209fe7fdd4ab",
            "03efccef-fa3b-45b9-b19c-49ee5e0fdda0",
            "3ac09d12-3521-4a2d-b50f-6efc8149cd36",
            "6c0eef7d-a95e-4b26-a590-41ddf2252ebc",
            "ebfe96b8-76de-4708-94aa-f45cc6b64f50",
            "dc9e1e3d-a6d7-49fa-926e-82518f9e5dd7",
            "ee14fdd3-bffb-422d-b4e7-2420c6a853dd",
            "836ec853-cb77-4a1a-8adf-03364ea62fd5",
            "7b1f6784-553b-4b19-8fb9-0aa010b97660",
            "bf0c5148-9b5f-44bc-be48-b5a5775036ba",
            "d2624cd1-9fb0-4f81-becd-7d5c3aee88ba",
            "f0d12f20-216c-49b8-a543-14f92596240e",
            "ce938cd4-7c98-4862-b44e-df636b9ec0b8",
            "eff45bec-48b2-4dae-bcf5-b6bc33f22e87",
            "77bb8dc6-2379-4533-94aa-9eaf98abedc4",
            "040e3e53-4176-49f3-b7f7-928c0a227c66",
            "69462643-d5ca-4246-b949-ed2083c1a15f",
            "056ad962-d809-4278-90b3-b2d1327aee04",
            "cf992311-7f0f-4daa-9993-5b3d37310596",
            "8c58b41e-5091-437f-b6ce-249372d9eef2",
            "cabcbf55-a58f-475c-a9ac-d2510eb92974",
            "14ff14b4-a617-4e03-adbf-5afd14cc64ce",
            "baea9b7e-12da-41d2-8ca6-72364dc0673f",
            "cd59a903-732c-4a76-8d20-3ad157c58eb6",
            "4f24caa3-941a-4449-9ad4-7da0365f7496",
            "8353405f-c2d5-40a9-bfa9-5d5027d4d5bd",
            "a73a06de-dfa9-4be0-9528-a343d9364626",
            "d5f71ef8-9ee0-4f32-835c-0f7795af2dce",
            "6e0636a7-bb58-4d51-adaf-45ea4bad143c",
            "a56d8e1f-600f-4073-8ec9-ea2def769712",
            "12aaeaea-450b-4a32-b21b-c04ee3010d81",
            "afe84abc-398e-49bb-a239-1e7347416877",
            "5e903edc-b9ce-4aba-9ee1-bc6960f836ce",
            "e634ee1a-6cdd-435b-b357-420646387c2f",
            "fec7dd39-00fd-4e4c-8aa0-17ca80e39b64",
            "8f15dd4c-ea36-48f0-adcb-6711b58bcce1",
            "d68110e9-3d03-4302-a04b-9f6a9cb69ff6",
            "88fe2387-ff40-4b0a-83d4-537631e9e7a9",
            "af0c6622-1dd8-4aaa-a8c2-96aa1414ac7f",
            "594e76da-aef5-48e8-bc50-088d021c0c00",
            "8617b60a-3c5c-4819-a486-3b9f0c554de6",
            "f12a66ab-58b5-4420-871b-7f1d581de04b",
            "fdc5985a-659b-41c7-ac50-8d80bc2960f9",
            "901688e0-d77e-4b81-9304-d9efb21c2b45",
            "242143eb-3c06-448f-919b-010000a623d5",
            "4f025d1a-7d77-4d90-9ae6-7c48d66b3808",
            "a39c78b1-d91b-419f-b58a-3e881e8a4649",
            "8d9a54f2-8581-4451-a94f-408234aec668",
            "4562900c-ec12-4835-8b8d-ad1753b18631",
            "6b27f42d-dcbf-42fd-b6a0-69e3ec6fd8b7",
            "9098887a-e5a1-4e1e-92f9-77763e742da5",
            "e107b974-f40a-4fca-bbc7-c402069047a9",
            "afb0453a-7e9d-4cb2-8b39-a65e3cc5d861",
            "366b2530-fae5-4974-85c1-a392357d6031",
            "f08d4d2b-3b06-4f24-bbac-baf9d2821a63",
            "*************-48e5-9949-26af71ba5030",
            "4ed382d4-c253-4870-95de-83e022d2c700",
            "0367d4b4-db39-41b1-9858-c2847bf52b7d",
            "64109380-b9ae-400c-b07f-9b8463f98478",
            "0c68abd6-bc1b-47eb-82b5-b89c1ca933f1"
        ];

        var guids = ids.Select(Guid.Parse).ToArray();

        const string sql = @"
            select b.id as batch_id,
                   b.workspace_id,
                   t.id as task_id,
                   t.model,
                   t.config
            from batching.batch b
            join batching.task t on b.id = t.batch_id
            where b.workspace_id = 'e244b45f-8531-4fef-b922-518b36853efd' and b.id = any(:_guids);
        ";

        await using var npgsql = await dataSource.OpenConnectionAsync(cancellationToken);
        var result = await npgsql.QueryAsync<BatchTaskModel>(sql, new { _guids = guids });

        var batches = result.GroupBy(x => new { x.BatchId, x.WorkspaceId }).Select(x => new
        {
            x.Key,
            Tasks = x.ToList()
        }).ToList();

        var counter = 1;
        foreach (var batch in batches)
        {
            var batchId = batch.Key.BatchId;
            var workspaceId = batch.Key.WorkspaceId;
            var totalTasks = batch.Tasks.Count;
            
            logger.LogInformation("Process batch {BatchId} with {NumTasks}", batchId, totalTasks);

            var tasks = batch.Tasks.Select(item => (RedisValue)JsonSerializer.Serialize(new RedisBatchItem
            {
                WorkspaceId = workspaceId,
                BatchId = batchId,
                TaskId = item.TaskId,
                ModelName = item.Model,
                ModelConfig = item.Config!,
                TotalTasks = totalTasks
            }, JsonDefaults.JsonSerializerOptions))
            .ToArray();

            // await db.StringSetAsync($"batch:{batchId}:remaining_tasks", totalTasks);
            // await db.ListLeftPushAsync("batch:fifo_queue", tasks);
            logger.LogInformation("#{Counter}: Completed {BatchId}", counter, batchId);
            await Task.Delay(100, cancellationToken);
            counter++;
        }
    }
}

public class BatchTaskModel
{
    public Guid BatchId { get; init; }

    public Guid WorkspaceId { get; init; }

    public Guid TaskId { get; init; }

    public required string Model { get; init; }

    public JsonDocument? Config { get; init; }
}
