using System.ComponentModel.DataAnnotations;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Entities;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Postgresql.Builder;

namespace Proptexx.Web.Command.System;

public sealed class CreateCoupon : ICommand
{
    [Required, <PERSON><PERSON><PERSON><PERSON>(4), <PERSON><PERSON>ength(15)]
    public required string Code { get; init; }

    [Required, Range(1, 100)]
    public int Discount { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        if (!context.User.HasRootAccess())
        {
            throw new UnauthorizedAccessException();
        }

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);

        try
        {
            var coupon = new Coupon
            {
                Code = this.Code.ToUpperInvariant(),
                DiscountType = 1,
                DiscountValue = this.Discount,
            };

            await npgsql.InsertAsync(coupon);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }
}