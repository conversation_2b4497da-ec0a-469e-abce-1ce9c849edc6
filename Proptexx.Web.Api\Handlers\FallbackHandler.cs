using Microsoft.Extensions.Options;
using Proptexx.Core.Options;

namespace Proptexx.Web.Api.Handlers;

public sealed class FallbackHandler
{
    private readonly ProptexxOptions _proptexxOptions;

    public FallbackHandler(IOptions<ProptexxOptions> proptexxOptions)
    {
        _proptexxOptions = proptexxOptions.Value;
    }

    public Task InvokeAsync(HttpContext context)
    {
        if (HttpMethods.IsGet(context.Request.Method))
        {
            // Extract the path from the request
            var requestPath = context.Request.Path.Value?.TrimStart('/') ?? string.Empty;

            // Append the path to the DocsUrl
            var redirectUrl = string.IsNullOrEmpty(requestPath)
                ? $"{_proptexxOptions.DocsUrl}/api"
                : $"{_proptexxOptions.DocsUrl}/api/{requestPath}";

            context.Response.Redirect(redirectUrl, false);
            return Task.CompletedTask;
        }

        context.Response.StatusCode = StatusCodes.Status404NotFound;
        return context.Response.WriteAsJsonAsync(new { error = "Invalid endpoint" });
    }
}