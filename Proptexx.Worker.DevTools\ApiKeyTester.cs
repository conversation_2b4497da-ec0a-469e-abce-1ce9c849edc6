using Proptexx.Core;

namespace Proptexx.Worker.DevTools;

public static class ApiKeyTester
{
    public static async Task ExecuteAsync()
    {
        var apiKey1 = ApiKeyService.CreateApiKey(Guid.Parse("8721e2e5-12b3-4ac4-8037-3ad4c2289f93"));
        Console.WriteLine(apiKey1);

        // var apiKey2 = ApiKeyService.CreateApiKey(Guid.Parse("2c072228-d737-4021-a518-e26e51bfb898"));
        // Console.WriteLine(apiKey2);
        // var x = ApiKeyService.ParseApiKey("M2ExMjAzNzEtNzRkOC00ZDA4LWFlYzUtOWMwODZmNDUwY2I2");
        // Console.WriteLine(x);
    }
}