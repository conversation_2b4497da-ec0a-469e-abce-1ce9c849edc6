using System.Text.Json;
using System.Text.Json.Serialization;

namespace Proptexx.Web.Partner.Json;

public sealed class TenantIdConverter : JsonConverter<string?>
{
    public override string? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        return reader.TokenType switch
        {
            JsonTokenType.Number when reader.TryGetInt32(out int intValue) => intValue.ToString(),
            JsonTokenType.String => reader.GetString(),
            _ => throw new JsonException($"Unexpected token type for TenantId: {reader.TokenType}")
        };
    }

    public override void Write(Utf8JsonWriter writer, string? value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value);
    }
}