using Proptexx.Core.DTOs.Outseta;
using Proptexx.Core.Services;
using Proptexx.Web;
using Proptexx.Web.Webhook.Handlers.Outseta;
using Proptexx.Web.Webhook.Middleware;
using Proptexx.Web.Auth;
using Proptexx.Core.Redis;
using Proptexx.AI.Widget;


var builder = WebApplication.CreateBuilder(args);

// Use common Proptexx web configurations
builder.AddProptexxWeb(); 

// Add required dependencies for auth resolvers
builder.Services.AddSingleton<IClientSecretStore, ClientSecretStore>();

// Add widget services (required by WidgetResolver)
builder.Services.AddTransient<WidgetService>();
builder.Services.AddTransient<WidgetStore>();

// Add authentication resolvers for API key handling
builder.Services.AddAuthResolvers();

builder.Services.AddOpenApi();

// Register the custom Outseta handler and dependencies
builder.Services.AddScoped<OutsetaWebhookHandler>();

// Register UserCreationService
var app = builder.Build();

// Add our middlewares
app.UseMiddleware<ApiKeyFromQueryMiddleware>();

// Use common Proptexx web pipeline configurations WITHOUT built-in auth
app.UseProptexxWeb();

// Configure the HTTP request pipeline specific to this project.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi(); 
}

// app.UseHttpsRedirection(); 

// Endpoint for Outseta Webhook - authentication handled in handler
app.MapPost("/webhook/outseta", async (HttpContext context, OutsetaData account, OutsetaWebhookHandler handler) =>
{
    return await handler.HandleOutsetaEvent(context.Request.Query["type"].ToString(), account, context);
})
.WithName("Outseta")
.WithTags("Webhooks.Outseta");
 
app.Run();