﻿using Dapper;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Dashboard;

public sealed class GetRequestCountPerEndpoint : BaseFilter, IQuery
{

    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
      
        var result = await npgsql.QueryAsync<RequestCountModel>(Sql, GetParameters());
        return result;
    }


    public static string Sql => @"
SELECT 
   COALESCE(c.title, 'Unknown') AS WorkspaceName,  
   CASE 
       WHEN v.endpoint LIKE 'Status/%' THEN 'Status/'
       ELSE v.endpoint
   END AS endpoint,
   SUM(v.request_count) AS request_count
FROM telemetry.mv_api_requests_summary v
LEFT JOIN core.workspace c ON v.workspace_id = c.id  
WHERE 
   v.day BETWEEN @startDate::date AND @endDate::date
   AND (NULLIF(@ids, ARRAY[]::UUID[]) IS NULL OR v.workspace_id = ANY(@ids))
   AND c.title IS NOT NULL
   AND v.endpoint ~ '^[a-zA-Z0-9]'
   AND v.endpoint <> 'Favicon.Ico'
GROUP BY c.title, 
   CASE 
       WHEN v.endpoint LIKE 'Status/%' THEN 'Status/'
       ELSE v.endpoint
   END
ORDER BY request_count DESC;
";

}

public sealed class RequestCountModel
{
    public required string WorkspaceName { get; init; }
    public required string Endpoint { get; init; }
    public int RequestCount { get; init; }
}
