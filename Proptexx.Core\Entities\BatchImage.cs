using Proptexx.Core.Services;

namespace Proptexx.Core.Entities;

public sealed class BatchImage : IBatchImageStatus
{
    public BatchImage(BatchImg entry)
    {
        this.Url = entry.ImageUrl;
        this.Type = entry.SceneType;
        this.RoomType = entry.RoomType;
        this.IsEmptyRoom = entry.IsEmptyRoom;
        this.Error = entry.Error;
        this.Status = entry.Status;
    }

    public string Url { get; }

    public string? Type { get; set; }

    public string? RoomType { get; }
    
    public bool? IsEmptyRoom { get; }
    
    public int Status { get; }

    public string? Error { get; }
}