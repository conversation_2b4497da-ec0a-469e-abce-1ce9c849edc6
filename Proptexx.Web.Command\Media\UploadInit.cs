using Azure.Storage.Blobs.Specialized;
using Azure.Storage.Sas;
using Proptexx.Core.Cqrs.Command;

namespace Proptexx.Web.Command.Media;

public sealed class UploadInit : ICommand
{
    public IEnumerable<string> Files { get; init; } = [];
    
    public Task ExecuteAsync(CommandContext context)
    {
        var ttl = DateTime.UtcNow.AddSeconds(30);
        const string accountName = "";
        const string keyValue = "";
        var cred = new Azure.Storage.StorageSharedKeyCredential(accountName, keyValue);

        var baseUri = new Uri("https://ddfinancestorage.blob.core.windows.net");
        var pathUri = new Uri(baseUri, "/files/");

        var list = new List<IDictionary<string, string>>();
        foreach (var origFilename in this.Files)
        {
            if (string.IsNullOrWhiteSpace(origFilename)) continue;

            var newFilename = Guid.NewGuid() + Path.GetExtension(origFilename);
            var fileUri = new Uri(pathUri, newFilename);
            
            var client = new BlockBlobClient(fileUri, cred);
            var sasUri = client.GenerateSasUri(BlobSasPermissions.Write | BlobSasPermissions.Create, ttl);

            list.Add(new Dictionary<string, string>
            {
                ["uploadUri"] = sasUri.AbsoluteUri,
                ["origFilename"] = origFilename,
                ["newFilename"] = newFilename
            });
        }
            
        context.AddData("list", list);
        return Task.CompletedTask;
    }
}