using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations
{
    [Migration(250609001)]
    public class AddWidgetClientsTable : FluentMigrator.Migration
    {
        public override void Up()
        {
            Create.Table("widget_clients").InSchema("core")
                .WithColumn("api_key").AsString().PrimaryKey()
                .WithColumn("domain").AsString().NotNullable()
                .WithColumn("allowed_paths").AsCustom("JSONB").NotNullable()
                .WithColumn("renders_used").AsInt32().NotNullable().WithDefaultValue(0)
                .WithColumn("quota").AsInt32().NotNullable()
                .WithColumn("outseta_id").AsString().Nullable();
        }

        public override void Down()
        {
            Delete.Table("widget_clients");
        }
    }
}