﻿using StackExchange.Redis;
using Npgsql;
using Proptexx.Core.Entities;

namespace Proptexx.Worker.Telemetry;

public static class TelemetryProcessorExtensions
{
    public static IServiceCollection AddTelemetryProcessor(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddHostedService<TelemetryProcessor>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<TelemetryProcessor>>();
            var dataSource = provider.GetRequiredService<NpgsqlDataSource>();
            var redis = provider.GetRequiredService<IConnectionMultiplexer>();
            return new TelemetryProcessor(logger, dataSource, redis);
        });

        return services;
    }
}