using Microsoft.AspNetCore.SignalR;

namespace Proptexx.Web.Socket;

public sealed class ProptexxHub : Hub
{
    public ProptexxHub()
    {
        
    }

    public override Task OnConnectedAsync()
    {
        return base.OnConnectedAsync();
    }

    public override Task OnDisconnectedAsync(Exception? exception)
    {
        return base.OnDisconnectedAsync(exception);
    }

    public async Task<int> ServerQuery(object config)
    {
        Console.WriteLine($"Query from {Context.ConnectionId}");
        return 123;
    }
    
    public async Task ServerCommand(object config)
    {
        // await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        Console.WriteLine($"Query from {Context.ConnectionId}");
    }

    public async Task JoinGroup(string groupName)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        Console.WriteLine($"Connection {Context.ConnectionId} joined group {groupName}");
    }

    public async Task LeaveGroup(string groupName)
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
        Console.WriteLine($"Connection {Context.ConnectionId} left group {groupName}");
    }
}