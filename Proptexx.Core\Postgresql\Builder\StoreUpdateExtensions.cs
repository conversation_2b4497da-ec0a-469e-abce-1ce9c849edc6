using System.Data;
using System.Reflection;
using System.Text;
using Dapper;
using Proptexx.Core.Extensions;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Postgresql.Builder;

public static class StoreUpdateExtensions
{
     public static Task<int> UpdateAsync<T>(this IDbConnection conn, T row, params string[] fieldsToUpdate) where T : class, IDbTable
     {
         return conn.UpdateAsync(new[] {row}, fieldsToUpdate);
     }
     
     public static async Task<int> UpdateAsync<T>(this IDbConnection conn, IEnumerable<T> rows, params string[] fieldsToUpdate) where T : class, IDbTable
     {
         var statement = BuildUpdateStatement<T>(fieldsToUpdate);
         var result = await conn.ExecuteAsync(statement, rows);
         return result;
     }
     
     private static string BuildUpdateStatement<T>(string[]? fieldsToUpdate = null) where T : class, IDbTable
     {
         var tableName = DbRefProvider.Get<T>();

         var separator = ',';
         if (fieldsToUpdate is null || fieldsToUpdate.Length <= 0)
         {
             fieldsToUpdate = typeof(T)
                 .GetProperties()
                 .Where(FilterIgnored)
                 .Select(x => x.Name)
                 .ToArray();
         }

         var strProps = new StringBuilder();
         for (var i = 0; i < fieldsToUpdate.Length; i++)
         {
             if (i == fieldsToUpdate.Length - 1) separator = ' ';
             var column = fieldsToUpdate[i].ToSnakeCase();
             var prop = fieldsToUpdate[i];
             strProps.Append(column + '=' + '@' + prop + separator);
         }

         var result = $"update {tableName} set {strProps} where id=@Id";
         return result;
     }

     private static bool FilterIgnored(MemberInfo p)
     {
         if (System.Attribute.IsDefined(p, typeof(IgnoreOnUpdateAttribute))) return false;
         if (System.Attribute.IsDefined(p, typeof(IgnoreOnInsertOrUpdateAttribute))) return false;
         return true;
     }
}
