using System.Text.Json.Serialization;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Http;
using Proptexx.Core.Services;
using StackExchange.Redis;

namespace Proptexx.AI.Models.CV;

public sealed class PreProcessorForObjects : BaseComputerVisionModel
{
    public PreProcessorForObjects(
        IComputerVisionClient computerVisionClient, 
        IImageAssessmentClient imageAssessmentClient,
        IConnectionMultiplexer connectionMultiplexer, 
        ILoggerFactory loggerFactory)
        : base(
            "PreProcessorForObjects", 
            computerVisionClient,
            imageAssessmentClient,
            connectionMultiplexer, 
            loggerFactory)
    {
    }

    public class Result
    {
        [JsonPropertyName("hasPerson")]
        public bool Has<PERSON>erson { get; set; }

        [JsonPropertyName("hasRealAnimal")]
        public bool HasRealAnimal { get; set; }

        [JsonPropertyName("hasFood")]
        public bool HasFood { get; set; }

        [JsonPropertyName("hasMultipleImages")]
        public bool HasMultipleImages { get; set; }

        [JsonPropertyName("hasCloseUpObject")]
        public bool HasCloseUpObject { get; set; }
    }
}