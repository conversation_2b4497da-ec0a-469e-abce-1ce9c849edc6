using Dapper;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Proptexx.Core.Postgresql.Mappers;

namespace Proptexx.Core.Postgresql;

public static class Extensions
{
    public static IServiceCollection AddPostgresql(this IServiceCollection services, IConfiguration configuration)
    {
        var postgresConnStr = configuration.GetConnectionString("Postgresql")
                              ?? throw new NullReferenceException("postgres connection string");

        return services.AddPostgresql(postgresConnStr);
    }

    public static IServiceCollection AddPostgresql(this IServiceCollection services, string postgresConnStr)
    {
        services.AddNpgsqlDataSource(postgresConnStr, x =>
        {
            x.EnableDynamicJson();
        });

        DefaultTypeMap.MatchNamesWithUnderscores = true;
        SqlMapper.AddTypeHandler(new DictionaryTypeMapper());
        SqlMapper.AddTypeHandler(new JsonDocumentTypeMapper());
        SqlMapper.AddTypeHandler(new JsonNodeTypeMapper());
        SqlMapper.AddTypeHandler(new ProductConfigTypeMapper());
        SqlMapper.AddTypeHandler(new JsonElementTypeMapper());
        SqlMapper.AddTypeHandler(new ServiceSettingsTypeMapper());

        return services;
    }
}