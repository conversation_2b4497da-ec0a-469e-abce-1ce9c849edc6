using System.Text.Json;
using System.Text.Json.Serialization;
using Proptexx.Web.Partner.Json;

namespace Proptexx.Web.Partner.Store.Tenants;

public abstract class GryptechRepTenant : ITenantService
{
    public Task SignupAsync(StoreSignupPayload payload)
    {
        return Task.CompletedTask;
    }

    public Task<StoreSession?> InitSessionAsync(JsonDocument payload)
    {
        var data = payload.RootElement.Deserialize<GryptechRepReceiveRequest>();
        return Task.FromResult<StoreSession?>(null);
    }
}

public sealed class GryptechRepReceiveRequest
{
    [JsonPropertyName("partner_token")]
    public required string PartnerToken { get; init; }

    [JsonPropertyName("user_ref")]
    public required string UserRef { get; init; }

    [JsonPropertyName("module")]
    public required string Module { get; init; }

    [JsonPropertyName("listing_id")]
    public required string ListingId { get; init; }
    
    [JsonPropertyName("tenantId")]
    [JsonConverter(typeof(TenantIdConverter))]
    public string? TenantId { get; init; }
}