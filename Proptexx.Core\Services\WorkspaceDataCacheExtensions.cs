namespace Proptexx.Core.Services;

// public static class WorkspaceDataCacheExtensions
// {
//     public static Task SetGenerativeEndpointConfigAsync(this IWorkspaceDataCache cache, Guid workspaceId, string model, string endpoint, string? watermark)
//     {
//         return cache.SetValueAsync(workspaceId, [
//             ($"{model}:endpoint", endpoint),
//             ($"{model}:watermark", watermark)
//         ]);
//     }
//
//     public static async Task<(string? endpoint, string? watermark)> GetGenerativeEndpointConfigAsync(
//         this IWorkspaceDataCache cache, Guid workspaceId, string model)
//     {
//         var values = await cache.GetValuesAsync(workspaceId, [$"{model}:endpoint", $"{model}:watermark"]);
//         values.TryGetValue($"{model}:endpoint", out var endpoint);
//         values.TryGetValue($"{model}:watermark", out var watermark);
//         return (endpoint, watermark);
//     }
// }