using Microsoft.Extensions.Options;
using Proptexx.Core.Options;
using Proptexx.Web.Auth.Auth;

namespace Proptexx.Web.Auth.Handlers;

public sealed class SignoutHandler
{
    private readonly ILogger<SignoutHandler> _logger;
    private readonly ISessionResolver _sessionResolver;
    private readonly ProptexxOptions _proptexxOptions;

    public SignoutHandler(
        IConfiguration configuration,
        ILogger<SignoutHandler> logger, 
        ISessionResolver sessionResolver,
        IOptions<ProptexxOptions> proptexxOptions)
    {
        _logger = logger;
        _sessionResolver = sessionResolver;
        _proptexxOptions = proptexxOptions.Value;
    }

    public async Task InvokeAsync(HttpContext httpContext)
    {
        try
        {
            if (_proptexxOptions.Cookie is not null && 
                httpContext.Request.Cookies.TryGetValue(_proptexxOptions.Cookie.SessionRefreshToken, out var refreshToken) &&
                !string.IsNullOrWhiteSpace(refreshToken))
            {
                await _sessionResolver.DeleteAsync(refreshToken);

                if (_proptexxOptions.Cookie is not null)
                {
                    var maxAge = TimeSpan.FromMinutes(-1);
                    var cookieOptions = _proptexxOptions.Cookie.GetCookieOptions(maxAge);
                    httpContext.Response.Cookies.Delete(_proptexxOptions.Cookie.SessionRefreshToken, cookieOptions);
                }
            }

            httpContext.Response.StatusCode = StatusCodes.Status204NoContent;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "System error in {TypeName}", nameof(SignoutHandler));
            httpContext.Response.StatusCode = StatusCodes.Status500InternalServerError;
        }
    }
}