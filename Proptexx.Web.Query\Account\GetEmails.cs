using Dapper;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Attributes;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Account;

public sealed class GetEmails : IQuery
{
    [GuidNotEmpty] public Guid? Id { get; init; }

    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var accountId = Id ?? Guid.Parse(context.User.GetCallerId());
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var result = await npgsql.QueryAsync<EmailModel>(EmailModel.Sql, new { _account_id = accountId  });
        return result;
    }
    
    private class EmailModel
    {
        public required Guid Id { get; init; }
        
        public required Guid AccountId { get; init; }
        
        public required string Email { get; init; }
        
        public DateTime? VerifiedAt { get; init; }

        public static string Sql => @"
            select ae.id,
                   ae.account_id,
                   ae.email,
                   ae.verified_at
            from core.account_email ae
            where ae.account_id = :_account_id
        ";
    }
}