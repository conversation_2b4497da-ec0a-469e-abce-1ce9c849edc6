using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Proptexx.Core.Http;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddProptexxHttpClients(
        this IServiceCollection source)
    {
        source.AddHttpClient<IImageAssessmentClient, ImageAssessmentHttpClient>((client, sp) =>
        {
            var loggerFactory = sp.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger<ImageAssessmentHttpClient>();
            client.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36");
            client.DefaultRequestHeaders.Add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8");
            client.DefaultRequestHeaders.Add("Accept-Language", "en-US,en;q=0.9");
            client.DefaultRequestHeaders.Add("Connection", "keep-alive");
            client.DefaultRequestHeaders.Add("Upgrade-Insecure-Requests", "1");
            client.Timeout = TimeSpan.FromSeconds(15);
            return new ImageAssessmentHttpClient(client, logger);
        });
        
        source.AddHttpClient<IImageEnhancementClient, ImageEnhancementHttpClient>((client, sp) =>
        {
            var loggerFactory = sp.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger<ImageEnhancementHttpClient>();
            client.Timeout = TimeSpan.FromMinutes(2);
            return new ImageEnhancementHttpClient(client, logger);
        });

        source.AddHttpClient<IComputerVisionClient, ComputerVisionHttpClient>((client, sp) =>
        {
            var loggerFactory = sp.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger<ComputerVisionHttpClient>();
            client.Timeout = TimeSpan.FromMinutes(1);
            return new ComputerVisionHttpClient(client, logger);
        });
        
        source.AddHttpClient<IGenerativeClient, GenerativeHttpClient>((client, sp) =>
        {
            var loggerFactory = sp.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger<GenerativeHttpClient>();
            client.Timeout = TimeSpan.FromMinutes(5);
            return new GenerativeHttpClient(client, logger);
        });
        
        source.AddHttpClient<ITextualClient, TextualHttpClient>((client, sp) =>
        {
            var loggerFactory = sp.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger<TextualHttpClient>();
            client.Timeout = TimeSpan.FromSeconds(15);
            return new TextualHttpClient(client, logger);
        });

        return source;
    }
}
