namespace Proptexx.Core.DTOs;

/// <summary>
/// Client usage summary DTO for tracking usage statistics
/// </summary>
public record ClientUsageSummary
{
    /// <summary>
    /// The client ID
    /// </summary>
    public Guid ClientId { get; init; }
    
    /// <summary>
    /// Total number of secrets for this client
    /// </summary>
    public long TotalSecrets { get; init; }
    
    /// <summary>
    /// Total usage across all secrets for this client
    /// </summary>
    public long TotalUsage { get; init; }
} 