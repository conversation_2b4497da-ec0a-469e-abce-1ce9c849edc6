using Proptexx.Core.Postgresql;
using Proptexx.Core.Services;
using Stripe;
using Stripe.Checkout;

namespace Proptexx.Web.Payment;

public sealed class StripeCallback
{
    private readonly ILogger<StripeCallback> _logger;
    private readonly string _webhookSecret;

    public StripeCallback(
        ILogger<StripeCallback> logger, 
        IConfiguration configuration)
    {
        _logger = logger;
        _webhookSecret = configuration.GetValue<string>("Stripe:WebhookSecret")
                         ?? throw new NullReferenceException("Missing env var for Stripe:WebhookSecret");
    }
    
    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            _logger.LogInformation("Receive callback request");
            if (!context.Request.Headers.TryGetValue("Stripe-Signature", out var stripeSignatureValues))
            {
                throw new BadHttpRequestException("Missing request header");
            }

            var json = await new StreamReader(context.Request.Body).ReadToEndAsync();
            var stripeSignatureHeader = stripeSignatureValues.ToString();
            var stripeEvent = EventUtility.ConstructEvent(json, stripeSignatureHeader, _webhookSecret);

            // await LogRequestAsync(context.Request, json);

            if (stripeEvent.Type == EventTypes.CheckoutSessionCompleted)
            {
                _logger.LogInformation("Event CheckoutSessionCompleted: Starting..");

                if (stripeEvent.Data.Object is not Session session) throw new NullReferenceException("The CheckoutSession object from Stripe is null");
                if (!session.Metadata.TryGetValue("order_id", out var tmpOrderId) 
                    || !Guid.TryParse(tmpOrderId, out var orderId))
                {
                    throw new ApplicationException("order_id");
                }

                await using var npgsql = await context.OpenNpgsqlAsync();

                var order = await OrderManager.GetOrderAsync(npgsql, orderId);
                var amount = session.AmountTotal is null or <= 0 ? -1 : Convert.ToDecimal(session.AmountTotal / 100);
                await OrderManager.RecordPaymentAsync(npgsql, order.Id, amount, "stripe", session.Id, context.RequestAborted);

                // if (amount < order.TotalPrice)
                // {
                //     throw new Exception("The amount paid does not correspond to the order price");
                // }

                await OrderManager.MarkAsPaidAsync(npgsql, orderId);
                await OrderManager.EnableServicesAsync(npgsql, orderId, context.RequestAborted);
                _logger.LogInformation("Even CheckoutSessionCompleted: Completed");
            }

            context.Response.StatusCode = StatusCodes.Status200OK;
        }
        catch (StripeException e)
        {
            _logger.LogError(e, "StripeException");
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception");
        }

        context.Response.StatusCode = StatusCodes.Status200OK;
    }
}