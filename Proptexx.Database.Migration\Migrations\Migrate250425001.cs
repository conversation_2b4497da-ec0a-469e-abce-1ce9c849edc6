using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations;

[Migration(250425001)]
public class Migrate250425001 : FluentMigrator.Migration
{
    public override void Up()
    {
        Alter.Table("webhook_feedback").InSchema("batching")
            .AddColumn("retry_attempt").AsInt32().NotNullable().WithDefaultValue(0);
    }

    public override void Down()
    {
        Delete.Column("retry_attempt")
            .FromTable("webhook_feedback").InSchema("batching");
    }
}