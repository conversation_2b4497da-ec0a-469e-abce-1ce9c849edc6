using System.Text.Json;
using Npgsql;
using Proptexx.AI;
using Proptexx.Core.Entities;
using Proptexx.Core.Json;
using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Redis;
using StackExchange.Redis;

namespace Proptexx.Web.Api.Helpers;

public static class BatchHelper
{
    public static string ParseModelName(HttpContext context)
    {
        var modelName = context.Request.Path.ToUriComponent().Trim('/', ' ').Trim();

        if (string.IsNullOrWhiteSpace(modelName) || modelName == "/")
        {
            throw new ApplicationException("The requested model is not accessible - no model name provided");
        }
        
        if (!context.RequestServices.HasModel(modelName))
        {
            var similarModels = ModelFactory.GetModelsContaining(modelName.Split('/').LastOrDefault() ?? "").Take(5).ToList();
            var suggestion = similarModels.Any() 
                ? $" Similar models: [{string.Join(", ", similarModels)}]"
                : "";
            
            // Provide specific guidance for common RoomType issues
            var guidance = similarModels.Any()
                ? " If one of the above models is the intended one, please update your request path to match it exactly (including prefix and hyphens)."
                : " Please verify the model name against the list of available models.";
            
            throw new ApplicationException($"The requested model '{modelName}' is not accessible - model not found in registry.{suggestion}{guidance}");
        }

        return modelName;
    }

    internal static BatchTask ParseBatchPayloadForBatchItem(Guid batchId, JsonElement el, int index)
    {
        if (!el.TryGetProperty("model", out var model))
        {
            var msg = $"'model' must be provided at items[{index}]";
            throw new ApplicationException(msg);
        }

        var modelName = model.GetString();
        if (string.IsNullOrWhiteSpace(modelName))
        {
            var msg = $"'model' must be provided at items[{index}]";
            throw new ApplicationException(msg);
        }

        if (!el.TryGetProperty("config", out var config))
        {
            var msg = $"'config' must be provided at items[{index}]";
            throw new ApplicationException(msg);
        }

        return ParsePayloadForBatchItem(batchId, modelName, config);
    }

    internal static BatchTask ParsePayloadForBatchItem(Guid batchId, string modelName, JsonElement config)
    {
        var configElement = JsonComparer.JsonElementToCanonicalForm(config);

        return new BatchTask
        {
            Id = Guid.NewGuid(),
            BatchId = batchId,
            Model = modelName,
            Config = JsonDocument.Parse(configElement.GetRawText()) // TODO: Look at the use of GetRawText
        };
    }

    internal static async Task PersistBatchAsync(ILogger logger, NpgsqlConnection npgsql, Batch batch, IEnumerable<BatchTask> tasks, CancellationToken cancellationToken)
    {
        await using var transaction = await npgsql.BeginTransactionAsync(cancellationToken);

        try
        {
            await npgsql.InsertAsync(batch);
            await npgsql.InsertAsync(tasks);
            await transaction.CommitAsync(cancellationToken);
        }
        catch (Exception e)
        {
            await transaction.RollbackAsync(cancellationToken);
            logger.LogError(e, "Unable to persist transaction in {ClassName}", nameof(BatchHelper));
            throw new Exception("Unable to persist batch");
        }
    }

    internal static string? GetCallbackUrl(HttpContext context)
    {
        const string callbackUrlToken = "x-callback-url";
        if (!context.Request.Headers.TryGetValue(callbackUrlToken, out var value)) return null;
        var callbackUrl = value.ToString();
        return Uri.IsWellFormedUriString(callbackUrl, UriKind.Absolute) ? callbackUrl.Trim() : null;
    }

    public static async Task PushToRedisAsync(IDatabase redis, string workspaceName, Batch batch, IEnumerable<BatchTask> batchTasks, DateTimeOffset? scheduledDateTime)
    {
        var messages = batchTasks.Select(t => 
            (RedisValue)JsonSerializer.Serialize(new RedisTaskMessage
            {
                Task = t,
                WorkspaceId = batch.WorkspaceId,
                CallbackUrl = batch.CallbackUrl
            }, JsonDefaults.CompactOptions))
            .ToArray();

        var nextAllowedProcessingTimeForThisWorkspace = (scheduledDateTime ?? DateTime.UtcNow).ToUnixTimeSeconds();

        var redisBatch = redis.CreateBatch();
        var batchTask = redisBatch.ListLeftPushAsync($"batch:{workspaceName}", messages);
        var workspacesTask = redisBatch.SortedSetAddAsync("batch_workspaces", workspaceName, nextAllowedProcessingTimeForThisWorkspace, SortedSetWhen.GreaterThan);

        var callbackTrackerTask = !string.IsNullOrWhiteSpace(batch.CallbackUrl)
            ? redisBatch.HashSetAsync("batch_callback_tracker", batch.Id.ToString(), messages.Length)
            : Task.CompletedTask;

        redisBatch.Execute();
        await Task.WhenAll(batchTask, callbackTrackerTask, workspacesTask);
    }
}
