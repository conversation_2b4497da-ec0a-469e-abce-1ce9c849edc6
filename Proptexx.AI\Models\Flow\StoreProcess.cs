using System.Text.Json;
using System.Text.Json.Nodes;
using Microsoft.Extensions.Logging;
using Proptexx.Core.AI;
using Proptexx.Core.Extensions;
using Proptexx.Core.Resilience;
using Proptexx.Core.Services;
using StackExchange.Redis;

namespace Proptexx.AI.Models.Flow;

public sealed class StoreProcess : IModel
{
    private readonly IServiceProvider _services;

    private readonly ILogger<StoreProcess> _logger;
    private const int RetryCount = 3;
    private const int DelaySeconds = 1;
    private static readonly List<string> CustomOrder =
    [
        "gen/LightCorrection",
        "gen/SkyReplacement",
        "gen/GrassRepair",
        "flow/VirtualStagingOrRefurnishing"
    ];

    public StoreProcess(IServiceProvider services, ILogger<StoreProcess> logger)
    {
        _services = services;
        _logger = logger;
    }

    public async Task<ModelResponse> InferAsync(ModelContext context)
    {
        if (context.Payload is null)
        {
            throw new ApplicationException("Payload is empty");
        }

        var imageUrl = PayloadService.GetRequiredString(context.Payload, "imageUrl");

        try
        {
            var models = ParseModelProperty(context.Payload);
            foreach (var (modelName, modelConfig) in models)
            {
                var model = _services.GetModel(modelName);
                modelConfig["imageUrl"] = imageUrl;

                var payload = await modelConfig.ToJsonDocumentAsync();
                var response = await PollyRetryExtensions.GetDefaultRetryPolicy(_logger, $"StoreProcess", RetryCount, DelaySeconds).ExecuteAsync(() =>
                {
                    return model.InferAsync(new ModelContext
                    {
                        ItemId = context.ItemId,
                        WorkspaceId = context.WorkspaceId,
                        CancellationToken = context.CancellationToken,
                        Payload = payload
                    });
                });
             
                if (response.Document is null)
                {
                    throw new ApplicationException($"The process failed, because the model returned an empty response");
                }

                imageUrl = PayloadService.GetOptionalString(response.Document, "imageUrl");
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }

        return new ModelResponse
        {
            Document = JsonDocument.Parse($$"""{ "imageUrl": "{{imageUrl}}" }""")
        };
    }

    private static Dictionary<string, JsonNode> ParseModelProperty(JsonDocument contextPayload)
    {
        var modelEl = PayloadService.GetRequiredElement(contextPayload, "models");

        if (modelEl.ValueKind != JsonValueKind.Object)
        {
            throw new ApplicationException("Required property `models` must be an object");
        }

        var obj = modelEl.EnumerateObject();

        if (!obj.Any())
        {
            throw new ApplicationException("Required property `models` must contain least one item");
        }

        var dict = new Dictionary<string, JsonNode>();
        foreach (var item in obj)
        {
            if (item.Value.ValueKind != JsonValueKind.Object)
            {
                throw new ApplicationException($"Item {item.Name} must have a value of type object");
            }

            var node = JsonNode.Parse(item.Value.GetRawText())
                ?? throw new ApplicationException($"Unable to parse config value of {item.Name}");

            // Only rename roomType and architectureStyle to snake_case
            if (node is JsonObject objNode)
            {
                if (objNode.ContainsKey("roomType"))
                {
                    objNode["room_type"] = objNode["roomType"]?.DeepClone();
                    objNode.Remove("roomType");
                }
                if (objNode.ContainsKey("architectureStyle"))
                {
                    objNode["architecture_style"] = objNode["architectureStyle"]?.DeepClone();
                    objNode.Remove("architectureStyle");
                }
            }

            dict.Add(item.Name, node);
        }

        return dict
            .OrderBy(kv => CustomOrder.IndexOf(kv.Key) >= 0 ? CustomOrder.IndexOf(kv.Key) : int.MaxValue)
            .ThenBy(kv => kv.Key)
            .ToDictionary(kv => kv.Key, kv => kv.Value);
    }
}