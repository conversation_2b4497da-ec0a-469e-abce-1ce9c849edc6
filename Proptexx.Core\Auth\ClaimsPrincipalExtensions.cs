using System.Diagnostics.CodeAnalysis;
using System.Security.Claims;

namespace Proptexx.Core.Auth;

public static class ClaimsPrincipalExtensions
{
    private const string SessionIdToken = "$sessionId";
    private const string ClientIdToken = "clientId";
    private const string ClientSecretIdToken = "clientSecretId";
    private const string CallerIdToken = "$accountId";
    private const string ExpToken = "exp";

    // Get ClientId methods
    public static string GetClientIdentifier(this ClaimsPrincipal source)
    {
        var result = TryGetClientId(source);

        if (string.IsNullOrEmpty(result))
        {
            throw new NullReferenceException(ClientIdToken);
        }

        return result;
    }

    public static bool TryGetClientId(this ClaimsPrincipal source, [NotNullWhen(true)] out string? clientId)
    {
        clientId = TryGetClientId(source);
        if (!string.IsNullOrEmpty(clientId)) return true;

        clientId = null;
        return false;
    }

    public static string? TryGetClientId(this ClaimsPrincipal source)
    {
        var result = source.FindFirst(ClientIdToken)?.Value;
        return string.IsNullOrEmpty(result) ? null : result;
    }

    // Get ClientSecretId methods
    public static string GetClientSecretId(this ClaimsPrincipal source)
    {
        var result = TryGetClientSecretId(source);

        if (string.IsNullOrEmpty(result))
        {
            throw new NullReferenceException(ClientSecretIdToken);
        }

        return result;
    }

    public static bool TryGetClientSecretId(this ClaimsPrincipal source, [NotNullWhen(true)] out string? clientSecretId)
    {
        clientSecretId = TryGetClientSecretId(source);
        if (!string.IsNullOrEmpty(clientSecretId)) return true;

        clientSecretId = null;
        return false;
    }

    public static string? TryGetClientSecretId(this ClaimsPrincipal source)
    {
        var result = source.FindFirst(ClientSecretIdToken)?.Value;
        return string.IsNullOrEmpty(result) ? null : result;
    }

    // Get SessionId methods
    public static string GetSessionId(this ClaimsPrincipal source)
    {
        var result = TryGetSessionId(source);

        if (string.IsNullOrEmpty(result))
        {
            throw new NullReferenceException(SessionIdToken);
        }

        return result;
    }

    public static bool TryGetSessionId(this ClaimsPrincipal source, [NotNullWhen(true)] out string? sessionId)
    {
        sessionId = TryGetSessionId(source);
        if (!string.IsNullOrEmpty(sessionId)) return true;

        sessionId = null;
        return false;
    }

    public static string? TryGetSessionId(this ClaimsPrincipal source)
    {
        var result = source.FindFirst(SessionIdToken)?.Value;
        return string.IsNullOrEmpty(result) ? null : result;
    }

    // Get UserId methods
    public static string GetCallerId(this ClaimsPrincipal source)
    {
        var result = TryGetCallerId(source);

        if (string.IsNullOrEmpty(result))
        {
            throw new NullReferenceException(nameof(CallerIdToken));
        }

        return result;
    }

    public static bool TryGetCallerId(this ClaimsPrincipal source, [NotNullWhen(true)] out string? callerId)
    {
        callerId = TryGetCallerId(source);
        if (!string.IsNullOrEmpty(callerId)) return true;

        callerId = null;
        return false;
    }

    public static string? TryGetCallerId(this ClaimsPrincipal source)
    {
        var callerIdentifier = CallerIdToken;
        if (source.Identity is ProptexxIdentity identity)
        {
            callerIdentifier = identity.CallerIdentifier;
        }

        var result = source.FindFirst(callerIdentifier)?.Value;
        return string.IsNullOrEmpty(result) ? null : result;
    }

    // Session ExpiresIn method
    public static TimeSpan? ExpiresIn(this ClaimsPrincipal source)
    {
        var strExp = source.FindFirst(ExpToken);
        if (strExp is null) return null;

        var exp = long.Parse(strExp.Value);
        var uExp = DateTimeOffset.FromUnixTimeSeconds(exp);
        return uExp.Subtract(DateTime.UtcNow);
    }
}