using System.Data;
using Dapper;

namespace Proptexx.Core.Postgresql.Builder;

public static class StoreDeleteExtensions
{
    public static Task<int> DeleteAsync<T>(this IDbConnection conn, string id, bool confirm = false) where T : class, IDbTable
    {
        var tableName = DbRefProvider.Get<T>();
        var param = new {_id = id, _confirm = confirm};

        if (!confirm) return Task.FromResult(0);

        return conn.ExecuteAsync(
            $"delete from {tableName} where @_confirm and id=@_id", param);
    }
}