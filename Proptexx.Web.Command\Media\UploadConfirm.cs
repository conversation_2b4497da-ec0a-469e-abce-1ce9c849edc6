using Proptexx.Core.Cqrs.Command;

namespace Proptexx.Web.Command.Media;

public sealed class UploadConfirm : ICommand
{
    public string DomainName { get; init; }

    public string DomainId { get; init; }

    public IEnumerable<ConfirmFileInfo> Files { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        // var httpContextAccessor = services.GetRequiredService<IHttpContextAccessor>().HttpContext;
        // var callerId = httpContextAccessor?.GetUserId() ?? throw new UnauthorizedAccessException();
        //
        // var files = this.Files
        //     .Aggregate(ImmutableList.Create<File>(), (list, element) =>
        //     {
        //         var (filename, origFilename) = element;
        //         var file = new File(callerId, this.DomainName, this.DomainId, filename, origFilename);
        //         return list.Add(file);
        //     });
        //     
        // await using var conn = services.GetRequiredService<PostgresFactory>().New();
        // var fileStore = new FileStore(conn);
        // var changes = await fileStore.InsertAsync(files);
    }

    public sealed class ConfirmFileInfo(string NewFilename, string OrigFilename);
}