using System.Text.Json.Nodes;
using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Attributes;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Workspace;

public sealed class GetProductPlans : IQuery
{
    [GuidNotEmpty] public Guid? WorkspaceId { get; init; }
    
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var workspaceId = WorkspaceId ?? context.User.GetWorkspaceGuid();
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var result = await npgsql.QueryAsync<ProductPlanModel>(ProductPlanModel.Sql, new { _workspace_id = workspaceId });
        return result;
    }

    public sealed class ProductPlanModel
    {
        public Guid Id { get; init; }
        
        public required string Title { get; init; }
        
        public string? Description { get; init; }

        public decimal PriceAmount { get; init; }

        public required string Currency { get; init; }
        
        public required JsonNode Config { get; init; }

        public static string Sql => @"
            select p.id,
                   p.title,
                   p.description,
                   p.price_amount,
                   p.currency,
                   p.config
            from core.product p
                     join core.product_service_binding psb on p.id = psb.product_id
                     left outer join core.workspace_service_binding tsb on tsb.service_id = psb.service_id and tsb.workspace_id = :_workspace_id
            where p.active = true and (tsb.expires_at is null or tsb.expires_at < current_timestamp)
            order by p.title;
        ";
    }
}