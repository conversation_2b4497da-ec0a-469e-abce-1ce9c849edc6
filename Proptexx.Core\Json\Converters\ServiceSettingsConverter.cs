using Proptexx.Core.Entities;

namespace Proptexx.Core.Json.Converters;

public sealed class ServiceSettingsConverter : DiscriminatorJsonConverter<ServiceSettings>
{
    public ServiceSettingsConverter() : base("type", false, Types)
    {
    }

    private static Dictionary<string, Type> Types => new()
    {
        ["gg-api"] = typeof(GeminiModelApiServiceSettings),
        ["gen-api"] = typeof(GenerativeApiServiceSettings),
        ["batch-api"] = typeof(BatchApiServiceSettings),
        ["flow-api"] = typeof(FlowApiServiceSettings),
        ["seq-api"] = typeof(SequentialApiServiceSettings),
        ["widget-access"] = typeof(WidgetAccessServiceSettings),
        ["ecommerce-widget"] = typeof(EcommerceWidgetServiceSettings),
        ["real-estate-widget"] = typeof(RealEstateWidgetServicesSettings),
    };
}