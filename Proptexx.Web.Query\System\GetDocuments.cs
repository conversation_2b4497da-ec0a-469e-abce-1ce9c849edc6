using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.System;

public sealed class GetDocuments : IQuery
{
    public Guid? WebsiteId { get; init; }

    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        if (!WebsiteId.HasValue) throw new BadHttpRequestException("Required param is not provided");

        context.User.EnsureRootAccess();
        
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var enumerable = await npgsql.QueryAsync<DocumentModel>(DocumentModel.Sql, new { _website_id = this.WebsiteId.Value });
        return enumerable;
    }

    public class DocumentModel
    {
        public Guid Id { get; init; }
        
        public Guid WebsiteId { get; init; }
    
        public required string Slug { get; init; }
    
        public required string Language { get; init; }

        public required string Path { get; init; }

        public required string Title { get; init; }
    
        public string? Description { get; init; }
    
        public DateTime CreatedAt { get; init; }
    
        public int NumRevisions { get; init; }
    
        public static string Sql => @"
        select d.id,
               d.website_id,
               w.slug,
               d.language,
               d.path,
               d.title,
               d.description,
               min(r.created_at) as created_at,
               max(r.created_at) filter (where r.published_at <= current_timestamp ) as updated_at,
               count(r.id) as num_revisions
        from core.document d
        join core.website w on d.website_id = w.id
        left outer join core.document_revision r on d.id = r.document_id
        where d.website_id = :_website_id
        group by d.id, w.id;
    ";
    }
}