using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class OrderLine : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; init; } = Guid.NewGuid();
    
    public Guid OrderId { get; init; }
    
    public Guid WorkspaceId { get; init; }
    
    public Guid ProductId { get; init; }
    
    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public string GetDbRef() => "core.order_line";
}