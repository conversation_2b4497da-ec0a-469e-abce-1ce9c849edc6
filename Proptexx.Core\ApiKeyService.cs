namespace Proptexx.Core;

public static class ApiKeyService
{
    public static string CreateApiKey(Guid clientId, Guid secretId)
    {
        if (clientId.Equals(Guid.Empty)) throw new NullReferenceException($"{nameof(CreateApiKey)}: {nameof(clientId)} is Guid.Empty");
        if (secretId.Equals(Guid.Empty)) throw new NullReferenceException($"{nameof(CreateApiKey)}: {nameof(secretId)} is Guid.Empty");
        return EncodingService.Encode(clientId.ToString(), secretId.ToString());
    }
    
    public static string CreateApiKey(string secretId)
    {
        if (string.IsNullOrWhiteSpace(secretId)) throw new NullReferenceException($"{nameof(CreateApiKey)}: {nameof(secretId)} is Guid.Empty");
        return EncodingService.Encode(secretId);
    }
    
    public static string CreateApiKey(Guid secretId)
    {
        if (secretId.Equals(Guid.Empty)) throw new NullReferenceException($"{nameof(CreateApiKey)}: {nameof(secretId)} is Guid.Empty");
        return CreateApiKey(secretId.ToString());
    }

    public static string ParseApiKey(string apiKey)
    {
        if (apiKey.Length > 60)
        {
            var (_, secretId) = ParseLongApiKey(apiKey);
            return secretId.ToString();
        }

        return ParseSmallApiKey(apiKey).ToString();
    }

    private static Guid ParseSmallApiKey(string apiKey)
    {
        var array = EncodingService.Decode(apiKey);
        if (array.Length != 1)
        {
            throw new ArgumentOutOfRangeException(nameof(apiKey), apiKey, $"{nameof(ParseSmallApiKey)}: Unable to parse ${apiKey}");
        }

        var secretId = Guid.Parse(array[0]);
        return secretId;
    }

    private static (Guid clientId, Guid secretId) ParseLongApiKey(string apiKey)
    {
        var array = EncodingService.Decode(apiKey);
        if (array.Length != 2)
        {
            throw new ArgumentOutOfRangeException(nameof(apiKey), apiKey, $"{nameof(ParseLongApiKey)}: Unable to parse {apiKey}");
        }
    
        var clientId = Guid.Parse(array[0]);
        var secretId = Guid.Parse(array[1]);
        return (clientId, secretId);
    }
}
