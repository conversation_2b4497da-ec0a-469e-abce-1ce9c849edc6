using System.Collections.ObjectModel;

namespace Proptexx.Core.Tasks;

public delegate Task TaskDelegate(TaskContext context);

public class TaskContext
{
    public TaskContext(Dictionary<string, object> state)
    {
        State = state;
    }

    public Dictionary<string, object> State { get; }

    public void Continue(object? result)
    {
        
    }
}

public interface ITaskSet
{
    IEnumerable<TaskDelegate> Tasks { get; }
}

public class GenerativeModelForIs : ITaskSet
{
    public IEnumerable<TaskDelegate> Tasks => new Collection<TaskDelegate>
    {
        FirstStepAsync,
        SecondStepAsync,
        ThirdStepAsync,
        FourtStepAsync
    };

    private static Task FirstStepAsync(TaskContext context)
    {
        throw new NotImplementedException();
    }

    private static Task SecondStepAsync(TaskContext context)
    {
        throw new NotImplementedException();
    }

    private static Task ThirdStepAsync(TaskContext context)
    {
        throw new NotImplementedException();
    }

    private static Task FourtStepAsync(TaskContext context)
    {
        throw new NotImplementedException();
    }
}

public class TaskExecutor
{
    public static async Task ExecuteTasks()
    {
        var tasks = new GenerativeModelForIs();

        // for (var i = 0; i < tasks.Tasks.Count; i++)
        // {
        //     var state = new Dictionary<string, object>();
        //     var context = new TaskContext(state);
        //     await tasks.Tasks[i](context);
        // }
    }
}