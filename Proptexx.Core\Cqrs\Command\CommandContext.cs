using System.Security.Claims;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Proptexx.Core.Json;

namespace Proptexx.Core.Cqrs.Command;

public class CommandContext : ProptexxContext
{
    private CommandContext(IServiceProvider services, ClaimsPrincipal user) : base(services, user)
    {
    }

    internal Dictionary<string, object> Data { get; } = [];

    public required IDictionary<object, object?> Items { get; init; }

    public required CommandDefinition Definition { get; init; }

    public JsonSerializerOptions? JsonSerializerOptions { get; set; }

    internal static async Task<CommandContext> FromHttpAsync(HttpContext context)
    {
        var definition = await JsonSerializer.DeserializeAsync<CommandDefinition>(
            context.Request.Body, JsonDefaults.JsonSerializerOptions, context.RequestAborted)
            ?? throw new NullReferenceException("Unable to deserialize request payload");

        return new CommandContext(context.RequestServices, context.User)
        {
            Items = context.Items,
            Definition = definition,
            CancellationToken = context.RequestAborted
        };
    }

    public void AddData(string key, object value)
    {
        this.Data.Add(key, value);
    }
}