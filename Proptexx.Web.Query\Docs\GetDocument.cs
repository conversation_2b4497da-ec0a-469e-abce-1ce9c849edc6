using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Redis;

namespace Proptexx.Web.Query.Docs;

public sealed class GetDocument : IQuery
{
    private readonly DocumentHashStore _documentStore;

    public GetDocument(DocumentHashStore documentStore)
    {
        _documentStore = documentStore;
    }

    public required string Path { get; init; }

    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var site = "docs";
        var language = "en";
        var path = this.Path;
        var key = DocumentHashStore.ParsePath(site, language, path);
        var content = await _documentStore.GetEntryAsync(key);
        return content;
    }
}