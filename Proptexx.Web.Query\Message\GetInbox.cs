using Dapper;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Messaging;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Message;

public sealed class GetInbox : IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var accountId = Guid.Parse(context.User.GetCallerId());

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);

        const string sql = @"
            select m.id,
                   m.sender_id,
                   (case when a is null then 'System' else concat_ws(' ', a.first_name, a.family_name) end) as sender,
                   m.subject,
                   m.content,
                   mr.status,
                   mr.message_type,
                   m.created_at,
                   mr.processed_at
            from core.message m
            join core.message_recipient mr on m.id = mr.message_id
            left outer join core.account a on m.sender_id = a.id
            where mr.recipient_id = :_account_id
            order by m.created_at desc;
        ";

        var param = new { _account_id = accountId };
        var result = await npgsql.QueryAsync<InboxModel>(sql, param);
        return result;
    }
    
    public class InboxModel
    {
        public required Guid Id { get; init; }
        
        public required Guid? SenderId { get; init; }

        public string? Sender { get; init; }
        
        public required string Subject { get; init; }
        
        public string? Content { get; init; }
        
        public RecipientStatus Status { get; init; }
        
        public MessageType MessageType { get; init; }
        
        public DateTime CreatedAt { get; init; }
        
        public DateTime? ProcessedAt { get; init; }
    }
}
