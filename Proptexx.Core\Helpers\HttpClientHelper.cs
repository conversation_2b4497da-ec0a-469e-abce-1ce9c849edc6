﻿namespace Proptexx.Core.Helpers
{
    public static class HttpClientHelper
    {
        public static async Task<string> PostAsync(this HttpClient client, string endpoint, string payload, CancellationToken cancellationToken)
        {
            using var content = new StringContent(payload, System.Text.Encoding.UTF8, "application/json");
            var response = await client.PostAsync(endpoint, content, cancellationToken);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync(cancellationToken);
        }


        public static async Task<string> DownloadImageAsBase64Async(this HttpClient client, string endpoint, CancellationToken cancellationToken)
        {
            var imageBytes = await client.GetByteArrayAsync(endpoint, cancellationToken);
            return Convert.ToBase64String(imageBytes);
        }
    }
}
