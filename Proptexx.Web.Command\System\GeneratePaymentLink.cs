using System.ComponentModel.DataAnnotations;
using Dapper;
using Proptexx.Core;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Attributes;
using Proptexx.Core.Entities;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Postgresql.Builder;
using Proptexx.Stripe;

namespace Proptexx.Web.Command.System;

public sealed class GeneratePaymentLink : ICommand
{
    [Required, GuidNotEmpty]
    public required Guid OrderId { get; init; }
    
    public async Task ExecuteAsync(CommandContext context)
    {
        context.User.EnsureRootAccess();

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        await using var trx = await npgsql.BeginTransactionAsync(context.CancellationToken);

        try
        {
            var callerId = Guid.Parse(context.User.GetCallerId());
            var configuration = context.GetService<IConfiguration>();

            var order = await npgsql.QueryFirstAsync<OrderModel>(OrderModel.Sql,
                new { _order_id = this.OrderId });

            if (string.IsNullOrWhiteSpace(order.PaymentLink))
            {
                var tmpProductId = order.ProductIds?.Split().First();
                if (!Guid.TryParse(tmpProductId, out var productId)) return;

                var product = await npgsql.QueryFirstAsync<ProductModel>(ProductModel.Sql, new { _product_id = productId });
                var session = await StripeManager.CreateCheckoutSessionAsync(configuration, product, order.Id, context.CancellationToken);
                order.PaymentLink = session.Url;
                await npgsql.UpdateAsync<Order>(order, "PaymentLink");
            }

            await trx.CommitAsync(context.CancellationToken);
            context.AddData("paymentLink", order.PaymentLink);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            await trx.RollbackAsync(context.CancellationToken);
            throw;
        }
    }
}

public class OrderModel : Order
{
    public string? ProductIds { get; init; }

    public static string Sql => @"
        select o.*,
               string_agg(ol.product_id::text, ' ') as product_ids
        from core.order o
        left outer join core.order_line ol on o.id = ol.order_id
        where o.id = :_order_id
        group by o.id;
    ";
}