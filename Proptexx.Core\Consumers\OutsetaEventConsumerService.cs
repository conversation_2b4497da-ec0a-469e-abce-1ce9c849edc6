using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Proptexx.Core.Constants;
using Proptexx.Core.DTOs;
using Proptexx.Core.DTOs.Outseta;
using Proptexx.Core.Helpers;
using Proptexx.Core.Interface;
using StackExchange.Redis;
using Microsoft.Extensions.DependencyInjection;

namespace Proptexx.Core.Consumers;

public class OutsetaEventConsumerService : BackgroundService
{
    private readonly ILogger<OutsetaEventConsumerService> _logger;
    private readonly IDatabase _redisDb;
    private readonly IServiceProvider _serviceProvider;

    // Simple configuration constants
    private const int BatchSize = 10;
    private const int NormalDelayMs = 5000;
    private const int ErrorDelayMs = 10000;

    public OutsetaEventConsumerService(
        ILogger<OutsetaEventConsumerService> logger, 
        IConnectionMultiplexer connectionMultiplexer,
        IServiceProvider serviceProvider)
    {
        _logger = logger;
        _redisDb = connectionMultiplexer.GetDatabase();
        _serviceProvider = serviceProvider;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("OutsetaEventConsumerService started and waiting for messages...");
        
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                // Pop messages from Redis queue
                var messages = await _redisDb.ListRightPopAsync(RedisQueueNames.OutsetaWebhook, BatchSize);
                if (messages is null || messages.Length == 0)
                {
                    await Task.Delay(NormalDelayMs, stoppingToken);
                    continue;
                }

                foreach (var message in messages)
                {
                    if (message.IsNullOrEmpty)
                        continue;

                    try
                    {
                        await ProcessMessageAsync(message!, stoppingToken);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to process message: {Message}", message);
                        
                        // Push to dead letter queue
                        await _redisDb.ListLeftPushAsync(RedisQueueNames.OutsetaWebhookDeadLetter, message!);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in OutsetaEventConsumerService main loop");
                await Task.Delay(ErrorDelayMs, stoppingToken);
            }
        }
    }

    private async Task ProcessMessageAsync(string messageJson, CancellationToken cancellationToken)
    {
        try
        {
            var messageData = JsonSerializer.Deserialize<MessageData>(messageJson);
            if (messageData?.Data == null)
            {
                _logger.LogWarning("Received message with null data: {Message}", messageJson);
                return;
            }

            _logger.LogInformation("Processing Outseta event: {EventType}", messageData.EventType);

            switch (messageData.EventType?.ToLowerInvariant())
            {
                case var eventType when eventType == OutsetaEvents.AccountCreated.ToLowerInvariant():
                    // Cast to OutsetaData first, then call GetAccountData
                    if (messageData.Data is JsonElement jsonElement)
                    {
                        var outsetaData = JsonSerializer.Deserialize<OutsetaData>(jsonElement.GetRawText());
                        var accountData = outsetaData?.GetAccountData();
                        
                        if (accountData != null)
                        {
                            using (var scope = _serviceProvider.CreateScope())
                            {
                                var userCreationService = scope.ServiceProvider.GetRequiredService<IOutsetaUserCreationService>();
                                await userCreationService.ProcessUserRegistrationOutSetaAsync(accountData);
                            }
                            _logger.LogInformation("Successfully processed account creation for: {Email}", accountData.Email);
                        }
                    }
                    break;

                default:
                    _logger.LogDebug("Unhandled event type: {EventType}", messageData.EventType);
                    break;
            }
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Failed to deserialize message: {Message}", messageJson);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process Outseta event: {Message}", messageJson);
            throw;
        }
    }
} 