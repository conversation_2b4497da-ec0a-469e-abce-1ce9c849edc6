using StackExchange.Redis;

namespace Proptexx.Core;

public sealed class RedisOtpService
{
    private readonly IDatabase _database;

    public RedisOtpService(IDatabase database)
    {
        _database = database;
    }

    public Task<bool> SetOtpAsync(string identifier, string otp, TimeSpan? ttl = null)
    {
        var key = $"OTP:{identifier}";
        return _database.StringSetAsync(key, otp, ttl ?? TimeSpan.FromMinutes(120));
    }

    public async Task<string?> GetOtpAsync(string identifier)
    {
        var key = $"OTP:{identifier}";
        var result = await _database.StringGetDeleteAsync(key);
        return !result.HasValue ? null : result.ToString();
    }
}
