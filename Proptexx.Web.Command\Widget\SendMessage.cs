using Proptexx.AI;
using Proptexx.AI.Models.Chat;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Resilience;

namespace Proptexx.Web.Command.Widget;

public sealed class SendMessage : ICommand
{
    public string? Message { get; init; }
    private const int RetryCount = 3;
    private const int DelaySeconds = 1;

    public async Task ExecuteAsync(CommandContext context)
    {
        try
        {
            var model = context.Services.GetModel<ChatSendMessage>();
            var logger = context.GetService<ILogger<SendMessage>>();

            var response = await PollyRetryExtensions.GetDefaultRetryPolicy(logger, "PreProcessorForRoomScene - RunPreprocessForRoomAsync", RetryCount, DelaySeconds).ExecuteAsync(() =>
            {
                return model.InferAsync(this.Message ?? "", context.CancellationToken);
            });

            if (response.Document is null)
            {
                throw new NullReferenceException(nameof(response.Document));
            }

            if (!response.Document.RootElement.TryGetProperty("output", out var output))
            {
                throw new NullReferenceException("output property");
            }

            var responseMessage = output.GetString();

            if (string.IsNullOrWhiteSpace(responseMessage))
            {
                throw new NullReferenceException(nameof(responseMessage));
            }

            context.AddData("responseMessage", responseMessage);
        }
        catch (Exception)
        {
            context.AddData("responseMessage", "I was unable to reply with something meaningful");
        }
    }
}