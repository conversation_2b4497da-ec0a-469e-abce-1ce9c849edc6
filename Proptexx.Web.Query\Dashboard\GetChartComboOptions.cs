﻿using Dapper;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Dashboard;

public sealed class GetChartComboOptions : IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var resultWorkspace = await npgsql.QueryAsync<ComboOptionsDto>(SqlWorkspace);

        return resultWorkspace.DistinctBy(x => x.Id);
    }
    public static string SqlWorkspace => @"
      SELECT 
      w.id, 
      w.name, 
      w.title, 
      concat_ws(' ', first_name, family_name) AS full_name, 
      ae.email,
	  core.generate_api_key(cs.id) as api_key
          FROM core.workspace w 
                INNER JOIN core.account a ON w.founder_id = a.id
                INNER JOIN core.account_email ae ON ae.account_id = a.id
	            INNER JOIN core.client c ON c.workspace_id = w.id
	            INNER JOIN core.client_secret cs ON cs.client_id = c.id
          ORDER BY w.title;
";

}

public class ComboOptionsDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string ApiKey { get; set; } = string.Empty;
}