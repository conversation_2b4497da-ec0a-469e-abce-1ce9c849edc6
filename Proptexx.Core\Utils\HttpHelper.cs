using System.Net.Http.Json;

namespace Proptexx.Core.Utils;

public static class HttpHelper
{
    public static async Task<string> PostReqAsync(string url, Dictionary<string, string>? headers, object? body)
    {
        using var httpClient = new HttpClient();

        // Add headers to the request
        if (headers is not null)
        {
            foreach (var (key, value) in headers)
            {
                httpClient.DefaultRequestHeaders.Add(key, value);
            }
        }

        using var content = JsonContent.Create(body);
        using var response = await httpClient.PostAsync(url, content);
        var responseContent = await response.Content.ReadAsStringAsync();

        if (!response.IsSuccessStatusCode)
        {
            throw new Exception($"Request failed with status code: {response.StatusCode}. Response content: {responseContent}");
        }
        
        return responseContent;
    }
}