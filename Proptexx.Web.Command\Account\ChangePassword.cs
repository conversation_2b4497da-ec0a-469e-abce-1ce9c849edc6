using System.ComponentModel.DataAnnotations;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Stores;

namespace Proptexx.Web.Command.Account;

public sealed class ChangePassword : ICommand
{
    [Required, <PERSON><PERSON><PERSON>th(6)]
    public required string Password { get; init; }
    
    [Required, <PERSON><PERSON>ength(6)]
    public required string ConfirmPassword { get; init; }

    public string? Url { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        var accountId = context.User.GetCallerGuid();

        if (string.IsNullOrWhiteSpace(Password) || !Password.Equals(ConfirmPassword))
        {
            throw new CommandException("The passwords does not seem to match");
        }

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        await npgsql.Account().SetSecret(accountId, this.Password);
    }
}