﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <Authors>Proptexx</Authors>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <LangVersion>12</LangVersion>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\Proptexx.Core\Proptexx.Core.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Google.Cloud.AIPlatform.V1" Version="3.25.0" />
        <PackageReference Include="System.Drawing.Common" Version="9.0.5" />
    </ItemGroup>

</Project>
