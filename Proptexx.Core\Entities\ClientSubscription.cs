using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

/// <summary>
/// Unified Subscription entity for managing all types of subscriptions:
/// - Real Estate Widgets
/// - Ecommerce Widgets  
/// - Enterprise APIs
/// - Other Services
/// </summary>
public class ClientSubscription : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; init; } = Guid.NewGuid();

    [IgnoreOnUpdate] public Guid ClientSecretId { get; init; } // Foreign key to the client (tenant) this subscription belongs to
    public required bool IsDemo { get; set; } = false; // Indicates if this is a demo subscription

    // Subscription Information
    public required string Type { get; init; } // "Period", "OneTime", "PayAsYouGo"
    
    // Unified Usage & Limits System
    public long Quota { get; set; } // Period quota for Monthly/Yearly subscriptions (monthly for Monthly, yearly for Yearly)
    public long TotalUsage { get; set; } // Current period usage for Monthly/Yearly subscriptions
     
    // Status & Lifecycle
    public required string Status { get; init; } // "Active", "Inactive", "Suspended", "Expired"
    
    [IgnoreOnUpdate] 
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; } = null;

    // Optional fields
    public string? Notes { get; init; }
    
    public string? PlanId { get; init; }
    public string? Domain { get; init; }
    public string? AllowedPaths { get; init; }
    
    // Widget-specific fields (migrated from widget_clients table)
    public string? OutsetaAccountId { get; init; }
    
    // Navigation Properties
    public SubscriptionPlan? SubscriptionPlan { get; set; }
    
    public string GetDbRef() => "core.client_subscription";
} 