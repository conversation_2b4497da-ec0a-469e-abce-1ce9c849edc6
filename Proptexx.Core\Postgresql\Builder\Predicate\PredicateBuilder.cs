using System.Linq.Expressions;

namespace Proptexx.Core.Postgresql.Builder.Predicate;

public static class PredicateBuilder
{
    private static readonly PredicateBuilderConfig DefaultConfig = new();

    public static void SetDefaults(Action<PredicateBuilderConfig> configAction)
    {
        configAction(DefaultConfig);
    }

    public static PredicateBuilder<T> Build<T>(Expression<Predicate<T>> expression, PredicateBuilderConfig? config = null)
    {
        var builder = new ExpressionPredicateEngine(config ?? DefaultConfig);
        builder.Visit(expression);

        return new PredicateBuilder<T>(builder);
    }
}

public class PredicateBuilder<T>
{
    internal PredicateBuilder(ExpressionPredicateEngine builder)
    {
        Builder = builder;
    }

    public Type Type { get; } = typeof(T);

    internal ExpressionPredicateEngine Builder { get; }

    public string SqlString 
        => Builder.ToString();

    public Dictionary<string, object?> ParameterValues 
        => Builder.Parameters;

    public override string ToString() 
        => SqlString;
}