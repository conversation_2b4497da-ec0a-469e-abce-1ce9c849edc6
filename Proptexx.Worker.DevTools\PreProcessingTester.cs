using System.Text.Json;
using Dapper;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Npgsql;
using Proptexx.AI;
using Proptexx.AI.Models.CV;
using Proptexx.Core.Http;
using Proptexx.Core.Json;
using Proptexx.Core.Services;

namespace Proptexx.Worker.DevTools;

public static class PreProcessingTester
{
    public static async Task FromDatabase(IServiceProvider services, CancellationToken cancellationToken)
    {
        var dataSource = services.GetRequiredService<NpgsqlDataSource>();
        await using var npgsql = await dataSource.OpenConnectionAsync(cancellationToken);

        var result = await npgsql.QueryAsync<OutputModel>(@"
            select t.id as task_id,
                   b.workspace_id,
                   coalesce(t.config->>'imageUrl', t.config->>'image_url') as image_url,
                   t.model,
                   t.config,
                   t.created_at,
                   r.error_message
            from batching.batch b
            join batching.task t on b.id = t.batch_id
            left outer join batching.result r on t.id = r.task_id
            where t.created_at > current_timestamp - interval '23 hours'
            order by t.created_at
            limit 1
        ");

        var tasks = result.ToList();
        var imageUrls = tasks.Select(x => x.ImageUrl).ToArray();
        await ProcessImages(services, imageUrls, cancellationToken);
    }

    public static async Task FromFiles(IServiceProvider services, CancellationToken cancellationToken)
    {
        string[] imageUrls = [
            "https://pictures.immobilienscout24.de/listings/98f1736c-8c8e-4d11-a314-4083fa58c648-**********.png",
            "https://pictures.immobilienscout24.de/listings/3c347dc4-f950-4919-8128-54b828319d88-**********.jpg",
            "https://pictures.immobilienscout24.de/listings/6179245b-9e34-46eb-bba6-8d614eca82ad-**********.jpg",
            "https://pictures.immobilienscout24.de/listings/307833a2-ddf2-4bd5-beb6-fa8b38225f18-**********.jpg/ORIG/resize/1106x830>/format/jpg",
            "https://pictures.immobilienscout24.de/listings/b8cbbfa9-7c28-46b9-a5b0-0ea40192bec5-**********.png/ORIG/resize/1106x830>/format/jpg",
            "https://pictures.immobilienscout24.de/listings/75c8d454-c6a4-45ab-8f14-ce84fa963687-**********.jpg/ORIG/resize/1106x830>/format/jpg",
            "https://pictures.immobilienscout24.de/listings/083c0064-f226-4709-8fa0-deba6e356dcb-**********.jpeg/ORIG/resize/1106x830>/format/jpg",
            "https://pictures.immobilienscout24.de/listings/860326c2-e893-4ffe-aeeb-e2e693e4f782-1871398487.jpg/ORIG/resize/1106x830%3e/format/jpg",
            "https://pictures.immobilienscout24.de/listings/58bfde4b-8115-4787-a6a1-3b7ef8a78d61-1774830647.jpg/ORIG/resize/1106x830>/format/jpg",
            "https://pictures.immobilienscout24.de/listings/de183bb5-2be1-417d-8118-99aca8a9f415-1871407330.jpg/ORIG/resize/1106x830>/format/jpg",
            "https://pictures.immobilienscout24.de/listings/ea973eed-15aa-4727-82d1-5d828e8f82b4-1871338289.jpg/ORIG/resize/1106x830>/format/jpg",
            "https://pictures.immobilienscout24.de/listings/251aacfa-753f-45cf-8b7d-db9a1636c82a-1871338313.jpg/ORIG/resize/1106x830>/format/jpg",
            "https://pictures.immobilienscout24.de/listings/d0bf063a-860e-4312-8a0c-46bcb79d3a84-1871338262.jpg/ORIG/resize/1106x830>/format/jpg",
            "https://pictures.immobilienscout24.de/listings/180f3579-2974-4321-addb-2cd1dd084151-1871338281.jpg/ORIG/resize/1106x830>/format/jpg",
            "https://pictures.immobilienscout24.de/listings/1d86a03b-a34a-415b-8c19-eecb87cf18d0-1871145790.jpg/ORIG/resize/1106x830>/format/jpg",
            "https://pictures.immobilienscout24.de/listings/baeee611-4472-4aef-9f41-b8c919cef569-1871142270.jpg/ORIG/resize/1106x830>/format/jpg",
            "https://pictures.immobilienscout24.de/listings/7a7d3d5b-621f-49ba-b7d6-326928eb5bbe-1871126929.jpg/ORIG/resize/1106x830>/format/jpg",
            "https://pictures.immobilienscout24.de/listings/3fcd317c-606c-4c6e-ab43-6523508b87b3-1871114523.jpg/ORIG/resize/1106x830>/format/jpg",
            "https://pictures.immobilienscout24.de/listings/91a72621-4d78-4bdb-b5b3-c12e5366178f-1871112869.jpg/ORIG/resize/1106x830>/format/jpg",
            "https://pictures.immobilienscout24.de/listings/b12a041b-d4cf-4a57-8e77-e0b711ca48e0-1871090932.jpg/ORIG/resize/1106x830>/format/jpg"
            // "https://pictures.immobilienscout24.de/listings/d0aafacd-5fd7-422b-964e-970184b69bdd-1849313610.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73",
            // "https://www.carolinaonerealestate.com/propertyphoto/qr?imageid=MjQwMjg0NzR8Q1RBUmZmZg%3D%3DT4Y%2Ba9vnaZ%2Byqqso0xLTQlfWo56ZsnxNULBdxWOkRG3hXoc5liStbIueVU9SyvNhzLLpJxBO%2Fb0H4yaKpUpcuQHCoHHxPB7Qe3ENngm0%2Bxn78%2B79YWcz5S3t4BncO5IfSdGptC4bqXItoggX1uPeBA%3D%3D",
            // "https://pictures.immobilienscout24.de/listings/6f2ffa14-7be9-4ebb-8e9e-e90592921a9d-**********.jpg/ORIG/resize/1106x830%3E/format/jpg",
            // "https://www.remax.ro/themes/frontend/assets/img/2021/proptexx.png",
            // "https://www.carolinaonerealestate.com/propertyphoto/qr?imageid=MjQwMjc3MTh8Q1RBUmZmZg%3D%3D2G%2BZ3NMi9SCNlP0E6CPc4IjKyOZ3DvWluTFGwK42hqj5oey%2FAsrC7uvS%2Fne3khTUG0k0xOEtYpmBy1KlSdCZ8a5M%2BpHcPChXwF8lSIpN%2BywwKLI6QOrE%2BfzyoMMb3FxaOcQ3En95qAzJQ7cPArqBHg%3D%3D",
            // "https://www.carolinaonerealestate.com/propertyphoto/qr?imageid=MjQwMjc3MTh8Q1RBUmZmZg%3D%3D2G%2BZ3NMi9SCNlP0E6CPc4IjKyOZ3DvWluTFGwK42hqhtCi5xcCcPuLSg3DegzaMObcvyvwU4ag9KfzaT0dpqPHhtzGiIeJk6gWEW4fcMwzQ%3D",
            // "https://www.carolinaonerealestate.com/propertyphoto/qr?imageid=MjQwMjc3MTh8Q1RBUmZmZg%3D%3D1FzsD0w2%2FW3AgUKxNPBVrjEMJRW4TVfWIVIcwFfCe16YlG38%2BDCtjTPrhOcPrVBu",
            // "https://pictures.immobilienscout24.de/listings/6f2ffa14-7be9-4ebb-8e9e-e90592921a9d-**********.jpg/ORIG/resize/1106x830%3E/format/jpg",
            // "https://encrypted-tbn0.gstatic.com/shopping?q=tbn:ANd9GcSk3JSzGrREua694THoJ-m_YLZNvqsKT3xvcsXgRNQNO5LFWrGXR6aXMVNnLci2JjKtKqAOeCp4Mwahy4OCnxHxgYPSvIIZRwWO32lr1-JOfr-lR9z7lt0l5A",
            // "http://t3.gstatic.com/licensed-image?q=tbn:ANd9GcTrDQsblTouiK0iyjo3sXJw759sqbMXeh4dD4pk92TaIrEbO4JP2U_s-519UUC_-_u8rjyHS1g-cCTrCAoXkwI",
            // "https://cdn.prod.website-files.com/634053de3cf351fe4c9ff01b/634053de3cf35101179ff53a_AR0000004_1.jpg",
            // "https://carolinaonestage.e-net.com/propertyphoto/qr?imageid=MjQwMDE1NDB8Q1RBUmZmZg%3D%3Dvnp6NVWwfdvCWkCWageW%2F8TLe4bTY9gHOqz6FMg61DfUTLiA3k65E4hiO%2Bcob31%2F76dRAgnOcWhHj3bQoqt0EXwOpIqhdfQrl0zTOEmRDqj0SW%2BEmyYQwn7sle91Eq4TtiFHXx3bwFgdBS5%2B8LhVXw%3D%3D",
            // "https://pictures.immobilienscout24.de/listings/747763db-1f77-4e9f-891b-3453704f81a2-**********.jpg/ORIG/resize/1106x830%3E/format/jpg",
            // "https://pictures.immobilienscout24.de/listings/3a5b06ba-33d0-40a0-b3ec-1186882c7e6d-**********.jpg/ORIG/resize/1106x830>/format/jpg",
            // "https://pictures.immobilienscout24.de/listings/8008f67e-c919-4a33-90ee-d340638cc2e2-**********.jpg/ORIG/resize/1106x830%3E/format/jpg",
            // "https://pictures.immobilienscout24.de/listings/84a215cc-8d14-4f13-ad25-38e05523fa70-1866667085.jpg/ORIG/resize/1106x830%3E/format/jpg",
            // "https://pictures.immobilienscout24.de/listings/d7ea38ca-f42c-45b3-a664-f2cadf008331-1866664485.jpg/ORIG/resize/1106x830%3E/format/jpg",
            // "https://pictures.immobilienscout24.de/listings/66b84760-f7a2-451b-9cd0-406e7d9e1ca4-1866665956.jpg/ORIG/resize/1106x830>/format/jpg",
            // "https://pictures.immobilienscout24.de/listings/64ca9b9a-1971-4afd-9078-f4b915766bbb-**********.jpg/ORIG/resize/1106x830%3E/format/jpg",
            // "https://pictures.immobilienscout24.de/listings/12ce51d8-81b1-4069-96c0-c33d590f4900-**********.jpg/ORIG/resize/1106x830%3E/format/jpg",
            // "https://pictures.immobilienscout24.de/listings/5a328218-2f28-4b55-a45b-517e53b3e2b8-**********.jpg/ORIG/resize/1106x830%3E/format/jpg"
        ];

        await ProcessImages(services, imageUrls, cancellationToken);
    }

    private static async Task ProcessImages(IServiceProvider services, IEnumerable<string> imageUrls, CancellationToken cancellationToken)
    {
        var loggerFactory = services.GetRequiredService<ILoggerFactory>();
        var logger = loggerFactory.CreateLogger(nameof(PreProcessingTester));
        var imageAssessmentClient = services.GetRequiredService<IImageAssessmentClient>();

        var success = 0;
        var errors = 0;
        var i = 0;
        var lockObject = new object();
        var errorMessages = new Dictionary<string, int>();
        var successImages = new List<string>();
        var errorImages = new Dictionary<string, List<string>>();
        var semaphore = new SemaphoreSlim(50);

        foreach (var imageUrl in imageUrls)
        {
            try
            {
                // await Task.Delay(500, cancellationToken);
                var assessment = await imageAssessmentClient.InspectImageAsync(imageUrl, cancellationToken);

                if (assessment.Width <= 300 || assessment.Height <= 300)
                {
                    throw new ApplicationException(
                        "The image size is too small - require width and height greater than 300 pixels");
                }

                Console.WriteLine($"\n {i}:");
                Console.WriteLine(imageUrl);

                var objectsModel = services.GetModel<PreProcessorForObjects>();
                // var objectsResult = await objectsModel.InferAsync(workspaceId, assessment, false);
                // var obj = objectsResult.GetRequiredResult<PreProcessorForObjects.Result>();
                // Console.WriteLine($"{JsonSerializer.Serialize(obj)}");

                // var roomSceneModel = services.GetModel<PreProcessorForRoomScene>();
                // var roomSceneResult = await roomSceneModel.InferAsync(assessment.Base64String, assessment.MimeType, cancellationToken);
                // Console.WriteLine($"{roomSceneResult.Document?.RootElement}\n");

                // if (cvRoomScene.RoomTypes.Count <= 0)
                // {
                //     throw new ApplicationException($"The length of roomTypes is 0");
                // }
                //
                // var roomKeys = cvRoomScene.RoomTypes.Keys.ToList();
                // if (roomKeys.Count <= 0)
                // {
                //     throw new ApplicationException($"The length of roomTypes keys is 0");
                // }
                //
                // if (!IsOrderedDescending(cvRoomScene.RoomTypes))
                // {
                //     throw new ApplicationException($"The order of roomTypes is not in order");
                // }
                //
                // if (cvRoomScene.SceneTypes.Contains("indoor", StringComparer.OrdinalIgnoreCase) 
                //     && !(roomKeys.Contains("living room", StringComparer.OrdinalIgnoreCase) || 
                //     roomKeys.Contains("bedroom", StringComparer.OrdinalIgnoreCase) || 
                //     roomKeys.Contains("kitchen", StringComparer.OrdinalIgnoreCase) || 
                //     roomKeys.Contains("bathroom", StringComparer.OrdinalIgnoreCase)))
                // {
                //     var z = string.Join(',', cvRoomScene.RoomTypes.Keys);
                //     throw new ApplicationException($"The values of roomTypes does not contain one of the four: {z}");
                // }
                //
                // // var model1 = services.GetModel<PreProcessorForObjects>();
                // // var cvResult1 = await model1.InferAsync(assessment.Base64String, assessment.MimeType, cancellationToken);
                // // var cvObjects = cvResult1.GetRequiredResult<PreProcessorForObjects.Result>()
                // //                 ?? throw new ApplicationException("Precheck object model returned empty result");
                //
                // // var x = VirtualStagingOrRefurnishingIs.VerifyAndDetermineRoomStyle(value.Config, cvRoomScene, cvObjects);
                //
                // lock (lockObject)
                // {
                //     successImages.Add(imageUrl);
                //     success++;
                // }
            }
            catch (Exception e)
            {
                lock (lockObject)
                {
                    if (errorMessages.TryGetValue(e.Message, out var count))
                    {
                        errorMessages[e.Message] = count + 1;
                    }
                    else
                    {
                        errorMessages[e.Message] = 1;
                    }

                    // if (!string.IsNullOrWhiteSpace(imageUrl))
                    // {
                    //     if (!errorImages.TryGetValue(e.Message, out var imageUrls1))
                    //     {
                    //         imageUrls = new List<string>();
                    //         errorImages.Add(e.Message, imageUrls1);
                    //     }
                    //
                    //     imageUrls1.Add(imageUrl);
                    // }

                    errors++;
                }
            }
            finally{
                semaphore.Release();

                int iteration;
                lock (lockObject)
                {
                    iteration = ++i;
                }

                // logger.LogInformation("#{iteration}; Success: {success}; Errors: {errors}", iteration, success, errors);
            }
        }

        // var taskEnumerable = imageUrls.Select(async imageUrl =>
        // {
        //     await semaphore.WaitAsync(cancellationToken);
        //
        // });
        //
        // await Task.WhenAll(taskEnumerable);

        // foreach (var em in errorMessages.OrderBy(x => x.Value))
        // {
        //     logger.LogInformation("{errorCount} times: {errorMessage}", em.Value, em.Key);
        // }

        // logger.LogInformation("Faulty image URLs:");
        // foreach (var errorImg in errorImages)
        // {
        //     Console.WriteLine($"\n\n{errorImg.Key}");
        //     foreach (var img in errorImg.Value)
        //     {
        //         Console.WriteLine(img);
        //     }
        // }
        //
        // logger.LogInformation("\n\nSuccessful image URLs:");
        //
        // foreach (var imgUrl in successImages)
        // {
        //     Console.WriteLine(imgUrl);
        // }
    }

    private static bool IsOrderedDescending(Dictionary<string, float> dictionary)
    {
        float? previousValue = null;

        foreach (var kvp in dictionary.Values)
        {
            if (kvp > previousValue)
            {
                // The order is incorrect if the current value is greater than the previous value
                return false;
            }
            previousValue = kvp;
        }

        return true; // Values are in descending order
    }

    public class OutputModel
    {
        public Guid TaskId { get; init; }
    
        public Guid WorkspaceId { get; init; }

        public required string ImageUrl { get; init; }
    
        public required string Model { get; init; }
    
        public required JsonDocument Config { get; init; }
    
        public DateTime CreatedAt { get; init; }
    
        public string? ErrorMessage { get; init; }

        public override string ToString()
        {
            return JsonSerializer.Serialize(this, JsonDefaults.JsonSerializerOptions);
        }
    }
}