using Proptexx.Core.Entities.Outseta;

namespace Proptexx.Web.ClientServices.Interfaces  
{
    public interface IOutsetaApiClient
    {
        Task<PeopleResponse?> GetAllPeopleAsync(int limit = 100, int offset = 0, string? fields = null, string? orderBy = null, Dictionary<string, string>? filters = null);
        Task<Person?> GetPersonAsync(string personUid, string? fields = null);
        Task<Person?> AddPersonAsync(Person person);
        Task<Person?> UpdatePersonAsync(string personUid, Person person);
        Task DeletePersonAsync(string personUid);
        Task SetTemporaryPasswordAsync(string personUid, string temporaryPassword);
        Task InitiatePasswordResetAsync(string email);
        Task SendConfirmationEmailToPrimaryPersonAsync(string accountUid);
        Task SendConfirmationEmailToSpecificPersonAsync(string accountUid, string personUid);
        Task SendConfirmationEmailToAllPeopleAsync(string accountUid);
        Task<Account?> GetAccountAsync(string accountUid, string? fields = null);
        Task<AccountsResponse?> GetAllAccountsAsync(int limit = 100, int offset = 0, string? fields = null, string? orderBy = null, Dictionary<string, string>? filters = null);

        // Added for posting custom activity / webhook data
        Task<HttpResponseMessage> PostCustomActivityAsync(string webhookUrl, object payload);
    }
}