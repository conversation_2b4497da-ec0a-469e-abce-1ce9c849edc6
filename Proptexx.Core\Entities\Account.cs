﻿using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class Account : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; } = Guid.NewGuid();

    public required string FirstName { get; set; }

    public required string FamilyName { get; set; }

    public DateTime? DateOfBirth { get; set; }

    public int? Gender { get; set; }

    public string? Language { get; set; }
    
    public Guid? WorkspaceId { get; set; }

    public bool IsRoot { get; set; }

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public DateTime? UpdatedAt { get; } = null!;

    public DateTime? CancelledAt { get; private set; }

    public string? CancellationReason { get; private set; }
    
    public string GetDbRef() => "core.account";

    public string FullName() => $"{this.FirstName} {this.FamilyName}";

    public Account SetCancellation(DateTime cancelledAt, string cancellationReason)
    {
        this.CancelledAt = cancelledAt;
        this.CancellationReason = cancellationReason;
        return this;
    }

    public Account RevokeCancellation()
    {
        this.CancelledAt = null;
        this.CancellationReason = null;
        return this;
    }
}
