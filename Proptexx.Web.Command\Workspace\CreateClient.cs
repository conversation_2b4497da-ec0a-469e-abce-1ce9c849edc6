using System.ComponentModel.DataAnnotations;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Stores;

namespace Proptexx.Web.Command.Workspace;

public sealed class CreateClient : ICommand
{
    [Required]
    public required string Name { get; init; }
    
    [Required]
    public required string[] Services { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        var workspaceId = context.User.GetWorkspaceGuid();
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        await using var transaction = await npgsql.BeginTransactionAsync(context.CancellationToken);

        try
        {
            const string scopes = "api";
            var clientId = await npgsql.Client().Create(workspaceId, this.Name, scopes);
            var clientSecret = await npgsql.Client().AddSecret(clientId);

            await transaction.CommitAsync(context.CancellationToken);

            var apiKey = ApiKeyService.CreateApiKey(clientSecret.Id);
            context.AddData("secret", apiKey);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            await transaction.RollbackAsync(context.CancellationToken);
        }
    }
}
