### Authenticate
POST https://auth.dev.local/_auth
Content-Type: application/json
Authorization: <PERSON><PERSON><PERSON><PERSON> ZTQwYzhmOTEtZjQ2Ni00YzFkLTlkYzctYmJmOGY2MjAyMjE4

{}

> {% client.global.set("accessToken", response.body.$accessToken) %}

### Call Batch API with two model requests, using the accessToken from previous request
POST https://api.dev.local/batch
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "items": [
    {
      "model": "flow/virtual-staging-or-refurnishing-for-portals",
      "config": {
        "image_url": "https://ansainteriors.com/wp-content/uploads/2022/03/interior-designer-in-delhi.jpg"
      }
    }
  ]
}

> {% client.global.set("batchId", response.body.id) %}


### Call Status API with the response id from previous call to get status of batch. Also using the accessToken from first request

#GET https://api.proptexx.com/status/{{batchId}}
#Content-Type: application/json
#Authorization: Bearer {{accessToken}}