
### Authenticate
POST https://auth.proptexx.com/_auth
Content-Type: application/json
Authorization: <PERSON><PERSON><PERSON><PERSON> [Your token here]

{}

> {% client.global.set("accessToken", response.body.$accessToken) %}

### Sync images of widget url
POST https://api.proptexx.com/widget/sync
Content-Type: application/json
Authorization: Bearer {{ accessToken }}

{
  "url": "https://proptexx.com/1",
  "images": [
    "https://www.domain.com/images/image1.jpg",
    "https://www.domain.com/images/image2.jpg"
  ]
}
