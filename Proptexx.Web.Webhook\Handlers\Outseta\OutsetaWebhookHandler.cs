﻿using Microsoft.AspNetCore.Http;
using Proptexx.Core.Constants;
using Proptexx.Core.DTOs;
using Proptexx.Core.DTOs.Outseta;
using Proptexx.Core;
using Proptexx.Web.Auth.Auth;
using Proptexx.Core.Options;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.JsonWebTokens;
using System.Security.Claims;
using StackExchange.Redis;
using System.Text.Json;
using Proptexx.Core.Auth;
using Dapper;
using Npgsql;

namespace Proptexx.Web.Webhook.Handlers.Outseta;

public class OutsetaWebhookHandler
{
    // Constants for better maintainability
    private const string WorkspaceIdClaimType = "workspaceId";
    private const string BearerScheme = "bearer";
    private const string ApiKeyScheme = "apiKey";
    private const string UndefinedValue = "undefined";
    private const string NullValue = "null";
    private const string SessionIdClaimType = "$sessionId";
    
    // SQL query as constant for better performance and maintainability
    private const string AdminValidationQuery = @"
        SELECT COUNT(*) 
        FROM core.workspace w  
        JOIN core.account a ON w.id = a.workspace_id 
        WHERE w.id = @workspace_id
          AND a.is_root = true
          AND a.cancelled_at IS NULL";

    private readonly ILogger<OutsetaWebhookHandler> _logger;
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly IClientResolver _clientResolver;
    private readonly ProptexxOptions _proptexxOptions;
    private readonly NpgsqlDataSource _dataSource;

    public OutsetaWebhookHandler(
        ILogger<OutsetaWebhookHandler> logger, 
        IConnectionMultiplexer connectionMultiplexer,
        IClientResolver clientResolver,
        IOptions<ProptexxOptions> proptexxOptions,
        NpgsqlDataSource dataSource)
    {
        _logger = logger;
        _connectionMultiplexer = connectionMultiplexer;
        _clientResolver = clientResolver;
        _proptexxOptions = proptexxOptions.Value;
        _dataSource = dataSource;
    }

    public async Task<IResult> HandleOutsetaEvent(string type, OutsetaData payload, HttpContext httpContext)
    {
        // Optimized logging with structured data
        LogWebhookRequest(type, httpContext);

        // Authenticate user first
        if (!await TryAuthenticateAsync(httpContext))
        {
            _logger.LogWarning("Authentication failed for Outseta webhook");
            return Results.Unauthorized();
        }

        // Extract workspace ID and validate admin access
        var workspaceId = ExtractWorkspaceId(httpContext);
        if (!await ValidateAdminAccessFromDatabase(workspaceId))
        {
            return Results.Problem(
                title: "Admin Access Required",
                detail: "Webhook access requires administrator privileges",
                statusCode: 403
            );
        }
        
        var validatedClientId = GetClientId(httpContext);
        _logger.LogInformation("Authentication successful for admin client: {ClientId}", validatedClientId);

        try
        {
            await PublishEventToRedis(type, payload, validatedClientId);
            
            return CreateSuccessResponse(type, validatedClientId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish Outseta event: {EventType} by admin client: {ClientId}", type, validatedClientId);
            return Results.Problem(
                title: "Webhook Processing Error",
                detail: "Failed to process webhook request",
                statusCode: 500
            );
        }
    }

    /// <summary>
    /// Logs webhook request details in an optimized manner
    /// </summary>
    private void LogWebhookRequest(string type, HttpContext httpContext)
    {
        var authHeader = httpContext.Request.Headers.Authorization.FirstOrDefault() ?? "Not present";
        var queryParams = httpContext.Request.Query.Count > 0 
            ? string.Join(", ", httpContext.Request.Query.Select(q => $"{q.Key}={q.Value}"))
            : "None";

        _logger.LogInformation("Processing Outseta webhook - Type: {EventType}, Auth: {AuthHeader}, Params: {QueryParams}", 
            type, authHeader, queryParams);
    }

    /// <summary>
    /// Extracts workspace ID from user claims
    /// </summary>
    private static string? ExtractWorkspaceId(HttpContext httpContext)
    {
        return httpContext.User.Claims.FirstOrDefault(c => c.Type == WorkspaceIdClaimType)?.Value;
    }

    /// <summary>
    /// Publishes event to Redis queue
    /// </summary>
    private async Task PublishEventToRedis(string type, OutsetaData payload, string? validatedClientId)
    {
        var database = _connectionMultiplexer.GetDatabase();
        
        var messageData = new MessageData
        {
            EventType = type,
            Data = payload
        };

        var jsonMessage = JsonSerializer.Serialize(messageData);
        await database.ListLeftPushAsync(RedisQueueNames.OutsetaWebhook, jsonMessage);
        
        _logger.LogInformation("Outseta event published to Redis queue: {EventType} by admin client: {ClientId}", type, validatedClientId);
    }

    /// <summary>
    /// Creates standardized success response
    /// </summary>
    private static IResult CreateSuccessResponse(string type, string? validatedClientId)
    {
        return Results.Ok(new
        {
            EventType = type,
            Status = "Success",
            Message = "Event has been queued for processing. Check logs for actual processing results.",
            ClientId = validatedClientId
        });
    }

    /// <summary>
    /// Attempts to authenticate the request using API key
    /// </summary>
    private async Task<bool> TryAuthenticateAsync(HttpContext httpContext)
    {
        try
        {
            var identity = await ResolveIdentityAsync(httpContext);
            httpContext.User = new ClaimsPrincipal(identity);
            _logger.LogInformation("User authenticated successfully");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Authentication failed");
            return false;
        }
    }

    /// <summary>
    /// Gets the client ID from the authenticated user
    /// </summary>
    private static string? GetClientId(HttpContext httpContext)
    {
        httpContext.User.TryGetClientId(out var clientId);
        return clientId;
    }

    /// <summary>
    /// Validates admin access by checking if client belongs to workspace with system admin accounts
    /// For webhook access, we check if the client's workspace has any system administrators (is_root = true)
    /// This follows the pattern used in Command/Query services where system admins can access admin-only endpoints
    /// </summary>
    private async Task<bool> ValidateAdminAccessFromDatabase(string? workspaceId)
    {
        if (string.IsNullOrEmpty(workspaceId) || !Guid.TryParse(workspaceId, out var workspaceIdGuid))
        {
            _logger.LogWarning("Invalid workspace ID: {WorkspaceId}", workspaceId);
            return false;
        }

        try
        {
            await using var connection = await _dataSource.OpenConnectionAsync();
            
            var adminCount = await connection.QueryFirstOrDefaultAsync<int>(
                AdminValidationQuery, 
                new { workspace_id = workspaceIdGuid });
            
            var hasAdminAccess = adminCount > 0;
            _logger.LogInformation("Database admin check for workspace {WorkspaceId}: {HasAdminAccess} (found {AdminCount} system admins)", 
                workspaceId, hasAdminAccess, adminCount);
            
            return hasAdminAccess;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to validate admin access from database for workspace: {WorkspaceId}", workspaceId);
            return false;
        }
    }

    /// <summary>
    /// Resolves identity from HTTP context for webhook authentication
    /// Optimized version of AuthHandler.ResolveIdentityAsync with webhook-specific logic
    /// </summary>
    private async Task<ProptexxIdentity> ResolveIdentityAsync(HttpContext httpContext)
    {
        // Check for bearer token first (early return pattern)
        var bearerIdentity = TryExtractBearerToken(httpContext);
        if (bearerIdentity != null)
        {
            return bearerIdentity;
        }

        var (apiKey, refreshToken, issueCookie) = ExtractAuthenticationTokens(httpContext);

        if (IsInvalidApiKey(apiKey))
        {
            throw new UnauthorizedAccessException("No valid API key found");
        }

        var clientIdentifier = ApiKeyService.ParseApiKey(apiKey!);

        if (!string.IsNullOrWhiteSpace(refreshToken))
        {
            // Session resolver logic would be here, but for webhooks we typically don't use sessions
            throw new UnauthorizedAccessException("Session-based authentication not supported for webhooks");
        }

        var clientResult = await _clientResolver.ResolveAsync(clientIdentifier, null)
                           ?? throw new UnauthorizedAccessException($"Invalid API key: {clientIdentifier}");

        var claims = BuildClaims(clientResult, httpContext.TraceIdentifier, clientIdentifier);
        
        var identityClaims = claims.Select(x => new Claim(x.Key, x.Value));
        return new ProptexxIdentity(identityClaims, _proptexxOptions.CallerIdentifier, clientResult.IssueCookie);
    }

    /// <summary>
    /// Attempts to extract bearer token and return identity if found
    /// </summary>
    private ProptexxIdentity? TryExtractBearerToken(HttpContext httpContext)
    {
        bool? issueCookie = null;

        // Check for refresh token in cookies
        if (_proptexxOptions.Cookie is not null
            && httpContext.Request.Cookies.TryGetValue(_proptexxOptions.Cookie.SessionRefreshToken, out _))
        {
            issueCookie = true;
        }

        var headers = httpContext.Request.Headers;
        if (headers.TryGetValue("Authorization", out var authorization))
        {
            var authParts = authorization.ToString().Split(' ', 2);
            if (authParts.Length == 2)
            {
                var scheme = authParts[0];
                var value = authParts[1];

                if (scheme.Equals(BearerScheme, StringComparison.OrdinalIgnoreCase))
                {
                    return ProptexxIdentity.FromBearer(_proptexxOptions, value, issueCookie);
                }
            }
        }

        return null;
    }

    /// <summary>
    /// Extracts authentication tokens from HTTP context (excluding bearer tokens)
    /// </summary>
    private (string? apiKey, string? refreshToken, bool? issueCookie) ExtractAuthenticationTokens(HttpContext httpContext)
    {
        string? apiKey = null;
        string? refreshToken = null;
        bool? issueCookie = null;

        // Check for refresh token in cookies
        if (_proptexxOptions.Cookie is not null
            && httpContext.Request.Cookies.TryGetValue(_proptexxOptions.Cookie.SessionRefreshToken, out refreshToken))
        {
            issueCookie = true;
        }

        var headers = httpContext.Request.Headers;

        // Check Authorization header for API key
        if (headers.TryGetValue("Authorization", out var authorization))
        {
            var authParts = authorization.ToString().Split(' ', 2);
            if (authParts.Length == 2)
            {
                var scheme = authParts[0];
                var value = authParts[1];

                if (scheme.Equals(ApiKeyScheme, StringComparison.OrdinalIgnoreCase))
                {
                    apiKey = value;
                }
            }
        }
        // Check direct apiKey header
        else if (headers.TryGetValue(ApiKeyScheme, out var apiToken))
        {
            apiKey = apiToken.ToString();
        }

        return (apiKey, refreshToken, issueCookie);
    }

    /// <summary>
    /// Checks if API key is invalid or empty
    /// </summary>
    private static bool IsInvalidApiKey(string? apiKey)
    {
        return string.IsNullOrWhiteSpace(apiKey) 
               || apiKey.Equals(UndefinedValue, StringComparison.OrdinalIgnoreCase)
               || apiKey.Equals(NullValue, StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Builds claims dictionary for the authenticated client
    /// </summary>
    private Dictionary<string, string> BuildClaims(
        dynamic clientResult, 
        string traceIdentifier, 
        string clientIdentifier)
    {
        var claims = new Dictionary<string, string>(clientResult.AdditionalClaims)
        {
            [JwtRegisteredClaimNames.Jti] = traceIdentifier,
            [_proptexxOptions.ClientIdentifier] = clientIdentifier,
            [SessionIdClaimType] = Guid.NewGuid().ToString()
        };

        return claims;
    }
 
}
 