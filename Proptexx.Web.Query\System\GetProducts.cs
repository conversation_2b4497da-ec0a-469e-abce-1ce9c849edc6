using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.System;

public sealed class GetProducts : IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        context.User.EnsureRootAccess();
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var result = await npgsql.QueryAsync<ProductModel>(ProductModel.Sql, new { });
        return result;
    }

    private sealed class ProductModel
    {
        public Guid Id { get; init; }
        
        public required string Title { get; init; }
        
        public string? Description { get; init; }

        public decimal PriceAmount { get; init; }
        
        public long NumServices { get; init; }

        public long NumOrders { get; init; }
        
        public long NumSales { get; init; }

        public decimal TotalRevenue => PriceAmount * NumSales;

        public static string Sql => @"
            select p.id,
                   p.title,
                   p.description,
                   p.price_amount,
                   count(distinct psb.service_id) as num_services,
                   count(distinct ol.workspace_id) as num_orders,
                   count(distinct ol.workspace_id) filter (where o.paid_at is not null) as num_sales
            from core.product p
            left outer join core.product_service_binding psb on p.id = psb.product_id
            left outer join core.order_line ol on p.id = ol.product_id
            left outer join core.order o on ol.order_id = o.id
            group by p.id, p.title
            order by p.title;
        ";
    }
}