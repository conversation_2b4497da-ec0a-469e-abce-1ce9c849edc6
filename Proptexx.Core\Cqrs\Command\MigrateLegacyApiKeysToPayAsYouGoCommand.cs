using Proptexx.Core.Entities;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Services;
using Microsoft.Extensions.Logging;
using Dapper;
using Npgsql;
using System.Data;
using Proptexx.Core.Interface;


namespace Proptexx.Core.Cqrs.Command;

/// <summary>
/// Command to migrate legacy API keys to PayAsYouGo subscriptions
/// </summary>
public class MigrateLegacyApiKeysToPayAsYouGoCommand
{
    public bool DryRun { get; set; } = true; // Default to dry run for safety
    public int? MaxApiKeys { get; set; } = null; // Limit number of API keys to process
    public string? ClientNamePrefix { get; set; } = "Migrated";
    public string? ContactName { get; set; } = "System Migration";
    public string? ContactEmail { get; set; } = "<EMAIL>";
}

/// <summary>
/// Result of the migration operation
/// </summary>
public class MigrationResult
{
    public int TotalApiKeysFound { get; set; }
    public int SubscriptionsCreated { get; set; }
    public int SubscriptionsSkipped { get; set; }
    public int Errors { get; set; }
    public List<string> ErrorMessages { get; set; } = new();
    public List<string> CreatedApiKeys { get; set; } = new();
    public List<string> SkippedApiKeys { get; set; } = new();
    public bool IsDryRun { get; set; }
}

/// <summary>
/// Handler for migrating legacy API keys to PayAsYouGo subscriptions
/// </summary>
public class MigrateLegacyApiKeysToPayAsYouGoCommandHandler
{
    private readonly ISubscriptionUsageService _usageService;
    private readonly ISubscriptionService _subscriptionService;
    private readonly NpgsqlDataSource _dataSource;
    private readonly ILogger<MigrateLegacyApiKeysToPayAsYouGoCommandHandler> _logger;

    public MigrateLegacyApiKeysToPayAsYouGoCommandHandler(
        ISubscriptionUsageService usageService,
        ISubscriptionService subscriptionService,
        NpgsqlDataSource dataSource,
        ILogger<MigrateLegacyApiKeysToPayAsYouGoCommandHandler> logger)
    {
        _usageService = usageService;
        _subscriptionService = subscriptionService;
        _dataSource = dataSource;
        _logger = logger;
    }

    public async Task<MigrationResult> Handle(MigrateLegacyApiKeysToPayAsYouGoCommand request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting migration of legacy API keys to PayAsYouGo subscriptions. Dry run: {DryRun}", request.DryRun);

            var result = new MigrationResult { IsDryRun = request.DryRun };

            // Get all API keys from WidgetClient table using Dapper
            using var connection = _dataSource.CreateConnection();
            var widgetClientApiKeys = (await connection.QueryAsync<string>(
                "SELECT api_key FROM widget_clients")).ToList();

            // Get all API keys from AccountData table (where DataKey = 'api_key') using Dapper
            var accountDataApiKeys = (await connection.QueryAsync<string>(
                "SELECT data_value FROM account_data WHERE data_key = 'api_key' AND data_value IS NOT NULL AND data_value <> ''")).ToList();

            // Combine and deduplicate API keys
            var allApiKeys = widgetClientApiKeys.Union(accountDataApiKeys).ToList();
            result.TotalApiKeysFound = allApiKeys.Count;

            _logger.LogInformation("Found {Count} unique API keys to process", allApiKeys.Count);

            // Limit the number of API keys to process if specified
            if (request.MaxApiKeys.HasValue && allApiKeys.Count > request.MaxApiKeys.Value)
            {
                allApiKeys = allApiKeys.Take(request.MaxApiKeys.Value).ToList();
                _logger.LogInformation("Limited to {Count} API keys for processing", allApiKeys.Count);
            }

            foreach (var apiKey in allApiKeys)
            {
                try
                {
                    // Check if subscription already exists
                    var existingSubscription = await _subscriptionService.GetByApiKeyAsync(apiKey, cancellationToken);
                    if (existingSubscription != null)
                    {
                        result.SubscriptionsSkipped++;
                        result.SkippedApiKeys.Add(apiKey);
                        _logger.LogDebug("Subscription already exists for API key: {ApiKey}, skipping", apiKey);
                        continue;
                    }

                    if (request.DryRun)
                    {
                        result.SubscriptionsSkipped++;
                        result.SkippedApiKeys.Add(apiKey);
                        _logger.LogDebug("DRY RUN: Would create PayAsYouGo subscription for API key: {ApiKey}", apiKey);
                        continue;
                    }

                    // Create PayAsYouGo subscription
                    var clientName = $"{request.ClientNamePrefix} Client ({apiKey.Substring(0, Math.Min(8, apiKey.Length))}...)";
                    

                    // Use SubscriptionService for creation, not UsageService
                    var subscription = await _subscriptionService.CreatePayAsYouGoSubscriptionAsync(
                        apiKey,
                        clientName,
                        request.ContactName,
                        request.ContactEmail,
                        new List<string> { "migrated" },
                        cancellationToken);

                    if (subscription != null)
                    {
                        result.SubscriptionsCreated++;
                        result.CreatedApiKeys.Add(apiKey);
                        _logger.LogInformation("Successfully created PayAsYouGo subscription {SubscriptionId} for API key {ApiKey}", 
                            subscription.Id, apiKey);
                    }
                    else
                    {
                        result.Errors++;
                        var errorMsg = $"Failed to create subscription for API key: {apiKey}";
                        result.ErrorMessages.Add(errorMsg);
                        _logger.LogWarning(errorMsg);
                    }
                }
                catch (Exception ex)
                {
                    result.Errors++;
                    var errorMsg = $"Error processing API key {apiKey}: {ex.Message}";
                    result.ErrorMessages.Add(errorMsg);
                    _logger.LogError(ex, "Error processing API key: {ApiKey}", apiKey);
                }
            }

            _logger.LogInformation("Migration completed. Created: {Created}, Skipped: {Skipped}, Errors: {Errors}", 
                result.SubscriptionsCreated, result.SubscriptionsSkipped, result.Errors);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during migration of legacy API keys");
            throw;
        }
    }
} 