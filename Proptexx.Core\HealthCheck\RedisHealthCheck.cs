using Microsoft.Extensions.Diagnostics.HealthChecks;
using StackExchange.Redis;

namespace Proptexx.Core.HealthCheck;

public sealed class RedisHealthCheck : IHealthCheck
{
    private readonly IConnectionMultiplexer _redis;

    public RedisHealthCheck(IConnectionMultiplexer redis)
    {
        _redis = redis;
    }

    public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        var database = _redis.GetDatabase();
        var result = database.Ping();

        return result > TimeSpan.Zero
            ? Task.FromResult(HealthCheckResult.Healthy("Redis is healthy."))
            : Task.FromResult(HealthCheckResult.Unhealthy("Redis is down."));
    }
}