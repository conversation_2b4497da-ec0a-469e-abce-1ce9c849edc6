﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Proptexx.Core.DTOs.Email
{
    public class EmailMessage
    {
        public string To { get; set; } = string.Empty;
        public string? ToName { get; set; }
        public string? From { get; set; } = string.Empty;
        public string FromName { get; set; }
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public List<EmailAttachment> Attachments { get; set; }

        public bool IsHtml { get; set; } = true;
        public Dictionary<string, string> Headers { get; set; }

    }
}
