using System.Data;
using Proptexx.Web.Auth.Auth;
using Proptexx.Web.Auth.Resolvers;

namespace Proptexx.Web.Auth.Helpers;

public static class PortalLoginHelper
{
    public static async Task LoginAsync(ScopeContext context, LoginPayload payload)
    {
        // var username = payload.GetUsername();
        // var password = payload.Password ?? throw new UnauthorizedAccessException();
        //
        // await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        //
        // var sql = username.Contains('@') ? LoginEmailSql(npgsql) : LoginPhoneSql(npgsql);
        // var param = new { _username = username };
        // var account = await npgsql.QueryFirstOrDefaultAsync<LoginModel>(sql, param) 
        //        ?? throw new CommandException("Invalid credentials");
        //
        // if (payload.AuthType == "oauth" || PasswordService.VerifyPassword(password, account.Hash, account.Salt))
        // {
        //     await LoginHelper.SetAccountClaimsAsync(context, account.Id, $"{account.FirstName} {account.FamilyName}");
        //     await LoginHelper.SetWorkspaceClaimsAsync(context, npgsql, account.Id);
        //     await LoginHelper.SetSystemClaimsAsync(context, account.IsRoot);
        // }
    }

    private static string LoginEmailSql(IDbConnection conn)
    {
        const string sql = @"
            select a.id,
                   a.first_name,
                   a.family_name,
                   a.is_root,
                   acs.hash,
                   acs.salt
            from core.account a
            join core.account_secret acs on a.id = acs.account_id and acs.expired_at is null
            join core.account_email ace on a.id = ace.account_id
            where ace.email = :_username and a.cancelled_at is null";
        return sql;
    }

    private static string LoginPhoneSql(IDbConnection conn)
    {
        const string sql = @"
            select a.id,
                   a.first_name,
                   a.family_name,
                   a.is_root,
                   acs.hash,
                   acs.salt
            from core.account a
            join core.account_secret acs on a.id = acs.account_id and acs.expired_at is null
            join core.account_phone pp on a.id = pp.account_id
            where pp.number = :_username and a.cancelled_at is null";
        return sql;
    }
    
    private class LoginModel
    {
        public required Guid Id { get; init; }

        public required string FirstName { get; init; }

        public required string FamilyName { get; init; }

        public required string Hash { get; init; }

        public required string Salt { get; init; }
        
        public bool IsRoot { get; init; }
    }
}
