namespace Proptexx.Core.Attributes;

[AttributeUsage(AttributeTargets.Class, AllowMultiple = false)]
public class RequiredServiceAttribute : Attribute
{
    /// <summary>
    /// Services required for access to this command or query
    /// </summary>
    public string[] RequiredServices { get; }

    /// <summary>
    /// Whether ALL specified services are required (true) or ANY one of them is sufficient (false)
    /// </summary>
    public bool RequireAllServices { get; set; } = false;

    /// <summary>
    /// Custom error message when access is denied
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Initialize the attribute with required services
    /// </summary>
    /// <param name="requiredServices">Array of service names that are required</param>
    public RequiredServiceAttribute(params string[] requiredServices)
    {
        RequiredServices = requiredServices ?? throw new ArgumentNullException(nameof(requiredServices));
        
        if (requiredServices.Length == 0)
        {
            throw new ArgumentException("At least one required service must be specified", nameof(requiredServices));
        }
    }
} 