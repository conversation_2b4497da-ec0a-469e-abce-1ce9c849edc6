using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Http;
using Proptexx.Core.Redis;
using Proptexx.Core.Services;
using Proptexx.Core.Storage;

namespace Proptexx.AI.Models.Flow;

// ReSharper disable once ClassNeverInstantiated.Global
public class VirtualStagingOrRefurnishingForPortals : VirtualStagingOrRefurnishing
{
    public VirtualStagingOrRefurnishingForPortals(
        IServiceProvider services,
        IConfiguration configuration,
        ILoggerFactory loggerFactory,
        IGenerativeClient generativeClient,
        IStorageService storageService,
        WorkspaceDataHashStore workspaceDataHashStore,
        IImageAssessmentClient imageAssessmentService,
        IHostEnvironment env) 
        : base(services, configuration, loggerFactory, generativeClient, storageService, workspaceDataHashStore, imageAssessmentService, env)
    {
    }
}