using Proptexx.Core.Redis;
using Proptexx.Worker;
using Proptexx.Worker.DataSync;

var builder = Host.CreateApplicationBuilder(args);
builder.AddProptexxWorker();
builder.Services.AddSingleton<IClientSecretStore, ClientSecretStore>();
builder.Services.AddTransient<AccountHashStore>();
builder.Services.AddTransient<EmailHashStore>();
builder.Services.AddTransient<PhoneHashStore>();
builder.Services.AddTransient<WidgetStore>();

var host = builder.Build();
var loggerFactory = host.Services.GetRequiredService<ILoggerFactory>();
var lifetime = host.Services.GetRequiredService<IHostApplicationLifetime>();

var cancellationTokenSource = new CancellationTokenSource();
var logger = loggerFactory.CreateLogger(nameof(Program));

lifetime.ApplicationStopping.Register(() =>
{
    cancellationTokenSource.Cancel();
    Console.WriteLine("Application is stopping...");
});

List<ScheduledTask> tasks = [
    new(nameof(AccountSync), 0.5f, AccountSync.SyncAsync),
    new(nameof(WidgetDataSync),0.5f, WidgetDataSync.SyncAsync),
    new(nameof(WorkspaceDataSync), 0.5f, WorkspaceDataSync.SyncAsync),
    // new(nameof(MViewProcessor), 0.5f, MViewProcessor.SyncAsync),
    new(nameof(IClientSecretStore), 0.5f, async (serviceProvider, ct) =>
    {
        var clientSecretStore = serviceProvider.GetRequiredService<IClientSecretStore>();
        await clientSecretStore.SyncAsync(ct);
    })
];

logger.LogInformation("SyncWorker started");

while (!cancellationTokenSource.IsCancellationRequested)
{
    var applicable = tasks.Where(task => task.ShouldRun()).ToList();
    var executed = await Task.WhenAll(applicable.Select(x => x.RunAsync(host.Services, cancellationTokenSource.Token)));

    if (executed.Length > 0)
    {
        var serviceNames = string.Join(',', executed.Select(x => x.name));
        var timeTakenSum = executed.Sum(x => x.timeTaken);
        
        logger.LogInformation("Ran {numServices} services ({serviceNames}) in {timeTakenSum} ms", executed.Length, serviceNames, timeTakenSum);

        var withExceptions = executed
            .Where(x => x.exception is not null)
            .ToList();

        if (withExceptions.Count > 0)
        {
            foreach (var withException in withExceptions)
            {
                logger.LogError(withException.exception, $"Exception in {withException.name}");
            }
        }
    }

    await Task.Delay(TimeSpan.FromSeconds(5), cancellationTokenSource.Token);
}

logger.LogInformation("Service is shutting down");