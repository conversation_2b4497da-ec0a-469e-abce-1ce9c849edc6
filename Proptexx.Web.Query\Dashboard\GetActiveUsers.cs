﻿using Dapper;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Dashboard;
public sealed class GetActiveUsers : BaseFilter, IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);

        var result = (await npgsql.QueryAsync<GetActiveUsersDto>(Sql, GetParameters())).ToList();

        // Ensure returning an empty array instead of `null`
        return result ?? new List<GetActiveUsersDto>();

    } 

    public static string Sql => @"
        SELECT 
            DATE(timestamp) AS usageDate, 
            COUNT(DISTINCT session_id) AS activeUsers
        FROM telemetry.api_logs
        WHERE timestamp BETWEEN @startDate AND @endDate
        AND (
            (@userType IS NULL AND @id IS NULL) OR -- No filter applied 
            (@userType = 'workspace' AND workspace_id = COALESCE(@id, workspace_id)) OR
            (@userType = 'client' AND client_id = COALESCE(@id, client_id)) OR
            (@userType = 'account' AND account_id = COALESCE(@id, account_id))
        )
        GROUP BY DATE(timestamp)
        ORDER BY usageDate;
;

    ";
}

public class GetActiveUsersDto
{
    public DateTime UsageDate { get; set; }
    public int ActiveUsers { get; set; }
}
