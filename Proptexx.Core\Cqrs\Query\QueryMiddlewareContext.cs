using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Proptexx.Core.Json;

namespace Proptexx.Core.Cqrs.Query;

public sealed class QueryMiddlewareContext : ProptexxContext
{
    private QueryMiddlewareContext(IServiceProvider services, ProptexxPrincipal user) : base(services, user)
    {
    }

    public required IDictionary<object, object?> Items { get; init; }
    
    public required QueryRequest Request { get; init; }

    internal static async Task<QueryMiddlewareContext> FromHttpAsync(HttpContext context)
    {
        if (context.User is null) throw new NullReferenceException("Claims Principal not found");

        var payload = await JsonSerializer.DeserializeAsync<QueryRequest>(
                          context.Request.Body, JsonDefaults.JsonSerializerOptions, context.RequestAborted)
                      ?? throw new NullReferenceException("Unable to deserialize request payload");

        var identity = context.User.Identity ?? throw new UnauthorizedAccessException();
        var user = new ProptexxPrincipal(identity);
        return new QueryMiddlewareContext(context.RequestServices, user)
        {
            Items = context.Items,
            Request = payload,
            CancellationToken = context.RequestAborted
        };
    }
}