using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace Proptexx.Core.Json;

public class <PERSON><PERSON><PERSON><PERSON>parer
{
    // Serializes a JsonDocument to a canonical form (minified and with ordered properties)
    public static string SerializeJsonDocument(JsonDocument document)
    {
        return JsonSerializer.Serialize(JsonElementToCanonicalForm(document.RootElement));
    }

    // Recursively converts a JsonElement to a canonical form with ordered properties
    public static JsonDocument JsonElementToCanonicalFormDocument(JsonElement element)
    {
        var el = JsonElementToCanonicalFormString(element);
        return JsonDocument.Parse(el);
    }

    public static string JsonElementToCanonicalFormString(JsonElement element)
    {
        var el = JsonElementToCanonicalForm(element);
        return el.GetRawText();
    }

    public static JsonElement JsonElementToCanonicalForm(JsonElement element)
    {
        var options = new JsonSerializerOptions { WriteIndented = false };
        
        switch (element.ValueKind)
        {
            case JsonValueKind.Object:
                var dictionary = new SortedDictionary<string, JsonElement>();
                foreach (var property in element.EnumerateObject())
                {
                    dictionary[property.Name] = JsonElementToCanonicalForm(property.Value);
                }
                return JsonDocument.Parse(JsonSerializer.Serialize(dictionary, options)).RootElement;

            case JsonValueKind.Array:
                var array = element.EnumerateArray()
                                   .Select(JsonElementToCanonicalForm)
                                   .ToArray();
                return JsonDocument.Parse(JsonSerializer.Serialize(array, options)).RootElement;

            default:
                return element;
        }
    }

    // Computes a hash for a canonical JSON string
    public static string ComputeHash(JsonDocument document)
    {
        var json = SerializeJsonDocument(document);
        return ComputeHash(json);
    }

    public static string ComputeHash(string text)
    {
        var hash = SHA256.HashData(Encoding.UTF8.GetBytes(text));
        return Convert.ToBase64String(hash);
    }

    // Compares two hash values
    public static bool AreDocumentsEqual(string hash1, string hash2)
    {
        return hash1 == hash2;
    }
}
