using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Proptexx.Core.Entities.Outseta
{
    public class Subscription
    {
        public int BillingRenewalTerm { get; set; }
        public Plan? Plan { get; set; }
        public int Quantity { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime RenewalDate { get; set; }
        public bool IsPlanUpgradeRequired { get; set; }
        public List<object>? SubscriptionAddOns { get; set; } // Placeholder
        public List<object>? DiscountCouponSubscriptions { get; set; } // Placeholder
        public decimal Rate { get; set; }
        public string? Uid { get; set; }
        [JsonPropertyName("_objectType")]
        public string? ObjectType { get; set; }
        public DateTime Created { get; set; }
        public DateTime Updated { get; set; }
    }
}
