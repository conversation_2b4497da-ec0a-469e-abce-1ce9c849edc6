using System.Text.Json;
using System.Text.Json.Serialization;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Extensions;
using Proptexx.Core.Http;
using Proptexx.Core.Json;
using Proptexx.Core.Services;
using StackExchange.Redis;

namespace Proptexx.AI.Models.CV;

public sealed class PreProcessorForObjectsV2 : BaseComputerVisionModel
{
    public PreProcessorForObjectsV2(
        IComputerVisionClient computerVisionClient, 
        IImageAssessmentClient imageAssessmentClient,
        IConnectionMultiplexer connectionMultiplexer, 
        ILoggerFactory loggerFactory)
        : base(
            "PreProcessorForObjectsV2", 
            computerVisionClient,
            imageAssessmentClient,
            connectionMultiplexer, 
            loggerFactory)
    {
    }

    public class Result
    {
        [JsonPropertyName("hasFood")]
        public bool HasFood { get; set; }
        
        [JsonPropertyName("hasPerson")]
        public bool HasPerson { get; set; }

        [JsonPropertyName("hasRealAnimal")]
        public bool HasRealAnimal { get; set; }

        [JsonPropertyName("hasCloseUpObject")]
        public bool HasCloseUpObject { get; set; }

        [JsonPropertyName("hasDominantObject")]
        public bool HasDominantObject { get; set; }

        [JsonPropertyName("hasMultipleImages")]
        public bool HasMultipleImages { get; set; }

        [JsonPropertyName("hasLittleSpace")]
        public bool HasLittleSpace { get; set; }

        [JsonPropertyName("hasBlurryArea")]
        public bool HasBlurryArea { get; set; }

        [JsonPropertyName("hasUnorganizedSpace")]
        public bool HasUnorganizedSpace { get; set; }
    }
}