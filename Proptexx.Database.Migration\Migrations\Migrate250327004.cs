using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations;

[Migration(250327004)]
public class Migrate250327004 : FluentMigrator.Migration
{
    public override void Up()
    {
        // Drop existing API materialized views
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_summary CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_today CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_past CASCADE;");

        // Create API Requests Today view (data from midnight UTC until now)
        Execute.Sql(@"
            CREATE MATERIALIZED VIEW telemetry.mv_api_requests_today AS
            SELECT 
                a.workspace_id,
                a.created_at::date AS day,
                a.processed_endpoint AS endpoint,
                COUNT(*) AS request_count
            FROM telemetry.api_logs a
            WHERE a.is_api_request
              AND a.created_at >= date_trunc('day', now() AT TIME ZONE 'UTC')
              AND a.created_at < now() AT TIME ZONE 'UTC'
              AND a.processed_endpoint IS NOT NULL
            GROUP BY a.workspace_id, a.created_at::date, a.processed_endpoint
            WITH NO DATA;
        ");
        Execute.Sql(@"CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_api_requests_today_unique 
                      ON telemetry.mv_api_requests_today(day, endpoint, workspace_id);");

        // Create API Requests Past view (data before today)
        Execute.Sql(@"
            CREATE MATERIALIZED VIEW telemetry.mv_api_requests_past AS
            SELECT 
                a.workspace_id,
                a.created_at::date AS day,
                a.processed_endpoint AS endpoint,
                COUNT(*) AS request_count
            FROM telemetry.api_logs a
            WHERE a.is_api_request
              AND a.created_at < date_trunc('day', now() AT TIME ZONE 'UTC')
              AND a.processed_endpoint IS NOT NULL
            GROUP BY a.workspace_id, a.created_at::date, a.processed_endpoint
            WITH NO DATA;
        ");
        Execute.Sql(@"CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_api_requests_past_unique 
                      ON telemetry.mv_api_requests_past(day, endpoint, workspace_id);");

        // Create combined API Requests summary view
        Execute.Sql(@"
            CREATE MATERIALIZED VIEW telemetry.mv_api_requests_summary AS
            SELECT 
                workspace_id,
                day,
                endpoint,
                SUM(request_count) AS request_count
            FROM (
                SELECT * FROM telemetry.mv_api_requests_today
                UNION ALL
                SELECT * FROM telemetry.mv_api_requests_past
            ) AS combined
            GROUP BY workspace_id, day, endpoint
            WITH NO DATA;
        ");
        Execute.Sql(@"CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_api_requests_summary_unique 
                      ON telemetry.mv_api_requests_summary(day, endpoint, workspace_id);");
    }

    public override void Down()
    {
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_summary CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_today CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_past CASCADE;");
    }
}