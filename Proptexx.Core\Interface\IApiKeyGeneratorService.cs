namespace Proptexx.Core.Services;

/// <summary>
/// Interface for API key generation service
/// Uses existing ApiKeyService format and logic
/// </summary>
public interface IApiKeyGeneratorService
{
    /// <summary>
    /// Generate API key using existing ApiKeyService logic
    /// </summary>
    /// <param name="secretId">Secret ID (usually client ID or generated GUID)</param>
    /// <returns>Generated API key using existing format</returns>
    string GenerateApiKey(Guid secretId);

    /// <summary>
    /// Generate API key using existing ApiKeyService logic with string secretId
    /// </summary>
    /// <param name="secretId">Secret ID as string</param>
    /// <returns>Generated API key using existing format</returns>
    string GenerateApiKey(string secretId);

    /// <summary>
    /// Generate API key using existing ApiKeyService logic with client and secret IDs
    /// </summary>
    /// <param name="clientId">Client ID</param>
    /// <param name="secretId">Secret ID</param>
    /// <returns>Generated API key using existing format</returns>
    string GenerateApiKey(Guid clientId, Guid secretId);

    /// <summary>
    /// Validate API key format using existing logic
    /// </summary>
    /// <param name="apiKey">API key to validate</param>
    /// <returns>True if format is valid</returns>
    bool IsValidFormat(string apiKey);

    /// <summary>
    /// Parse API key using existing logic to extract secret ID
    /// </summary>
    /// <param name="apiKey">API key to parse</param>
    /// <returns>Secret ID or null if invalid</returns>
    string? ParseApiKey(string apiKey);
} 