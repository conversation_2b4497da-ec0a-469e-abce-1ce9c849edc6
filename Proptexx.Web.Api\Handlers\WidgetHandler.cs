using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text.Json;
using System.Threading.Tasks;
using Proptexx.Core;
using Npgsql;
using Dapper;
using Proptexx.Web.ClientServices.Interfaces;

namespace Proptexx.Web.Api.Handlers
{
    public class RenderWidgetInput
    {
        public string? ApiKey { get; set; }
        public string? PageUrl { get; set; }
    }

    public class WidgetHandler
    {
        private readonly ILogger<WidgetHandler> _logger;
        private readonly string? _outsetaWebhookUrl;
        private readonly string _widgetLoaderBaseUrl;
        private readonly IOutsetaApiClient _outsetaApiClient;

        private readonly NpgsqlDataSource _dataSource;

        public WidgetHandler(
            ILogger<WidgetHandler> logger,
            IConfiguration configuration,
            IOutsetaApiClient outsetaApiClient,
            NpgsqlDataSource dataSource)
        {
            _logger = logger;
            _outsetaApiClient = outsetaApiClient;
            _outsetaWebhookUrl = configuration["OutsetaWebhookUrl"];
            _widgetLoaderBaseUrl = configuration["WidgetSettings:LoaderBaseUrl"] ?? "https://staging.widget.product.placement.proptexx.com/widget/";
            if (!_widgetLoaderBaseUrl.EndsWith("/"))
            {
                _widgetLoaderBaseUrl += "/";
            }
            _dataSource = dataSource;
        }

        public async Task<IResult> HandleRenderAsync(HttpContext context)
        {
            RenderWidgetInput? input;
            try
            {
                if (!context.Request.HasJsonContentType())
                {
                    return Results.BadRequest("Request must be JSON.");
                }
                input = await context.Request.ReadFromJsonAsync<RenderWidgetInput>(context.RequestAborted);
                if (input == null || string.IsNullOrWhiteSpace(input.ApiKey) || string.IsNullOrWhiteSpace(input.PageUrl))
                {
                    return Results.BadRequest("ApiKey and PageUrl are required in the JSON payload.");
                }
            }
            catch (JsonException jsonEx)
            {
                _logger.LogError(jsonEx, "Error deserializing RenderWidgetInput.");
                return Results.BadRequest("Invalid JSON payload.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading request body for HandleRenderAsync.");
                return Results.StatusCode(StatusCodes.Status500InternalServerError);
            }

            _logger.LogInformation("Handling render request for ApiKey: {ApiKey}, PageUrl: {PageUrl}", input.ApiKey, input.PageUrl);

            // 1. Check domain & path match
            // get path from pageUrl from request body
            string pageUrlToParse = input.PageUrl!;
            if (!pageUrlToParse.StartsWith("http://", StringComparison.OrdinalIgnoreCase) &&
                !pageUrlToParse.StartsWith("https://", StringComparison.OrdinalIgnoreCase))
            {
                pageUrlToParse = "https://" + pageUrlToParse;
            }

            if (!Uri.TryCreate(pageUrlToParse, UriKind.Absolute, out var uri))
            {
                _logger.LogWarning("Invalid PageUrl format: {PageUrl}", input.PageUrl);
                return Results.BadRequest("Invalid PageUrl format.");
            }
            var domain = uri.Host;
            if (domain.StartsWith("www.", StringComparison.OrdinalIgnoreCase))
            {
                domain = domain.Substring(4);
            }
            var path = uri.AbsolutePath.TrimStart('/');
            path = "/" + path;

            // get record from db where api_key = input.ApiKey
            await using var npgsql = await _dataSource.OpenConnectionAsync(context.RequestAborted);
            var query = @"
            SELECT * FROM core.widget_clients
            WHERE api_key = :api_key
            AND domain = :domain
            AND EXISTS (
                SELECT 1
                FROM jsonb_array_elements_text(allowed_paths) AS ap
                WHERE ap = :path
            )";
            var isValid = await npgsql.QueryAsync<dynamic>(query, new { api_key = input.ApiKey, domain, path });
            if (isValid == null || !isValid.Any())
            {
                _logger.LogWarning("Invalid ApiKey or domain/path mismatch for ApiKey: {ApiKey}, Domain: {Domain}, Path: {Path}", input.ApiKey, domain, path);
                return Results.BadRequest("Invalid domain or path for the provided ApiKey.");
            }

            _logger.LogInformation("Domain & paths are valid for: {ApiKey}", input.ApiKey);

            // 2. Increment render count if within quota
            // based on input.ApiKey, increment the renders_used in widget_clients table if renders_used < quota
            var incrementQuery = @"
                UPDATE core.widget_clients
                SET renders_used = renders_used + 1
                WHERE api_key = :api_key
                AND renders_used < quota
                RETURNING renders_used, quota";
            var result = await npgsql.QuerySingleOrDefaultAsync<dynamic>(incrementQuery, new { api_key = input.ApiKey });
            if (result == null)
            {
                _logger.LogWarning("Render count increment failed or quota exceeded for ApiKey: {ApiKey}", input.ApiKey);
                return Results.BadRequest("Render count increment failed or quota exceeded.");
            }

            _logger.LogInformation("Successfully increased renders_used for ApiKey: {ApiKey}", input.ApiKey);

            // 3. Optionally POST to Outseta webhook to log activity
            if (!string.IsNullOrWhiteSpace(_outsetaWebhookUrl)) // Check if _outsetaWebhookUrl is not null or whitespace
            {
                try
                {
                    var outsetaPayload = new
                    {
                        apiKey = input.ApiKey,
                        pageUrl = input.PageUrl,
                        eventTime = DateTime.UtcNow,
                        activityType = "widgetRender"
                    };

                    // Use IOutsetaApiClient to post the custom activity
                    HttpResponseMessage response = await _outsetaApiClient.PostCustomActivityAsync(_outsetaWebhookUrl, outsetaPayload);

                    if (!response.IsSuccessStatusCode)
                    {
                        var errorContent = await response.Content.ReadAsStringAsync(context.RequestAborted);
                        _logger.LogWarning("Failed to post to Outseta webhook via OutsetaApiClient. Status: {StatusCode}, ApiKey: {ApiKey}, Response: {Response}", response.StatusCode, input.ApiKey, errorContent);
                    }
                    else
                    {
                        _logger.LogInformation("Successfully posted activity to Outseta webhook via OutsetaApiClient for ApiKey: {ApiKey}", input.ApiKey);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error posting to Outseta webhook via OutsetaApiClient for ApiKey: {ApiKey}", input.ApiKey);
                }
            }
            else
            {
                _logger.LogInformation("Outseta webhook URL not configured or is empty. Skipping Outseta POST.");
            }

            return Results.Ok(new { message = "SUCCESS" });
        }

        public async Task<string?> GetApiKeyAsync(HttpContext context)
        {
            string? outsetaId = context.Request.Query["outseta_account_id"];
            if (string.IsNullOrWhiteSpace(outsetaId))
            {
                _logger.LogWarning("Outseta AccountId is missing in query for GetApiKeyAsync.");
                return null;
            }

            const string query = @"
            SELECT api_key
            FROM core.widget_clients
            WHERE LOWER(outseta_id) = LOWER(:outseta_id)
            LIMIT 1;
            ";

            await using var npgsql = await _dataSource.OpenConnectionAsync(context.RequestAborted);
            var apiKey = await npgsql.QuerySingleOrDefaultAsync<string>(query, new {outseta_id = outsetaId });

            return apiKey;
        }

        public Task<IResult> HandleGetInstallSnippetAsync(HttpContext context)
        {
            string? apiKey = context.Request.Query["apiKey"];

            if (string.IsNullOrWhiteSpace(apiKey))
            {
                _logger.LogWarning("API key is missing in query for install snippet.");
                return Task.FromResult(Results.BadRequest("API key is required as a query parameter."));
            }

            _logger.LogInformation("Generating install snippet for ApiKey: {ApiKey} using base URL: {WidgetLoaderBaseUrl}", apiKey, _widgetLoaderBaseUrl);

            var safeApiKey = System.Uri.EscapeDataString(apiKey);
            var loaderUrl = $"{_widgetLoaderBaseUrl}loader.js?k={safeApiKey}";

            var snippet = $@"
                <!-- Proptexx Widget Start -->
                <!-- 1. Add this script tag to your HTML's <head> or before the closing </body> tag. -->
                <script id=""proptexx-widget"" src=""{loaderUrl}""></script>

                <!-- 
                2. Configure the widget by setting the window.proptexx object *before* the script tag above.
                Example:-->
                <script>
                window.proptexx = {{
                    apikey: '{apiKey.Replace("'", "\\'")}', 
                    skipAuth: true, 
                    skipUrlRules: true, 
                    urlRules: [ 
                            // {{ type: 'prefix', pattern: '/products', enabled: true }},  
                            // {{ type: 'regex', pattern: '^/gallery/[^/]+$', enabled: true }}
                    ],
                }};
                </script>
                
                <!-- Proptexx Widget End -->";

            return Task.FromResult(Results.Content(snippet.Trim(), "text/html", System.Text.Encoding.UTF8));
        }
    }
}
