using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations
{
    [Migration(250707003)]
    public class InsertEcommerceWidgetService : FluentMigrator.Migration
    {
        public override void Up()
        {
            // Insert into core.service_type
            Execute.Sql(@"
                INSERT INTO core.service_type (id, settings)
                VALUES ('ecommerce-widget', '{}'::jsonb)
                ON CONFLICT (id) DO NOTHING;
            ");
            Execute.Sql(@"
                INSERT INTO core.service_type (id, settings)
                VALUES ('real-estate-widget', '{}'::jsonb)
                ON CONFLICT (id) DO NOTHING;
            ");

            // Insert into core.service
            Execute.Sql(@"
                INSERT INTO core.service (
                    id, title, description, service_type, credit_cost, settings, active_from, active_to
                )
                VALUES (
                    'ecommerce-widget',
                    'Ecommerce Widget',
                    NULL,
                    'ecommerce-widget',
                    0,
                    '{""type"": ""ecommerce-widget""}'::jsonb,
                    NULL,
                    NULL
                ),
                (
                    'real-estate-widget',
                    'Real-Estate Widget',
                    NULL,
                    'real-estate-widget',
                    0,
                    '{""type"": ""real-estate-widget""}'::jsonb,
                    NULL,
                    NULL
                )
                ON CONFLICT (id) DO NOTHING;
            ");
        }

        public override void Down()
        {
            // Delete from core.service first due to FK
            Execute.Sql("DELETE FROM core.service WHERE id = 'ecommerce-widget';");
            Execute.Sql("DELETE FROM core.service WHERE id = 'real-estate-widget';");
            Execute.Sql("DELETE FROM core.service_type WHERE id = 'ecommerce-widget';");
            Execute.Sql("DELETE FROM core.service_type WHERE id = 'real-estate-widget';");
        }
    }
}