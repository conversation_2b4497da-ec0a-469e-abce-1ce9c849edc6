using System.Text.Json;
using Microsoft.Extensions.Options;
using Npgsql;
using Proptexx.Core;
using Proptexx.Core.Json;
using Proptexx.Core.Options;
using Proptexx.Core.Services;
using Proptexx.Core.Stores;
using Proptexx.Core.Utils;
using Proptexx.Web.Auth.OAuth;
using StackExchange.Redis;

namespace Proptexx.Web.Auth.Handlers;

public sealed class OAuthCallbackHandler
{
    private readonly ILogger<OAuthCallbackHandler> _logger;
    private readonly NpgsqlDataSource _dataSource;
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly IOAuthProviderFactory _providerFactory;
    private readonly ProptexxOptions _proptexxOptions;

    public OAuthCallbackHandler(
        ILogger<OAuthCallbackHandler> logger,
        NpgsqlDataSource dataSource,
        IConnectionMultiplexer connectionMultiplexer,
        IOAuthProviderFactory providerFactory,
        IOptions<ProptexxOptions> proptexxOptions)
    {
        _logger = logger;
        _dataSource = dataSource;
        _connectionMultiplexer = connectionMultiplexer;
        _providerFactory = providerFactory;
        _proptexxOptions = proptexxOptions.Value;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var redirectUri = _proptexxOptions.AccountUrl;

        try
        {
            var query = context.Request.Query;

            // Validate query parameters
            if (!query.TryGetValue("code", out var code) 
                || !query.TryGetValue("state", out var state))
            {
                throw new ApplicationException("Missing required callback parameters");
            }

            var strState = state.ToString();
            var redis = _connectionMultiplexer.GetDatabase();

            // Verify state to prevent CSRF attacks
            var value = await redis.HashGetAsync("oauth_states", strState);
            if (value.IsNullOrEmpty || string.IsNullOrWhiteSpace(value))
            {
                throw new ApplicationException("The operation has timed out or the state is invalid");
            }

            // Get the appropriate provider
            var json = value.ToString();
            var payload = JsonSerializer.Deserialize<OAuthLinkPayload>(json, JsonDefaults.CompactOptions)
                ?? throw new ApplicationException("The operation has timed out or the state is invalid");

            var provider = _providerFactory.GetProvider(payload.Provider);

            // Exchange the code for a token
            var tokenResponse = await provider.ExchangeCodeForTokenAsync(code.ToString());

            // Fetch user information
            var userInfo = await provider.FetchUserInfoAsync(tokenResponse.AccessToken);

            // Determine the redirect URI based on user info
            var account = await SigninAsync(redis, userInfo, payload);

            if (account.HasValue)
            {
                var codeField = Guid.NewGuid().ToString();

                await redis.HashSetAsync(
                    "oauth_codes", 
                    codeField, 
                    new RedisValue($"{account.Value.accountId}|||{account.Value.fullName}|||{account.Value.email}"),
                    When.NotExists);

                await redis.HashFieldExpireAsync("oauth_codes", [codeField], TimeSpan.FromSeconds(30));

                redirectUri = $"{_proptexxOptions.AccountUrl}/code?code={codeField}";
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "An error occurred during the OAuth callback.");
        }
        finally
        {
            // Redirect to the determined URI
            context.Response.StatusCode = StatusCodes.Status307TemporaryRedirect;
            context.Response.Headers.Location = redirectUri;
        }
    }

    private async Task<(string accountId, string fullName, string email)?> SigninAsync(IDatabaseAsync redis, UserInfo userInfo, OAuthLinkPayload? oAuthPayload)
    {
        var email = userInfo.Email ?? throw new NullReferenceException(nameof(userInfo.Email));
        var fullName = userInfo.Name ?? throw new NullReferenceException(nameof(userInfo.Name));

        await using var npgsql = await _dataSource.OpenConnectionAsync();
        await using var trx = await npgsql.BeginTransactionAsync();

        try
        {
            var password = PasswordService.GeneratePassword(12);
            var account = await OnboardHelper.OnboardAccount(npgsql, fullName, email, true, password, null, false);
            
            if (oAuthPayload?.Type is not null && oAuthPayload.Type.StartsWith("widget"))
            {
                var ar = oAuthPayload.Type.Split('-');
                var secretId = ApiKeyService.ParseApiKey(ar[1]);
                var workspaceIdVal = await redis.HashGetAsync($"widget:{secretId}", ["workspaceId"]);
                var workspaceIdStr = workspaceIdVal[0].ToString();

                if (!string.IsNullOrWhiteSpace(workspaceIdStr))
                {
                    var workspaceId = Guid.Parse(workspaceIdStr);
                    await npgsql.Account().JoinCluster(account.Id, workspaceId, "lead", true);
                }
            }

            await trx.CommitAsync();
            return (account.Id.ToString(), account.FullName(), userInfo.Email);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception when creating OAuth account.");
            await trx.RollbackAsync();
        }

        return null;
    }
}


