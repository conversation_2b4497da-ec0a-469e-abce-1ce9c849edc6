using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations;

[Migration(250326002)]
public class Migrate250326002 : FluentMigrator.Migration
{
    public override void Up()
    {

        Execute.Sql(@"CREATE MATERIALIZED VIEW IF NOT EXISTS telemetry.mv_dashboard_summary_today
                        AS
                        SELECT created_at::date AS day,
                               COUNT(*) FILTER (WHERE is_api_request) AS total_api_requests,
                               COUNT(*) FILTER (WHERE is_ai_request) AS total_ai_requests,
                               COUNT(*) FILTER (WHERE is_batch) AS total_batch_requests,
                               ROUND(COALESCE(AVG(duration_ms), 0::double precision)::numeric, 1) AS average_request_duration,
                               COUNT(DISTINCT session_id) AS distinct_sessions
                        FROM telemetry.api_logs
                        WHERE created_at >= date_trunc('day', now() AT TIME ZONE 'UTC') -- Midnight UTC
                          AND created_at < now() AT TIME ZONE 'UTC'  -- Current time UTC
                        GROUP BY created_at::date
                        WITH NO DATA;");
        Execute.Sql(@"CREATE MATERIALIZED VIEW IF NOT EXISTS telemetry.mv_dashboard_summary_past
                        AS
                        SELECT created_at::date AS day,
                               COUNT(*) FILTER (WHERE is_api_request) AS total_api_requests,
                               COUNT(*) FILTER (WHERE is_ai_request) AS total_ai_requests,
                               COUNT(*) FILTER (WHERE is_batch) AS total_batch_requests,
                               ROUND(COALESCE(AVG(duration_ms), 0::double precision)::numeric, 1) AS average_request_duration,
                               COUNT(DISTINCT session_id) AS distinct_sessions
                        FROM telemetry.api_logs
                        WHERE created_at < date_trunc('day', now() AT TIME ZONE 'UTC') -- Before Midnight UTC
                        GROUP BY created_at::date
                        WITH NO DATA;
");
        Execute.Sql(@"CREATE MATERIALIZED VIEW IF NOT EXISTS telemetry.mv_dashboard_summary
                    AS
                    SELECT day,
                           SUM(total_api_requests) AS total_api_requests,
                           SUM(total_ai_requests) AS total_ai_requests,
                           SUM(total_batch_requests) AS total_batch_requests,
                           ROUND(COALESCE(AVG(average_request_duration), 0::double precision)::numeric, 1) AS average_request_duration,
                           SUM(distinct_sessions) AS distinct_sessions
                    FROM (
                        SELECT * FROM telemetry.mv_dashboard_summary_today
                        UNION ALL
                        SELECT * FROM telemetry.mv_dashboard_summary_past
                    ) AS combined
                    GROUP BY day
                    WITH NO DATA;
");

        Execute.Sql(@"CREATE INDEX IF NOT EXISTS idx_mv_dashboard_today_day 
                ON telemetry.mv_dashboard_summary_today (day);

                CREATE INDEX IF NOT EXISTS idx_mv_dashboard_today_total_requests 
                ON telemetry.mv_dashboard_summary_today (total_api_requests, total_ai_requests, total_batch_requests);");
        
        Execute.Sql(@"CREATE INDEX IF NOT EXISTS idx_mv_dashboard_past_day 
                ON telemetry.mv_dashboard_summary_past (day);

                CREATE INDEX IF NOT EXISTS idx_mv_dashboard_past_total_requests 
                ON telemetry.mv_dashboard_summary_past (total_api_requests, total_ai_requests, total_batch_requests);");
        
        Execute.Sql(@"CREATE INDEX IF NOT EXISTS idx_mv_dashboard_combined_day 
                ON telemetry.mv_dashboard_summary (day);

                CREATE INDEX IF NOT EXISTS idx_mv_dashboard_combined_total_requests 
                ON telemetry.mv_dashboard_summary (total_api_requests, total_ai_requests, total_batch_requests);");
    }

    public override void Down()
    {
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_dashboard_summary;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_dashboard_summary_past;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_dashboard_summary_today;");
    }
}