using System.Data;
using System.Reflection;
using System.Text;
using Dapper;
using Proptexx.Core.Extensions;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Postgresql.Builder;

public static class StoreInsertExtensions
{
     public static Task<int> InsertAsync<T>(this IDbConnection conn, T row, InsertConflictHandler? onConflict = null) where T : class, IDbTable
     {
         return conn.InsertAsync(new[] {row}, onConflict);
     }
     
     public static async Task<int> InsertAsync<T>(this IDbConnection conn, IEnumerable<T> rows, InsertConflictHandler? onConflict = null) where T : class, IDbTable
     {
         var statement = BuildInsertStatement<T>(onConflict);
         var result = await conn.ExecuteAsync(statement, rows);
         return result;
     }

     private static string BuildInsertStatement<T>(InsertConflictHandler? onConflict) where T : class, IDbTable
     {
         // string key = $"insert_{this.TableName}";
         // if (onConflict is not null) key = $"upsert_{this.TableName}_{onConflict.Constraint}";
         // if (StoreCache.Statements.ContainsKey(key)) return StoreCache.Statements[key];

         var separator = ",";
         var tRecord = typeof(T);
         var props = tRecord.GetProperties()
             .Where(FilterIgnored)
             .ToArray();

         var sqlProps = new StringBuilder();
         var clrProps = new StringBuilder();
         var upsertProps = new StringBuilder();

         for (var i = 0; i < props.Length; i++)
         {
             if (i == props.Length - 1) separator = "";
             var column = props[i].Name.ToSnakeCase();
             var prop = props[i].Name;
             var snakeCaseName = props[i].Name.ToSnakeCase();
             sqlProps.Append($"{column}{separator}");
             clrProps.Append($"@{prop}{separator}");

             switch (onConflict)
             {
                 case UpsertOnConflictHandler upsert when upsert.IgnoreFields.Contains(snakeCaseName):
                     continue;
                 case UpsertOnConflictHandler {UpdateFields: { }} upsert when !upsert.UpdateFields!.Contains(snakeCaseName):
                     continue;
                 case UpsertOnConflictHandler:
                 {
                     if (upsertProps.Length > 0) upsertProps.Append(separator);
                     upsertProps.Append($"{snakeCaseName}=@{props[i].Name}");
                     break;
                 }
                 case DeleteOnConflictHandler:
                     break;
             }
         }

         var tableName = DbRefProvider.Get(tRecord);
         var strBuilder = new StringBuilder($"insert into {tableName}({sqlProps}) values({clrProps})");

         if (onConflict is not null)
         {
             if (upsertProps.Length > 0)
             {
                 strBuilder.Append(onConflict.ConflictType == ConflictType.Constraint
                     ? $" on conflict on constraint {onConflict.Constraint} "
                     : $" on conflict({onConflict.Constraint}) ");

                 strBuilder.Append($"do update set {upsertProps};");
             }
             else if (onConflict is IgnoreOnConflictHandler)
             {
                 strBuilder.Append($" on conflict do nothing");
             }
         }

         var result = strBuilder.ToString();
         // StoreCache.Statements.Add(key, result);
         return result;
     }

     private static bool FilterIgnored(PropertyInfo p)
     {
        if (System.Attribute.IsDefined(p, typeof(IgnoreOnInsertAttribute))) return false;
        if (System.Attribute.IsDefined(p, typeof(IgnoreOnInsertOrUpdateAttribute))) return false;
        return true;
     }
}

public abstract class InsertConflictHandler
{
    protected InsertConflictHandler(ConflictType conflictType, string constraint = "id")
    {
        ConflictType = conflictType;
        Constraint = constraint;
    }

    public ConflictType ConflictType { get; }

    public string Constraint { get; }
}

public sealed class IgnoreOnConflictHandler : InsertConflictHandler
{
    public IgnoreOnConflictHandler(ConflictType conflictType, string constraint = "id") : base(conflictType, constraint)
    {
    }
}

public sealed class UpsertOnConflictHandler : InsertConflictHandler
{
    public UpsertOnConflictHandler(ConflictType conflictType, string constraint = "id", 
        IEnumerable<string>? updateFields = null, IEnumerable<string>? ignoreFields = null) : base(conflictType, constraint)
    {
        UpdateFields = updateFields;
        IgnoreFields = ignoreFields ?? new[] {"id", "created_at"};
    }

    public IEnumerable<string>? UpdateFields { get; }

    public IEnumerable<string> IgnoreFields { get; }
}

public sealed class DeleteOnConflictHandler : InsertConflictHandler
{
    public DeleteOnConflictHandler(ConflictType conflictType, string constraint = "id") : base(conflictType, constraint)
    {
    }
}

public enum ConflictType
{
    Column = 1,
    Constraint = 2
}