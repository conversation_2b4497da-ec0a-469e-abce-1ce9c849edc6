using System.Net.Http.Json;
using System.Text.Json;
using Proptexx.Core.AI;
using Proptexx.Core.Services;

namespace Proptexx.AI.Models.CV;

public sealed class ImageQualityEvaluation : BaseModel
{
    private readonly HttpClient _httpClient;

    public ImageQualityEvaluation(IHttpClientFactory httpClientFactory)
    {
        _httpClient = httpClientFactory.CreateClient(nameof(ImageQualityEvaluation));
    }

    public override async Task<ModelResponse> InferAsync(ModelContext context)
    {
        if (context.Payload is null)
        {
            throw new NullReferenceException("Payload is null");
        }

        var image1 = PayloadService.GetRequiredString(context.Payload, "image1");
        var image2 = PayloadService.GetRequiredString(context.Payload, "image2");
        using var content = JsonContent.Create(new { image1, image2 });

        const string modelEndpoint = "https://cv.vlm.proptexx.ai/image_quality_evaluation";
        var cancellationToken = context.CancellationToken;
        using var response = await _httpClient.PostAsync(modelEndpoint, content, cancellationToken);

        if (!response.IsSuccessStatusCode)
        {
            var responseText = await response.Content.ReadAsStringAsync(cancellationToken);
            var statusCode = (int)response.StatusCode;
            throw new ApplicationException($"Model returned code {statusCode}", new Exception(responseText));
        }

        await using var stream = await response.Content.ReadAsStreamAsync(cancellationToken);
        var doc = await JsonDocument.ParseAsync(stream, cancellationToken: cancellationToken);

        var modelInternalStatusCode = PayloadService.GetOptionalInt32(doc, "status_code");
        if (modelInternalStatusCode is >= 400)
        {
            var modelInternalDetail = PayloadService.GetOptionalString(doc, "detail");
            throw new ApplicationException(modelInternalDetail ?? "An internal model error was thrown");
        }

        var result = new ModelResponse
        {
            Document = doc
        };
        
        return result;
    }
}