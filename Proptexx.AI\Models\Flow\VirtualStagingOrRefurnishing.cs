using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Proptexx.AI.Models.CV;
using Proptexx.AI.Models.Gen;
using Proptexx.AI.Services;
using Proptexx.Core.AI;
using Proptexx.Core.Http;
using Proptexx.Core.Redis;
using Proptexx.Core.Resilience;
using Proptexx.Core.Services;
using Proptexx.Core.Storage;

namespace Proptexx.AI.Models.Flow;

public class VirtualStagingOrRefurnishing : IModel
{
    private readonly IServiceProvider _services;
    private readonly IConfiguration _configuration;
    private readonly ILogger _logger;
    private readonly IGenerativeClient _generativeClient;
    private readonly IStorageService _storageService;
    private readonly WorkspaceDataHashStore _workspaceDataStore;
    private readonly IImageAssessmentClient _imageAssessmentService;
    private readonly IHostEnvironment? _env;
    private static long _randomRoomTypeCounter;

    private string _roomType = string.Empty;
    private string _archStyle = string.Empty;

    private const int RetryCount = 3;
    private const int DelaySeconds = 1;

    private string[] _v2FlowWorkspaces = [
        "eb874e8c-d576-4803-ab2d-c879efd4c666", // AVIV
    ];

    private string[] _qualityEvalWorkspaces = [
        "eb874e8c-d576-4803-ab2d-c879efd4c666", // AVIV
    ];

    public VirtualStagingOrRefurnishing(
        IServiceProvider services,
        IConfiguration configuration,
        ILoggerFactory loggerFactory,
        IGenerativeClient generativeClient,
        IStorageService storageService,
        WorkspaceDataHashStore workspaceDataStore,
        IImageAssessmentClient imageAssessmentService,
        IHostEnvironment? env = null)
    {
        _services = services;
        _configuration = configuration;
        _logger = loggerFactory.CreateLogger<VirtualStagingOrRefurnishing>();
        _generativeClient = generativeClient;
        _storageService = storageService;
        _workspaceDataStore = workspaceDataStore;
        _imageAssessmentService = imageAssessmentService;
        _env = env;
    }

    public async Task<ModelResponse> InferAsync(ModelContext context)
    {
        if (context.Payload is null)
        {
            throw new ApplicationException("Payload is empty");
        }

        var imageUrl = PayloadService.GetRequiredString(context.Payload, "imageUrl");
        _roomType = PayloadService.GetOptionalString(context.Payload, "room_type") ?? string.Empty;
        _archStyle = PayloadService.GetOptionalString(context.Payload, "architecture_style") ?? string.Empty;

        var assessment = await _imageAssessmentService.InspectImageAsync(imageUrl, context.CancellationToken);

        if (assessment.Width <= 300 || assessment.Height <= 300)
        {
            throw new ApplicationException("The image size is too small - require width and height greater than 300 pixels");
        }

        if (_v2FlowWorkspaces.Contains(context.WorkspaceId))
        {
            return await InvokeV2FlowAsync(context, imageUrl, assessment);
        }

        return await InvokeV1FlowAsync(context, imageUrl, assessment);
    }

    private async Task<ModelResponse> InvokeV1FlowAsync(ModelContext context, string imageUrl, ImageAssessment assessment)
    {
        var results = new List<ModelResponse>();
        VirtualStagingModelType modelType;
        string roomType;
        string[] archStyles;

        if (string.IsNullOrWhiteSpace(_roomType) || string.IsNullOrWhiteSpace(_archStyle))
        {
            // old flow
            var roomSceneModel = _services.GetModel<PreProcessorForRoomScene>();
            var roomSceneResponse = await PollyRetryExtensions.GetDefaultRetryPolicy(_logger, "VirtualStagingOrRefurnishing - InvokeV1FlowAsync - PreProcessorForRoomScene", RetryCount, DelaySeconds).ExecuteAsync(() =>
            {
                return roomSceneModel.InferAsync(context);
            });

            results.Add(roomSceneResponse);
            (modelType, roomType, archStyles) = VerifyAndDetermineRoomStyle(context.Payload, roomSceneResponse);
        }
        else
        {
            // new flow
            modelType = VirtualStagingModelType.GenerativeVirtualRefurnishing; // default to refurnishing
            roomType = _roomType.ToLower();
            archStyles = _archStyle.ToLower().Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
        }

        var objectsModel = _services.GetModel<PreProcessorForObjects>();
        var objectsResponse = await PollyRetryExtensions.GetDefaultRetryPolicy(_logger, "VirtualStagingOrRefurnishing - InvokeV1FlowAsync - PreProcessorForObjects", RetryCount, DelaySeconds).ExecuteAsync(() =>
        {
            return objectsModel.InferAsync(context);
        });

        var objectsResult = objectsResponse.GetRequiredResult<PreProcessorForObjects.Result>()
                            ?? throw new ApplicationException("Precheck object model returned empty result");

        results.Add(objectsResponse);
        VerifyObjectsResult(objectsResult);

        Dictionary<string, object?>? requestParams = null;
        Dictionary<string, object?>? responseParams = null;

        foreach (var archStyle in archStyles)
        {
            if (string.IsNullOrWhiteSpace(_roomType) || string.IsNullOrWhiteSpace(_archStyle))
                VerifyRoomAndStyleCombination(modelType, roomType, archStyle);
            else
                VerifyRoomAndStyleCombinationReq(roomType, archStyle);

            const int maxRetries = 1;
            for (var i = 0; i <= maxRetries; i++)
            {
                try
                {
                    GenerativeModel m = modelType == VirtualStagingModelType.GenerativeVirtualStaging
                        ? new GenerativeVirtualStaging(_generativeClient, _storageService, _configuration, _workspaceDataStore, _env)
                        : new GenerativeVirtualRefurnishing(_generativeClient, _storageService, _configuration, _workspaceDataStore, _env);

                    var ctx = new GenerativeModelContext
                    {
                        ImageUrl = imageUrl,
                        Base64Image = assessment.Base64String,
                        ArchitectureStyle = archStyle,
                        RoomType = roomType,
                        ItemId = context.ItemId,
                        WorkspaceId = context.WorkspaceId,
                        CancellationToken = context.CancellationToken,
                        Payload = context.Payload
                    };

                    var response = await m.InferAsync(ctx);
                    requestParams = response.RequestParams;
                    responseParams = response.ResponseParams;

                    await CallPostChecksAsync(
                        ctx.WorkspaceId,
                        response.OutputImageUrl,
                        response.Base64Image,
                        response.MimeType,
                        roomType,
                        context.CancellationToken);

                    var qualityScore = "Undetected";

                    // Provide quality score for AVIV (and proptexx)
                    string[] qualityEvalWorkspaces = [
                        "eb874e8c-d576-4803-ab2d-c879efd4c666", // AVIV
                    ];

                    if (qualityEvalWorkspaces.Contains(ctx.WorkspaceId))
                    {
                        try
                        {
                            var qualityModel = _services.GetModel<ImageQualityEvaluation>();
                            var qualityPayload = JsonDocument.Parse($$"""
                                                                      {
                                                                        "image1": "{{imageUrl}}",
                                                                        "image2": "{{response.OutputImageUrl}}"
                                                                      }
                                                                      """);

                            var qualityResponse = await PollyRetryExtensions.GetDefaultRetryPolicy(_logger, "VirtualStagingOrRefurnishing - InvokeV1FlowAsync - ImageQualityEvaluation", RetryCount, DelaySeconds).ExecuteAsync(() =>
                            {
                                return qualityModel.InferAsync(new ModelContext
                                {
                                    WorkspaceId = ctx.WorkspaceId,
                                    ItemId = context.ItemId,
                                    Payload = qualityPayload,
                                    CancellationToken = context.CancellationToken
                                });
                            });

                            if (qualityResponse.Document is null)
                            {
                                throw new Exception("Unable to parse quality score");
                            }

                            qualityScore = PayloadService.GetOptionalString(qualityResponse.Document, "total_score_interpretation");
                        }
                        catch (Exception e)
                        {
                            // ignored
                        }
                    }

                    results.Add(new ModelResponse
                    {
                        Document = JsonDocument.Parse($$"""
                                                        {
                                                          "qualityScore": "{{qualityScore}}"
                                                        }
                                                        """)
                    });

                    results.Add(response);
                    break;
                }
                catch (Exception e)
                {
                    if (i == maxRetries)
                    {
                        var msg = e switch
                        {
                            ApplicationException ae => ae.Message,
                            JsonException => "Internal model problem",
                            _ => "Unable to produce a satisfying result"
                        };

                        throw new ApplicationException(msg, e);
                    }
                }
            }
        }

        var result = await JsonDocumentService.MergeResponsesAsync(results);

        return new VirtualStagingOrRefurnishingResponse
        {
            Document = result,
            RequestParams = requestParams,
            ResponseParams = responseParams
        };
    }

    private async Task<ModelResponse> InvokeV2FlowAsync(ModelContext context, string imageUrl, ImageAssessment assessment)
    {
        var results = new List<ModelResponse>();
        var roomSceneModel = _services.GetModel<PreProcessorForRoomScene>();
        var roomSceneResponse = await PollyRetryExtensions.GetDefaultRetryPolicy(_logger, "VirtualStagingOrRefurnishing - InvokeV1FlowAsync - PreProcessorForRoomScene", RetryCount, DelaySeconds).ExecuteAsync(() =>
        {
            return roomSceneModel.InferAsync(context);
        });
        results.Add(roomSceneResponse);
        var (modelType, roomType, archStyles) = VerifyAndDetermineRoomStyle(context.Payload, roomSceneResponse);

        var objectsModel = _services.GetModel<PreProcessorForObjectsV2>();
        var objectsResponse = await PollyRetryExtensions.GetDefaultRetryPolicy(_logger, "VirtualStagingOrRefurnishing - InvokeV1FlowAsync - PreProcessorForObjectsV2", RetryCount, DelaySeconds)
            .ExecuteAsync(() => { return objectsModel.InferAsync(context); });
        var objectsResult = objectsResponse.GetRequiredResult<PreProcessorForObjectsV2.Result>()
                            ?? throw new ApplicationException("Precheck object model returned empty result");
        results.Add(objectsResponse);
        VerifyObjectsResult(objectsResult);

        Dictionary<string, object?>? requestParams = null;
        Dictionary<string, object?>? responseParams = null;

        foreach (var archStyle in archStyles)
        {
            VerifyRoomAndStyleCombination(modelType, roomType, archStyle);

            const int maxRetries = 1;
            for (var i = 0; i <= maxRetries; i++)
            {
                try
                {
                    GenerativeModel m = modelType == VirtualStagingModelType.GenerativeVirtualStaging
                        ? new GenerativeVirtualStaging(_generativeClient, _storageService, _configuration, _workspaceDataStore, _env)
                        : new GenerativeVirtualRefurnishing(_generativeClient, _storageService, _configuration, _workspaceDataStore, _env);

                    var ctx = new GenerativeModelContext
                    {
                        ImageUrl = imageUrl,
                        Base64Image = assessment.Base64String,
                        ArchitectureStyle = archStyle,
                        RoomType = roomType,
                        ItemId = context.ItemId,
                        WorkspaceId = context.WorkspaceId,
                        CancellationToken = context.CancellationToken,
                        Payload = context.Payload
                    };

                    var response = await m.InferAsync(ctx);
                    requestParams = response.RequestParams;
                    responseParams = response.ResponseParams;

                    await CallPostChecksAsync(
                        ctx.WorkspaceId,
                        response.OutputImageUrl,
                        response.Base64Image,
                        response.MimeType,
                        roomType,
                        context.CancellationToken);

                    var qualityScore = "Undetected";

                    // Provide quality score for AVIV (and proptexx)
                    string[] qualityEvalWorkspaces = [
                        "eb874e8c-d576-4803-ab2d-c879efd4c666", // AVIV
                    ];

                    if (qualityEvalWorkspaces.Contains(ctx.WorkspaceId))
                    {
                        try
                        {
                            var qualityModel = _services.GetModel<ImageQualityEvaluation>();
                            var qualityPayload = JsonDocument.Parse($$"""
                                                                      {
                                                                        "image1": "{{imageUrl}}",
                                                                        "image2": "{{response.OutputImageUrl}}"
                                                                      }
                                                                      """);

                            var qualityResponse = await PollyRetryExtensions.GetDefaultRetryPolicy(_logger, "VirtualStagingOrRefurnishing - InvokeV1FlowAsync - ImageQualityEvaluation", RetryCount, DelaySeconds).ExecuteAsync(() =>
                            {
                                return qualityModel.InferAsync(new ModelContext
                                {
                                    WorkspaceId = ctx.WorkspaceId,
                                    ItemId = context.ItemId,
                                    Payload = qualityPayload,
                                    CancellationToken = context.CancellationToken
                                });
                            });


                            if (qualityResponse.Document is null)
                            {
                                throw new Exception("Unable to parse quality score");
                            }

                            qualityScore = PayloadService.GetOptionalString(qualityResponse.Document, "total_score_interpretation");
                        }
                        catch (Exception e)
                        {
                            // ignored
                        }
                    }

                    results.Add(new ModelResponse
                    {
                        Document = JsonDocument.Parse($$"""
                                                        {
                                                          "qualityScore": "{{qualityScore}}"
                                                        }
                                                        """)
                    });

                    results.Add(response);
                    break;
                }
                catch (Exception e)
                {
                    if (i == maxRetries)
                    {
                        var msg = e switch
                        {
                            ApplicationException ae => ae.Message,
                            JsonException => "Internal model problem",
                            _ => "Unable to produce a satisfying result"
                        };

                        throw new ApplicationException(msg, e);
                    }
                }
            }
        }

        var result = await JsonDocumentService.MergeResponsesAsync(results);

        return new VirtualStagingOrRefurnishingResponse
        {
            Document = result,
            RequestParams = requestParams,
            ResponseParams = responseParams
        };
    }

    private static void VerifyObjectsResult(PreProcessorForObjects.Result result)
    {
        if (result.HasPerson) throw new ApplicationException("Image contains a person");
        if (result.HasRealAnimal) throw new ApplicationException("Image contains an animal");
        if (result.HasFood) throw new ApplicationException("Image contains food");
        if (result.HasMultipleImages) throw new ApplicationException("Two or more frames detected within the image");
    }

    private static void VerifyObjectsResult(PreProcessorForObjectsV2.Result result)
    {
        if (result.HasPerson) throw new ApplicationException("Image contains a person");
        if (result.HasRealAnimal) throw new ApplicationException("Image contains an animal");
        if (result.HasFood) throw new ApplicationException("Image contains food");
        if (result.HasMultipleImages) throw new ApplicationException("Two or more frames detected within the image");
        if (result.HasBlurryArea) throw new ApplicationException("Image has a blurry area");
        // if (result.HasDominantObject) throw new ApplicationException("Image contains a dominant object");
        if (result.HasLittleSpace) throw new ApplicationException("Image has too narrow spacing");
        if (result.HasCloseUpObject) throw new ApplicationException("Image contains a close up object");
        // if (result.HasUnorganizedSpace) throw new ApplicationException("Image has unorganized space");
    }

    private static (VirtualStagingModelType modelType, string roomType, string[] archStyles) VerifyAndDetermineRoomStyle(
        JsonDocument? payload, ModelResponse modelResponse)
    {
        var cvRoomScene = modelResponse.GetRequiredResult<PreProcessorForRoomScene.Result>()
                          ?? throw new ApplicationException("Precheck room scene model returned empty result");

        if (!IsIndoor(cvRoomScene)) throw new ApplicationException("Must be an indoor image");

        var roomType = GetRoomType(cvRoomScene, payload);
        var archStyles = GetArchStyles(cvRoomScene, payload);
        var isLivingRoom = roomType == "living room";
        var isBedroom = roomType == "bedroom";
        var isKitchen = roomType == "kitchen";
        var isBathroom = roomType == "bathroom";

        VirtualStagingModelType modelType;
        if (cvRoomScene.IsEmptyRoom && (isLivingRoom || isBedroom))
        {
            modelType = VirtualStagingModelType.GenerativeVirtualStaging;
        }
        else if (!cvRoomScene.IsEmptyRoom && (isLivingRoom || isBedroom))
        {
            modelType = VirtualStagingModelType.GenerativeVirtualRefurnishing;
        }
        else if (!cvRoomScene.IsEmptyRoom && (isBathroom || isKitchen))
        {
            modelType = VirtualStagingModelType.GenerativeVirtualRefurnishing;
        }
        else
        {
            throw new ApplicationException("Generative models does not support this type of image");
        }

        return (modelType, roomType, archStyles);
    }

    private static bool IsIndoor(PreProcessorForRoomScene.Result cvRoomScene)
    {
        if (cvRoomScene.SceneTypes.Length <= 0) return false;
        return cvRoomScene.SceneTypes[0].ToLowerInvariant() is "indoor";
    }

    private static string[] GetArchStyles(PreProcessorForRoomScene.Result cvRoomScene, JsonDocument? payload)
    {
        string[] result = ["modern"];
        if (payload is not null)
        {
            var tempStyle = PayloadService.GetOptionalElement(payload, "architectureStyle");
            if (tempStyle.HasValue)
            {
                if (tempStyle.Value.ValueKind == JsonValueKind.Array)
                {
                    var array = tempStyle.Value.EnumerateArray().ToArray();

                    result = new string[array.Length];
                    for (var i = 0; i < array.Length; i++)
                    {
                        var tempStyleStr = array[i].GetString();
                        if (!string.IsNullOrWhiteSpace(tempStyleStr))
                        {
                            result[i] = tempStyleStr;
                        }
                    }
                }
                else
                {
                    var tempStyleStr = tempStyle.Value.GetString();
                    if (!string.IsNullOrWhiteSpace(tempStyleStr))
                    {
                        result[0] = tempStyleStr;
                    }
                }
            }
        }

        return result;
    }

    private static string GetRoomType(PreProcessorForRoomScene.Result cvRoomScene, JsonDocument? payload)
    {
        string? result = null;
        var roomTypes = cvRoomScene.RoomTypes.OrderByDescending(x => x.Value).ToList();
        var prioritizedRoomTypes = new[] { "living room", "bedroom", "kitchen", "bathroom" };

        for (var i = 0; i < roomTypes.Count; i++)
        {
            foreach (var type in prioritizedRoomTypes)
            {
                if (roomTypes[i].Key.Equals(type, StringComparison.OrdinalIgnoreCase) && roomTypes[i].Value > 0.2f)
                {
                    result ??= type;
                    break;
                }
            }

            if (result != null) break;
        }

        var roomKeys = cvRoomScene.RoomTypes.Keys;
        if (roomKeys.Contains("living room", StringComparer.OrdinalIgnoreCase)
            && roomKeys.Contains("bedroom", StringComparer.OrdinalIgnoreCase))
        {
            result = Interlocked.Increment(ref _randomRoomTypeCounter) % 2 == 0 ? "living room" : "bedroom";
        }

        if (payload is not null)
        {
            var providedRoomType = PayloadService.GetOptionalString(payload, "roomType");
            if (!string.IsNullOrWhiteSpace(providedRoomType))
            {
                if (result is "living room" or "bedroom" && providedRoomType is "living room" or "bedroom")
                {
                    result = providedRoomType;
                }
            }
        }

        return result ?? throw new ApplicationException("Room type not detected as either living room, bedroom, bathroom or kitchen");
    }

    private static void VerifyRoomAndStyleCombination(VirtualStagingModelType modelType, string requestedRoomType, string requestedStyle)
    {
        string modelName;
        Dictionary<string, string[]> dict;
        if (modelType == VirtualStagingModelType.GenerativeVirtualStaging)
        {
            modelName = "virtual staging";
            dict = new Dictionary<string, string[]>
            {
                // ["living room"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "wooden", "industrial", "italian"],
                // ["bedroom"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "wooden", "industrial", "italian"]
                ["living room"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "wooden", "industrial", "italian", "traditional"],
                ["bedroom"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "wooden", "industrial", "italian", "traditional"],
                ["bathroom"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "wooden", "industrial", "italian", "traditional"],
                ["kitchen"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "wooden", "industrial", "italian", "traditional"]
            };
        }
        else
        {
            modelName = "virtual refurnishing";
            dict = new Dictionary<string, string[]>
            {
                // ["living room"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "industrial", "italian", "traditional", "wooden"],
                // ["bedroom"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "industrial", "italian", "traditional", "wooden"],
                // ["bathroom"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "industrial", "italian", "traditional"],
                // ["kitchen"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "industrial", "italian", "traditional"]
                ["living room"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "wooden", "industrial", "italian", "traditional"],
                ["bedroom"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "wooden", "industrial", "italian", "traditional"],
                ["bathroom"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "wooden", "industrial", "italian", "traditional"],
                ["kitchen"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "wooden", "industrial", "italian", "traditional"]
            };
        }

        if (!dict.TryGetValue(requestedRoomType, out var styles))
        {
            throw new ApplicationException($"The requested room type `{requestedRoomType}` is not supported for {modelName}");
        }

        if (!styles.Contains(requestedStyle))
        {
            throw new ApplicationException($"The requested style '{requestedStyle}' is not supported for {modelName} of room type `{requestedRoomType}`");
        }
    }

    private static void VerifyRoomAndStyleCombinationReq(
        string requestedRoomType,
        string requestedStyle)
    {
        string modelName;
        Dictionary<string, string[]> dict;
        modelName = "virtual refurnishing";
        dict = new Dictionary<string, string[]>
        {
            // ["bathroom"] = ["modern", "traditional", "countryside", "coastal", "contemporary", "italian", "industrial", "scandinavian"],
            // ["kitchen"] = ["modern", "traditional", "countryside", "coastal", "contemporary", "italian", "industrial", "scandinavian"]
            ["living room"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "wooden", "industrial", "italian", "traditional"],
            ["bedroom"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "wooden", "industrial", "italian", "traditional"],
            ["bathroom"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "wooden", "industrial", "italian", "traditional"],
            ["kitchen"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "wooden", "industrial", "italian", "traditional"]
        };

        if (!dict.TryGetValue(requestedRoomType, out var styles))
        {
            throw new ApplicationException($"The requested room type `{requestedRoomType}` is not supported for {modelName}");
        }

        if (!styles.Contains(requestedStyle))
        {
            throw new ApplicationException($"The requested style '{requestedStyle}' is not supported for {modelName} of room type `{requestedRoomType}`");
        }
    }

    private async Task CallPostChecksAsync(string workspaceId, string imageUrl, string base64Image, string mimeType, string requestedRoomType, CancellationToken cancellationToken)
    {
        var model = _services.GetModel<PostProcessor>();

        await PollyRetryExtensions.GetDefaultRetryPolicy(_logger, $"StoreProcess", RetryCount, DelaySeconds).ExecuteAsync(async () =>
        {
            var response = await PollyRetryExtensions.GetDefaultRetryPolicy(_logger, $"StoreProcess", RetryCount, DelaySeconds).ExecuteAsync(() =>
             {
                 return model.InferAsync(workspaceId, imageUrl, base64Image, mimeType, cancellationToken);
             });

            var postCvResult = response.GetRequiredResult<PostProcessor.Result>() ?? throw new ApplicationException("Post-check failed");
            if (postCvResult.HasRealAnimal is not false) throw new ApplicationException($"The output image contained a real animal {imageUrl}");
            if (postCvResult.HasHorrorPoster is not false) throw new ApplicationException($"The output image contained a horror poster {imageUrl}");
            if (postCvResult.HasPerson is not false) throw new ApplicationException($"The output image contained a person {imageUrl}");
            if (postCvResult.HasAbnormalSize is not false) throw new ApplicationException($"The output image had an abnormal size {imageUrl}");

            if (requestedRoomType.Equals("living room", StringComparison.OrdinalIgnoreCase)
                 && postCvResult.IsEmptyLivingRoom is true)
            {
                throw new ApplicationException($"The output image is an empty living room {imageUrl}");
            }

            if (requestedRoomType.Equals("bedroom", StringComparison.OrdinalIgnoreCase)
                 && postCvResult.IsEmptyBedroom is true)
            {
                throw new ApplicationException($"The output image is an empty bedroom {imageUrl}");
            }
        });

    }
}
