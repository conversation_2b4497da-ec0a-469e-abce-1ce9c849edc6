using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class AccountEmail : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; } = Guid.NewGuid();

    public required Guid AccountId { get; init; }

    public required string Email { get; set; }

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public DateTime? VerifiedAt { get; private set; }

    public string GetDbRef() => "core.account_email";

    public AccountEmail Update(string email)
    {
        this.Email = email;
        this.VerifiedAt = null;
        return this;
    }

    public AccountEmail Verify()
    {
        this.VerifiedAt = DateTime.UtcNow;
        return this;
    }
}