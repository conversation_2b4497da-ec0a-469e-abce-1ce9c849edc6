using System.Text.Json.Serialization;

namespace Proptexx.Core.Entities.Outseta
{
    public class Account
    {
        public string? Uid { get; set; }
        public string? Name { get; set; }
        public MailingAddress? MailingAddress { get; set; }
        // Renamed from AccountStageValue to AccountStage to match payload
        public int? AccountStage { get; set; } 
        // Removed LatestSubscriptionUid as it's covered by LatestSubscription object
        public DateTime? Created { get; set; }
        public DateTime? Updated { get; set; }
        // Renamed from People to PersonAccount to match payload and type
        public List<PersonAccount>? PersonAccount { get; set; }

        // New properties from payload:
        public string? InvoiceNotes { get; set; }
        public bool? IsDemo { get; set; }
        public List<object>? StripeInvoices { get; set; } // Placeholder - define if structure is known
        public List<object>? StripePaymentMethods { get; set; } // Placeholder
        public List<object>? StripeSubscriptions { get; set; } // Placeholder
        public List<Subscription>? Subscriptions { get; set; }
        public List<object>? Deals { get; set; } // Placeholder
        public List<object>? TaxIds { get; set; } // Placeholder
        public string? TaxStatus { get; set; }
        public string? AccountStageLabel { get; set; }
        public Subscription? CurrentSubscription { get; set; }
        public bool? HasLoggedIn { get; set; }
        public Subscription? LatestSubscription { get; set; }
        public decimal? LifetimeRevenue { get; set; }
        public Person? PrimaryContact { get; set; }
        public bool? TaxIdIsInvalid { get; set; }
        public bool? IsLivemode { get; set; }
        public bool? SchemaLessDataLoaded { get; set; }
        public ActivityEventData? ActivityEventData { get; set; }
        [JsonPropertyName("_objectType")]
        public string? ObjectType { get; set; }
        public string? StoreUrl { get; set; }
        public string? Platform { get; set; }
        public int? Viewers { get; set; } // Made nullable as it might not always be present
        public string? OutsetaAccountId { get; set; } // Added OutsetaAccountId
    }

    public class AccountsResponse
    {
        public Metadata? Metadata { get; set; }
        public List<Account>? Items { get; set; }
    }
}
