using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.System;

public sealed class GetAccounts : IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        context.User.EnsureRootAccess();
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var result = await npgsql.QueryAsync<AccountModel>(AccountModel.Sql);
        return result;
    }

    private sealed class AccountModel
    {
        public required Guid Id { get; init; }

        public required string FirstName { get; init; }

        public required string FamilyName { get; init; }

        public DateTime MemberSince { get; init; }

        public string? Emails { get; init; }

        public static string Sql => @"
            select a.id,
                   a.first_name,
                   a.family_name,
                   a.created_at as member_since,
                   string_agg(ae.email, ',') as emails
            from core.account a
            left outer join core.account_email ae on a.id = ae.account_id
            group by a.id, a.first_name, a.family_name, a.created_at
            order by a.first_name, a.family_name;
        ";
    }
}