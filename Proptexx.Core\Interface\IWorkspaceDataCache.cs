using StackExchange.Redis;

namespace Proptexx.Core.Interface;
//
// public interface IWorkspaceDataCache
// {
//     Task<string?> GetValueAsync(Guid workspaceId, string dataKey);
//     
//     Task<IDictionary<string, string?>> GetValuesAsync(Guid workspaceId, params string[] dataKeys);
//
//     Task SetValueAsync(Guid workspaceId, IEnumerable<(string dataKey, string? dataValue)> entries);
//
//     Task SetValuesAsync(IEnumerable<(Guid workspaceId, string dataKey, string? dataValue)> values);
//
//     Task ClearAsync();
// }
//
// public class WorkspaceDataCache : IWorkspaceDataCache
// {
//     private const string KeyPrefix = "workspace_data";
//     private readonly IDatabase _redis;
//
//     public WorkspaceDataCache(IConnectionMultiplexer connectionMultiplexer)
//     {
//         _redis = connectionMultiplexer.GetDatabase();
//     }
//
//     public async Task<string?> GetValueAsync(Guid workspaceId, string dataKey)
//     {
//         var key = GetKey(workspaceId);
//         var value = await _redis.HashGetAsync(key, dataKey);
//         return RedisValueToNullableString(value);
//     }
//
//     public async Task<IDictionary<string, string?>> GetValuesAsync(Guid workspaceId, params string[] dataKeys)
//     {
//         var key = GetKey(workspaceId);
//         var keys = dataKeys.Select(x => (RedisValue)x).ToArray();
//         var values = await _redis.HashGetAsync(key, keys);
//
//         return dataKeys
//             .Zip(values, (k, v) => new { Key = k, Value = RedisValueToNullableString(v) })
//             .ToDictionary(x => x.Key, x => x.Value);
//     }
//
//     public async Task SetValueAsync(Guid workspaceId, IEnumerable<(string dataKey, string? dataValue)> entries)
//     {
//         var key = GetKey(workspaceId);
//         await _redis.SetRemoveAsync($"{KeyPrefix}:keys", key);
//
//         var fields = entries
//             .Where(x => !string.IsNullOrWhiteSpace(x.dataValue))
//             .Select(x => new HashEntry(x.dataKey, x.dataValue))
//             .ToArray();
//
//         await _redis.HashSetAsync(key, fields);
//         await _redis.SetAddAsync($"{KeyPrefix}:keys", key);
//     }
//
//     public async Task SetValuesAsync(IEnumerable<(Guid workspaceId, string dataKey, string? dataValue)> values)
//     {
//         foreach (var grouping in values.GroupBy(x => x.workspaceId))
//         {
//             var entries = grouping
//                 .Select(x => (x.dataKey, x.dataValue))
//                 .ToArray();
//
//             if (entries.Length <= 0) continue;
//             await SetValueAsync(grouping.Key, entries);
//         }
//     }
//
//     public async Task ClearAsync()
//     {
//         var members = await _redis.SetMembersAsync($"{KeyPrefix}:keys");
//         var keys = members.Select(x => new RedisKey(x.ToString())).ToArray();
//         await _redis.KeyDeleteAsync(keys);
//     }
//     
//     private static string GetKey(Guid workspaceId) => $"{KeyPrefix}:{workspaceId}";
//
//     private static string? RedisValueToNullableString(RedisValue value) 
//         => value is { HasValue: true, IsNullOrEmpty: false } ? value.ToString() : null;
// }