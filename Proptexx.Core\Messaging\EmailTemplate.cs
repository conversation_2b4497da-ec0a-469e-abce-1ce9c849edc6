using System.Text;
using Proptexx.Core.DTOs.Email;

namespace Proptexx.Core.Messaging;

public static class EmailTemplate
{
    public static (string subject, string body) OnWidgetSignupToLead(string otp)
    {
        const string subject = "Welcome to Proptexx AI";
        var body = WrapToHtml($"""
                           <p>Hi there,</p>
                           <p>Welcome aboard! We're thrilled to have you join the Proptexx AI community, where we blend the wonders of artificial intelligence with property technology to deliver unparalleled accuracy, speed, and assurance</p>
                           <p>Your OTP is: <strong>{otp}</strong></p>
                           <p>If you didn't request this or believe there's been a mistake, please contact our support team immediately at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
                           <p>Kind regards</p>
                           <p>The PropTexx Team</p>
                           """);
        return (subject, body);
    }

    public static (string subject, string body) OnOtpRequested(string otp)
    {
        const string subject = "Proptexx AI - Authentication code";
        var body = WrapToHtml($"""
                           <p>Hi there,</p>
                           <p>Your authentication code is: <strong>{otp}</strong></p>
                           <p>If you didn't request this or believe there's been a mistake, please contact our support team immediately at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
                           <p>Kind regards</p>
                           <p>The PropTexx Team</p>
                           """);
        return (subject, body);
    }

    public static (string subject, string body) OnPortalAccountRegistration(string fullName)
    {
        const string subject = "Welcome to Proptexx AI";
        var body = WrapToHtml($"""
                           <p>Dear {fullName},</p>
                           <p>Thank you for signing up with Proptexx! We're excited to have you on board and look forward to helping you optimize your property management experience.</p>
                           <p>Your account has been successfully created, and you are all set to begin. Here are a few details to get you started:</p>
                           <ul>
                               <li>Dashboard: Access your personalized dashboard at <a href="https://portal.proptexx.com">Proptexx Dashboard</a>.</li>
                               <li>Support: Should you need any assistance or have questions, our support team is here to help. Contact us anytime at <a href="mailto:<EMAIL>"><EMAIL></a>.</li>
                           </ul>
                           <p>We're committed to providing you with the tools you need to succeed and are eager to see what you will achieve. Dive in, explore, and let us know how we can enhance your experience.</p>
                           <p>Warm regards,</p>
                           <p>The Proptexx Team</p>
                           """);
        return (subject, body);
    }

    public static (string subject, string body) OnPartnerAccountRegistration(string partnerName, string? password, bool isWidget, string? stripePaymentLink)
    {
        const string subject = "Your Proptexx account is ready";

        var str = new StringBuilder("<h1>Welcome to Proptexx!</h1>");
        str.AppendLine($"<p>We are pleased to inform you that your account has been successfully created following a request from {partnerName}.</p>");

        if (!string.IsNullOrWhiteSpace(stripePaymentLink))
        {
            str.AppendLine($"<p>To complete the order {(isWidget ? "of our AI widget" : "")} you can proceed by clicking <a href='{stripePaymentLink}'>on the Stripe checkout link here</a></p>");
        }

        str.AppendLine($"<p>If you did not initiate this request or if you are unaware of this action, please ignore this email");
        str.AppendLine("or contact our support team for immediate assistance by email <a href='mailto:<EMAIL>'><EMAIL></a>.</p>");
        str.AppendLine("<p>We hope you find our AI services helpful. Welcome aboard!</p>");
        return (subject, str.ToString());
    }

    public static (string subject, string body) OnWidgetSignupToAdmin(string? listingUrl, string leadName, string leadEmail)
    {
        const string subject = "New lead from your AI widget";

        var str = new StringBuilder(@"
            <p>Hi there!</p>
            <p>We are excited to inform you that a new user has just signed up on your AI widget! Here are the details:</p>
        ");

        if (string.IsNullOrWhiteSpace(listingUrl))
        {
            str.Append($@"
                <p><strong>Listing URL</strong></p>
                <p>{listingUrl}</p>
            ");
        }

        str.Append($@"
            <br>
            <p><strong>User Information</strong></p>
            <ul>
                <li>Name: {leadName}</li>
                <li>Email: {leadEmail}</li>
            </ul>
            <p>This new signup reflects the growing interest and engagement with your AI widget. We encourage you to check the user's activity and interactions to better understand their needs and enhance their experience with your service. Full details can be found within your Portal.</p>
            <p>If it's your first time logging into the portal, you can use your email address and request a new password.</p>
            <p>If you have any questions or need further assistance, please do not hesitate to reach out to us. We are here to support you and ensure the success of our partnership.</p>
            <p>Thank you for choosing our service and for being a valued member of the PropTexx community.</p>
            <p>Best regards,</p>
            <p>The PropTexx Team</p>
        ");

        var body = WrapToHtml(str.ToString());
        return (subject, body);
    }    
    public static (string subject, string body) OnOutsetaAccountRegistration(OutSetaEmailData emailData)
    {
        var subject = "Getting Started - Your Widget Install Guide";
        
        // Generate snippet directly without service dependency
        var snippet = GenerateWidgetSnippet(emailData.ApiKey);
        var snippetHtml = System.Web.HttpUtility.HtmlEncode(snippet);

        var content = $@"<div style='text-align: center; margin-bottom: 30px;'>
                <img src='https://ci3.googleusercontent.com/meips/ADKq_NZpIyThDAEkXAFqlndhMZWYjJsdvzpfZgUEJDuXkSwrgDIXUYzPGw7CwLVtBYQ6Ihs3i2EsVQOP0G-tz6r0BOXkYxGA0OLZTUuF_L2G1sO_y6iytA9YorsYzgOUO7BsEtib0jLLkmwfJSQst7_GnYbOfvAvXUq2q2MJc7FY0DQ=s0-d-e1-ft#https://outseta-production-template-images.s3.us-east-1.amazonaws.com/*************-Design+sans+titre.png' alt='PropTexx' style='width: 60px; height: 60px;' />
            </div>
              <div style='text-align: left; max-width: 600px; margin: 0 auto;'>
                <h1>Getting Started - Your Widget Install Guide</h1>
                
                <p>Hi {emailData.FirstName},</p>
                
                <p>Thanks for signing up for PropTexx!</p>
                
                <p>We're now setting up your personalized installation, which includes:</p>
                <ul>
                    <li>Activating your API key</li>
                    <li>Configuring your widget for the right store pages</li>
                    <li>Ensuring a smooth launch</li>
                </ul>            
            </div>              
              
              <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px auto; border-left: 4px solid #a53620; max-width: 600px; text-align: left;'>
                <p><strong>Workspace:</strong> {emailData.WorkspaceName}</p>
                <p><strong>Workspace ID:</strong> <code style='background: #e9ecef; padding: 4px 8px; border-radius: 4px; font-family: monospace; color: #000000;'>{emailData.WorkspaceId}</code></p>
                <p><strong>API Key:</strong> <code style='background: #e9ecef; padding: 4px 8px; border-radius: 4px; font-family: monospace; color: #000000; word-break: break-all;'>{emailData.ApiKey}</code></p>
                <p style='font-size: 11px; color: #dc3545; margin: 10px 0 0 0; font-weight: bold;'>
                    ⚠️ Keep this API key secure - treat it like a password
                </p>
            </div>

              <div style='text-align: left; max-width: 600px; margin: 20px auto;'>
                <h2 style='color: #a53620; margin-bottom: 15px;'>🚀 Quick Install - Copy & Paste Ready</h2>
                <p>Copy the code below and paste it into your website's HTML (preferably in the &lt;head&gt; section or before the closing &lt;/body&gt; tag):</p>
                
                <div style='background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; margin: 15px 0; position: relative;'>
                    <div style='background: #e9ecef; padding: 8px 15px; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0; font-weight: bold; color: #495057; font-size: 14px;'>
                        📋 Installation Code
                    </div>
                    <div style='padding: 15px; overflow-x: auto;'>
                        <pre style='margin: 0; white-space: pre-wrap; word-wrap: break-word; font-family: ""Courier New"", Courier, monospace; font-size: 12px; line-height: 1.4; color: #333;'>{snippetHtml}</pre>
                    </div>
                    <div style='background: #e7f3ff; padding: 8px 15px; border-top: 1px solid #dee2e6; border-radius: 0 0 8px 8px; font-size: 12px; color: #0c5460;'>
                        💡 <strong>Tip:</strong> Make sure to place the configuration script BEFORE the loader script for proper initialization.
                    </div>
                </div>
                
                <div style='background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 15px 0;'>
                    <h3 style='margin: 0 0 10px 0; color: #856404; font-size: 16px;'>📝 Next Steps:</h3>
                    <ol style='margin: 0; padding-left: 20px; color: #856404;'>
                        <li>Copy the installation code above</li>
                        <li>Paste it into your website's HTML</li>
                        <li>Test on your staging environment first</li>
                        <li>Deploy to production when ready</li>
                    </ol>
                </div>
            </div>

              <div style='text-align: left; max-width: 600px; margin: 0 auto;'>
                <p>You'll receive additional setup instructions and optimization tips shortly.<br>
                We aim to get you fully optimized within 12–24 hours.</p>
                
                <p>In the meantime, if you'd like to chat with our AI assistant about advanced configuration, you can get help here:</p>
                <p>👉 <a href='https://docsbot.ai/chat/tljRm3CVKRKdXXTgSmjL/2iNjhSs3BRYYNpdpWsL5' style='color: #a53620; text-decoration: underline;'>https://docsbot.ai/chat/tljRm3CVKRKdXXTgSmjL/2iNjhSs3BRYYNpdpWsL5</a></p>
                
                <p>Thanks for choosing PropTexx — we're excited to help you boost your conversions!</p>
                
                <p>— The PropTexx Team</p>
            </div>";
        return (subject, WrapToHtml(content));
    }

    /// <summary>
    /// Generates widget installation snippet for email templates
    /// </summary>
    private static string GenerateWidgetSnippet(string apiKey, string? baseUrl = null)
    {
        var widgetLoaderBaseUrl = baseUrl ?? "https://staging.widget.product.placement.proptexx.com/widget/";
        if (!widgetLoaderBaseUrl.EndsWith("/"))
        {
            widgetLoaderBaseUrl += "/";
        }
        
        var safeApiKey = System.Uri.EscapeDataString(apiKey);
        var loaderUrl = $"{widgetLoaderBaseUrl}loader.js?k={safeApiKey}";

        return $@"<!-- Proptexx Widget Start -->
                    <!-- 1. Add this script tag to your HTML's <head> or before the closing </body> tag. -->
                    <script id=""proptexx-widget"" src=""{loaderUrl}""></script>

                    <!-- 
                    2. Configure the widget by setting the window.proptexx object *before* the script tag above.
                    Example:-->
                    <script>
                    window.proptexx = {{
                        apikey: '{apiKey.Replace("'", "\\'")}', 
                        skipAuth: true, 
                        skipUrlRules: true, 
                        urlRules: [ 
                                // {{ type: 'prefix', pattern: '/products', enabled: true }},  
                                // {{ type: 'regex', pattern: '^/gallery/[^/]+$', enabled: true }}
                        ],
                    }};
            </script>

        <!-- Proptexx Widget End -->";
    }

    private static string WrapToHtml(string content)
    {
        var body = $$"""
                     <html>
                     <head>
                     <meta charset="UTF-8">
                     <meta name="viewport" content="width=device-width, initial-scale=1.0">                     
                     <style>
                         body {
                             font-family: Arial, sans-serif;
                             line-height: 1.6;
                             color: #333333;
                         }
                         h1 {
                             color: #a53620;
                         }                         
                         a {
                             color: #a53620;
                             text-decoration: none;
                         }</style>
                     </head>
                     <body>
                        <main style="max-width: 600px; margin: 0 auto; padding: 20px;">{{content}}</main>
                        <footer style="text-align: center; padding: 30px 20px; border-top: 1px solid #e9ecef; margin-top: 40px; color: #6c757d; font-size: 14px;">
                            <div style="margin-bottom: 15px;">
                                <strong>PropTexx</strong><br>
                                Intelligent Product Discovery for E-commerce<br>
                                <a href="https://proptexx.com" style="color: #a53620;">proptexx.com</a>
                            </div>
                            <div style="font-size: 12px; color: #adb5bd;">
                                © 2024 PropTexx. All rights reserved.<br>
                                <a href="mailto:<EMAIL>" style="color: #6c757d;">Contact Support</a> | 
                                <a href="#" style="color: #6c757d;">Unsubscribe</a>
                            </div>
                        </footer>
                     </body>
                     </html>
                     """;
        return body;
    }
}