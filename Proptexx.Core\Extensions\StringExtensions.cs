using System.Globalization;
using System.Text;

namespace Proptexx.Core.Extensions;

public static class StringExtensions
{
    public static string GetUriSlug(this string source, bool toLower)
    {
        if (string.IsNullOrWhiteSpace(source)) return source;

        var normalised = source.Normalize(NormalizationForm.FormKD);
        const int maxLen = 80;
        var len = normalised.Length;
        var prevDash = false;
        var sb = new StringBuilder(len);

        for (var i = 0; i < len; i++)
        {
            var c = normalised[i];
            if ((c >= 'a' && c <= 'z') || (c >= '0' && c <= '9'))
            {
                if (prevDash)
                {
                    sb.Append('-');
                    prevDash = false;
                }
                sb.Append(c);
            }
            else if (c >= 'A' && c <= 'Z')
            {
                if (prevDash)
                {
                    sb.Append('-');
                    prevDash = false;
                }

                // Tricky way to convert to lowercase
                if (toLower)
                    sb.Append((char)(c | 32));
                else
                    sb.Append(c);
            }
            else if (c == ' ' || c == ',' || c == '.' || c == '/' || c == '\\' || c == '-' || c == '_' || c == '=')
            {
                if (!prevDash && sb.Length > 0)
                {
                    prevDash = true;
                }
            }
            else
            {
                var swap = SlugConvertEdgeCases(c, toLower);
                if (swap != null)
                {
                    if (prevDash)
                    {
                        sb.Append('-');
                        prevDash = false;
                    }
                    sb.Append(swap);
                }
            }

            if (sb.Length == maxLen)
                break;
        }

        return sb.ToString();
    }

    public static string ToFirstCaseUpper(this string source, bool everyWord)
    {
        if (string.IsNullOrWhiteSpace(source)) return source;

        if (everyWord)
            return CultureInfo.InvariantCulture.TextInfo.ToTitleCase(source);

        char[] a = source.ToCharArray();
        a[0] = char.ToUpper(a[0]);
        return new string(a);
    }

    public static string ToFirstCaseLower(this string source, bool everyWord)
    {
        if (string.IsNullOrWhiteSpace(source)) return source;

        if (everyWord)
            return CultureInfo.InvariantCulture.TextInfo.ToTitleCase(source);

        var a = source.ToCharArray();
        a[0] = char.ToLower(a[0]);
        return new string(a);
    }

    public static string ToCamelCase(this string source)
    {
        var result = new StringBuilder();
        for (var i = 0; i < source.Length; i++)
        {
            var c = source[i];

            // Allow letters and digits through
            if (!char.IsLetterOrDigit(c)) continue;

            // Make uppercase if not the first char and the previous char was an underscore
            var isUpper = i > 0 && source[i - 1].Equals('_');

            // Append with correct casing
            result.Append(isUpper
                ? char.ToUpperInvariant(c)
                : char.ToLowerInvariant(c));
        }

        return result.ToString();
    }

    public static int UtfNumBits(this string source)
    {
        var bitsPerChar = "A"u8.ToArray().Length * 8;
        return source.Length * bitsPerChar;
    }

    private static string? SlugConvertEdgeCases(char c, bool toLower)
    {
        return c switch
        {
            'ı' => "i",
            'ł' => "l",
            'Ł' => toLower ? "l" : "L",
            'đ' => "d",
            'ß' => "ss",
            'ø' => "o",
            'Þ' => "th",
            _ => null
        };
    }

    public static string ToSnakeCase(this string source)
    {
        if (string.IsNullOrEmpty(source)) return source;

        var sb = new StringBuilder();

        for (var i = 0; i < source.Length; i++)
        {
            var currentChar = source[i];

            if (char.IsUpper(currentChar))
            {
                if (i > 0) sb.Append('_');
                sb.Append(char.ToLower(currentChar));
            }
            else if (char.IsLetterOrDigit(currentChar))
            {
                sb.Append(currentChar);
            }
        }

        return sb.ToString();
    }
}