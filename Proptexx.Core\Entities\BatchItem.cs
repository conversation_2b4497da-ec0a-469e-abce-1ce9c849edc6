using System.Text.Json;
using Proptexx.Core.Postgresql.Builder;

namespace Proptexx.Core.Entities;

public class BatchItem : IDbTable
{
    public Guid Id { get; init; } = Guid.NewGuid();
    
    public required Guid BatchId { get; init; }

    public int Status { get; set; }

    public required string Model { get; set; }

    public required JsonDocument Config { get; init; }

    public required string ItemHash { get; init; }

    public string? FileHash { get; set; }
    
    public string? MimeType { get; set; }
    
    public JsonDocument? Result { get; set; }
    
    public string? ErrorMessage { get; set; }

    public DateTime CreatedAt { get; init; } = DateTime.UtcNow;

    public DateTime? ProcessedAt { get; set; }
    
    public DateTime? CompletedAt { get; set; }

    public string GetDbRef() => "batching.batch_item";
}