using Proptexx.Core.DTOs;

namespace Proptexx.Core.Interface
{
    public interface ISubscriptionUsageService
    {
        /// <summary>
        /// Enhanced validation that checks subscription quota, expiry dates, and request type-specific rules
        /// </summary>
        Task<EnhancedValidationResult> ValidateRequestWithQuotaAndExpiryAsync(
            string apiKey,
            RequestClassification requestType,
            CancellationToken cancellationToken);

        /// <summary>
        /// Increments usage based on request type
        /// </summary>
        Task<bool> IncrementUsageForRequestTypeAsync(
            string apiKey,
            RequestClassification requestType,
            CancellationToken cancellationToken);
 
        /// <summary>
        /// Gets client secret ID from API key
        /// </summary>
        Task<Guid?> GetClientSecretIdFromApiKeyAsync(string apiKey, CancellationToken cancellationToken);

        /// <summary>
        /// Gets comprehensive subscription information
        /// </summary>
        Task<SubscriptionInfo?> GetSubscriptionInfoAsync(string apiKey, CancellationToken cancellationToken);
    }
}