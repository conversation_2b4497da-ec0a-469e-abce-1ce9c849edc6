namespace Proptexx.AI;

public static class ModelFactory
{
    private static readonly Dictionary<string, Type> Registry;

    static ModelFactory()
    {
        Registry = new Dictionary<string, Type>(StringComparer.OrdinalIgnoreCase);
    }

    public static void Add(string relativePath, Type modelType)
    {
        Registry.TryAdd(relativePath, modelType);
    }

    public static Type Get(string relativePath)
    {
        if (!Registry.TryGetValue(relativePath, out var modelType))
        {
            var similarModels = GetModelsContaining(relativePath.Split('/').LastOrDefault() ?? "").Take(5).ToList();
            var suggestion = similarModels.Any() 
                ? $" Similar models: [{string.Join(", ", similarModels)}]"
                : "";
            Console.WriteLine($"Model '{relativePath}' not found. Registered models: [{string.Join(", ", Registry.Keys)}].{suggestion}");
            throw new Exception($"Unable to find '{relativePath}' in ModelFactory registry.{suggestion}");
        }

        return modelType;
    }

    public static bool Has(string modelName)
    {
        return Registry.ContainsKey(modelName);
    }
    
    public static IEnumerable<string> GetAvailableModels()
    {
        return Registry.Keys.OrderBy(x => x);
    }
    
    public static IEnumerable<string> GetModelsContaining(string search)
    {
        return Registry.Keys
            .Where(x => x.Contains(search, StringComparison.OrdinalIgnoreCase))
            .OrderBy(x => x);
    }

    /// <summary>
    /// Diagnostic method to help troubleshoot model registration issues
    /// </summary>
    public static void PrintRegisteredModels()
    {
        Console.WriteLine("=== Registered Models ===");
        foreach (var kvp in Registry.OrderBy(x => x.Key))
        {
            Console.WriteLine($"{kvp.Key} -> {kvp.Value.FullName}");
        }
        Console.WriteLine($"Total: {Registry.Count} models");
    }

    /// <summary>
    /// Get models that match a specific pattern (useful for debugging)
    /// </summary>
    public static IEnumerable<string> GetModelsMatching(string pattern)
    {
        return Registry.Keys
            .Where(x => x.IndexOf(pattern, StringComparison.OrdinalIgnoreCase) >= 0)
            .OrderBy(x => x);
    }
}