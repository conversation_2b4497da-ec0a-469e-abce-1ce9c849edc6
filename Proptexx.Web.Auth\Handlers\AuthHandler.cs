using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;
using System.Text.Encodings.Web;
using System.Text.Json;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.JsonWebTokens;
using Proptexx.Core;
using Proptexx.Core.Auth;
using Proptexx.Core.Extensions;
using Proptexx.Core.Json;
using Proptexx.Core.Options;
using Proptexx.Web.Auth.Auth;

namespace Proptexx.Web.Auth.Handlers;

public sealed class AuthHandler
{
    private readonly ILogger<AuthHandler> _logger;
    private readonly IClientResolver _clientResolver;
    private readonly ISessionResolver _sessionResolver;
    private readonly IScopeResolverFactory _scopeResolverFactory;
    private readonly ProptexxOptions _proptexxOptions;

    private static readonly JsonSerializerOptions JsonSerializerOptions = new(JsonDefaults.JsonSerializerOptions)
    {
        Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping
    };

    public AuthHandler(
        ILogger<AuthHandler> logger, 
        IClientResolver clientResolver, 
        ISessionResolver sessionResolver,
        IScopeResolverFactory scopeResolverFactory,
        IOptions<ProptexxOptions> proptexxOptions)
    {
        _logger = logger;
        _clientResolver = clientResolver;
        _sessionResolver = sessionResolver;
        _scopeResolverFactory = scopeResolverFactory;
        _proptexxOptions = proptexxOptions.Value;
    }

    public async Task InvokeAsync(HttpContext httpContext)
    {
        try
        {
            var (scope, payload) = await ParseRequestScopes(httpContext);
            var sessionId = ParseSessionId(httpContext, _proptexxOptions);
            var identity = await ResolveIdentityAsync(httpContext, _proptexxOptions);
            httpContext.User = new ProptexxPrincipal(identity);

            var scopeData = ReadOnlyDictionary<string, object>.Empty;
            var scopeClaims = ReadOnlyDictionary<string, string>.Empty;
            
            if (scope is not null)
            {
                var resolver = _scopeResolverFactory.GetResolver(scope)
                               ?? throw new NullReferenceException("No such scope");

                var ctx = new ScopeContext(httpContext.User, payload);
                await resolver.ResolveAsync(ctx, httpContext.RequestAborted);
                scopeClaims = ctx.GetClaims();
                scopeData = ctx.GetData();
            }

            var claims = httpContext.User.Claims
                .ToDictionary(x => x.Type, x => x.Value);

            claims.MergeClaims(scopeClaims);

            if (httpContext.Request.Headers.TryGetValue("Client-Version", out var clientVersion))
            {
                claims["clientVersion"] = clientVersion.ToString();
            }

            var token = GenerateToken(_proptexxOptions, claims);
            await PersistSessionAsync(httpContext, _sessionResolver, _proptexxOptions, identity.IssueCookie is true, sessionId, claims);

            var response = new Dictionary<string, object>(scopeData) { ["$accessToken"] = token };
            await httpContext.Response.WriteAsJsonAsync(response, JsonSerializerOptions);
        }   
        catch (AuthException)
        {
            httpContext.Response.StatusCode = StatusCodes.Status400BadRequest;
        }
        catch (ApplicationException)
        {
            httpContext.Response.StatusCode = StatusCodes.Status400BadRequest;
        }
        catch (ValidationException)
        {
            httpContext.Response.StatusCode = StatusCodes.Status403Forbidden;
        }
        catch (UnauthorizedAccessException)
        {
            httpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
        }
        catch (Exception e)
        {
            _logger.LogError(e, nameof(AuthHandler));
            httpContext.Response.StatusCode = StatusCodes.Status500InternalServerError;
        }
    }

    private async Task<ProptexxIdentity> ResolveIdentityAsync(HttpContext httpContext, ProptexxOptions proptexxOptions)
    {
        string? apiKey = null;
        string? refreshToken = null;
        bool? issueCookie = null;

        if (proptexxOptions.Cookie is not null
            && httpContext.Request.Cookies.TryGetValue(proptexxOptions.Cookie.SessionRefreshToken, out refreshToken))
        {
            issueCookie = true;
        }

        var h = httpContext.Request.Headers;

        if (h.TryGetValue("Authorization", out var authorization))
        {
            var authTokenAr = authorization.ToString().Split();
            if (authTokenAr.Length != 2)
            {
                throw new UnauthorizedAccessException();
            }
            
            var key = authTokenAr[0];
            var value = authTokenAr[1];

            if (key.Equals("bearer", StringComparison.OrdinalIgnoreCase))
            {
                return ProptexxIdentity.FromBearer(proptexxOptions, value, issueCookie);
            }

            if (key.Equals("apiKey", StringComparison.OrdinalIgnoreCase))
            {
                apiKey = value;
            }
        }
        else if (h.TryGetValue("apiKey", out var apiToken))
        {
            apiKey = apiToken.ToString();
        }

        if (string.IsNullOrWhiteSpace(apiKey) 
            || apiKey.Equals("undefined", StringComparison.OrdinalIgnoreCase)
            || apiKey.Equals("null", StringComparison.OrdinalIgnoreCase))
        {
            throw new UnauthorizedAccessException();
        }

        var clientIdentifier = ApiKeyService.ParseApiKey(apiKey);

        Dictionary<string, string> claims;

        if (!string.IsNullOrWhiteSpace(refreshToken))
        {
            var session = await _sessionResolver.RetriveAsync(refreshToken)
                          ?? throw new UnauthorizedAccessException();

            issueCookie = true;
            claims = new Dictionary<string, string>(session.Claims)
            {
                [JwtRegisteredClaimNames.Jti] = httpContext.TraceIdentifier,
                [proptexxOptions.ClientIdentifier] = clientIdentifier,
                ["$sessionId"] = session.SessionId
            };
        }
        else
        {
            var clientResult = await _clientResolver.ResolveAsync(clientIdentifier, null)
                               ?? throw new UnauthorizedAccessException();

            issueCookie = clientResult.IssueCookie;
            claims = new Dictionary<string, string>(clientResult.AdditionalClaims)
            {
                [JwtRegisteredClaimNames.Jti] = httpContext.TraceIdentifier,
                [proptexxOptions.ClientIdentifier] = clientIdentifier,
                ["$sessionId"] = Guid.NewGuid().ToString()
            };
        }

        var identityClaims = claims.Select(x => new Claim(x.Key, x.Value));
        return new ProptexxIdentity(identityClaims, proptexxOptions.CallerIdentifier, issueCookie);
    }

    internal static string GenerateToken(ProptexxOptions proptexxOptions, Dictionary<string, string> claims)
    {
        var issuer = proptexxOptions.Issuer;
        var audience = proptexxOptions.Audience;
        var securityKey = proptexxOptions.GetSecurityKey();
        var accessTokenTtl = proptexxOptions.AccessTokenTtl;
        return AuthService.GenerateAccessToken(issuer, audience, securityKey, accessTokenTtl, claims);
    }

    private static string ParseSessionId(HttpContext httpContext, ProptexxOptions proptexxOptions)
    {
        if (proptexxOptions.Cookie is null 
            || !httpContext.Request.Cookies.TryGetValue(
                proptexxOptions.Cookie.SessionRefreshToken, out var sessionId))
        {
            sessionId = Guid.NewGuid().ToString();
        }

        return sessionId;
    }

    private static async Task<(string? scope, JsonDocument? payload)> ParseRequestScopes(HttpContext httpContext)
    {
        string? scope = null;
        JsonDocument? payload = null;

        if (httpContext.Request.RouteValues.Count > 0)
        {
            scope = httpContext.Request.RouteValues.First().Key;
            payload = await JsonDocument.ParseAsync(httpContext.Request.Body);
        }
        else
        {
            var body = await JsonSerializer.DeserializeAsync<AuthRequestPayload>(
                httpContext.Request.Body, JsonDefaults.JsonSerializerOptions);

            if (body is not null && body.Scopes.Count > 0)
            {
                (scope, payload) = body.Scopes.First();
            }
        }

        return (scope, payload);
    }

    internal static async Task PersistSessionAsync(HttpContext httpContext, ISessionResolver sessionResolver, ProptexxOptions proptexxOptions, bool? issueCookie, string sessionId, Dictionary<string, string> claims)
    {
        if (string.IsNullOrWhiteSpace(proptexxOptions.Cookie?.Domain)) return;
        if (issueCookie is false) return;
        if (issueCookie is null && !httpContext.Request.Headers.TryGetValue("X-ISSUE-COOKIE", out _)) return;

        var maxAge = TimeSpan.FromMinutes(proptexxOptions.Cookie.MaxAgeMinutes);
        var cookieOptions = proptexxOptions.Cookie.GetCookieOptions(maxAge);

        var session = new SessionPayload
        {
            SessionId = sessionId,
            Claims = claims
        };

        await sessionResolver.PersistAsync(sessionId, maxAge, session);
        httpContext.Response.Cookies.Append(proptexxOptions.Cookie.SessionRefreshToken, sessionId, cookieOptions);
    }
}

public sealed class AuthRequestPayload
{
    public Dictionary<string, JsonDocument?> Scopes { get; init; } = [];
}
