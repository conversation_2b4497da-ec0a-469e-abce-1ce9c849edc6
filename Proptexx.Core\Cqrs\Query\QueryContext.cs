using System.Security.Claims;
using Microsoft.AspNetCore.Http;

namespace Proptexx.Core.Cqrs.Query;

public sealed class QueryContext : ProptexxContext
{
    private QueryContext(IServiceProvider services, ClaimsPrincipal user) : base(services, user)
    {
    }

    public required IDictionary<object, object?> Items { get; init; }

    public required QueryDefinition Definition { get; init; }

    internal static QueryContext FromHttp(HttpContext context, QueryDefinition definition)
    {
        return new QueryContext(context.RequestServices, context.User)
        {
            Items = context.Items,
            CancellationToken = context.RequestAborted,
            Definition = definition
        };
    }
}