using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations
{
    [Migration(250808001)] // This will run after 250321001
    public class AddScopeSelector : FluentMigrator.Migration
    {
        public override void Up()
        {
            Alter.Table("widget").InSchema("core")
                .AddColumn("scope_selector").AsString().Nullable();
        }

        public override void Down()
        {
            Delete.Column("scope_selector").FromTable("widget").InSchema("core");
        }
    }
} 
