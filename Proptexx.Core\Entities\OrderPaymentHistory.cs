using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class OrderPaymentHistory : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; init; } = Guid.NewGuid();
    
    public Guid OrderId { get; init; }
    
    public decimal Amount { get; init; }
    
    public string? PaymentChannel { get; init; }
    
    public string? PaymentRef { get; init; }

    public string GetDbRef() => "core.order_payment_history";
}