using Microsoft.Extensions.Logging;
using Proptexx.Core.Http;
using Proptexx.Core.Services;
using StackExchange.Redis;

namespace Proptexx.AI.Models.CV;

public sealed class FurnitureConditionDetector(
    IComputerVisionClient computerVisionClient,
    IImageAssessmentClient imageAssessmentClient,
    IConnectionMultiplexer connectionMultiplexer,
    ILoggerFactory loggerFactory)
    : BaseComputerVisionModel(
        "FurnitureConditionDetector",
        computerVisionClient,
        imageAssessmentClient,
        connectionMultiplexer,
        loggerFactory)
{
    public sealed class Result
    {
        public string[]? FurnitureQuality { get; init; }
    }
}