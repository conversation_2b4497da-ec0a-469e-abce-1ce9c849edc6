import subprocess
import sys
import time
import argparse
from os import path

# Define a function to handle command-line arguments
def parse_args():
    parser = argparse.ArgumentParser(description="Container Build Script")

    # Add arguments for container builder and container registry
    parser.add_argument(
        "--registry", required=True, help="Specify the container registry"
    )

    parser.add_argument(
        "--program",
        default="docker",
        help="Specify 'docker' or 'podman' as the container build program (default: 'docker')",
    )

    parser.add_argument(
        "--tags",
        default="latest",
        help="Specify the git commit ID"
    )

    parser.add_argument(
        "directory",
        nargs="?",
        default=".",
        help="Specify the directory (default: current directory)",
    )

    return parser.parse_args()


args = parse_args()
CTX_DIR = path.abspath(args.directory)

RED = "\033[31m"
GREEN = "\033[32m"
RESET = "\033[0m"

DICT = {
    "proptexx.server.sdk": "Dockerfile",
    "proptexx.web.auth": "Proptexx.Web.Auth/Dockerfile",
    "proptexx.web.api": "Proptexx.Web.Api/Dockerfile",
    "proptexx.web.command": "Proptexx.Web.Command/Dockerfile",
    "proptexx.web.partner": "Proptexx.Web.Partner/Dockerfile",
    "proptexx.web.payment": "Proptexx.Web.Payment/Dockerfile",
    "proptexx.web.query": "Proptexx.Web.Query/Dockerfile",
    "proptexx.web.webhook": "Proptexx.Web.Webhook/Dockerfile",
    "proptexx.worker.batchworker": "Proptexx.Worker.BatchWorker/Dockerfile",
    "proptexx.worker.batchreporter": "Proptexx.Worker.BatchReporter/Dockerfile",
    "proptexx.worker.datasync": "Proptexx.Worker.DataSync/Dockerfile",
    "proptexx.worker.messaging": "Proptexx.Worker.Messaging/Dockerfile",
    "proptexx.worker.telemetry": "Proptexx.Worker.Telemetry/Dockerfile",
    "proptexx.database.migration": "Proptexx.Database.Migration/Dockerfile"
}

args = parse_args()
tags = args.tags.split(',')

def build_item(image_name, dockerfile_path, index, total):
    print(f"\nBuilding {image_name} using {dockerfile_path} ({index}/{total})")

    command = [ args.program, "build", "--no-cache" ]

    for tag in tags:
        print(f"\nwith tag: {tag}")
        command.extend(["-t", f"{args.registry}/{image_name}:{tag}"])

    command.extend([
        "-f", dockerfile_path,
        "--build-arg", f"REGISTRY={args.registry}",
        "."
    ])

    result = subprocess.run(command, capture_output=False, text=False)

    if result.returncode == 0:
        print(f"{GREEN}Successfully built {image_name} ({index}/{total}){RESET}\n")
        return 1

    else:
        print(f"{RED}Failed to build {image_name} ({index}/{total}){RESET}\n")
        sys.exit(1)


def main():
    try:

        start_time = time.time()
        num_success = 0

        # Iterate and build containers
        for index, (image_name, dockerfile) in enumerate(DICT.items(), start=1):
            dockerfile_path = path.join(CTX_DIR, dockerfile)
            num_success += build_item(image_name, dockerfile_path, index, len(DICT))

        end_time = time.time()
        total_time = end_time - start_time
        all_success = num_success == len(DICT)

        print(
            f"{GREEN if all_success else RED}Built {num_success} / {len(DICT)} in {total_time}{RESET}"
        )

    except KeyboardInterrupt:
        print(f"\n{RED}Script interrupted by user{RESET}")
        sys.exit(1)


if __name__ == "__main__":
    main()
