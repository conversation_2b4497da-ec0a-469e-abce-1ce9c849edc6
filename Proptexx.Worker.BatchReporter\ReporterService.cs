using System.Collections.Concurrent;
using System.Net.Http.Json;
using System.Text.Json;
using Npgsql;
using Proptexx.Core.Entities;
using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Redis;
using Proptexx.Core.Stores;
using Proptexx.Core.Services;
using Proptexx.Core.BigQuery;
using StackExchange.Redis;

namespace Proptexx.Worker.BatchReporter;

internal class ReporterService : BackgroundService
{
    private readonly ILogger<ReporterService> _logger;
    private readonly NpgsqlDataSource _dataSource;
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly IConfiguration _configuration;
    private readonly BatchDataService _batchDataService;
    private readonly BigQueryWebhookFeedbackExporter _webhookFeedbackExporter;

    public ReporterService(
        IConfiguration configuration,
        ILoggerFactory loggerFactory,
        NpgsqlDataSource dataSource,
        IConnectionMultiplexer connectionMultiplexer,
        BatchDataService batchDataService,
        BigQueryWebhookFeedbackExporter webhookFeedbackExporter)
    {
        _configuration = configuration;
        _logger = loggerFactory.CreateLogger<ReporterService>();
        _dataSource = dataSource;
        _connectionMultiplexer = connectionMultiplexer;
        _batchDataService = batchDataService;
        _webhookFeedbackExporter = webhookFeedbackExporter;
    }

    protected override async Task ExecuteAsync(CancellationToken cancellationToken)
    {
        var batchPopCount = _configuration.GetValue("popCount", 30);
        var parallelRuns = _configuration.GetValue("parallelRuns", 10);
        var intervalMs = _configuration.GetValue("intervalMs", 500);
        var httpTimeoutMs = _configuration.GetValue("httpTimeoutMs", 300000);

        var httpClient = new HttpClient
        {
            Timeout = TimeSpan.FromMilliseconds(httpTimeoutMs)
        };

        var redis = _connectionMultiplexer.GetDatabase();

        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var retryLogs = new ConcurrentQueue<WebhookFeedback>();

                var callbackList = await RedisQueueHelper
                    .WaitForQueueItemsAsync<BatchCallbackPayload>(redis, "batch_callbacks", batchPopCount, cancellationToken);

                var batchIds = callbackList
                    .Select(p => p.BatchId)
                    .Distinct()
                    .ToArray();

                if (batchIds.Length <= 0) continue;

                // Get batch results from Redis for webhooks
                var batchResults = new List<BatchResultModel>();
                foreach (var batchId in batchIds)
                {
                    var batchData = await _batchDataService.GetBatchDataForExportAsync(batchId.ToString());
                    
                    // Convert Redis data to BatchResultModel format for webhook compatibility
                    foreach (var task in batchData.Tasks)
                    {
                        var correspondingResult = batchData.Results.FirstOrDefault(r => r.TaskId == task.TaskId);
                        if (correspondingResult != null && task.Metadata != null)
                        {
                            try
                            {
                                var taskMetadata = JsonSerializer.Deserialize<JsonDocument>(JsonSerializer.Serialize(task.Metadata));
                                var resultData = JsonSerializer.Deserialize<JsonDocument>(JsonSerializer.Serialize(correspondingResult.Result));
                                
                                var resultModel = new BatchResultModel
                                {
                                    BatchId = batchId,
                                    TaskId = Guid.Parse(task.TaskId),
                                    Model = taskMetadata?.RootElement.GetProperty("model").GetString() ?? "unknown",
                                    Config = taskMetadata ?? JsonDocument.Parse("{}"),
                                    Status = resultData?.RootElement.GetProperty("status").GetInt32() ?? 0,
                                    Result = resultData?.RootElement.TryGetProperty("output", out var output) == true ? 
                                             JsonDocument.Parse(output.GetRawText()) : null,
                                    ErrorMessage = resultData?.RootElement.TryGetProperty("error_message", out var error) == true ? 
                                                  error.GetString() : null,
                                    StartedAt = resultData?.RootElement.TryGetProperty("started_at", out var started) == true ? 
                                               started.GetDateTime() : null,
                                    ResultCompletedAt = resultData?.RootElement.TryGetProperty("completed_at", out var completed) == true ? 
                                                       completed.GetDateTime() : null
                                };
                                batchResults.Add(resultModel);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, "Failed to parse Redis data for task {TaskId} in batch {BatchId}", task.TaskId, batchId);
                            }
                        }
                    }
                }

                var semaphore = new SemaphoreSlim(parallelRuns);

                var tasks = callbackList.Select(callbackDef =>
                    HandleCallbackSafelyAsync(callbackDef, batchResults, httpClient, semaphore, redis, _logger, retryLogs, cancellationToken));

                await Task.WhenAll(tasks);
                
                // Persist webhook feedback to BigQuery
                await PersistWebhookFeedbackToBigQueryAsync(retryLogs);
                
                // NOTE: BigQuery export now happens in BatchWorkerService immediately when tasks complete
                // This ensures data is persisted regardless of webhook success/failure (same as old PostgreSQL behavior)
                // ReporterService only handles webhook delivery, not data persistence
            }
            catch (Exception e)
            {
                _logger.LogError(e, "System Exception");
                await Task.Delay(intervalMs * 10, cancellationToken);
            }
        }
    }

    private static async Task HandleCallbackSafelyAsync(
        BatchCallbackPayload callbackDef,
        IList<BatchResultModel> batchResults,
        HttpClient httpClient,
        SemaphoreSlim semaphore,
        IDatabase redis,
        ILogger logger,
        ConcurrentQueue<WebhookFeedback> feedbackQueue,
        CancellationToken cancellationToken)
    {
        try
        {
            await semaphore.WaitAsync(cancellationToken);
            var feedback = await DispatchCallbackAsync(callbackDef, batchResults, httpClient, logger, redis, cancellationToken);
            feedbackQueue.Enqueue(feedback);

            if (!feedback.IsSuccess)
            {
                if (callbackDef.RetryCount < 5)
                {
                    callbackDef.RetryCount++;
                    logger.LogWarning("Callback failed. Requeue batch {BatchId} with retry #{RetryCount}", callbackDef.BatchId, callbackDef.RetryCount);
                    await RequeueAsync(redis, logger, callbackDef);
                }
                else
                {
                    logger.LogError("Giving up on batch {BatchId} after {RetryCount} retries", callbackDef.BatchId, callbackDef.RetryCount);
                }
            }
        }
        catch (Exception e)
        {
            logger.LogError(e, "Unexpected error processing batch {BatchId}. Re-queuing", callbackDef.BatchId);
            await RequeueAsync(redis, logger, callbackDef);
        }
        finally
        {
            semaphore.Release();
        }
    }

    private static async Task<WebhookFeedback> DispatchCallbackAsync(
        BatchCallbackPayload callbackDef,
        IList<BatchResultModel> batchResults,
        HttpClient httpClient,
        ILogger logger,
        IDatabase redis,
        CancellationToken cancellationToken)
    {
        var batchId = callbackDef.BatchId;
        var callbackUrl = callbackDef.CallbackUrl;
        var feedback = new WebhookFeedback
        {
            Id = Guid.NewGuid(),
            BatchId = batchId,
            CallbackUrl = callbackUrl,
            SentAt = DateTime.UtcNow,
            RetryAttempt = callbackDef.RetryCount
        };

        try
        {
            var models = batchResults.Where(x => x.BatchId == batchId).ToList();
            var payload = CallbackResponse.Success(batchId, models);
            var response = await httpClient.PostAsJsonAsync(callbackUrl, payload, cancellationToken);
            var content = await response.Content.ReadAsStringAsync(cancellationToken);

            feedback.IsSuccess = response.IsSuccessStatusCode;
            feedback.StatusCode = (int)response.StatusCode;
            feedback.ResponseText = content;

            logger.LogInformation(
                "Webhook callback sent for batch {BatchId} to {CallbackUrl} - Status: {StatusCode}, Success: {IsSuccess}",
                batchId, callbackUrl, response.StatusCode, response.IsSuccessStatusCode);

            // // Push telemetry log to Redis
            // await PushTelemetryToRedisAsync(redis, new TelemetryEntry
            // {
            //     Timestamp = DateTime.UtcNow,
            //     Endpoint = callbackUrl,
            //     Headers = "{}",
            //     Body = JsonSerializer.Serialize(payload),
            //     WorkspaceId = null, // Set if available
            //     SessionId = null,
            //     ClientVersion = string.Empty, // Default to empty string
            //     AccountId = null,
            //     HttpMethod = "POST",
            //     ClientId = null,
            //     DurationMs = 0, // Default to 0
            //     ResponseCode = (int)response.StatusCode,
            //     Identifier = batchId.ToString(),
            //     SecretId = null
            // });
        }
        catch (Exception ex)
        {
            feedback.IsSuccess = false;
            feedback.StatusCode = 0;
            feedback.ResponseText = ex.Message;
            logger.LogError(ex, "Exception during webhook callback for batch {BatchId} to {CallbackUrl}", batchId, callbackUrl);
        }

        return feedback;
    }

    private static async Task PushTelemetryToRedisAsync(IDatabase redis, TelemetryEntry logEntry)
    {
        var jsonLog = JsonSerializer.Serialize(logEntry);
        await redis.ListRightPushAsync("telemetry_queue", jsonLog);
    }

    private static async Task RequeueAsync(IDatabase redis, ILogger logger, BatchCallbackPayload callbackDef)
    {
        var payload = JsonSerializer.Serialize(callbackDef);

        try
        {
            await redis.ListLeftPushAsync("batch_callbacks_retry", payload);
        }
        catch (Exception redisEx)
        {
            logger.LogError(redisEx, "Failed to requeue callback for batch {BatchId}", callbackDef.BatchId);
        }
    }

    private async Task PersistWebhookFeedbackToBigQueryAsync(ConcurrentQueue<WebhookFeedback> feedbackQueue)
    {
        if (feedbackQueue.IsEmpty)
            return;

        var webhookFeedbacks = new List<WebhookFeedback>();
        while (feedbackQueue.TryDequeue(out var feedback))
            webhookFeedbacks.Add(feedback);

        if (webhookFeedbacks.Count == 0)
            return;

        try
        {
            await _webhookFeedbackExporter.ExportWebhookFeedbackAsync(webhookFeedbacks);
            _logger.LogInformation("Successfully persisted {Count} webhook feedback records to BigQuery", webhookFeedbacks.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to persist webhook feedback to BigQuery. Re-queuing {Count} records", webhookFeedbacks.Count);
            
            // Re-queue failed feedback for retry
            foreach (var feedback in webhookFeedbacks)
            {
                feedbackQueue.Enqueue(feedback);
            }
        }
    }

    private static async Task PersistToDatabaseAsync(NpgsqlConnection npgsql, ILogger logger, ConcurrentQueue<WebhookFeedback> retryLogs)
    {
        if (!retryLogs.TryDequeue(out var first))
            return;

        var webhookFeedbacks = new List<WebhookFeedback> { first };
        while (retryLogs.TryDequeue(out var feedback))
            webhookFeedbacks.Add(feedback);

        try
        {
            await npgsql.InsertAsync(webhookFeedbacks);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to persist webhook feedbacks to database");

            foreach (var feedback in webhookFeedbacks)
            {
                retryLogs.Enqueue(feedback);
            }

            await Task.Delay(2000);
        }
    }
}
