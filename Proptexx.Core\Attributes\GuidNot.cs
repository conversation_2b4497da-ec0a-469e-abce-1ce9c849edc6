using System.ComponentModel.DataAnnotations;

namespace Proptexx.Core.Attributes;

public class GuidNotEmptyAttribute : ValidationAttribute
{
    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value is not Guid guid) return ValidationResult.Success;
        return guid.Equals(Guid.Empty)
            ? new ValidationResult("Guid is empty")
            : ValidationResult.Success;
    }
}
