using Dapper;
using Npgsql;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.System;

public sealed class GetWorkspaces : BaseFilter, IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        context.User.EnsureRootAccess();

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);

        // Get total items and total pages
        var totalItems = await GetCountTotalAsync(npgsql);
        var totalPages = (int)Math.Ceiling((double)totalItems / PageSize);

        // Get paginated workspaces
        var workspaces = await GetPaginatedWorkspacesAsync(npgsql);

        // Return structured response
        return new
        {
            Data = workspaces,
            TotalPages = totalPages,
        };
    }

    private async Task<int> GetCountTotalAsync(NpgsqlConnection npgsql)
    {
        var sql = "SELECT COUNT(*) FROM core.workspace w";
        if (!string.IsNullOrEmpty(Filter))
        {
            sql += " WHERE w.title ILIKE @Filter";
        }

        return await npgsql.ExecuteScalarAsync<int>(sql, new { Filter = $"%{Filter}%" });
    }

    private async Task<List<WorkspaceModel>> GetPaginatedWorkspacesAsync(NpgsqlConnection npgsql)
    {
        var sql = @"
        WITH api_requests AS (
            SELECT 
                workspace_id,
                SUM(CASE WHEN day = CURRENT_DATE THEN request_count ELSE 0 END) AS today_api_requests,
                SUM(CASE WHEN day = (CURRENT_DATE - INTERVAL '1 day') THEN request_count ELSE 0 END) AS yesterday_api_requests,
                SUM(request_count) AS total_api_requests
            FROM telemetry.mv_api_requests_summary
            GROUP BY workspace_id
        )
        SELECT w.id,
               w.parent_id,
               w.name,
               w.title,
               w.created_at,
               COALESCE(COUNT(DISTINCT wsb.service_id), 0) AS num_services,
               COALESCE(COUNT(DISTINCT cab.account_id), 0) AS num_accounts,
               COALESCE(ar.today_api_requests, 0) AS today_api_requests,
               COALESCE(ar.yesterday_api_requests, 0) AS yesterday_api_requests,
               COALESCE(ar.total_api_requests, 0) AS total_api_requests,
               CASE 
                   WHEN COALESCE(ar.yesterday_api_requests, 0) = 0 AND COALESCE(ar.today_api_requests, 0) = 0 THEN 0
                   WHEN COALESCE(ar.yesterday_api_requests, 0) = 0 AND COALESCE(ar.today_api_requests, 0) != 0 THEN 100
                   ELSE ((COALESCE(ar.today_api_requests, 0) - COALESCE(ar.yesterday_api_requests, 0))::NUMERIC / COALESCE(ar.yesterday_api_requests, 1)) * 100
               END AS trend
        FROM core.workspace w
        LEFT OUTER JOIN core.workspace_service_binding wsb ON w.id = wsb.workspace_id
        LEFT OUTER JOIN core.cluster c ON w.id = c.workspace_id
        LEFT OUTER JOIN core.cluster_account_binding cab ON c.id = cab.cluster_id AND LOWER(c.name) != 'lead'
        LEFT JOIN api_requests ar ON w.id = ar.workspace_id
    ";

        if (!string.IsNullOrEmpty(Filter))
        {
            sql += " WHERE w.title ILIKE @Filter";
        }

        sql += " GROUP BY w.id, w.name, ar.total_api_requests, ar.today_api_requests, ar.yesterday_api_requests";

        if (!string.IsNullOrEmpty(SortBy))
        {
            sql += $" ORDER BY {SortBy} {SortOrder}";
        }
        else
        {
            sql += " ORDER BY w.name ASC";
        }

        sql += " LIMIT @PageSize OFFSET @Offset";

        return [.. await npgsql.QueryAsync<WorkspaceModel>(sql, new
        {
            PageSize,
            Offset = (PageNumber - 1) * PageSize,
            Filter = $"%{Filter}%"
        })];
    }

    public sealed class WorkspaceModel
    {
        public Guid Id { get; init; }

        public Guid? ParentId { get; init; }

        public required string Name { get; init; }

        public required string Title { get; init; }

        public DateTime CreatedAt { get; init; }

        public long NumServices { get; init; }

        public long NumAccounts { get; init; }

        public long TotalApiRequests { get; set; }

        public long TodayApiRequests { get; set; }

        public long YesterdayApiRequests { get; set; }

        public long Trend { get; set; }
    }
}