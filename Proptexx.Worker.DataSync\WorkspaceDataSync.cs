using Npgsql;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Redis;

namespace Proptexx.Worker.DataSync;

public static class WorkspaceDataSync
{
    private const string Sql = """
                               select wd.workspace_id,
                                      wd.data_key,
                                      wd.data_value
                               from core.workspace_data wd
                               """;

    internal static async Task SyncAsync(IServiceProvider services, CancellationToken cancellationToken)
    {
        var dataSource = services.GetRequiredService<NpgsqlDataSource>();
        var accountStore = services.GetRequiredService<WorkspaceDataHashStore>();

        await using var npgsql = await dataSource.OpenConnectionAsync(cancellationToken);
        var response = await npgsql.ListQueryAsync<WorkspaceDataPgModel>(Sql);
        var data = response
            .GroupBy(x => x.WorkspaceId)
            .Select(x =>
            {
                return new WorkspaceDataHashStore.WorkspaceDataModel
                {
                    Id = x.Key,
                    Data = x.ToDictionary(z => z.<PERSON>Key, z => z.DataValue)
                };
            });

        accountStore.Persist(data);
    }

    internal class WorkspaceDataPgModel
    {
        public required Guid WorkspaceId { get; init; }

        public required string DataKey { get; init; }

        public required string DataValue { get; init; }
    }
}