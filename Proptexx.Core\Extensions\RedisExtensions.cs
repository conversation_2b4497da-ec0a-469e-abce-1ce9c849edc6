using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Proptexx.Core.Json;
using StackExchange.Redis;

namespace Proptexx.Core.Extensions;

public static class RedisExtensions
{
    public static IServiceCollection AddRedis(this IServiceCollection services, IConfiguration configuration)
    {
        var redisConnStr = configuration.GetConnectionString("Redis")
                           ?? throw new NullReferenceException("redis connection string");

        return services.AddRedis(redisConnStr);
    }

    public static IServiceCollection AddRedis(this IServiceCollection services, string redisConnStr)
    {
        services.AddSingleton<IConnectionMultiplexer>(_ =>
        {
            var configurationOpt = ConfigurationOptions.Parse(redisConnStr);

            // Customize the retry policy for better resilience
            configurationOpt.ReconnectRetryPolicy = new ExponentialRetry(5000);

            var multiplexer = ConnectionMultiplexer.Connect(configurationOpt);
    
            // Subscribe to events for logging and handling connection issues
            multiplexer.ConnectionFailed += (sender, args) =>
            {
                // Implement logging or alerting mechanism
                Console.WriteLine("Redis connection FAILED");
            };

            multiplexer.ConnectionRestored += (sender, args) =>
            {
                // Implement recovery actions or 
                Console.WriteLine("Redis connection RESTORED");
            };
    
            return multiplexer;
        });

        return services;
    }

    public static async Task<T?> HashGetObjectAsync<T>(this IDatabaseAsync redis, RedisKey key, RedisValue hashField, CommandFlags flags = CommandFlags.None) where T : class
    {
        var value = await redis.HashGetAsync(key, hashField, flags);
        var json = value.IsNullOrEmpty ? null : value.ToString();
        return string.IsNullOrWhiteSpace(json) ? null : JsonSerializer.Deserialize<T>(json, JsonDefaults.CompactOptions);
    }

    public static async Task<IEnumerable<T>> HashGetObjectsAsync<T>(this IDatabaseAsync redis, RedisKey key, RedisValue[] hashFields, CommandFlags flags = CommandFlags.None) where T : class
    {
        var values = await redis.HashGetAsync(key, hashFields, flags) ?? [];
        if (values.Length <= 0) return [];

        var result = values
            .Where(x => !x.IsNullOrEmpty)
            .Select(x => JsonSerializer.Deserialize<T>(x!, JsonDefaults.CompactOptions));

        return result!;
    }

    public static Task<bool> HashSetObjectAsync<T>(this IDatabaseAsync redis, RedisKey key, RedisValue hashField, T obj, When when = When.Always) where T : class
    {
        var value = JsonSerializer.Serialize(obj, JsonDefaults.CompactOptions);
        return redis.HashSetAsync(key, hashField, value, when);
    }
}