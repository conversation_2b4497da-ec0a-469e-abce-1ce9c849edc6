using Npgsql;
using Proptexx.Core.Stores;
using Proptexx.Web.Auth.Auth;
using StackExchange.Redis;

namespace Proptexx.Web.Auth.Resolvers;

public sealed class CodeResolver : IScopeResolver<CodePayload>
{
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly NpgsqlDataSource _dataSource;

    public CodeResolver(IConnectionMultiplexer connectionMultiplexer, NpgsqlDataSource dataSource)
    {
        _connectionMultiplexer = connectionMultiplexer;
        _dataSource = dataSource;
    }
    
    public async Task ResolveAsync(ScopeContext context, CodePayload payload, CancellationToken cancellationToken)
    {
        var redis = _connectionMultiplexer.GetDatabase();
        var value = await redis.HashGetAsync("oauth_codes", payload.Code);

        if (!value.IsNullOrEmpty)
        {
            var v = value.ToString().Split("|||");
            var accountId = Guid.Parse(v[0]);
            var fullName = v[1];
            var email = v[2];

            await using var npgsql = await _dataSource.OpenConnectionAsync(cancellationToken);
            var account = await npgsql.Account().GetAccountIdByEmailAsync(email);

            await LoginHelper.SetAccountClaimsAsync(context, accountId, fullName, email);
            await LoginHelper.SetWorkspaceClaimsAsync(context, npgsql, accountId);
            await LoginHelper.SetSystemClaimsAsync(context, account?.IsRoot ?? false);
        }
    }
}

public class CodePayload
{
    public required string Code { get; init; }
}