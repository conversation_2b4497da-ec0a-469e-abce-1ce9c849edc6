using System.ComponentModel.DataAnnotations;
using Proptexx.Core.Services;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Stores;
using Proptexx.Web.Command.Helpers;

namespace Proptexx.Web.Command.Account;

public sealed class Register : ICommand
{
    [Required, EmailAddress, MaxLength(100)]
    public required string Email { get; init; }

    [Required, <PERSON><PERSON>ength(5), <PERSON><PERSON>ength(100)]
    public required string Name { get; init; }
    
    [MaxLength(50)]
    public string? Phone { get; init; }
    
    [MinLength(5), MaxLength(50)]
    public string? Password { get; init; }

    public string? Url { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        var clientId = context.User.GetClientGuid();

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var clientScopes = await npgsql.Client().GetClientScopes(clientId);

        if (clientScopes is null || clientScopes.Length == 0)
        {
            throw new UnauthorizedAccessException();
        }

        var password = Password ?? PasswordService.GeneratePassword(8);

        if (clientScopes.Contains("widget"))
        {
            await WidgetRegister.ExecuteAsync(context, Url, Name, Email, Phone, password, []);
        }
        else if (clientScopes.Contains("portal"))
        {
            await PortalRegister.ExecuteAsync(context, Name, Email, Phone, password, []);
        }
        else
        {
            throw new UnauthorizedAccessException();
        }
    }
}