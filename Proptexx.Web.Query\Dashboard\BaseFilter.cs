﻿using System.ComponentModel.DataAnnotations;

namespace Proptexx.Web.Query.Dashboard
{
    public abstract class BaseFilter
    {
        public Guid[]? Ids { get; init; }

        [Required]
        public required DateTime? StartDate { get; init; }

        [Required]
        public required DateTime? EndDate { get; init; }

        public object GetParameters()
        {
            return new
            {
                startDate = StartDate,
                endDate = EndDate,
                ids = Ids?.Length > 0 ? Ids : null  // Pass the array of IDs to the SQL query
            };
        }
    }
}
