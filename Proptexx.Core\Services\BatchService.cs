
namespace Proptexx.Core.Services;

public interface IBatchImageStatus
{
    int Status { get; }
}

public static class BatchService
{
    public static int GetStatus(IReadOnlyCollection<IBatchImageStatus>? imageList)
    {
        if (imageList is null || imageList.Count <= 0) return 0;

        int status;
        if (imageList.All(x => x.Status < 0))
        {
            status = -1;
        }
        else if (imageList.Any(x => x.Status is 0 or 1))
        {
            status = 1;
        }
        else if (imageList.Any(x => x.Status == 2))
        {
            status = 2;
        }
        else
        {
            status = 0;
        }

        return status;
    }
}
