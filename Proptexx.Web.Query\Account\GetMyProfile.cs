using Dapper;
using Proptexx.Core.Attributes;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Account;

public sealed class GetMyProfile : IQuery
{
    [GuidNotEmpty] public Guid? Id { get; init; }

    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var accountId = Id ?? Guid.Parse(context.User.GetCallerId());
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var profile = await npgsql.QueryFirstOrDefaultAsync<ProfileModel>(ProfileModel.Sql, new { _account_id = accountId });
        return profile;
    }
    
    private class ProfileModel
    {
        public required Guid Id { get; init; }
        
        public required string FirstName { get; init; }
        
        public required string FamilyName { get; init; }
        
        public DateTime? DateOfBirth { get; init; }
        
        public int? Gender { get; init; }
        
        public DateTime CreatedAt { get; init; }
        
        public string? Email { get; init; }

        public static string Sql => @"
            select a.id,
                   a.first_name,
                   a.family_name,
                   a.date_of_birth,
                   a.gender,
                   a.created_at,
                   ae.email
            from core.account a
            left outer join core.account_email ae on a.id = ae.account_id
            where a.id = :_account_id
            order by ae.verified_at desc nulls last
            limit 1;
        ";
    }
}