using Microsoft.Extensions.Configuration;
using Proptexx.Core.AI;
using Proptexx.Core.Helpers;
using Proptexx.Core.Services;
using Proptexx.Core.Storage;
using System.Text.Json;

namespace Proptexx.AI.Models.Flow
{
 
    public class StagingOrRefunishingV2 : IModel
    {
        private readonly HttpClient _httpClient;
        private readonly IStorageService _storageService;
        private readonly string _apiEndpoint;

        public StagingOrRefunishingV2(
            IStorageService storageService,
            IHttpClientFactory httpClientFactory,
            IConfiguration configuration)
        {
            _storageService = storageService;
            _httpClient = httpClientFactory.CreateClient(nameof(StagingOrRefunishingV2));
            _httpClient.Timeout = TimeSpan.FromSeconds(130);

            _apiEndpoint = configuration["staging_refunishing_predict_v2"]
                ?? throw new ApplicationException("API endpoint (staging_refunishing_predict_v2) not configured");
        }

        public async Task<ModelResponse> InferAsync(ModelContext context)
        {
            if (context.Payload is null)
            {
                throw new ApplicationException("Payload is empty");
            }
 
            var payload = JsonSerializer.Serialize(new 
            {
                image = PayloadService.GetRequiredString(context.Payload, "ImageBase64"),
                room_type = PayloadService.GetRequiredString(context.Payload, "SceneType"),
                scene_type = PayloadService.GetRequiredString(context.Payload, "RoomType")
            });
        
            var apiResponse = await _httpClient.PostAsync(_apiEndpoint, payload, context.CancellationToken);

            var document = JsonDocument.Parse(apiResponse);

            // check document response 200
            if (document.RootElement.TryGetProperty("status", out var status) && status.GetUInt16() != 200)
            {
                throw new Exception($"Error calling image API: {apiResponse}");
            }

            // Parse the correct path: data.image
            if (!document.RootElement.TryGetProperty("data", out var dataElement) ||
                !dataElement.TryGetProperty("image", out var imageBase64))
            {
                throw new Exception($"Unable to retrieve result for product replacement in room image");
            }

             if (string.IsNullOrWhiteSpace(imageBase64.GetString()))
            {
                throw new Exception("Result from model was null or empty");
            }

            // upload image to storage
            const string contentType = "image/jpeg";
            var filename = $"{context.ItemId}_{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}.jpg";
            var url = await _storageService.UploadImageAsync(context.WorkspaceId, filename, imageBase64.ToString(), contentType);

            // create a new JSON object with all properties from the original document and add imageUrl
            using var docStream = new MemoryStream();
            using (var writer = new Utf8JsonWriter(docStream))
            {
                writer.WriteStartObject();
                foreach (var property in document.RootElement.EnumerateObject())
                {
                    property.WriteTo(writer);
                }
                writer.WriteString("imageUrl", url);
                writer.WriteEndObject();
            }
            docStream.Position = 0;
            var updatedDocument = JsonDocument.Parse(docStream);

            return new ModelResponse
            {
                Document = updatedDocument
            };
        }
    }
}
