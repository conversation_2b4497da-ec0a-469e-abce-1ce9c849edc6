using System.Data;
using Dapper;
using Proptexx.Core.Entities;

namespace Proptexx.Core.Stores;

public class ProductStore
{
    private readonly IDbConnection _connection;

    public ProductStore(IDbConnection connection) => _connection = connection;

    public Task<Product?> GetProduct(Guid productId)
    {
        const string sql = @"
            select p.* from core.product p where p.id = :_product_id
        ";

        return _connection.QueryFirstOrDefaultAsync<Product>(sql, new { _product_id = productId });
    }
}