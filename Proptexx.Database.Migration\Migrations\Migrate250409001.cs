using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations;

[Migration(250409001)]
public class Migrate250409001 : FluentMigrator.Migration
{
    public override void Up()
    {
        // Add the new ip column (extracted from X-Real-IP header)
        Alter.Table("api_logs").InSchema("telemetry")
            .AddColumn("ip").AsString().Nullable();

        // Add the new is_widget column (flag for widget-related requests)
        Alter.Table("api_logs").InSchema("telemetry")
            .AddColumn("is_widget").AsBoolean().NotNullable().WithDefaultValue(false);
    }

    public override void Down()
    {
        // Remove the newly added columns if rolling back the migration.
        Delete.Column("ip").FromTable("api_logs").InSchema("telemetry");
        Delete.Column("is_widget").FromTable("api_logs").InSchema("telemetry");
    }
}