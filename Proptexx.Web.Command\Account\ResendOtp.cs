using System.ComponentModel.DataAnnotations;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Utils;

namespace Proptexx.Web.Command.Account;

public sealed class ResendOtp : ICommand
{
    [Required] public required string Ref { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        await using var trx = await npgsql.BeginTransactionAsync();

        try
        {
            var otp = await OtpHelper.Create(context, this.Ref);
            await MessageHelper.SendOtp(npgsql, this.Ref, otp);
            await trx.CommitAsync(context.CancellationToken);
        }
        catch (Exception e)
        {
            await trx.RollbackAsync(context.CancellationToken);
            Console.WriteLine(e);
            throw;
        }
    }
}