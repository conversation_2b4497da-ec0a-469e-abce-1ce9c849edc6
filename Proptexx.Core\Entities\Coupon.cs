using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class Coupon : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; init; } = Guid.NewGuid();
    
    public Guid? WorkspaceId { get; init; }

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public required string Code { get; init; }
    
    public required int DiscountType { get; init; }
    
    public required decimal DiscountValue { get; init; }

    public string GetDbRef() => "core.coupon";
}