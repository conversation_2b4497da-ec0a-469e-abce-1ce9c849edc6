using System.Data;
using Dapper;
using Proptexx.Core.Entities;
using Proptexx.Core.Postgresql.Builder;

namespace Proptexx.Core.Stores;

public class ClientStore
{
    private readonly IDbConnection _connection;

    public ClientStore(IDbConnection connection) => _connection = connection;

    public async Task<Guid> Create(Guid workspaceId, string name, string scopes, bool issueToken = false)
    {
        var client = new Client
        {
            WorkspaceId = workspaceId,
            Name = name,
            Scopes = scopes,
            IssueToken = issueToken
        };

        await _connection.InsertAsync(client);
        return client.Id;
    }

    public async Task<ClientSecret> AddSecret(Guid clientId)
    {
        var clientSecret = new ClientSecret
        {
            Id = Guid.NewGuid(),
            ClientId = clientId
        };

        await _connection.InsertAsync(clientSecret);
        return clientSecret;
    }

    public Task<ClientSecret?> GetSecret(Guid workspaceId, string clientName)
    {
        const string sql = @"
            select cs.*
            from core.client_secret cs
            join core.client c on cs.client_id = c.id
            where c.workspace_id = :_workspace_id and c.name = :_client_name;
        ";

        var param = new { _workspace_id = workspaceId, _client_name = clientName };
        return _connection.QueryFirstOrDefaultAsync<ClientSecret>(sql, param);
    }

    public async Task<string[]?> GetClientScopes(Guid secretId)
    {
        const string sql = @"
            select c.scopes
            from core.client_secret cs 
            join core.client c on cs.client_id = c.id
            where cs.id = :_secret_id;
        ";

        var param = new { _secret_id = secretId };
        var result = await _connection.QueryFirstOrDefaultAsync<string>(sql, param);
        return result?.Split();
    }
    public Task<Guid> GetClientSecretId(Guid clientId)
    {
        const string sql = @"
            select cs.id
            from core.client_secret cs
            where cs.client_id = :_client_id;
        ";

        var param = new { _workspace_id = clientId };
        return _connection.QueryFirstOrDefaultAsync<Guid>(sql, param);
    }
}