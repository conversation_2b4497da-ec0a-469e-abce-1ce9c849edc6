using System.Web;
using Proptexx.Core.AI;

namespace Proptexx.AI;

public abstract class BaseModel : IModel
{
    public abstract Task<ModelResponse> InferAsync(ModelContext context);
}

public abstract class BaseModel<T> : BaseModel where T : class
{
    public async Task<T> InferToTypeAsync(ModelContext context)
    {
        var response = await InferAsync(context);
        return response.GetRequiredResult<T>();
    }

    protected static string BuildQueryString(Dictionary<string, string> queryParams)
    {
        return string.Join("&", queryParams.Select(kvp 
            => $"{HttpUtility.UrlEncode(kvp.Key)}={HttpUtility.UrlEncode(kvp.Value)}"));
    }
}
