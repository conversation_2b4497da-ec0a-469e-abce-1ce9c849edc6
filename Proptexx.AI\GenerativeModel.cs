using System.Text.Json;
using Proptexx.AI.Interfaces;
using Proptexx.Core.AI;
using Proptexx.Core.Http;
using Proptexx.Core.Services;
using Proptexx.Core.Storage;

namespace Proptexx.AI;

public abstract class GenerativeModel : BaseModel
{
    private readonly IGenerativeClient _generativeClient;
    private readonly IStorageService _storageService;

    protected GenerativeModel(IGenerativeClient generativeClient, IStorageService storageService)
    {
        _generativeClient = generativeClient;
        _storageService = storageService;
    }

    public override async Task<ModelResponse> InferAsync(ModelContext context)
    {
        if (context.Payload is null)
        {
            throw new NullReferenceException(nameof(context.Payload));
        }

        var imageUrl = PayloadService.GetRequiredString(context.Payload, "imageUrl");
        var architectureStyle = PayloadService.GetRequiredString(context.Payload, "architectureStyle");
        var roomType = PayloadService.GetRequiredString(context.Payload, "roomType");
        var seedEl = PayloadService.GetOptionalElement(context.Payload, "seed");

        int? seed = null;
        if (seedEl is { ValueKind: JsonValueKind.Number })
        {
            seed = seedEl.Value.GetInt32();
        }

        var ctx = new GenerativeModelContext
        {
            ImageUrl = imageUrl,
            ArchitectureStyle = architectureStyle,
            RoomType = roomType,
            Seed = seed,
            WorkspaceId = context.WorkspaceId,
            ItemId = context.ItemId,
            Payload = context.Payload,
            CancellationToken = context.CancellationToken
        };

        var document = await InferAsync(ctx);
        return document;
    }

    public virtual async Task<GenerativeModelResponse> InferAsync(GenerativeModelContext context)
    {
        var endpointConfig = GetEndpointConfig();

        var payload = new Dictionary<string, object?>
        {
            // ["image"] = context.Base64Image,
            // ["architecture_style"] = context.ArchitectureStyle,
            // ["room_type"] = context.RoomType,
            // ["scene_type"] = "indoor"
            ["room_type"] = context.RoomType,
            ["architecture_style"] = context.ArchitectureStyle,
            ["watermark_type"] = "is24",
            ["image_url"] = context.ImageUrl
        };

        var headers = new Dictionary<string, string>
        {
            ["Authorization"] = "Bearer dc_8a1923de4542e2c99386c96b0cb34133a2fc94ba6be06d14186d6b30c1b8f1fdf1a313218ad4068c642e4c1455cc13757d749f46f2b028eadc54ee0dffba7bd107588227aa75770eb72cf00cd5fe36b18c8306b931cead789a0716c55c2b609f90b4a1c69aaf8a4f6e4a0842f9ce115150b03501ceb1bdbcb04bf6bfe59d108a"
        };
        var doc = await _generativeClient.SendAsync(endpointConfig, payload, context.CancellationToken, headers);

        if (doc.RootElement.TryGetProperty("error", out var jsonError))
        {
            throw new Exception($"Model returned error on {context.ImageUrl} : {jsonError.GetString()}");
        }

        string base64Image = string.Empty;
        if (
            // doc.RootElement.TryGetProperty("data", out var dataElement) &&
            // dataElement.TryGetProperty("image", out var imageElement) &&
            doc.RootElement.TryGetProperty("result", out var imageElement) && 
            imageElement.ValueKind == JsonValueKind.String)
        {
            base64Image = imageElement.GetString() ?? string.Empty;
        }

        if (string.IsNullOrWhiteSpace(base64Image))
        {
            throw new Exception($"Result on {context.ImageUrl} from model was null or empty");
        }

        const string contentType = "image/jpeg";
        var filename = $"{context.ItemId}_{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}.jpg";
        var url = await _storageService.UploadImageAsync(context.WorkspaceId, filename, base64Image, contentType);

        var document = JsonDocument.Parse($$"""{"imageUrl": "{{url}}"}""");

        return new GenerativeModelResponse
        {
            RequestParams = payload,
            Base64Image = base64Image,
            MimeType = contentType,
            OutputImageUrl = url,
            Document = document
        };
    }

    protected abstract string GetEndpointConfig();
}

public class GenerativeModelContext : ModelContext
{
    public required string ImageUrl { get; init; }

    public string? Base64Image { get; init; }

    public string? RoomType { get; init; }

    public string? ArchitectureStyle { get; init; }

    public int? Seed { get; init; }
}

public class GenerativeModelResponse : ModelResponse, IModelRequestResponseParams
{
    public required string Base64Image { get; init; }

    public required string MimeType { get; init; }

    public Dictionary<string, object?>? RequestParams { get; init; }

    public Dictionary<string, object?>? ResponseParams { get; init; }

    public required string OutputImageUrl { get; init; }
}
