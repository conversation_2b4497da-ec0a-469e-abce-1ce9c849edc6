using Microsoft.Extensions.DependencyInjection;
using Proptexx.Core.Services;

namespace Proptexx.Core.Extensions;

/// <summary>
/// Extension methods for registering subscription services
/// </summary>
public static class SubscriptionServiceExtensions
{
    /// <summary>
    /// Add subscription services to the service collection
    /// </summary>
    public static IServiceCollection AddSubscriptionServices(this IServiceCollection services)
    {
        services.AddScoped<ISubscriptionService, SubscriptionService>();
        services.AddScoped<ISubscriptionValidationService, SubscriptionValidationService>();
        
        return services;
    }
} 