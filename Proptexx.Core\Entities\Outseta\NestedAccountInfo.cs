using System;
using System.Text.Json.Serialization;

namespace Proptexx.Core.Entities.Outseta
{
    public class NestedAccountInfo
    {
        public bool IsDemo { get; set; }
        public int AccountStage { get; set; }
        public string? AccountStageLabel { get; set; }
        public bool HasLoggedIn { get; set; }
        public decimal LifetimeRevenue { get; set; }
        public bool TaxIdIsInvalid { get; set; }
        public bool IsLivemode { get; set; }
        public bool SchemaLessDataLoaded { get; set; }
        public string? Uid { get; set; }
        [JsonPropertyName("_objectType")]
        public string? ObjectType { get; set; }
        public DateTime Created { get; set; }
        public DateTime Updated { get; set; }
    }
}
