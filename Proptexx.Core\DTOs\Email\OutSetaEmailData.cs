﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Proptexx.Core.DTOs.Email
{
    public class OutSetaEmailData
    {
        public string Email { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string WorkspaceName { get; set; } = string.Empty;
        public string WorkspaceId { get; set; } = string.Empty;
        public string ApiKey { get; set; } = string.Empty;
        public string LoginUrl { get; set; } = string.Empty;
        public DateTime RegistrationDate { get; set; } = DateTime.UtcNow;
    }
}
