using System.Data;
using System.Linq.Expressions;
using System.Text;
using Dapper;
using Proptexx.Core.Postgresql.Builder.Predicate;

namespace Proptexx.Core.Postgresql.Builder;

public static class StoreGetExtensions
{
    public static Task<IEnumerable<T>> GetAsync<T>(this IDbConnection conn, string dbRef, PredicateBuilder<T> predicate) where T : class
    {
        if (string.IsNullOrWhiteSpace(dbRef)) throw new NullReferenceException(nameof (dbRef));
        var statement = BuildSelectStatement<T>(dbRef, predicate.SqlString);
        return conn.QueryAsync<T>(statement, predicate.ParameterValues);
    }

    public static Task<IEnumerable<T>> GetAsync<T>(this IDbConnection conn, PredicateBuilder<T> predicate) where T : class, IDbEntity
    {
        var statement = BuildSelectStatement<T>(null, predicate.SqlString);
        return conn.QueryAsync<T>(statement, predicate.ParameterValues);
    }

    public static Task<IEnumerable<T>> GetAsync<T>(this IDbConnection conn, Expression<Predicate<T>> expression) where T : class, IDbEntity
    {
        var predicate = PredicateBuilder.Build(expression);
        return conn.GetAsync<T>(predicate);
    }

    public static async Task<IList<T>> GetListAsync<T>(this IDbConnection conn, Expression<Predicate<T>> expression) where T : class, IDbEntity
    {
        var result = await GetAsync<T>(conn, expression);
        return result.ToList();
    }

    public static async Task<T> GetFirstAsync<T>(this IDbConnection conn, Expression<Predicate<T>> expression) where T : class, IDbEntity
    {
        var result = await GetAsync<T>(conn, expression);
        return result.First();
    }

    public static async Task<T?> GetFirstOrDefaultAsync<T>(this IDbConnection conn, Expression<Predicate<T>> expression) where T : class, IDbEntity
    {
        var result = await GetAsync<T>(conn, expression);
        return result.FirstOrDefault();
    }

    public static async Task<T> GetLastAsync<T>(this IDbConnection conn, Expression<Predicate<T>> expression) where T : class, IDbEntity
    {
        var result = await GetAsync<T>(conn, expression);
        return result.Last();
    }

    public static async Task<T?> GetLastOrDefaultAsync<T>(this IDbConnection conn, Expression<Predicate<T>> expression) where T : class, IDbEntity
    {
        var result = await GetAsync<T>(conn, expression);
        return result.LastOrDefault();
    }

    public static async Task<T> GetSingleAsync<T>(this IDbConnection conn, Expression<Predicate<T>> expression) where T : class, IDbEntity
    {
        var result = await GetAsync<T>(conn, expression);
        return result.Single();
    }

    public static async Task<T?> GetSingleOrDefaultAsync<T>(this IDbConnection conn, Expression<Predicate<T>> expression) where T : class, IDbEntity
    {
        var result = await GetAsync<T>(conn, expression);
        return result.SingleOrDefault();
    }

    private static string BuildSelectStatement<T>(string? dbRef, string? whereStatement) where T : class
    {
        var type = typeof(T);
        dbRef ??= DbRefProvider.Get(type); // If dbRef is null, assume its a IDbEntity and acquire the dbRef string
        var props = type.GetProperties();

        var fields = new StringBuilder();

        for (var i = 0; i < props.Length; i++)
        {
            var fieldName = PredicateBuilderConfig.ColumnNameRender(props[i].Name);
            fields.Append(fieldName);
            if (i < props.Length - 1) fields.Append(',');
        }

        var statement = $"select {fields} from {dbRef}";

        if (!string.IsNullOrWhiteSpace(whereStatement))
        {
            statement += $" where {whereStatement}";
        }

        return statement;
    }
}