using Proptexx.Core.Services;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Stores;

namespace Proptexx.Web.Command.Widget;

public sealed class SetPassword : ICommand
{
    public async Task ExecuteAsync(CommandContext context)
    {
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        await using var trx = await npgsql.BeginTransactionAsync();

        var accountStore = npgsql.Account();
        // var account = await accountStore.Create(this.FirstName, this.FamilyName);
        // await accountStore.JoinCluster(account.Id, workspaceId, "Lead", false);

        var password = PasswordService.GeneratePassword(12);
        // await accountStore.SetSecret(account.Id, password);
    }
}