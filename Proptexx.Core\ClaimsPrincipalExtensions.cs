using System.Diagnostics.CodeAnalysis;
using System.Security.Claims;
using Proptexx.Core.Auth;

namespace Proptexx.Core;

public static class ClaimsPrincipalExtensions
{
    private const string FormatWorkspaceIdToken = "workspaceId";
    private const string FormatWorkspaceNameToken = "workspaceName";
    private const string FormatIsRootToken = "$isRoot";
    private const string FormatClientNameToken = "clientName";

    public static bool HasClientVersion(this ClaimsPrincipal source)
        => source.HasClaim(x => x.Type.Equals("ClientVersion", StringComparison.OrdinalIgnoreCase));

    // Get ClientId as Guid methods
    public static Guid GetClientGuid(this ClaimsPrincipal source)
        => Guid.Parse(source.GetClientIdentifier());

    public static bool GetClientGuid(this ClaimsPrincipal source, out Guid? clientId)
    {
        if (source.TryGetClientId(out var strClientId) 
            && Guid.TryParse(strClientId, out var guidClientId))
        {
            clientId = guidClientId;
            return true;
        }

        clientId = null;
        return false;
    }

    // Get ClientSecretId as Guid methods
    public static Guid GetClientSecretGuid(this ClaimsPrincipal source)
        => Guid.Parse(source.GetClientSecretId());

    public static bool GetClientSecretGuid(this ClaimsPrincipal source, out Guid? clientSecretId)
    {
        if (source.TryGetClientSecretId(out var strClientSecretId) 
            && Guid.TryParse(strClientSecretId, out var guidClientSecretId))
        {
            clientSecretId = guidClientSecretId;
            return true;
        }

        clientSecretId = null;
        return false;
    }

    // Get WorkspaceId methods
    public static string GetWorkspaceId(this ClaimsPrincipal source)
    {
        var result = TryGetWorkspaceId(source);

        if (string.IsNullOrEmpty(result))
        {
            throw new NullReferenceException(FormatWorkspaceIdToken);
        }

        return result;
    }

    public static bool TryGetWorkspaceId(this ClaimsPrincipal source, [NotNullWhen(true)] out string? workspaceId)
    {
        workspaceId = TryGetWorkspaceId(source);
        if (!string.IsNullOrEmpty(workspaceId)) return true;

        workspaceId = null;
        return false;
    }

    public static string? TryGetWorkspaceId(this ClaimsPrincipal source)
    {
        var result = source.FindFirst(FormatWorkspaceIdToken)?.Value;
        return string.IsNullOrEmpty(result) ? null : result;
    }

    public static Guid GetWorkspaceGuid(this ClaimsPrincipal source)
    {
        var result = TryGetWorkspaceGuid(source);

        if (!result.HasValue || result.Value.Equals(Guid.Empty))
        {
            throw new NullReferenceException(FormatWorkspaceIdToken);
        }

        return result.Value;
    }

    public static bool TryGetWorkspaceGuid(this ClaimsPrincipal source, [NotNullWhen(true)] out Guid? workspaceId)
    {
        var result = TryGetWorkspaceGuid(source);
        if (!result.HasValue || result.Value.Equals(Guid.Empty))
        {
            workspaceId = null;
            return false;
        }

        workspaceId = result;
        return true;
    }

    public static Guid? TryGetWorkspaceGuid(this ClaimsPrincipal source)
    {
        var result = source.TryGetWorkspaceId();
        return string.IsNullOrEmpty(result) || !Guid.TryParse(result, out var guid) ? null : guid;
    }
    
    
    // Get WorkspaceName methods
    public static string GetWorkspaceName(this ClaimsPrincipal source)
    {
        var result = TryGetWorkspaceName(source);

        if (string.IsNullOrEmpty(result))
        {
            throw new NullReferenceException(FormatWorkspaceNameToken);
        }

        return result;
    }

    public static bool TryGetWorkspaceName(this ClaimsPrincipal source, [NotNullWhen(true)] out string? workspaceName)
    {
        workspaceName = TryGetWorkspaceName(source);
        if (!string.IsNullOrEmpty(workspaceName)) return true;
        workspaceName = null;
        return false;
    }

    public static string? TryGetWorkspaceName(this ClaimsPrincipal source)
    {
        var result = source.FindFirst(FormatWorkspaceNameToken)?.Value;
        return string.IsNullOrEmpty(result) ? null : result;
    }
    
    
    // Get ClientName methods
    public static string GetClientName(this ClaimsPrincipal source)
    {
        var result = TryGetClientName(source);

        if (string.IsNullOrEmpty(result))
        {
            throw new NullReferenceException(FormatClientNameToken);
        }

        return result;
    }

    public static bool TryGetClientName(this ClaimsPrincipal source, [NotNullWhen(true)] out string? clientName)
    {
        clientName = TryGetClientName(source);
        if (!string.IsNullOrEmpty(clientName)) return true;
        clientName = null;
        return false;
    }

    public static string? TryGetClientName(this ClaimsPrincipal source)
    {
        var result = source.FindFirst(FormatClientNameToken)?.Value;
        return string.IsNullOrEmpty(result) ? null : result;
    }

    // Get CallerId as Guid methods
    public static Guid GetCallerGuid(this ClaimsPrincipal source)
        => Guid.Parse(source.GetCallerId());

    public static bool TryGetCallerGuid(this ClaimsPrincipal source, [NotNullWhen(true)] out Guid? callerId)
    {
        var result = TryGetCallerGuid(source);
        if (!result.HasValue || result.Value.Equals(Guid.Empty))
        {
            callerId = null;
            return false;
        }

        callerId = result;
        return true;
    }

    public static Guid? TryGetCallerGuid(this ClaimsPrincipal source)
    {
        var result = source.TryGetCallerId();
        return string.IsNullOrEmpty(result) || !Guid.TryParse(result, out var guid) ? null : guid;
    }

    public static bool HasRootAccess(this ClaimsPrincipal source)
    {
        var result = source.FindFirst(FormatIsRootToken);
        return !string.IsNullOrWhiteSpace(result?.Value) && result.Value.Equals("true", StringComparison.OrdinalIgnoreCase);
    }

    public static void EnsureRootAccess(this ClaimsPrincipal source)
    {
        var claim = source.FindFirst(FormatIsRootToken);
        var result = !string.IsNullOrWhiteSpace(claim?.Value) && claim.Value.Equals("true", StringComparison.OrdinalIgnoreCase);
        if (!result) throw new UnauthorizedAccessException();
    }
}