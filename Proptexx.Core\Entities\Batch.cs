using System.Text.Json;
using Proptexx.Core.Json;
using Proptexx.Core.Postgresql.Builder;

namespace Proptexx.Core.Entities;

public class Batch : IDbTable
{
    public Guid Id { get; init; } = Guid.NewGuid();
    
    public required Guid WorkspaceId { get; init; }
    
    public Guid? AccountId { get; init; }
    
    public string? Session { get; init; }
    
    public DateTime CreatedAt { get; init; } = DateTime.UtcNow;

    public string? CallbackUrl { get; init; }

    public bool IsSync { get; set; }

    public string GetDbRef() => "batching.batch";

    public override string ToString() => JsonSerializer.Serialize(this, JsonDefaults.JsonSerializerOptions);
}

public class BatchRequestMeta : IDbTable
{
    public Guid Id { get; } = Guid.NewGuid();
    
    public required Guid BatchId { get; init; }
    
    public string? CorrelationId { get; set; }

    public string? ClientIp { get; set; }

    public string? UserAgent { get; set; }

    public string? Referrer { get; set; }

    public string? Language { get; set; }

    public string GetDbRef() => "batching.request_meta";
}

public class BatchTask : IDbTable
{
    public required Guid Id { get; init; }
    
    public required Guid BatchId { get; init; }

    public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public required string Model { get; init; }

    public required JsonDocument Config { get; init; }

    public string GetDbRef() => "batching.task";
}

public class BatchResult : IDbTable
{
    public required Guid Id { get; init; }
    
    public required Guid TaskId { get; init; }

    public BatchResultStatus Status { get; set; }

    public JsonDocument? Output { get; set; }
    
    public string? ErrorMessage { get; set; }
    
    public required DateTime StartedAt { get; init; }
    
    public DateTime? CompletedAt { get; set; }
    
    public IDictionary<string, object?>? RequestParams { get; set; }
    
    public IDictionary<string, object?>? ResponseParams { get; set; }
    
    public string? Exception { get; set; }

    public string GetDbRef() => "batching.result";
}

public enum BatchResultStatus
{
    Error = -1,
    Pending = 0,
    Processing = 1,
    Success = 2
}


public class WidgetImage : IDbTable
{
    public required string Id { get; set; }
    
    public required ImageAssessmentStatus Status { get; set; }
    
    public required string ImageUrl { get; set; }
    
    public string? OriginalUrl { get; set; }
    
    public string? ErrorMessage { get; set; }
    
    public string? FileHash { get; set; }
    
    public string? MimeType { get; set; }

    public int? Width { get; set; }
    
    public int? Height { get; set; }

    public string? RoomType { get; set; }

    public bool? IsEmptyRoom { get; set; }
    

    public string GetDbRef() => "widget.image";
}

public class WidgetListing : IDbTable
{
    public Guid Id { get; } = Guid.NewGuid();

    public required string ImageId { get; init; }

    public required Guid WorkspaceId { get; init; }

    public required string ListingUrl { get; set; }

    public string GetDbRef() => "widget.listing";
}

public enum ImageAssessmentStatus
{
    Error = -1,
    Pending = 0,
    Processing = 1,
    Success = 2
}

public class WebhookFeedback : IDbTable
{
    public required Guid Id { get; init; }
    
    public required Guid BatchId { get; init; }
    
    public required string CallbackUrl { get; init; }
    
    public bool IsSuccess { get; set; }
    
    public string? ResponseText { get; set; }
    
    public int? StatusCode { get; set; }
    
    public DateTime? SentAt { get; set; }

    public int RetryAttempt { get; set; } = 0;

    public string GetDbRef() => "batching.webhook_feedback";
}