using System.Text.Json;
using Proptexx.Core.Http;

namespace Proptexx.AI.Models.CV;

public sealed class RoomObjectDetection : LegacyComputerVisionModel
{
    public RoomObjectDetection(
        IComputerVisionClient computerVisionClient)  
        : base(computerVisionClient)
    {
    }

    protected override JsonDocument? ParseModelResponse(JsonDocument doc, string inputImageUrl, string modelEndpoint)
    {
        var computerVision = doc.RootElement.GetPropertyOrDefault("computer_vision");

        var propertyMapping = new Dictionary<string, string>
        {
            { "result", "output" },
            { "bboxes", "bboxes" },
            { "score", "scores" }
        };

        return computerVision?.GetPropertiesToJsonDocument(propertyMapping);
    }

    protected override string GetModelEndpoint(string workspaceId)
        => "http://preprocessing.prod.ai.proptexx.com/room-objects-detection/predict";
}