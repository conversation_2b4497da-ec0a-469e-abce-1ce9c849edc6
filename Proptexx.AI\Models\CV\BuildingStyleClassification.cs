using System.Text.Json;
using Proptexx.Core.Http;

namespace Proptexx.AI.Models.CV;

public sealed class BuildingStyleClassification(
    IComputerVisionClient computerVisionClient) 
    : LegacyComputerVisionModel(computerVisionClient)
{
    protected override string GetModelEndpoint(string workspaceId)
        => "http://preprocessing.prod.ai.proptexx.com/building-style-classification/predict";

    protected override JsonDocument? ParseModelResponse(JsonDocument doc, string inputImageUrl, string modelEndpoint)
    {
        var computerVisionProperty = doc.RootElement
            .GetPropertyOrDefault("computer_vision");

        var result = computerVisionProperty?.GetPropertiesToJsonDocument(new Dictionary<string, string>
        {
            ["result"] = "output",
            ["score"] = "score"
        });

        return result;
    }
}