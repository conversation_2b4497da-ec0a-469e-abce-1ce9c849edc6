﻿using System.Reflection;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Services;

namespace Proptexx.Web.Query;

public static class Extensions
{
    public static TypeRegistry<IQuery> AddObjectQueries(this TypeRegistry<IQuery> registry)
    {
        var assembly = Assembly.GetExecutingAssembly();
        registry.Add("account", assembly, "Account", true);
        registry.Add("message", assembly, "Message", true);
        registry.Add("dashboard", assembly, "Dashboard", true);
        registry.Add("workspace", assembly, "Workspace", true);
        registry.Add("client", assembly, "Client", true);
        registry.Add("system", assembly, "System", true);
        registry.Add("ecommerce", assembly, "Ecommerce", true);
        registry.Add("store", assembly, "Store", true);
        registry.Add("docs", assembly, "Docs", true);
        registry.Add("widget", assembly, "Widget", true);
        return registry;
    }
}