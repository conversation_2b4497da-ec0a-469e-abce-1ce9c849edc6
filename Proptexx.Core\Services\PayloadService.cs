using System.Text.Json;

namespace Proptexx.Core.Services;

public static class PayloadService
{
    public static JsonElement GetRequiredElement(JsonDocument doc, string key)
    {
        var element = GetElement(doc, key);
        if (element is null)
        {
            throw new ApplicationException($"Unable to resolve required key '{key}' in json payload");
        }

        return element.Value;
    }

    public static JsonElement? GetOptionalElement(JsonDocument doc, string key)
    {
        return GetElement(doc, key);
    }
    
    public static string GetRequiredString(JsonDocument doc, string key)
    {
        var el = GetRequiredElement(doc, key);
        var tmpStr = el.GetString();

        if (string.IsNullOrWhiteSpace(tmpStr))
        {
            throw new ApplicationException($"Key '{key}' of json payload is null or empty");
        }

        return tmpStr;
    }

    public static int GetRequiredInt32(JsonDocument doc, string key)
    {
        var element = GetRequiredElement(doc, key);

        if (element.ValueKind is not JsonValueKind.Number)
        {
            throw new ApplicationException($"Key '{key}' of json payload is not a number");
        }

        return element.GetInt32();
    }

    public static long GetRequiredInt64(JsonDocument doc, string key)
    {
        var element = GetRequiredElement(doc, key);

        if (element.ValueKind is not JsonValueKind.Number)
        {
            throw new ApplicationException($"Key '{key}' of json payload is not a number");
        }

        return element.GetInt64();
    }

    public static string? GetOptionalString(JsonDocument doc, string key)
    {
        var element = GetOptionalElement(doc, key);
        return element?.GetString();
    }

    public static int? GetOptionalInt32(JsonDocument doc, string key)
    {
        var element = GetOptionalElement(doc, key);
        return element?.ValueKind is JsonValueKind.Number ? element.Value.GetInt32() : null;
    }

    public static long? GetOptionalInt64(JsonDocument doc, string key)
    {
        var element = GetOptionalElement(doc, key);
        return element?.ValueKind is JsonValueKind.Number ? element.Value.GetInt64() : null;
    }

    public static decimal? GetOptionalDecimal(JsonDocument doc, string key)
    {
        var element = GetOptionalElement(doc, key);
        return element?.ValueKind is JsonValueKind.Number ? element.Value.GetDecimal() : null;
    }

    private static JsonElement? GetElement(JsonDocument doc, string key)
    {
        var root = doc.RootElement;

        // First try with the key as it is (camelCase expected)
        if (TryGetPropertyIgnoreCase(root, key, out var el))
        {
            return el;
        }

        // Fallback to snake_case
        var snakeCaseKey = ConvertToSnakeCase(key);
        if (TryGetPropertyIgnoreCase(root, snakeCaseKey, out el))
        {
            return el;
        }

        // Return null if neither found
        return null;
    }

    private static bool TryGetPropertyIgnoreCase(JsonElement element, string key, out JsonElement value)
    {
        foreach (var prop in element.EnumerateObject())
        {
            if (string.Equals(prop.Name, key, StringComparison.OrdinalIgnoreCase))
            {
                value = prop.Value;
                return true;
            }
        }

        value = default;
        return false;
    }

    private static string ConvertToSnakeCase(string key)
    {
        if (string.IsNullOrEmpty(key))
            return key;

        var builder = new System.Text.StringBuilder();
        for (var i = 0; i < key.Length; i++)
        {
            var c = key[i];
            if (char.IsUpper(c))
            {
                if (i > 0)
                {
                    builder.Append('_');
                }
                builder.Append(char.ToLower(c));
            }
            else
            {
                builder.Append(c);
            }
        }
        return builder.ToString();
    }

    public static JsonElement.ArrayEnumerator GetRequiredArray(JsonDocument doc, string key, int? minLength = null)
    {
        var items = GetRequiredElement(doc, key);

        if (items.ValueKind != JsonValueKind.Array)
        {
            throw new ApplicationException($"Expected property `{key}` of type array");
        }

        if (minLength is not null && items.GetArrayLength() <= 0)
        {
            throw new ApplicationException($"Property `{key}` must contain at least {minLength} {(minLength == 1 ? "item" : "items")}");
        }

        return items.EnumerateArray();
    }
}