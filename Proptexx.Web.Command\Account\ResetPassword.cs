using System.ComponentModel.DataAnnotations;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Stores;
using Proptexx.Core.Utils;

namespace Proptexx.Web.Command.Account;

public sealed class ResetPassword : ICommand
{
    [Required, EmailAddress]
    public required string Username { get; init; }
    
    public async Task ExecuteAsync(CommandContext context)
    {
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);

        var email = this.Username.Trim();
        var account = await npgsql.Account().GetAccountIdByEmailAsync(email);
        if (account is null) throw new CommandException("Invalid username");
        var otp = await OtpHelper.Create(context, email);
        await MessageHelper.SendOtp(npgsql, email, otp);
    }
}