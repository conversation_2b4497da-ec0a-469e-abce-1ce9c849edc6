using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class SaleChannel : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; init; } = Guid.NewGuid();

    public required Guid WorkspaceId { get; init; }

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;
    
    public required string Name { get; init; }
    
    public string? Description { get; init; }

    public string GetDbRef() => "core.sale_channel";
}