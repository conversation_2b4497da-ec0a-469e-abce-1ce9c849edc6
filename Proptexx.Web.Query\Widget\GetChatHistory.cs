using Proptexx.AI.Services;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Query;

namespace Proptexx.Web.Query.Widget;

public sealed class GetChatHistory : IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        using var httpClient = context.GetService<HttpClient>();
        var chatService = context.GetService<IChatService>();

        var userId = context.User.GetCallerId().Replace("-", "");
        var sessionId = context.User.GetSessionId().Replace("-", "");

        var response = await chatService.GetChatHistoryAsync(httpClient, userId, sessionId, context.CancellationToken);
        return response;
    }
}