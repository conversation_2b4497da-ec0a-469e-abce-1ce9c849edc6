using System.Text.Json;
using System.ComponentModel.DataAnnotations;
using Proptexx.Core.Json;
using Proptexx.Core.Services;

namespace Proptexx.Core.Cqrs.Query.Handlers;

public sealed class ObjectQueryHandler : IQueryHandler
{
    private readonly TypeRegistry<IQuery> _registry;

    public ObjectQueryHandler(Action<TypeRegistry<IQuery>> registryConfig)
    {
        _registry = new TypeRegistry<IQuery>();
        registryConfig(_registry);
        Initialize();
    }

    public string Identifier => string.Empty;

    private void Initialize() => _registry.Initialize();

    public Task<bool> HasAccessAsync(QueryContext ctx) 
        => Task.FromResult(true);

    public async Task<QueryHandlerResult> ExecuteAsync(QueryContext ctx)
    {
        var response = new ObjectQueryResponse();
        if (string.IsNullOrWhiteSpace(ctx.Definition.Identifier))
        {
            response.Error("No provided query identifier");
            return response;
        }
        
        var type = _registry.Get(ctx.Definition.Identifier);

        if (type is null)
        {
            response.Error("Query not found");
            return response;
        }

        var payload = ctx.Definition.Payload.Deserialize(type, JsonDefaults.JsonSerializerOptions);

        if (payload is not IQuery query)
        {
            response.Error("Query not parsable");
            return response;
        }

        // Query validation
        Validate(query);

        var result = await query.ExecuteAsync(ctx);
        if (result is null)
        {
            response.Error("Query returned no result");
            return response;
        }

        response.Success(result);
        return response;
    }

    private static void Validate(IQuery query)
    {
        var validationCtx = new ValidationContext(query);
        var validationResults = new List<ValidationResult>();
        var isValid = Validator.TryValidateObject(query, validationCtx, validationResults, validateAllProperties: true);

        if (!isValid)
        {
            var messages = validationResults.Select(x => x.ErrorMessage).ToList();
            var msg = string.Join(Environment.NewLine, messages);
            throw new ValidationException(msg);
        }
    }

    private sealed class ObjectQueryResponse : QueryHandlerResult { }
}
