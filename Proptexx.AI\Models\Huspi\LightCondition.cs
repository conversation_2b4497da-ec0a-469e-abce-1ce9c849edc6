using System.Text.Json.Serialization;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Http;
using Proptexx.Core.Services;
using StackExchange.Redis;

namespace Proptexx.AI.Models.Huspi;

public sealed class LightCondition(
    IComputerVisionClient computerVisionClient,
    IImageAssessmentClient imageAssessmentClient,
    IConnectionMultiplexer connectionMultiplexer,
    ILoggerFactory loggerFactory)
    : BaseComputerVisionModel(
        "LightCondition",
        computerVisionClient,
        imageAssessmentClient,
        connectionMultiplexer,
        loggerFactory)
{
    public sealed class Result
    {
        [JsonPropertyName("lightCondition")]
        public Dictionary<string, float>? LightCondition { get; init; }
    }
}
