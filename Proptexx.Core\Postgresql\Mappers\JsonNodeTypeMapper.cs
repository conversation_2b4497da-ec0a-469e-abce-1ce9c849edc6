using System.Text.Json.Nodes;
using Npgsql;
using NpgsqlTypes;

namespace Proptexx.Core.Postgresql.Mappers;

public sealed class JsonNodeTypeMapper : NpgsqlTypeMapper<JsonNode?>
{
    public override JsonNode? Parse(object value)
    {
        if (value is not string str)
        {
            throw new NullReferenceException(nameof (str));
        }

        return JsonNode.Parse(str);
    }

    protected override void SetValue(NpgsqlParameter parameter, JsonNode? value)
    {
        parameter.NpgsqlDbType = NpgsqlDbType.Jsonb;
        parameter.Value = value;
    }
}