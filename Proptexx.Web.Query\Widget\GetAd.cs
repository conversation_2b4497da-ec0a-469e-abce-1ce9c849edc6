﻿using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Proptexx.Web.Query.Widget
{
    public class GetAd : IQuery
    {
        [Required] public required string Url { get; init; }

        public async Task<object?> ExecuteAsync(QueryContext context)
        {
            var log = new StringBuilder("Processing GetAd request\n");

            try
            {
                var workspaceId = context.User.GetWorkspaceGuid();
                log.AppendLine($"Workspace ID: {workspaceId}");

                var images = new List<object>
                {
                    {  new { url = "https://static.proptexx.com/ads/default_ad1.png" } },
                    {  new { url = "https://static.proptexx.com/ads/default_ad2.png" } }
                };

                log.AppendLine($"Returning {images.Count} image URLs.");

                return new { images };
            }
            catch (Exception ex)
            {
                log.AppendLine($"Error: {ex.Message}");
                throw;
            }
            finally
            {
                Console.WriteLine(log.ToString());
            }

            throw new NotImplementedException();
        }
    }
}
