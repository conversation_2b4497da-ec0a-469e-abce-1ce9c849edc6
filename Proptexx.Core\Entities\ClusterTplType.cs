using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class ClusterTplType : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; } = Guid.NewGuid();

    public required Guid ClusterTplId { get; init; }
    
    public required string Name { get; set; }
    
    public required string NamePlural { get; set; }

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public DateTime? UpdatedAt { get; } = null!;

    public string GetDbRef() => "core.cluster_tpl_type";
}