using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Stores;
using Proptexx.Core.Utils;

namespace Proptexx.Web.Command.Helpers;

public sealed class WidgetRegister
{
    public static async Task ExecuteAsync(CommandContext context, string? listingUrl, string name, string email, string? phone, string password, Dictionary<string, object> additionalData)
    {
        Console.WriteLine($"Registration from {listingUrl}: {email}");

        var workspaceId = context.User.GetWorkspaceGuid();

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);

        if (await npgsql.Account().EmailExistsOnWorkspace(workspaceId, email))
        {
            throw new CommandException("Check your info and try again");
        }

        await using var trx = await npgsql.BeginTransactionAsync();

        try
        {
            var account = await OnboardHelper.OnboardAccount(npgsql, name, email, false, password, phone, false);
            await npgsql.Account().JoinCluster(account.Id, workspaceId, "Lead", true);

            // Email w/ OTP
            var otp = await OtpHelper.Create(context, email);
            await MessageHelper.OnWidgetSignupToLead(npgsql, email, otp);

            if (!email.Contains("@proptexx.com", StringComparison.OrdinalIgnoreCase))
            {
                var widgetSettings = await npgsql.QueryFirstOrDefaultAsync<WidgetSettingsModel>(
                    WidgetSettingsModel.Sql, new { _workspace_id = workspaceId });

                if (widgetSettings is not null)
                {
                    if (!string.IsNullOrWhiteSpace(widgetSettings.SignupCallbackUrl) && !string.IsNullOrWhiteSpace(listingUrl))
                    {
                        // Send webhook
                        await WebhookHelper.OnWidgetSignup(widgetSettings.SignupCallbackUrl, listingUrl, name, email, phone);
                    }

                    if (widgetSettings.SignupEmailAdmin is not false)
                    {
                        // Notify workspace admin or registered signup email address that a new lead has signed up
                        await MessageHelper.OnWidgetSignupToAdmin(npgsql, workspaceId, listingUrl, name, email, widgetSettings.SignupEmail);
                    }
                }
            }

            await trx.CommitAsync(context.CancellationToken);
            context.AddData("otpRef", email);
        }
        catch (Exception)
        {
            await trx.RollbackAsync(context.CancellationToken);
            throw;
        }
    }
    
    public class WidgetSettingsModel
    {
        public Guid WorkspaceId { get; init; }
        
        public string? SignupCallbackUrl { get; init; }
        
        public string? SignupEmail { get; init; }
        
        public bool? SignupEmailAdmin { get; init; }

        public static string Sql => @"
            select w.id as workspace_id,
                   wi.signup_callback_url,
                   wi.signup_email_admin,
                   wi.signup_email
            from core.workspace w
            left outer join core.widget wi on w.id = wi.workspace_id
            where w.id = :_workspace_id
        ";
    }
}
