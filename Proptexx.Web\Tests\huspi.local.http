
### Authenticate
POST https://auth.dev.local/_auth
Content-Type: application/json
Authorization: A<PERSON><PERSON><PERSON> YWY1MWI5YzctYzBhYS00Nzc4LTgzZmYtODZiMTE1ZDdmMzc1

{}

> {% client.global.set("accessToken", response.body.$accessToken) %}

### Call Huspi LightCondition CV model with accessToken from previous request
POST https://api.dev.local/huspi/lightCondition
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "imageUrl": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-1858468991.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
}

### Call Huspi PropertyAmenitiesGeneration CV model with accessToken from previous request

POST https://api.dev.local/huspi/propertyAmenitiesGeneration
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "imageUrl": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-1858468991.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
}


### Call Huspi PropertyCondition CV model with accessToken from previous request

POST https://api.dev.local/huspi/propertyCondition
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "imageUrl": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-1858468991.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
}


### Call Huspi PropertyLocationGeneration CV model with accessToken from previous request

POST https://api.dev.local/huspi/propertyLocationGeneration
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "imageUrl": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-1858468991.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
}


### Call Huspi PropertyQuality CV model with accessToken from previous request

POST https://api.dev.local/huspi/propertyQuality
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "imageUrl": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-1858468991.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
}


### Call Huspi RoomType CV model with accessToken from previous request

POST https://api.dev.local/huspi/roomType
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "imageUrl": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-1858468991.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
}


### Call Huspi TitleGeneration CV model with accessToken from previous request

POST https://api.dev.local/huspi/titleGeneration
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "imageUrl": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-1858468991.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
}


### Call Huspi ViewAnalyzer CV model with accessToken from previous request

POST https://api.dev.local/huspi/viewAnalyzer
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "imageUrl": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-1858468991.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
}


### Call Huspi PropertyDescriptionGenerator CV model with accessToken from previous request

POST https://api.dev.local/huspi/propertyDescriptionGenerator
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "imageUrl": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-1858468991.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
}

