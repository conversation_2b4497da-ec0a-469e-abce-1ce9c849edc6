using System.ComponentModel.DataAnnotations;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Command;

namespace Proptexx.Web.Command.Widget;

public sealed class SubmitFeedback : ICommand
{
    [Required] public required string Url { get; init; }
    
    [Required] public required string Source { get; init; }
    
    [Required] public required string Output { get; init; }
    
    public string? Type { get; init; }
    
    public string? Value { get; init; }
    
    public Task ExecuteAsync(CommandContext context)
    {
        var workspaceId = context.User.GetWorkspaceGuid();
        return Task.CompletedTask;
    }
}