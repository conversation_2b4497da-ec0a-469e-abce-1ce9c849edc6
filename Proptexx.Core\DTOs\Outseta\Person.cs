using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Proptexx.Core.DTOs.Outseta // Changed namespace
{
    public class Person
    {
        public string? Email { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public MailingAddress? MailingAddress { get; set; }
        public string? PhoneMobile { get; set; }
        public string? PhoneWork { get; set; }
        public string? Timezone { get; set; }
        public DateTime? EmailBounceDateTime { get; set; }
        public DateTime? EmailSpamDateTime { get; set; }
        public DateTime? EmailUnsubscribeDateTime { get; set; }
        public DateTime? EmailLastDeliveredDateTime { get; set; }
        public List<PersonAccountItem>? PersonAccount { get; set; }
        public string? FullName { get; set; }
        public string? Uid { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Updated { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public string? TemporaryPassword { get; set; }
    }

    public class PersonAccountItem
    {
        public string? AccountUid { get; set; }
        public string? Role { get; set; }
    }
}
