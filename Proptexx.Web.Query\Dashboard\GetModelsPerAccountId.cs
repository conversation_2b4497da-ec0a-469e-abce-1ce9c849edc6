﻿using Dapper;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Dashboard
{
    public sealed class GetModelsPerAccountId : BaseFilter, IQuery
    {
        public async Task<object?> ExecuteAsync(QueryContext context)
        {
            await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);

            // Query the materialized view instead of querying the api_logs directly
            var result = await npgsql.QueryAsync<CvRequestStat>(Sql, GetParameters());
            return result;
        }

        public static string Sql => @"
                             SELECT 
                COALESCE(c.title, 'Unknown') AS WorkspaceName,  
                v.endpoint AS endpoint,
                SUM(v.request_count) AS request_count
            FROM telemetry.mv_ai_requests_summary v
            LEFT JOIN core.workspace c ON v.workspace_id = c.id  
            WHERE 
                v.day BETWEEN @StartDate::date AND @EndDate::date
                AND (NULLIF(@ids, ARRAY[]::UUID[]) IS NULL OR v.workspace_id = ANY(@ids))     
            GROUP BY c.title, v.endpoint
            ORDER BY request_count DESC;";
    }

    public sealed class CvRequestStat
    {
        public required string WorkspaceName { get; init; }
        public required string Endpoint { get; init; }
        public required int RequestCount { get; init; }
    }
}
