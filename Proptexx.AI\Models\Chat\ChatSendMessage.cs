using Proptexx.Core.Http;

namespace Proptexx.AI.Models.Chat;

public sealed class ChatSendMessage : TextModel<ChatMessageResponse>
{
    public ChatSendMessage(ITextualClient textualClient) : base(textualClient)
    {
    }

    public override Task<string> ResolvePromptAsync(string message)
    {
        return Task.FromResult($$"""
               The following chat messages was sent from my client, which you should respond to in a polite manner. 
               Remember that this is about real estate and real estate listings, so if the question is of something else, please guide them into the right topic.
               Return the information in JSON format.
               Do not prefixed the string with 'json' ```json``` or similar.

               **Message:**
               `{{message}}`

               Output Format:
               {
                "output": String
               }
               
               Output Example 1:
               {
                 "output": "Yes, this is a nice property"
               }
               
               Output Example 2:
               {
                 "output": "I'd like to show you a detailed comparison about the various properties"
               }
               
               """);
    }
}

public class ChatMessageResponse
{
    public string? Output { get; init; }
}