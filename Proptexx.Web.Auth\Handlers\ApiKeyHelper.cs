using System.Diagnostics.CodeAnalysis;

namespace Proptexx.Web.Auth.Handlers;

public static class ApiKeyHelper
{
    internal static bool TryGetApiKey(HttpContext httpContext, [NotNullWhen(true)] out string? apiKey)
    {
        apiKey = null;

        // Check for the "apiKey" header
        if (httpContext.Request.Headers.TryGetValue("apiKey", out var tmpApiKey))
        {
            apiKey = tmpApiKey.ToString().Trim();
            return !string.IsNullOrWhiteSpace(apiKey);
        }

        // Check for the "Authorization" header with "ApiKey" scheme
        if (httpContext.Request.Headers.TryGetValue("Authorization", out var authorizationHeader))
        {
            var authHeader = authorizationHeader.ToString().Trim();

            if (!string.IsNullOrWhiteSpace(authHeader))
            {
                const string apiKeyScheme = "ApiKey ";
                if (authHeader.StartsWith(apiKeyScheme, StringComparison.OrdinalIgnoreCase))
                {
                    apiKey = authHeader[apiKeyScheme.Length..].Trim();
                    return !string.IsNullOrWhiteSpace(apiKey);
                }
            }
        }

        return false;
    }
}