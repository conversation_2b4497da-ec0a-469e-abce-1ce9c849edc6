using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Proptexx.Core.Configuration;
using Proptexx.Core.Extensions;
using Proptexx.Core.Http;
using Proptexx.Core.Json;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Redis;

namespace Proptexx.Worker;

public static class ProptexxWorkerBuilder
{
    public static IHostApplicationBuilder AddProptexxWorker(this IHostApplicationBuilder builder)
    {
        builder.AddProptexxConfiguration();
        builder.AddProptexxLogging();

        builder.Services.AddJsonConverters();
        builder.Services.AddProptexxHttpClients();
        builder.Services.AddPostgresql(builder.Configuration);
        builder.Services.AddRedis(builder.Configuration);
        builder.Services.AddSingleton<WorkspaceDataHashStore>();

        return builder;
    }
}