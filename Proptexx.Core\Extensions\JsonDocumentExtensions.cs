using System.Text.Json;

namespace Proptexx.Core.Extensions;

public static class JsonDocumentExtensions
{
    public static JsonDocument ToJsonDocument(this object obj, JsonSerializerOptions? options = null)
    {
        using var memoryStream = new MemoryStream();
        using var writer = new Utf8JsonWriter(memoryStream);
        JsonSerializer.Serialize(writer, obj, options);
        memoryStream.Position = 0;
        return JsonDocument.Parse(memoryStream);
    }
    
    public static async Task<JsonDocument> ToJsonDocumentAsync(this object obj, JsonSerializerOptions? options = null)
    {
        using var memoryStream = new MemoryStream();
        await JsonSerializer.SerializeAsync(memoryStream, obj, options);
        memoryStream.Position = 0;
        var result = await JsonDocument.ParseAsync(memoryStream);
        return result;
    }
}