namespace Proptexx.Core.Constants;

/// <summary>
/// Constants for Subscription management system
/// </summary>
public static class SubscriptionConstants
{
    /// <summary>
    /// Product service types for subscriptions
    /// </summary>
    public static class ProductServices
    {
        public const string RealEstateWidgets = "Real Estate Widgets";
        public const string EcommerceWidgets = "Ecommerce Widgets";
        public const string EnterpriseApis = "Enterprise APIs";
        public const string OtherServices = "Other Services";

        public static readonly string[] All = 
        {
            RealEstateWidgets,
            EcommerceWidgets,
            EnterpriseApis,
            OtherServices
        };
    }

    /// <summary>
    /// Status values for subscriptions
    /// </summary>
    public static class Status
    {
        public const string Active = "Active";
        public const string Inactive = "Inactive";
        public const string Suspended = "Suspended";
        public const string Expired = "Expired";

        public static readonly string[] All = 
        {
            Active,
            Inactive,
            Suspended,
            Expired
        };
    }
 

    /// <summary>
    /// Subscription type values for subscriptions
    /// </summary>
    public static class SubscriptionTypes
    {
        public const string Period = "Period";
        public const string OneTime = "OneTime";
        public const string PayAsYouGo = "PayAsYouGo";

        public static readonly string[] All = 
        {
            Period,
            OneTime,
            PayAsYouGo
        };
    }

    /// <summary>
    /// Service types used for subscription classification
    /// </summary>
    public static class ServiceTypes
    {
        public const string Widget = "widget";
        public const string WidgetAccess = "widget-access";
        public const string RealEstateWidget = "real-estate-widget";
        public const string EcommerceWidget = "ecommerce-widget";
        public const string Generative = "gen";
        public const string ComputerVision = "cv";
        public const string Flow = "flow";
        public const string Batch = "batch";
        public const string Sequential = "seq";

        public static readonly string[] WidgetServices = 
        {
            Widget,
            WidgetAccess,
            RealEstateWidget,
            EcommerceWidget
        };

        public static readonly string[] EnterpriseServices = 
        {
            Generative,
            ComputerVision,
            Flow,
            Batch,
            Sequential
        };
    }

    /// <summary>
    /// Default values for different subscription types
    /// </summary>
    public static class Defaults
    {
        // Monthly and Yearly subscriptions
        public const int PeriodQuota = 1000;
        
        // OneTime subscriptions
        public const int TotalQuota = 1000;
        
        // PayAsYouGo subscriptions (no quota limit)
        public const int PayAsYouGoQuota = 0;
        
        public const string Status = SubscriptionConstants.Status.Active;
        public const string SubscriptionType = SubscriptionTypes.PayAsYouGo;
        public const string AllowedPath = "/";
        
        /// <summary>
        /// Get default quota for subscription type
        /// </summary>
        public static int GetDefaultQuota(string subscriptionType) => subscriptionType switch
        {
            SubscriptionTypes.Period  => PeriodQuota,
            SubscriptionTypes.OneTime => TotalQuota,
            SubscriptionTypes.PayAsYouGo => PayAsYouGoQuota,
            _ => PeriodQuota
        };
    }
} 