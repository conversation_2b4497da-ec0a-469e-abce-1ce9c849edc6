using System.Collections.ObjectModel;
using System.Linq.Expressions;

namespace Proptexx.Core.Postgresql.Builder.Predicate;

public sealed class MethodCallExpressionContext
{
    public string MethodName => Expression.Method.Name;
    
    public ReadOnlyCollection<Expression> Arguments => Expression.Arguments;

    public required MethodCallExpression Expression { get; init; }

    public required Action<string> Write { get; init; }

    public required Func<Expression?, Expression?> Visit { get; init; }
}