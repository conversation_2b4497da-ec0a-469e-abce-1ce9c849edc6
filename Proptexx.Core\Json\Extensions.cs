using Microsoft.Extensions.DependencyInjection;
using Proptexx.Core.Json.Converters;

namespace Proptexx.Core.Json;

public static class JsonExtensions
{
    public static IServiceCollection AddJsonConverters(this IServiceCollection services)
    {
        JsonDefaults.AddConverter(new ProductConfigJsonConverter());
        JsonDefaults.AddConverter(new ServiceSettingsConverter());
        return services;
    }
}