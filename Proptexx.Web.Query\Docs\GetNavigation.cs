using Proptexx.Core.Cqrs.Query;

namespace Proptexx.Web.Query.Docs;

public class GetNavigation : IQuery
{
    public Task<object?> ExecuteAsync(QueryContext context)
    {
        var result = new List<NavigationItem>
        {
            new NavigationItem
            {
                Title = "Proptexx API",
                Link = "/api",
                Children = new List<NavigationItem>
                {
                    new NavigationItem { Title = "Authentication API", Link = "/api/auth" },
                    new NavigationItem { Title = "Vision API", Link = "/api/cv" },
                    new NavigationItem { Title = "Flow API", Link = "/api/flow" },
                    new NavigationItem { Title = "Batch API", Link = "/api/batch" },
                    new NavigationItem { Title = "Status API", Link = "/api/status" }
                }
            },
            new NavigationItem
            {
                Title = "Proptexx Widget",
                Link = "/widget",
                Children = new List<NavigationItem>
                {
                    new NavigationItem
                    {
                        Title = "Get Started",
                        Link = "/widget#get-started",
                        Children = new List<NavigationItem>
                        {
                            new NavigationItem { Title = "Copy the Script Tag", Link = "/widget#copy-script" },
                            new NavigationItem { Title = "Add the Script Tag to Your Website", Link = "/widget#add-script" },
                            new NavigationItem { Title = "Save and Upload", Link = "/widget#save-upload" },
                            new NavigationItem { Title = "Verify the Installation", Link = "/widget#verify" },
                            new NavigationItem { Title = "Troubleshooting", Link = "/widget#troubleshooting" }
                        }
                    },
                    new NavigationItem
                    {
                        Title = "Scraping",
                        Link = "/widget/scraping",
                        Children = new List<NavigationItem>
                        {
                            new NavigationItem { Title = "Meta Attributes", Link = "/widget/scraping#meta-attributes" }
                        }
                    },
                    new NavigationItem
                    {
                        Title = "URL Filtering",
                        Link = "/widget/url-filtering",
                        Children = new List<NavigationItem>
                        {
                            new NavigationItem { Title = "Prefix Matching", Link = "/widget/url-filtering#prefix" },
                            new NavigationItem { Title = "Exact Matching", Link = "/widget/url-filtering#exact" },
                            new NavigationItem { Title = "Regex Pattern Matching", Link = "/widget/url-filtering#regex" }
                        }
                    }
                }
            }
        };

        return Task.FromResult<object?>(new { navigation = result });
    }
}

public class NavigationItem
{
    public required string Title { get; set; }

    public string? Link { get; set; } // Optional: URL or route path

    public List<NavigationItem> Children { get; set; } = new();
}
