using Proptexx.Core.Redis;
using Proptexx.Web.Auth.Auth;

namespace Proptexx.Web.Auth.Resolvers;

public sealed class ClientResolver : IClientResolver
{
    private readonly IClientSecretStore _clientSecretStore;
    private readonly IConfiguration _configuration;

    public ClientResolver(IClientSecretStore clientSecretStore, IConfiguration configuration)
    {
        _clientSecretStore = clientSecretStore;
        _configuration = configuration;
    }

    public async Task<IClientResolverResult> ResolveAsync(string identifier, string? accountId, params string[] scopes)
    {
        var clientSecret = await _clientSecretStore.GetAsync(identifier)
            ?? throw new UnauthorizedAccessException();

        var result = new ClientResolverResult
        {
            ClientId = clientSecret.ClientId.ToString(),
            IssueCookie = clientSecret.IssueToken,
            AccessTokenTtl = clientSecret.TokenLifespan ?? _configuration.GetValue<int>("Proptexx:AccessTokenTtl"),
            SessionTokenTtl = clientSecret.SessionLifespan ?? _configuration.GetValue<int>("Proptexx:SessionTokenTtl")
        };

        result.AdditionalClaims.Add("workspaceId", clientSecret.WorkspaceId.ToString());
        result.AdditionalClaims.Add("workspaceName", clientSecret.WorkspaceName);
        result.AdditionalClaims.Add("workspaceTitle", clientSecret.WorkspaceTitle);
        result.AdditionalClaims.Add("clientName", clientSecret.ClientName);
        result.AdditionalClaims.Add("clientSecretId", clientSecret.SecretId.ToString());
        return result;
    }
}

public class ClientResolverResult : IClientResolverResult
{
    public required string ClientId { get; init; }

    public int? AccessTokenTtl { get; init; }

    public int? SessionTokenTtl { get; init; }

    public bool? IssueCookie { get; init; }

    public Dictionary<string, string> AdditionalClaims { get; } = [];
}