using System.Text.Json;
using Microsoft.Extensions.DependencyInjection;
using Proptexx.AI;
using Proptexx.AI.Models.Flow;
using Proptexx.Core.AI;
using Proptexx.Core.Http;
using Proptexx.Core.Json;
using Proptexx.Core.Services;

namespace Proptexx.Worker.DevTools;

public static class TestVirtualRefurnishing
{
    public static async Task ExecuteAsync(ServiceProvider services, CancellationToken cancellationToken)
    {
        var imageAssessmentClient = services.GetRequiredService<IImageAssessmentClient>();

        string[] imageUrls = [
            "https://storage.googleapis.com/proptexx-web-static/temp/shared_images/101d20ca-9642-482c-898c-5cf7c438e24e-**********.jpg",
            "https://storage.googleapis.com/proptexx-web-static/temp/shared_images/13020da1-b476-464b-8006-1f18c6cb8e07-**********.jpg",
            "https://storage.googleapis.com/proptexx-web-static/temp/shared_images/149fa67b-0c02-4bcb-8887-44c69b3e2d99-**********.jpg",
            "https://storage.googleapis.com/proptexx-web-static/temp/shared_images/165cfdc4-ae8d-4749-b35c-ce6c03106728-**********.jpg",
            "https://storage.googleapis.com/proptexx-web-static/temp/shared_images/1923bc43-298e-4612-8176-0fb1879f2a8f-**********.jpg"
        ];

        foreach (var imageUrl in imageUrls)
        {
            await RunAsync(services, imageAssessmentClient, imageUrl, cancellationToken);
        }
    }

    private static async Task RunAsync(IServiceProvider services, IImageAssessmentClient imageAssessmentClient, string imageUrl, CancellationToken cancellationToken)
    {
        var workspaceId = Guid.NewGuid().ToString();
        var assessment = await imageAssessmentClient.InspectImageAsync(imageUrl, cancellationToken);
        var roomScene = services.GetModel<VirtualStagingOrRefurnishing>();
        var payload = JsonDocument.Parse($$"""
                                           {
                                             "imageUrl": "{{imageUrl}}"
                                           }
                                           """);

        var response = await roomScene.InferAsync(new ModelContext
        {
            ItemId = Guid.NewGuid().ToString(),
            WorkspaceId = Guid.NewGuid().ToString(),
            CancellationToken = cancellationToken,
            Payload = payload
        });

        Console.WriteLine(JsonSerializer.Serialize(response.Document, JsonDefaults.JsonSerializerOptions));
    }
}