using System.Text.Json;
using Proptexx.Core.Json;
using Npgsql;
using NpgsqlTypes;

namespace Proptexx.Core.Postgresql.Mappers;

public sealed class JsonDocumentTypeMapper : NpgsqlTypeMapper<JsonDocument>
{
    private readonly JsonSerializerOptions _jsonSettings = JsonDefaults.JsonSerializerOptions;

    public override JsonDocument Parse(object value)
    {
        if (value is not string str)
        {
            throw new NullReferenceException(nameof (str));
        }

        return JsonDocument.Parse(str);
    }

    protected override void SetValue(NpgsqlParameter parameter, JsonDocument? value)
    {
        parameter.NpgsqlDbType = NpgsqlDbType.Jsonb;
        parameter.Value = value;
    }
}