using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Attributes;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Workspace;

public sealed class GetAvailableServices : IQuery
{
    [GuidNotEmpty] public Guid? WorkspaceId { get; init; }
    
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var workspaceId = WorkspaceId ?? context.User.GetWorkspaceGuid();

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var enumerable = await npgsql.QueryAsync<AvailableServiceModel>(AvailableServiceModel.Sql, new { _workspace_id = workspaceId });
        return enumerable;
    }

    public sealed class AvailableServiceModel
    {
        public required string Id { get; init; }
        
        public required string Title { get; init; }
        
        public string? Description { get; init; }
        
        public bool HasService { get; init; }

        public static string Sql => @"
            select s.id,
                   s.title,
                   s.description,
                   (wsb is not null and wsb.expires_at > current_timestamp) as has_service
            from core.service s
            left outer join core.workspace_service_binding wsb on s.id = wsb.service_id and wsb.workspace_id = :_workspace_id
            order by s.title;
        ";
    }
}