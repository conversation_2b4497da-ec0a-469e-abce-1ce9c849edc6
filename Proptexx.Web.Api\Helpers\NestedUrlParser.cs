using System.Text.RegularExpressions;
using System.Web;

namespace Proptexx.Web.Api.Helpers;

public static class NestedUrlParser
{
    private static readonly Regex UrlPatternRegex = new(@"(https?://[^\s/$.?#].[^\s]*)", RegexOptions.Compiled);
 
    public static List<string> GetNestedUrls(string url)
    {
        var result = new List<string>();

        try
        {
            // Validate the initial URL
            if (!Uri.TryCreate(url, UriKind.Absolute, out var uri))
            {
                result.Add(url);
                return result;
            }

            // Parse query parameters for nested URLs
            var queryParams = HttpUtility.ParseQueryString(uri.Query);

            foreach (var key in queryParams.AllKeys)
            {
                if (key is null || queryParams[key] is null) continue;
                var value = queryParams[key];

                if (Uri.IsWellFormedUriString(value, UriKind.Absolute))
                {
                    result.Add(value);
                }
            }

            if (result.Count == 0)
            {
                // Extract URLs from the entire URL string using regex
                var matchUrl = url[2..];
                var matches = UrlPatternRegex.Matches(matchUrl);

                foreach (Match match in matches)
                {
                    var nestedUrl = match.Value;
                    if (Uri.IsWellFormedUriString(nestedUrl, UriKind.Absolute) && !result.Contains(nestedUrl))
                    {
                        result.Add(nestedUrl);
                    }
                }
            }

            // Add the initial URL to the result list at the end if it is valid
            result.Add(url);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error parsing URL: {ex.Message}");
        }

        return result;
    }
}