using System.Collections.Concurrent;

namespace Proptexx.Core.Postgresql.Builder;

internal static class DbRefProvider
{
    private static readonly ConcurrentDictionary<Type, string> Cache = new();
    
    public static string Get<T>() where T : class, IDbEntity 
        => Get(typeof(T));

    public static string Get(Type type)
    {
        if (Cache.TryGetValue(type, out var value))
        {
            return value;
        }
        
        var getNameMethod = type.GetInterface("IDbEntity")?.GetMethod("GetDbRef");

        if (getNameMethod is null || type.IsValueType)
        {
            throw new Exception($"Unable to get the table name from {type}. Make sure it inherits from IDbEntity");
        }

        var instance = Activator.CreateInstance(type);

        if (instance is not IDbEntity entity)
        {
            throw new Exception($"The instance created ({type}) was not of IDbEntity");
        }

        var tableName = entity.GetDbRef();
        Cache.TryAdd(type, tableName);
        return tableName;
    }
}