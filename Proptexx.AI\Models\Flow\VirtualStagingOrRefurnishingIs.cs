using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Proptexx.AI.Models.CV;
using Proptexx.AI.Models.Gen;
using Proptexx.AI.Services;
using Proptexx.Core.AI;
using Proptexx.Core.Http;
using Proptexx.Core.Redis;
using Proptexx.Core.Resilience;
using Proptexx.Core.Storage;
using PayloadService = Proptexx.Core.Services.PayloadService;

namespace Proptexx.AI.Models.Flow;

// ReSharper disable once ClassNeverInstantiated.Global
public class VirtualStagingOrRefurnishingIs : IModel
{
    private readonly IServiceProvider _services;
    private readonly IConfiguration _configuration;
    private readonly ILogger _logger;
    private readonly IGenerativeClient _generativeClient;
    private readonly IStorageService _storageService;
    private readonly WorkspaceDataHashStore _workspaceDataHashStore;
    private readonly IImageAssessmentClient _imageAssessmentService;

    private static long _randomRoomTypeCounter;

    private const int RetryCount = 3;
    private const int DelaySeconds = 1;

    private static DateTime _rateLimitMinute = DateTime.MinValue;
    private static int _rateLimitCount = 0;
    private static readonly object _rateLimitLock = new();
    private static int _rateLimitHigh;
    private static int _rateLimitLow;

    public VirtualStagingOrRefurnishingIs(
        IServiceProvider services,
        IConfiguration configuration,
        ILogger<VirtualStagingOrRefurnishingIs> logger,
        IGenerativeClient generativeClient,
        IStorageService storageService,
        WorkspaceDataHashStore workspaceDataHashStore,
        IImageAssessmentClient imageAssessmentService)
    {
        _services = services;
        _configuration = configuration;
        _logger = logger;
        _generativeClient = generativeClient;
        _storageService = storageService;
        _workspaceDataHashStore = workspaceDataHashStore;
        _imageAssessmentService = imageAssessmentService;

        _rateLimitHigh = configuration.GetValue<int>("is24_bfl_rate_limit_high", 250);
        _rateLimitLow = configuration.GetValue<int>("is24_bfl_rate_limit_low", 150);
    }

    public async Task<ModelResponse> InferAsync(ModelContext context)
    {
        var results = new List<ModelResponse>();

        if (context.Payload is null)
        {
            throw new ApplicationException("Payload is empty");
        }

        var imageUrl = PayloadService.GetRequiredString(context.Payload, "imageUrl");
        var assessment = await _imageAssessmentService.InspectImageAsync(imageUrl, context.CancellationToken);

        if (assessment.Width <= 300 || assessment.Height <= 300)
        {
            throw new ApplicationException("The image size is too small - require width and height greater than 300 pixels");
        }

        var roomSceneModel = _services.GetModel<PreProcessorForRoomScene>();
        var roomSceneResponse = await PollyRetryExtensions.GetDefaultRetryPolicy(_logger, "VirtualStagingOrRefurnishingIs  - InferAsync - PreProcessorForRoomScene", RetryCount, DelaySeconds).ExecuteAsync(()=> roomSceneModel.InferAsync(context.WorkspaceId, assessment, context.CancellationToken));
        results.Add(roomSceneResponse);
        var (modelType, roomType, archStyles) = VerifyAndDetermineRoomStyle(context.Payload, roomSceneResponse);

        var objectsModel = _services.GetModel<PreProcessorForObjects>();
        var objectsResponse = await PollyRetryExtensions.GetDefaultRetryPolicy(_logger, "VirtualStagingOrRefurnishingIs  - InferAsync - PreProcessorForObjects", RetryCount, DelaySeconds).ExecuteAsync(() => objectsModel.InferAsync(context.WorkspaceId, assessment, context.CancellationToken));
        results.Add(objectsResponse);
        VerifyObjectsResult(objectsResponse);

        Dictionary<string, object?>? requestParams = null;
        Dictionary<string, object?>? responseParams = null;

        foreach (var archStyle in archStyles)
        {
            VerifyRoomAndStyleCombination(modelType, roomType, archStyle);

            const int maxRetries = 1;
            for (var i = 0; i <= maxRetries; i++)
            {
                try
                {
                    var response = await CallBflWithRateLimitAsync(
                        modelType, 
                        imageUrl, 
                        assessment.Base64String, 
                        archStyle, 
                        roomType, 
                        context);
                    
                    requestParams = response.RequestParams;
                    responseParams = response.ResponseParams;

                    await CallPostChecksAsync(
                        context.WorkspaceId,
                        response.OutputImageUrl,
                        response.Base64Image,
                        response.MimeType,
                        roomType,
                        context.CancellationToken);

                    results.Add(response);
                    break;
                }
                catch (Exception e)
                {
                    if (i == maxRetries)
                    {
                        var msg = e switch
                        {
                            ApplicationException ae => ae.Message,
                            JsonException => "Internal model problem",
                            _ => "Unable to produce a satisfying result"
                        };

                        throw new ApplicationException(msg, e);
                    }

                    _logger.LogError(e, "Rerun {ImageUrl}", imageUrl);
                }
            }
        }

        var result = await JsonDocumentService.MergeResponsesAsync(results);

        return new VirtualStagingOrRefurnishingResponse
        {
            Document = result,
            RequestParams = requestParams,
            ResponseParams = responseParams
        };
    }

    private static void VerifyObjectsResult(ModelResponse modelResponse)
    {
        var result = modelResponse.GetRequiredResult<PreProcessorForObjects.Result>()
                        ?? throw new ApplicationException("Precheck object model returned empty result");

        if (result.HasPerson) throw new ApplicationException("Image contains a person");
        if (result.HasCloseUpObject) throw new ApplicationException("Image contains a dominant close up object");
        if (result.HasRealAnimal) throw new ApplicationException("Image contains an animal");
        if (result.HasFood) throw new ApplicationException("Image contains food");
        if (result.HasMultipleImages) throw new ApplicationException("Two or more frames detected within the image");
    }

    private static (VirtualStagingModelType modelType, string roomType, string[] archStyles) VerifyAndDetermineRoomStyle(
        JsonDocument? payload, ModelResponse modelResponse)
    {
        var cvRoomScene = modelResponse.GetRequiredResult<PreProcessorForRoomScene.Result>()
                          ?? throw new ApplicationException("Precheck room scene model returned empty result");

        if (!IsIndoor(cvRoomScene)) throw new ApplicationException("Must be an indoor image");

        var roomType = GetRoomType(cvRoomScene, payload);
        var archStyles = GetArchStyles(cvRoomScene, payload);
        var isLivingRoom = roomType == "living room";
        var isBedroom = roomType == "bedroom";
        var isKitchen = roomType == "kitchen";
        var isBathroom = roomType == "bathroom";

        VirtualStagingModelType modelType;
        if (cvRoomScene.IsEmptyRoom && (isLivingRoom || isBedroom))
        {
            modelType = VirtualStagingModelType.GenerativeVirtualStaging;
        }
        else if (!cvRoomScene.IsEmptyRoom && (isLivingRoom || isBedroom))
        {
            modelType = VirtualStagingModelType.GenerativeVirtualRefurnishing;
        }
        else if (!cvRoomScene.IsEmptyRoom && (isBathroom || isKitchen))
        {
            modelType = VirtualStagingModelType.GenerativeVirtualRefurnishing;
        }
        else
        {
            throw new ApplicationException("Generative models does not support this type of image");
        }

        return (modelType, roomType, archStyles);
    }

    private static bool IsIndoor(PreProcessorForRoomScene.Result cvRoomScene)
    {
        if (cvRoomScene.SceneTypes.Length <= 0) return false;
        return cvRoomScene.SceneTypes[0].ToLowerInvariant() is "indoor";
    }

    private static string[] GetArchStyles(PreProcessorForRoomScene.Result cvRoomScene, JsonDocument? payload)
    {
        string[] result = ["modern"];
        if (payload is not null)
        {
            var tempStyle = PayloadService.GetOptionalElement(payload, "architectureStyle");
            if (tempStyle.HasValue)
            {
                if (tempStyle.Value.ValueKind == JsonValueKind.Array)
                {
                    var array = tempStyle.Value.EnumerateArray().ToArray();

                    result = new string[array.Length];
                    for (var i = 0; i < array.Length; i++)
                    {
                        var tempStyleStr = array[i].GetString();
                        if (!string.IsNullOrWhiteSpace(tempStyleStr))
                        {
                            result[i] = tempStyleStr;
                        }
                    }
                }
                else
                {
                    var tempStyleStr = tempStyle.Value.GetString();
                    if (!string.IsNullOrWhiteSpace(tempStyleStr))
                    {
                        result[0] = tempStyleStr;
                    }
                }
            }
        }

        return result;
    }

    private static string GetRoomType(PreProcessorForRoomScene.Result cvRoomScene, JsonDocument? payload)
    {
        string? result = null;
        var roomTypes = cvRoomScene.RoomTypes.OrderByDescending(x => x.Value).ToList();
        var prioritizedRoomTypes = new[] { "living room", "bedroom", "kitchen", "bathroom" };

        for (var i = 0; i < roomTypes.Count; i++)
        {
            foreach (var type in prioritizedRoomTypes)
            {
                if (roomTypes[i].Key.Equals(type, StringComparison.OrdinalIgnoreCase) && roomTypes[i].Value > 0.2f)
                {
                    result ??= type;
                    break;
                }
            }

            if (result != null) break;
        }

        var roomKeys = cvRoomScene.RoomTypes.Keys;
        if (roomKeys.Contains("living room", StringComparer.OrdinalIgnoreCase)
            && roomKeys.Contains("bedroom", StringComparer.OrdinalIgnoreCase))
        {
            result = Interlocked.Increment(ref _randomRoomTypeCounter) % 2 == 0 ? "living room" : "bedroom";
        }

        if (payload is not null)
        {
            var providedRoomType = PayloadService.GetOptionalString(payload, "roomType");
            if (!string.IsNullOrWhiteSpace(providedRoomType))
            {
                if (result is "living room" or "bedroom" && providedRoomType is "living room" or "bedroom")
                {
                    result = providedRoomType;
                }
            }
        }

        return result ?? throw new ApplicationException("Room type not detected as either living room, bedroom, bathroom or kitchen");
    }

    private static void VerifyRoomAndStyleCombination(VirtualStagingModelType modelType, string requestedRoomType, string requestedStyle)
    {
        string modelName;
        Dictionary<string, string[]> dict;
        if (modelType == VirtualStagingModelType.GenerativeVirtualStaging)
        {
            modelName = "virtual staging";
            dict = new Dictionary<string, string[]>
            {
                ["living room"] = [ "scandinavian", "contemporary", "modern", "countryside", "coastal", "industrial", "italian", "wooden" ],
                ["bedroom"] = [ "scandinavian", "contemporary", "modern", "countryside", "coastal", "industrial", "italian", "wooden" ]
            };
        }
        else
        {
            modelName = "virtual refurnishing";
            dict = new Dictionary<string, string[]>
            {
                ["living room"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "industrial", "italian", "wooden", "traditional"],
                ["bedroom"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "industrial", "italian", "wooden", "traditional"],
                ["bathroom"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "industrial", "italian", "traditional"],
                ["kitchen"] = ["scandinavian", "contemporary", "modern", "countryside", "coastal", "industrial", "italian", "traditional"]
            };
        }

        if (!dict.TryGetValue(requestedRoomType, out var styles))
        {
            throw new ApplicationException($"The requested room type `{requestedRoomType}` is not supported for {modelName}");
        }

        if (!styles.Contains(requestedStyle))
        {
            throw new ApplicationException($"The requested style '{requestedStyle}' is not supported for {modelName} of room type `{requestedRoomType}`");
        }
    }

    private async Task CallPostChecksAsync(string workspaceId, string imageUrl, string base64Image, string mimeType, string requestedRoomType, CancellationToken cancellationToken)
    {
        var model = _services.GetModel<PostProcessor>();
        var response = await PollyRetryExtensions.GetDefaultRetryPolicy(_logger, "VirtualStagingOrRefurnishingIs - CallPostChecksAsync", RetryCount, DelaySeconds).ExecuteAsync(() =>
        {
            return model.InferAsync(workspaceId, imageUrl, base64Image, mimeType, cancellationToken);
        });

        var postCvResult = response.GetRequiredResult<PostProcessor.Result>();

        if (postCvResult is null) throw new ApplicationException("Post-check failed");
        if (postCvResult.HasRealAnimal is not false) throw new ApplicationException($"The output image contained a real animal {imageUrl}");
        if (postCvResult.HasHorrorPoster is not false) throw new ApplicationException($"The output image contained a horror poster {imageUrl}");
        if (postCvResult.HasPerson is not false) throw new ApplicationException($"The output image contained a person {imageUrl}");
        if (postCvResult.HasAbnormalSize is not false) throw new ApplicationException($"The output image had an abnormal size {imageUrl}");

        if (requestedRoomType.Equals("living room", StringComparison.OrdinalIgnoreCase) 
            && postCvResult.IsEmptyLivingRoom is true)
        {
            throw new ApplicationException($"The output image is an empty living room {imageUrl}");
        }
        
        if (requestedRoomType.Equals("bedroom", StringComparison.OrdinalIgnoreCase) 
            && postCvResult.IsEmptyBedroom is true)
        {
            throw new ApplicationException($"The output image is an empty bedroom {imageUrl}");
        }
    }

    private static async Task WaitForRateLimitAsync()
    {
        // Get CET timezone with cross-platform compatibility
        TimeZoneInfo cetTimeZone;
        try
        {
            // Try Windows ID first
            cetTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Central European Standard Time");
        }
        catch (TimeZoneNotFoundException)
        {
            try
            {
                // Fallback to IANA ID for Linux/macOS
                cetTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Europe/Berlin");
            }
            catch (TimeZoneNotFoundException)
            {
                // Final fallback - create custom timezone for CET (+1 UTC)
                cetTimeZone = TimeZoneInfo.CreateCustomTimeZone("CET", TimeSpan.FromHours(1), "Central European Time", "Central European Time");
            }
        }

        while (true)
        {
            var now = DateTime.UtcNow;
            var cet = TimeZoneInfo.ConvertTimeFromUtc(now, cetTimeZone);
            int limit = (cet.DayOfWeek == DayOfWeek.Saturday || cet.DayOfWeek == DayOfWeek.Sunday)
                ? _rateLimitLow
                : (cet.Hour >= 8 && cet.Hour < 18 ? _rateLimitHigh : _rateLimitLow);

            var thisMinute = new DateTime(cet.Year, cet.Month, cet.Day, cet.Hour, cet.Minute, 0);
            bool allowed;
            lock (_rateLimitLock)
            {
                if (_rateLimitMinute != thisMinute)
                {
                    _rateLimitMinute = thisMinute;
                    _rateLimitCount = 0;
                }
                allowed = _rateLimitCount < limit;
                if (allowed) _rateLimitCount++;
            }
            if (allowed) return;
            
            var wait = _rateLimitMinute.AddMinutes(1) - cet;
            var delayTime = wait > TimeSpan.Zero ? wait : TimeSpan.FromSeconds(1);
            
            // Add minimum delay to prevent tight loops
            if (delayTime < TimeSpan.FromMilliseconds(100))
                delayTime = TimeSpan.FromMilliseconds(100);
                
            await Task.Delay(delayTime);
        }
    }

    private async Task<GenerativeModelResponse> CallBflWithRateLimitAsync(
        VirtualStagingModelType modelType, 
        string imageUrl, 
        string base64Image, 
        string archStyle, 
        string roomType, 
        ModelContext context)
    {
        GenerativeModel m = modelType == VirtualStagingModelType.GenerativeVirtualStaging
            ? new GenerativeVirtualStaging(_generativeClient, _storageService, _configuration, _workspaceDataHashStore)
            : new GenerativeVirtualRefurnishing(_generativeClient, _storageService, _configuration, _workspaceDataHashStore);

        var ctx = new GenerativeModelContext
        {
            ImageUrl = imageUrl,
            Base64Image = base64Image,
            ArchitectureStyle = archStyle,
            RoomType = roomType,
            ItemId = context.ItemId,
            WorkspaceId = context.WorkspaceId,
            CancellationToken = context.CancellationToken,
            Payload = context.Payload
        };

        // Rate limiting ONLY for BFL calls - preprocessing can continue for other tasks
        await WaitForRateLimitAsync();
        return await m.InferAsync(ctx);
    }
}
