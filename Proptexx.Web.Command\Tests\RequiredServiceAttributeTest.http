### Test RequiredServiceAttribute Integration

### 1. Test ProcessRefurnishingProductReplacement with valid service access
# This should work if the client has real-estate-widget or widget-access service
POST https://command.dev.local/_command
Content-Type: application/json
Authorization: ApiKey YOUR_VALID_API_KEY_HERE

{
  "identifier": "widget.ProcessRefurnishingProductReplacement",
  "payload": {
    "RoomImage": "https://example.com/room.jpg",
    "ProductImageUrl": "https://example.com/product.jpg",
    "ProductName": "Modern Sofa",
    "ProductDescription": "A comfortable modern sofa"
  }
}

### 2. Test ProcessRefurnishingProductReplacement with invalid service access
# This should return 401/403 if the client doesn't have required services
POST https://command.dev.local/_command
Content-Type: application/json
Authorization: ApiKey INVALID_OR_RESTRICTED_API_KEY_HERE

{
  "identifier": "widget.ProcessRefurnishingProductReplacement",
  "payload": {
    "RoomImage": "https://example.com/room.jpg",
    "ProductImageUrl": "https://example.com/product.jpg",
    "ProductName": "Modern Sofa",
    "ProductDescription": "A comfortable modern sofa"
  }
}

### 3. Test command without RequiredServiceAttribute (should work normally)
POST https://command.dev.local/_command
Content-Type: application/json
Authorization: ApiKey YOUR_VALID_API_KEY_HERE

{
  "identifier": "widget.SendMessage",
  "payload": {
    "Message": "Hello, this is a test message"
  }
}

### Expected Results:
# 1. First test should succeed if API key has real-estate-widget or widget-access service
# 2. Second test should fail with "Access denied: Real estate widget or widget access service required"
# 3. Third test should work normally as SendMessage doesn't have RequiredServiceAttribute 