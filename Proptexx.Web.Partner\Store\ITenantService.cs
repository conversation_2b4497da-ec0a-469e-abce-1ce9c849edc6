using System.Text.Json;

namespace Proptexx.Web.Partner.Store;

public interface ITenantService
{
    Task SignupAsync(StoreSignupPayload payload);

    Task<StoreSession?> InitSessionAsync(JsonDocument payload);
}

public class StoreSignupPayload
{
    public required string PartnerToken { get; init; }
    public required string Name { get; init; }
    public required string Email { get; init; }
    public required string CountryCode { get; init; }
    public required string LanguageCode { get; init; }
    public required string RegionId { get; init; }
}

public class StoreSession
{
    public Guid Id { get; } = Guid.NewGuid();
    
    public required Guid UserId { get; init; }
    
    public required string ListingId { get; init; }
    
    public DateTime CreatedAt { get; } = DateTime.UtcNow;
}
