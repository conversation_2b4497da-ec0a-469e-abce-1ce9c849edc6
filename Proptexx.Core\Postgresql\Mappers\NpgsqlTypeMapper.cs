using System.Data;
using Dapper;
using Npgsql;

namespace Proptexx.Core.Postgresql.Mappers;

public abstract class NpgsqlTypeMapper<T> : SqlMapper.TypeHandler<T>
{
    public override void SetValue(IDbDataParameter parameter, T? value)
    {
        if (parameter is not NpgsqlParameter npgsqlParam) return;
        SetValue(npgsqlParam, value);
    }

    protected abstract void SetValue(NpgsqlParameter parameter, T? value);
}