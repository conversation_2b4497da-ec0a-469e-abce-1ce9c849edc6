using System.Collections.Immutable;

namespace Proptexx.Core.Messaging;

public abstract class MessageRecipientOption
{
    protected MessageRecipientOption(Guid accountId)
    {
        this.MessageType = MessageType.Internal;
        this.Recipient = accountId.ToString();
        this.AccountId = accountId;
    }
    
    protected MessageRecipientOption(MessageType messageType, string recipient, Guid? accountId)
    {
        this.MessageType = messageType;
        this.Recipient = recipient;
        this.AccountId = accountId;
    }

    public MessageType MessageType { get; }

    public Guid? AccountId { get; }
    
    public string Recipient { get; }
}

public sealed class InternalRecipientOption : MessageRecipientOption
{
    public InternalRecipientOption(Guid accountId) : base(accountId)
    {
    }
}

public sealed class EmailRecipientOption : MessageRecipientOption
{
    public EmailRecipientOption(string email, Guid? accountId) : base(MessageType.Email, email, accountId)
    {
    }
}

public sealed class SmsRecipientOption : MessageRecipientOption
{
    public SmsRecipientOption(string number, Guid? accountId) : base(MessageType.Sms, number, accountId)
    {
    }
}

public class MessageCreationOptions
{
    public string? Subject { get; private set; }

    public string? Body { get; private set; }
    
    public bool IsRich { get; private set; }

    public Guid? SenderId { get; private set; }

    public DateTime? ScheduledAt { get; private set; }

    public ImmutableList<MessageRecipientOption> Recipients { get; private set; } = ImmutableList<MessageRecipientOption>.Empty;

    public MessageCreationOptions SetSubject(string subject)
    {
        Subject = subject;
        return this;
    }

    public MessageCreationOptions SetBody(string? body, bool isRich)
    {
        Body = body;
        IsRich = isRich;
        return this;
    }

    public MessageCreationOptions SetSender(Guid senderId)
    {
        SenderId = senderId;
        return this;
    }

    public MessageCreationOptions AddRecipient(MessageRecipientOption recipientOption)
    {
        Recipients = Recipients.Add(recipientOption);
        return this;
    }

    public MessageCreationOptions AddRecipients(IEnumerable<MessageRecipientOption> recipients)
    {
        Recipients = Recipients.AddRange(recipients);
        return this;
    }

    public MessageCreationOptions SetScheduledAt(DateTime? scheduledAt)
    {
        ScheduledAt = scheduledAt;
        return this;
    }
}