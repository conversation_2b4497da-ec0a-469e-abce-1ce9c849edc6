using Microsoft.Extensions.Configuration;
using Proptexx.Core.AI;
using Proptexx.Core.Services;
using Proptexx.Core.Storage;
using System.Text.Json;

namespace Proptexx.AI.Models.Flow
{
 
    public class ProductReplacementInRoom : IModel
    {
        private readonly HttpClient _httpClient;
        private readonly IStorageService _storageService;
        private readonly string _apiEndpoint;

        public ProductReplacementInRoom(
            IStorageService storageService,
            IHttpClientFactory httpClientFactory,
            IConfiguration configuration)
        {
            _storageService = storageService;
            _httpClient = httpClientFactory.CreateClient(nameof(ProductReplacementInRoom));
            _httpClient.Timeout = TimeSpan.FromSeconds(130);

            _apiEndpoint = configuration["product_replacement_api_endpoint"]
                ?? throw new ApplicationException("API endpoint (product_replacement_api_endpoint) not configured");
        }

        public async Task<ModelResponse> InferAsync(ModelContext context)
        {
            if (context.Payload is null)
            {
                throw new ApplicationException("Payload is empty");
            }

            var roomImage = PayloadService.GetRequiredString(context.Payload, "roomImage");
            var productName = PayloadService.GetRequiredString(context.Payload, "productName");
            var productImageUrl = PayloadService.GetRequiredString(context.Payload, "productImageUrl");
            var productDescription = PayloadService.GetOptionalString(context.Payload, "productDescription");

            var productImage = await DownloadImageAsBase64Async(productImageUrl, context.CancellationToken);
            var payload = JsonSerializer.Serialize(new 
            { 
                room_image = roomImage,
                product_name = productName,
                product_image = productImage,
                product_description = productDescription
            });

            var apiResponse = await CallImageApiAsync(payload, context.CancellationToken);

            var document = JsonDocument.Parse(apiResponse);

            // check document response 200
            if (document.RootElement.TryGetProperty("status", out var status) && status.GetUInt16() != 200)
            {
                throw new Exception($"Error calling image API: {apiResponse}");
            }

            // Parse the correct path: data.image
            if (!document.RootElement.TryGetProperty("data", out var dataElement) ||
                !dataElement.TryGetProperty("image", out var jsonResult))
            {
                throw new Exception($"Unable to retrieve result for product replacement in room image");
            }

            var generatedBase64Image = jsonResult.GetString();
            if (string.IsNullOrWhiteSpace(generatedBase64Image))
            {
                throw new Exception("Result from model was null or empty");
            }

            // upload image to storage
            const string contentType = "image/jpeg";
            var filename = $"{context.ItemId}_{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}.jpg";
            var url = await _storageService.UploadImageAsync(context.WorkspaceId, filename, generatedBase64Image, contentType);

            // create a new JSON object with all properties from the original document and add imageUrl
            using var docStream = new MemoryStream();
            using (var writer = new Utf8JsonWriter(docStream))
            {
                writer.WriteStartObject();
                foreach (var property in document.RootElement.EnumerateObject())
                {
                    property.WriteTo(writer);
                }
                writer.WriteString("imageUrl", url);
                writer.WriteEndObject();
            }
            docStream.Position = 0;
            var updatedDocument = JsonDocument.Parse(docStream);

            return new ModelResponse
            {
                Document = updatedDocument
            };
        }

        private async Task<string> CallImageApiAsync(string payload, CancellationToken cancellationToken)
        {
            using var content = new StringContent(payload, System.Text.Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(_apiEndpoint, content, cancellationToken);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync(cancellationToken);
        }

        private async Task<string> DownloadImageAsBase64Async(string imageUrl, CancellationToken cancellationToken)
        {
            var imageBytes = await _httpClient.GetByteArrayAsync(imageUrl, cancellationToken);
            return Convert.ToBase64String(imageBytes);
        }

    }
}
