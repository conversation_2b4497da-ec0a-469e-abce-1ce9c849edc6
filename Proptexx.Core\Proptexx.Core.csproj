﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <Authors>Proptexx</Authors>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <LangVersion>12</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Azure.Storage.Blobs" Version="12.24.0" />
        <PackageReference Include="Dapper" Version="2.1.66" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.3" />
        <PackageReference Include="Npgsql" Version="9.0.3" />
        <PackageReference Include="Npgsql.DependencyInjection" Version="9.0.3" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
        <PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
        <PackageReference Include="Serilog.Sinks.Grafana.Loki" Version="8.3.0" />
        <PackageReference Include="DotNetEnv" Version="3.1.1" />
        <PackageReference Include="StackExchange.Redis" Version="2.8.31" />
        <PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
        <PackageReference Include="Polly" Version="8.4.0" />
        <PackageReference Include="Google.Cloud.BigQuery.V2" Version="3.9.0" />
    </ItemGroup>

    <ItemGroup>
      <Content Include=".env">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
      <Content Include=".env.development">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
    </ItemGroup>

</Project>
