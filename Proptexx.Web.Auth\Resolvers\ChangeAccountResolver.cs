using System.ComponentModel.DataAnnotations;
using Dapper;
using Npgsql;
using Proptexx.Core;
using Proptexx.Core.Auth;
using Proptexx.Web.Auth.Auth;

namespace Proptexx.Web.Auth.Resolvers;

public sealed class ChangeAccountResolver : IScopeResolver<ChangeAccountPayload>
{
    private const string TokenInitiatorId = "initiatorId";
    private readonly NpgsqlDataSource _dataSource;

    public ChangeAccountResolver(NpgsqlDataSource dataSource)
    {
        _dataSource = dataSource;
    }
    
    public async Task ResolveAsync(ScopeContext context, ChangeAccountPayload payload, CancellationToken cancellationToken)
    {
        string? initiatorId;
        var claim = context.Identity.Claims.FirstOrDefault(x => x.Type.Equals(TokenInitiatorId));
        if (claim is null)
        {
            context.Identity.EnsureRootAccess();
            initiatorId = context.Identity.GetCallerId();
        }
        else
        {
            initiatorId = claim.Value;
        }

        await using var npgsql = await _dataSource.OpenConnectionAsync(cancellationToken);
        
        var param = new { _account_id = payload.AccountId };
        var account = await npgsql.QueryFirstAsync<ChangeAccountModel>(ChangeAccountModel.Sql, param);

        if (account.Id.ToString().Equals(initiatorId))
        {
            context.RemoveClaim(TokenInitiatorId);
        }
        else
        {
            context.AddClaim(TokenInitiatorId, initiatorId);
        }

        await LoginHelper.SetAccountClaimsAsync(context, account.Id, $"{account.FirstName} {account.FamilyName}", account.Email);
        await LoginHelper.SetWorkspaceClaimsAsync(context, npgsql, account.Id);
        await LoginHelper.SetSystemClaimsAsync(context, account.IsRoot);
    }
}

public class ChangeAccountModel
{
    public required Guid Id { get; init; }
    
    public required string FirstName { get; init; }
    
    public required string FamilyName { get; init; }
    
    public bool IsRoot { get; init; }
    
    public required string Email { get; init; }

    public static string Sql => @"
        select a.id,
               a.first_name,
               a.family_name,
               a.is_root,
               ae.email
        from core.account a
        join core.account_email ae on a.id = ae.account_id
        where a.id = :_account_id
        order by ae.verified_at
        limit 1;
    ";
}

public sealed class ChangeAccountPayload
{
    [Required]
    public required Guid AccountId {  get; init; }
}