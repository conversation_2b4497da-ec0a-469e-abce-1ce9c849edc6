using System.ComponentModel.DataAnnotations;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Stores;

namespace Proptexx.Web.Command.Account;

public sealed class UpdateProfile : ICommand
{
    [Required]
    public required string Id { get; init; }
    
    [Required, MinLength(2)]
    public required string FirstName { get; init; }
    
    [Required, MinLength(2)]
    public required string FamilyName { get; init; }
    
    public int? Gender { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        var callerId = context.User.GetCallerId();
        var accountId = Guid.Parse(Id);

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        await using var trx = await npgsql.BeginTransactionAsync(context.CancellationToken);
        
        try
        {
            await npgsql.Account().Update(accountId, this.FirstName, this.FamilyName, this.Gender);
            await trx.CommitAsync(context.CancellationToken);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            await trx.RollbackAsync(context.CancellationToken);
        }
    }
}