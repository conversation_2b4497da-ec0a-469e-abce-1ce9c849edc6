using System.ComponentModel.DataAnnotations;
using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Attributes;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.System;

public sealed class GetProduct : IQuery
{
    [Required, GuidNotEmpty] public required Guid Id { get; init; }
    
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        context.User.EnsureRootAccess();
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var query = string.Concat(ProductModel.Sql, OrderModel.Sql);
        await using var grid = await npgsql.QueryMultipleAsync(query, new { _product_id = this.Id });

        return new
        {
            product = await grid.ReadFirstOrDefaultAsync<ProductModel>(),
            orders = await grid.ReadAsync<OrderModel>()
        };
    }

    private class ProductModel
    {
        public Guid Id { get; init; }
        
        public required string Title { get; init; }
        
        public string? Description { get; init; }
        
        public string? Summary { get; init; }
        
        public string? Content { get; init; }
        
        public required string PaymentType { get; init; }
        
        public decimal PriceAmount { get; init; }
        
        public required string Currency { get; init; }
        
        public DateTime CreatedAt { get; init; }

        public static string Sql => @"
            select p.id,
                   p.title,
                   p.description,
                   p.summary,
                   p.content,
                   p.payment_type,
                   p.price_amount,
                   p.currency,
                   p.created_at
            from core.product p
            where p.id = :_product_id;
        ";
    }
    
    public class OrderModel
    {
        public Guid Id { get; init; }
        
        public Guid AccountId { get; init; }
        
        public int OrderNbr { get; init; }
        
        public string? ReferencePerson { get; init; }
        
        public required string Currency { get; init; }
        
        public decimal TotalPrice { get; init; }
        
        public DateTime? PaidAt { get; init; }
        
        public required string WorkspaceName { get; init; }
        
        public static string Sql => @"
            select o.id,
                   o.account_id,
                   o.order_nbr,
                   o.created_at,
                   o.reference_person,
                   o.currency,
                   o.total_price,
                   o.paid_at,
                   ws.name as workspace_name
            from core.order o
            join core.order_line ol on o.id = ol.order_id
            join core.workspace ws on ol.workspace_id = ws.id
            where ol.product_id = :_product_id;
        ";
    }
}
