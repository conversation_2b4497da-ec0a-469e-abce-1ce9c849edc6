using Dapper;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Attributes;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Account;

public sealed class GetWorkspaces : IQuery
{
    [GuidNotEmpty] public Guid? Id { get; init; }

    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var accountId = Id ?? Guid.Parse(context.User.GetCallerId());
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var workspaces = await npgsql.QueryAsync<WorkspaceModel>(WorkspaceModel.Sql, new { _account_id = accountId });
        return workspaces;
    }
    
    public sealed class WorkspaceModel
    {
        public required Guid Id { get; init; }
    
        public required string Name { get; init; }
        
        public required string Title { get; init; }
    
        public string? Roles { get; init; }

        public static string Sql => @"
            select w.id,
                   w.name,
                   w.title,
                   string_agg(c.name, ',' order by c.name) as roles
            from core.workspace w
            join core.cluster c on w.id = c.workspace_id
            join core.cluster_account_binding cab on c.id = cab.cluster_id and lower(c.name) in ('administrator', 'member')
            where cab.account_id = :_account_id
            group by w.id, w.name
            order by w.name;
        ";
    }
}
