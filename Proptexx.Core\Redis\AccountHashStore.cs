using StackExchange.Redis;

namespace Proptexx.Core.Redis;

public sealed class AccountHashStore : RedisHashStore<AccountHashStore.AccountModel>
{
    public AccountHashStore(IConnectionMultiplexer connectionMultiplexer) 
        : base(connectionMultiplexer, "accounts")
    {
    }

    protected override string ResolveKey(AccountModel entry) => entry.Id.ToString();

    public class AccountModel
    {
        public required Guid Id { get; init; }

        public required string FirstName { get; init; }

        public required string FamilyName { get; init; }

        public DateTime? CancelledAt { get; init; }
            
        public bool IsRoot { get; init; }
            
        public string? Hash { get; init; }
            
        public string? Salt { get; init; }
    }
}