﻿using System.Globalization;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace Proptexx.Core.Entities
{
    public class TelemetryEntry
    {
        // Basic log properties
        public int Id { get; set; }
        public DateTime Timestamp { get; set; }
        public string Endpoint { get; set; } = string.Empty;
        public string Headers { get; set; } = "{}";
        public string Body { get; set; } = "{}";

        // User and context fields
        public Guid? WorkspaceId { get; set; }
        public Guid? SessionId { get; set; }
        public string ClientVersion { get; set; } = string.Empty;
        public Guid? AccountId { get; set; }
        public string HttpMethod { get; set; } = string.Empty;
        public Guid? ClientId { get; set; }
        public double DurationMs { get; set; }

        // Enrichment fields for filtering and computed endpoints
        public bool IsBatch { get; set; }
        public bool IsApiRequest { get; set; }
        public bool IsAIRequest { get; set; }
        public bool IsWidget { get; set; }
        public int ResponseCode { get; set; }
        public string Identifier { get; set; } = string.Empty;
        public string CleanEndpoint { get; set; } = string.Empty;
        public string ProcessedEndpoint { get; set; } = string.Empty;

        // New field for the client IP address extracted from headers
        public string IP { get; set; } = string.Empty;

        // Optional secret used to enrich user info later.
        public Guid? SecretId { get; set; }

        /// <summary>
        /// Sets flags and computes derived values based on the current properties.
        /// This includes validation of JSON, endpoint processing, widget detection, and IP extraction.
        /// </summary>
        public void SetFlags()
        {
            Headers = IsValidJson(Headers) ? Headers : "{}";
            Body = IsValidJson(Body) ? Body : "{}";

            // Set flag for batch requests.
            if (Endpoint.Contains("batch", StringComparison.OrdinalIgnoreCase))
                IsBatch = true;

            // Set flag for AI requests.
            if (Endpoint.Contains("/gen/", StringComparison.OrdinalIgnoreCase) ||
                Endpoint.Contains("/cv/", StringComparison.OrdinalIgnoreCase))
                IsAIRequest = true;

            // Detect widget-related requests based on the request Body.
            IsWidget = CheckIfWidget(Body);

            // If not batch, AI, or widget, assume it's a regular API request.
            if (!IsBatch && !IsAIRequest && !IsWidget)
                IsApiRequest = true;

            // Attempt to extract an identifier from the request body.
            try
            {
                using var doc = JsonDocument.Parse(Body);
                string extracted = ExtractIdentifier(doc.RootElement);
                if (!string.IsNullOrEmpty(extracted))
                {
                    Identifier = extracted;
                }
            }
            catch { }

            // Compute CleanEndpoint and ProcessedEndpoint.
            CleanEndpoint = Regex.Replace(Endpoint, @"^/(?:cv|gen)/", "", RegexOptions.IgnoreCase);
            CleanEndpoint = Regex.Replace(CleanEndpoint, @"^/", "", RegexOptions.IgnoreCase);
            ProcessedEndpoint = !string.IsNullOrWhiteSpace(Identifier)
                ? ProcessEndpoint(ExtractRelevantPart(Identifier))
                : ProcessEndpoint(CleanEndpoint);

            // Extract the client IP from the Headers.
            IP = ExtractIP();
        }

        private bool IsValidJson(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return false;
            try
            {
                JsonDocument.Parse(input);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private bool CheckIfWidget(string jsonBody)
        {
            try
            {
                using var doc = JsonDocument.Parse(jsonBody);
                if (doc.RootElement.TryGetProperty("scopes", out var scopes)
                    && scopes.ValueKind == JsonValueKind.Object
                    && scopes.TryGetProperty("widget", out _))
                {
                    return true;
                }
            }
            catch { }
            return false;
        }

        private string ExtractIdentifier(JsonElement element)
        {
            if (element.TryGetProperty("identifier", out var idElement))
                return idElement.GetString() ?? string.Empty;

            foreach (var property in element.EnumerateObject())
            {
                if (property.Value.ValueKind == JsonValueKind.Object &&
                    property.Value.TryGetProperty("identifier", out var nestedId))
                {
                    return nestedId.GetString() ?? string.Empty;
                }
            }
            return string.Empty;
        }

        private string ExtractRelevantPart(string identifier)
        {
            var parts = identifier.Split('.');
            return parts.Length > 1 ? parts[1] : identifier;
        }

        private string ProcessEndpoint(string entry)
        {
            var temp = Regex.Replace(entry, @"([a-z])([A-Z])", "$1 $2").Replace("-", " ");
            return CultureInfo.CurrentCulture.TextInfo.ToTitleCase(temp.ToLower());
        }

        /// <summary>
        /// Extracts the client IP from the Headers JSON using the "X-Real-IP" property.
        /// </summary>
        private string ExtractIP()
        {
            try
            {
                using var doc = JsonDocument.Parse(Headers);
                foreach (var property in doc.RootElement.EnumerateObject())
                {
                    if (property.Name.Equals("X-Real-IP", StringComparison.OrdinalIgnoreCase))
                    {
                        return property.Value.GetString() ?? string.Empty;
                    }
                }
            }
            catch { }
            return string.Empty;
        }
    }
}
