### Test API Key Validation Middleware - PathsToValidate Approach

### 1. Test paths that SHOULD be validated (API key required)

# CV endpoint - should require API key
POST https://api.dev.local/cv/alt-text-generator
Content-Type: application/json

{
  "imageUrl": "https://example.com/image.jpg"
}

# Chat endpoint - should require API key  
POST https://api.dev.local/chat/completions
Content-Type: application/json

{
  "messages": [{"role": "user", "content": "Hello"}]
}

# Batch endpoint - should require API key
POST https://api.dev.local/batch
Content-Type: application/json

{
  "requests": []
}

# Widget command endpoint - should require API key
POST https://api.dev.local/_command
Content-Type: application/json

{
  "identifier": "widget.SendMessage",
  "payload": {
    "Message": "Hello from widget"
  }
}

### 4. Test widget command categories

# Expensive command - requires quota > 10 (should work with sufficient quota)
POST https://api.dev.local/_command
Content-Type: application/json
Authorization: ApiKey MDEwNzVkMDktMmI4OC00ZjM1LWJlMTYtODVmN2M4NjE2YjYx

{
  "identifier": "widget.ProcessRefurnishingProductReplacement",
  "payload": {
    "RoomImage": "https://example.com/room.jpg",
    "ProductImageUrl": "https://example.com/product.jpg",
    "ProductName": "Modern Sofa"
  }
}

# Lightweight command - should work normally
POST https://api.dev.local/_command
Content-Type: application/json  
Authorization: ApiKey MDEwNzVkMDktMmI4OC00ZjM1LWJlMTYtODVmN2M4NjE2YjYx

{
  "identifier": "widget.SendChat",
  "payload": {
    "Message": "What's the best color for living room?"
  }
}

# Authentication command - special handling
POST https://api.dev.local/_command
Content-Type: application/json
Authorization: ApiKey MDEwNzVkMDktMmI4OC00ZjM1LWJlMTYtODVmN2M4NjE2YjYx

{
  "identifier": "widget.Login",
  "payload": {
    "Username": "<EMAIL>"
  }
}

### 2. Test paths that should NOT be validated (no API key required)

# Health check - should work without API key
GET https://api.dev.local/health

# Status endpoint - should work without API key  
GET https://api.dev.local/status

# Auth endpoint - should work without API key
POST https://api.dev.local/_auth/login
Content-Type: application/json

{
  "username": "test",
  "password": "test"
}

### 3. Test with valid API key (should work for validated paths)
POST https://api.dev.local/cv/alt-text-generator
Content-Type: application/json
Authorization: ApiKey MDEwNzVkMDktMmI4OC00ZjM1LWJlMTYtODVmN2M4NjE2YjYx

{
  "imageUrl": "https://example.com/image.jpg"
}

# Widget command with valid API key - should work
POST https://api.dev.local/_command
Content-Type: application/json
Authorization: ApiKey MDEwNzVkMDktMmI4OC00ZjM1LWJlMTYtODVmN2M4NjE2YjYx

{
  "identifier": "widget.IndoorStagingOrRefurnishing", 
  "payload": {
    "ImageUrl": "https://example.com/room.jpg",
    "RoomType": "living_room",
    "ArchitectureStyle": "modern"
  }
}

### 5. Test with valid API key using apiKey header (PayAsYouGo subscription)
POST https://api.dev.local/cv/alt-text-generator
Content-Type: application/json
apiKey: ZTQwYzhmOTEtZjQ2Ni00YzFkLTlkYzctYmJmOGY2MjAyMjE4

{
  "imageUrl": "https://example.com/image.jpg"
}

### 5. Test Widget endpoint (should be classified as Widget.General)
POST https://api.dev.local/widget/render
Content-Type: application/json
Authorization: ApiKey MDEwNzVkMDktMmI4OC00ZjM1LWJlMTYtODVmN2M4NjE2YjYx

{
  "apiKey": "MDEwNzVkMDktMmI4OC00ZjM1LWJlMTYtODVmN2M4NjE2YjYx",
  "pageUrl": "https://example.com/test"
}

### 6. Test Batch endpoint (should be classified as BatchHandler)
POST https://api.dev.local/batch
Content-Type: application/json
Authorization: ApiKey MDEwNzVkMDktMmI4OC00ZjM1LWJlMTYtODVmN2M4NjE2YjYx

{
  "items": [
    {
      "model": "cv/alt-text-generator",
      "config": {
        "imageUrl": "https://example.com/image.jpg"
      }
    }
  ]
}

### 7. Test Virtual Staging endpoint (should be classified as Generative.VirtualStaging)
POST https://api.dev.local/flow/virtual-staging-or-refurnishing
Content-Type: application/json
Authorization: ApiKey MDEwNzVkMDktMmI4OC00ZjM1LWJlMTYtODVmN2M4NjE2YjYx

{
  "image_url": "https://example.com/room.jpg"
}

### 8. Test Health endpoint (should be excluded from validation)
GET https://api.dev.local/health

### 9. Test Status endpoint (should be excluded from validation)
GET https://api.dev.local/status/12345678-1234-1234-1234-123456789012

### Expected Responses:
# 1. 401 Unauthorized - "API key is required"
# 2. 401 Unauthorized - "Invalid API key"
# 3. Success (200/202) with usage incremented for OneTime subscription
# 4. Success (200/202) with usage incremented for PayAsYouGo subscription
# 5. Success (200/202) with Widget.General classification
# 6. Success (200/202) with BatchHandler classification
# 7. Success (200/202) with Generative.VirtualStaging classification
# 8. Success (200) - Health check bypassed validation
# 9. Success (200) - Status check bypassed validation 