using System.Security.Claims;
using Microsoft.AspNetCore.Http;

namespace Proptexx.Core.Extensions;

public static class DictionaryExtensions
{
    public static IDictionary<string, string> ParseHeaderDictionary(this IHeaderDictionary dic)
    {
        Dictionary<string, string> result = [];
        foreach (var (key, value) in dic)
        {
            var v = string.Join(',', value.ToArray());
            if (string.IsNullOrWhiteSpace(v)) continue;
            result.Add(key, v.Replace("\"", "'"));
        }

        return result;
    }

    public static IList<Claim> ToClaims(this IDictionary<string, string> claims)
    {
        return claims.Select(x => new Claim(x.Key, x.Value)).ToList();
    }

    public static void MergeClaims(this IDictionary<string, string> original, IDictionary<string, string>? additions)
    {
        if (additions is null) return;
        foreach (var (key, value) in additions)
        {
            original[key] = value;
        }
    }
}