using Proptexx.AI;
using Proptexx.AI.Models.CV;

namespace Proptexx.Worker.DevTools;

public static class PostCheckTester
{
    internal static async Task ExecuteAsync(IServiceProvider services, string imageUrl, string base64Image, string mimeType, PreProcessorForRoomScene.Result cvResult, CancellationToken cancellationToken)
    {
        var model = services.GetModel<PostProcessor>();
        // var response = await model.InferAsync(imageUrl, base64Image, mimeType, false);
        // var postCvResult = response.Document?.RootElement.Deserialize<PostProcessor.Result>(JsonDefaults.JsonSerializerOptions);

        // if (postCvResult is null) throw new ApplicationException("Post-check failed");
        // if (postCvResult.HasRealAnimal is not false) throw new ApplicationException("The output image contained a real animal");
        // if (postCvResult.HasHorrorPoster is not false) throw new ApplicationException("The output image contained a horror poster");
        // if (postCvResult.HasPerson is not false) throw new ApplicationException("The output image contained a person");
        // if (postCvResult.HasAbnormalSize is not false) throw new ApplicationException("The output image had an abnormal size");
        //
        // var roomKeys = cvResult.RoomTypes.Keys.ToList();
        // if (roomKeys.Contains("living room", StringComparer.OrdinalIgnoreCase)
        //     && postCvResult.IsEmptyLivingRoom is true)
        // {
        //     throw new ApplicationException("The output image is an empty living room");
        // }
        //
        // if (roomKeys.Contains("bedroom", StringComparer.OrdinalIgnoreCase)
        //     && postCvResult.IsEmptyBedroom is true)
        // {
        //     throw new ApplicationException("The output image is an empty bedroom");
        // }
    }
}