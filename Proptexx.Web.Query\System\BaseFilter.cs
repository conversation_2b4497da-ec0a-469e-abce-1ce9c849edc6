﻿using System.ComponentModel.DataAnnotations;

namespace Proptexx.Web.Query.System;

public abstract class BaseFilter
{
    [Required]
    public required DateTime? StartDate { get; init; }

    [Required]
    public required DateTime? EndDate { get; init; }

    public int PageNumber { get; init; } = 1;

    public int PageSize { get; init; } = 10;

    public string? SortBy { get; init; }

    public string? SortOrder { get; init; } = "ASC";

    public string? Filter { get; init; }

    public object GetParameters()
    {
        return new
        {
            startDate = StartDate,
            endDate = EndDate,
            pageNumber = PageNumber,
            pageSize = PageSize,
            sortBy = SortBy,
            sortOrder = SortOrder,
            filters = Filter
        };
    }
}
