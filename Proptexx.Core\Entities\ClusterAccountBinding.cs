using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class ClusterAccountBinding : IDbTable
{
    public required Guid ClusterId { get; init; }

    public required Guid AccountId { get; init; }

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public DateTime? UpdatedAt { get; init; }

    private DateTime? _verifiedAt;
    public DateTime? VerifiedAt
    {
        get => _verifiedAt;
        init => _verifiedAt = value;
    }

    public Guid? VerifiedBy { get; private set; }

    public string GetDbRef() => "core.cluster_account_binding";

    public ClusterAccountBinding Verify(Guid verifiedBy, DateTime? verifiedAt)
    {
        this.VerifiedBy = verifiedBy;
        this._verifiedAt = verifiedAt ?? DateTime.UtcNow;
        return this;
    }
}