﻿using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Proptexx.Core.AI;
using Proptexx.Core.Services;
using Proptexx.Core.Storage;

namespace Proptexx.AI.Models.Flow;

public class VirtualStagingProductPlacement : IModel
{
    private readonly HttpClient _httpClient;
    private readonly IStorageService _storageService;
    private readonly string _apiEndpoint;

    public VirtualStagingProductPlacement(
        IStorageService storageService,
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration)
    {
        _storageService = storageService;
        _httpClient = httpClientFactory.CreateClient(nameof(VirtualStagingProductPlacement));
        _httpClient.Timeout = TimeSpan.FromSeconds(130);

        _apiEndpoint = configuration["img_api_endppint"]
            ?? throw new ApplicationException("API endpoint (img_api_endppint) not configured");
    }

    public async Task<ModelResponse> InferAsync(ModelContext context)
    {
        if (context.Payload is null)
        {
            throw new ApplicationException("Payload is empty");
        }

        var imageUrl = PayloadService.GetRequiredString(context.Payload, "imageUrl");

        var base64Image = await DownloadImageAsBase64Async(imageUrl, context.CancellationToken);
        var apiResponse = await CallImageApiAsync(base64Image, context.CancellationToken);
        var document = JsonDocument.Parse(apiResponse);

        // check document response 200
        if (document.RootElement.TryGetProperty("status", out var status) && status.GetUInt16() != 200)
        {
            throw new Exception($"Error calling image API: {apiResponse}");
        }

        if (!document.RootElement.TryGetProperty("generated_image", out var jsonResult))
        {
            throw new Exception($"Unable to retrieve result on {imageUrl} from VirtualStagingProductPlacement");
        }

        var generatedBase64Image = jsonResult.GetString();
        if (string.IsNullOrWhiteSpace(generatedBase64Image))
        {
            throw new Exception($"Result on {imageUrl} from model was null or empty");
        }

        // upload image to storage
        const string contentType = "image/jpeg";
        var filename = $"{context.ItemId}_{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}.jpg";
        var url = await _storageService.UploadImageAsync(context.WorkspaceId, filename, generatedBase64Image, contentType);

        // create a new JSON object with all properties from the original document and add imageUrl
        using var docStream = new MemoryStream();
        using (var writer = new Utf8JsonWriter(docStream))
        {
            writer.WriteStartObject();
            foreach (var property in document.RootElement.EnumerateObject())
            {
                property.WriteTo(writer);
            }
            writer.WriteString("imageUrl", url);
            writer.WriteEndObject();
        }
        docStream.Position = 0;
        var updatedDocument = JsonDocument.Parse(docStream);

        return new ModelResponse
        {
            Document = updatedDocument
        };
    }

    private async Task<string> DownloadImageAsBase64Async(string imageUrl, CancellationToken cancellationToken)
    {
        var imageBytes = await _httpClient.GetByteArrayAsync(imageUrl, cancellationToken);
        return Convert.ToBase64String(imageBytes);
    }

    private async Task<string> CallImageApiAsync(string base64Image, CancellationToken cancellationToken)
    {
        var payload = JsonSerializer.Serialize(new { image = base64Image });
        using var content = new StringContent(payload, System.Text.Encoding.UTF8, "application/json");

        using var response = await _httpClient.PostAsync(_apiEndpoint, content, cancellationToken);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadAsStringAsync(cancellationToken);
    }

}