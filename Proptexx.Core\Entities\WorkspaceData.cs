using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class WorkspaceData : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; } = Guid.NewGuid();

    public required Guid WorkspaceId { get; init; }

    public required string DataKey { get; init; }
    
    public string? DataValue { get; init; }

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;
    
    public string GetDbRef() => "core.workspace_data";
}