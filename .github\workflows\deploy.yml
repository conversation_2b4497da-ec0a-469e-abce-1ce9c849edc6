name: Container build for the Proptexx Server solution

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Set up Podman
      run: |
        sudo apt-get update
        sudo apt-get -y install podman

#    - name: Login to Azure
#      uses: azure/login@v1
#      with:
#        creds: ${{ secrets.AZURE_CREDENTIALS }}

    - name: Podman login to ACR
      run: |
        podman login ${{ secrets.REGISTRY }} -u ${{ secrets.ACR_USERNAME }} --password ${{ secrets.ACR_PASSWORD }}

    - name: Set short SHA
      run: echo "SHORT_SHA=${GITHUB_SHA::7}" >> $GITHUB_ENV

    - name: Run build script
      run: |
        python ./scripts/build.py \
          --program podman \
          --registry ${{ secrets.REGISTRY }} \
          --tags latest,${{ env.SHORT_SHA }} .

    - name: Run push script
      run: |
        python ./scripts/push.py \
          --program podman \
          --registry ${{ secrets.REGISTRY }} \
          --tags latest,${{ env.SHORT_SHA }}
