using System.Data;
using Proptexx.Core.Entities;
using Proptexx.Core.Messaging;
using Proptexx.Core.Postgresql.Builder;

namespace Proptexx.Core;

public static class MessageService
{
    public static async Task CreateAndSendAsync(IDbConnection conn, string subject, MessageCreationOptions? options = null)
    {
        options ??= new MessageCreationOptions();
        options.SetSubject(subject);

        if (string.IsNullOrWhiteSpace(options.Subject))
        {
            throw new NullReferenceException(nameof(options.Subject));
        }

        var message = new Message{
            Subject = options.Subject,
            Content = options.Body,
            SenderId = options.SenderId,
            IsRich = options.IsRich
        };

        var recipients = new List<MessageRecipient>();
        foreach (var r in options.Recipients)
        {
            recipients.Add(new MessageRecipient
            {
                MessageId = message.Id,
                MessageType = r.MessageType,
                Recipient = r.Recipient,
                RecipientId = r.AccountId,
            });
        }

        await conn.InsertAsync(message);
        await conn.InsertAsync(recipients);
    }

    public static Task ReplyToMessageAsync(Guid messageId, MessageCreationOptions replyOptions)
    {
        throw new NotImplementedException();
    }

    public static Task ForwardMessageAsync(Guid messageId, MessageCreationOptions forwardOptions)
    {
        throw new NotImplementedException();
    }
}