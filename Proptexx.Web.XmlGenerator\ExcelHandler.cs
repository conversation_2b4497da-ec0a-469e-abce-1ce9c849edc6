using ClosedXML.Excel;
using Dapper;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Storage;

namespace Proptexx.Web.XmlGenerator;

public sealed class ExcelHandler
{
    private readonly ILogger<ExcelHandler> _logger;
    private readonly IStorageService _storageService;

    public ExcelHandler(ILogger<ExcelHandler> logger, IStorageService storageService)
    {
        _logger = logger;
        _storageService = storageService;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await using var npgsql = await context.OpenNpgsqlAsync();

            const string sql = @"
                select coalesce(bi.config->>'imageUrl', bi.config->>'image_url') as image_url,
                       jsonb_object_agg(bi.model, bi.result) as model_results
                from batching.batch_item bi
                where bi.batch_id = :_batch_id
                group by image_url
            ";

            if (!context.Request.RouteValues.TryGetValue("batchId", out var tempBatchId) ||
                !Guid.TryParse(tempBatchId?.ToString(), out var batchId))
            {
                throw new ApplicationException("Parameter batchId is missing");
            }

            var param = new
            {
                _batch_id = batchId
            };

            var enumerable = await npgsql.QueryAsync<BatchModel>(sql, param);
            var list = enumerable.ToList();
            var firstItem = list.FirstOrDefault();
            
            if (firstItem is null)
            {
                throw new ApplicationException("Unable to parse the property names of the batched data");
            }

            using var workbook = new XLWorkbook();
            var worksheet = workbook.AddWorksheet("Sheet1");

            var row = 1;
            var column = 1;

            worksheet.Cell(row, column).Value = "Image URL";
            foreach (var key in firstItem.ModelResults.Keys)
            {
                worksheet.Cell(row, ++column).Value = key;
            }
            
            foreach (var item in list)
            {
                row++;
                column = 1;

                worksheet.Cell(row, column).Value = item.ImageUrl;
                foreach (var value in item.ModelResults.Values)
                {
                    var cell = worksheet.Cell(row, ++column);
                    cell.Value = value?.ToString() ?? "";
                }
            }

            await using var stream = new MemoryStream();
            workbook.SaveAs(stream);

            var containerName = "xlsx";
            var fileName = "temp-output.xlsx";
            var contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            var response = await _storageService.UploadImageAsync(containerName, fileName, stream, contentType);

            context.Response.StatusCode = StatusCodes.Status200OK;
            await context.Response.WriteAsJsonAsync(new { fileName = response });
        }
        catch (ApplicationException e)
        {
            context.Response.StatusCode = StatusCodes.Status400BadRequest;
            await context.Response.WriteAsJsonAsync(new { error = e.Message });
            _logger.LogError(e, "Application exception");
        }
        catch (Exception e)
        {
            context.Response.StatusCode = StatusCodes.Status500InternalServerError;
            await context.Response.WriteAsJsonAsync(new { error = "A system error occured" });
            _logger.LogError(e, "System exception");
        }
    }
}

public class BatchModel
{
    public required string ImageUrl { get; init; }

    public IDictionary<string, object?> ModelResults { get; init; } = new Dictionary<string, object?>();
}