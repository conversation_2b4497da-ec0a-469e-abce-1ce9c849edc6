using Google.Cloud.BigQuery.V2;
using Google.Apis.Auth.OAuth2;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Entities;

namespace Proptexx.Core.BigQuery;

public class BigQueryWebhookFeedbackExporter
{
    private readonly BigQueryClient _bigQueryClient;
    private readonly ILogger<BigQueryWebhookFeedbackExporter> _logger;
    private readonly string _projectId;
    private readonly string _datasetId;

    public BigQueryWebhookFeedbackExporter(IConfiguration configuration, ILogger<BigQueryWebhookFeedbackExporter> logger)
    {
        _logger = logger;
        
        // Read from configuration, same pattern as other BigQuery services
        _projectId = configuration.GetValue<string>("BIGQUERY_PROJECT_ID") 
            ?? throw new ArgumentNullException("BIGQUERY_PROJECT_ID configuration is required");
            
        _datasetId = configuration.GetValue<string>("BIGQUERY_DATASET_ID") 
            ?? throw new ArgumentNullException("BIGQUERY_DATASET_ID configuration is required");
        
        var credentialsJson = configuration.GetValue<string>("BIGQUERY_CREDENTIALS_JSON");
        var defaultLocation = configuration.GetValue<string>("BIGQUERY_DEFAULT_LOCATION") ?? "europe-west1";
            
        if (!string.IsNullOrEmpty(credentialsJson))
        {
            _bigQueryClient = BigQueryClient.Create(_projectId, GoogleCredential.FromJson(credentialsJson));
            _logger.LogInformation("Using BIGQUERY_CREDENTIALS_JSON for BigQuery webhook feedback authentication");
        }
        else
        {
            // Fall back to Application Default Credentials (ADC)
            _bigQueryClient = BigQueryClient.Create(_projectId);
            _logger.LogInformation("Using Application Default Credentials for BigQuery webhook feedback");
        }
        _bigQueryClient.WithDefaultLocation(defaultLocation);
        
        _logger.LogInformation("BigQuery webhook feedback exporter initialized for project {ProjectId}, dataset {DatasetId}", _projectId, _datasetId);
    }

    public async Task ExportWebhookFeedbackAsync(IEnumerable<WebhookFeedback> feedbacks, CancellationToken cancellationToken = default)
    {
        var feedbackList = feedbacks.ToList();
        if (!feedbackList.Any())
        {
            return;
        }

        try
        {
            _logger.LogInformation("Exporting {FeedbackCount} webhook feedback records to BigQuery", feedbackList.Count);

            // Ensure BigQuery tables exist before attempting to use them
            await BigQuerySchemas.EnsureTablesExistAsync(_bigQueryClient, _datasetId, _logger);

            var dataset = await _bigQueryClient.GetDatasetAsync(_datasetId, cancellationToken: cancellationToken);
            var table = await dataset.GetTableAsync("webhook_feedback", cancellationToken: cancellationToken);

            var rows = feedbackList.Select(feedback => new BigQueryInsertRow
            {
                ["id"] = feedback.Id.ToString(),
                ["batch_id"] = feedback.BatchId.ToString(),
                ["callback_url"] = feedback.CallbackUrl,
                ["sent_at"] = feedback.SentAt,
                ["retry_attempt"] = feedback.RetryAttempt,
                ["is_success"] = feedback.IsSuccess,
                ["status_code"] = feedback.StatusCode,
                ["response_text"] = feedback.ResponseText
            }).ToList();

            await table.InsertRowsAsync(rows, cancellationToken: cancellationToken);
            
            _logger.LogInformation("Successfully exported {FeedbackCount} webhook feedback records to BigQuery", feedbackList.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to export webhook feedback to BigQuery");
            throw;
        }
    }

    public async Task ExportSingleWebhookFeedbackAsync(WebhookFeedback feedback, CancellationToken cancellationToken = default)
    {
        await ExportWebhookFeedbackAsync(new[] { feedback }, cancellationToken);
    }
}
