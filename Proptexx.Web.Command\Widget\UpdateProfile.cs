using System.ComponentModel.DataAnnotations;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Stores;
using Proptexx.Core.Utils;

namespace Proptexx.Web.Command.Widget;

public sealed class UpdateProfile : ICommand
{
    [Required]
    public required string FullName { get; init; }
    
    [Required, EmailAddress]
    public required string Email { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        var accountId = context.User.GetCallerGuid();

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        await using var trx = await npgsql.BeginTransactionAsync(context.CancellationToken);
        
        try
        {
            var (firstName, familyName) = NameHelper.ParseFullName(this.FullName);
            await npgsql.Account().Update(accountId, firstName, familyName, this.Email);
            await trx.CommitAsync(context.CancellationToken);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            await trx.RollbackAsync(context.CancellationToken);
        }
    }
}