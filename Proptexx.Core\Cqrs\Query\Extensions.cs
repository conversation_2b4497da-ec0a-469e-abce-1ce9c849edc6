using Microsoft.Extensions.DependencyInjection;

namespace Proptexx.Core.Cqrs.Query;

public static class Extensions
{
    public static IServiceCollection AddQueries(this IServiceCollection services, Action<QueryConfig> configFn)
    {
        var config = new QueryConfig();
        configFn(config);
        services.AddSingleton(config);
        services.AddScoped<QueryHttpHandler>();
        return services;
    }
}
