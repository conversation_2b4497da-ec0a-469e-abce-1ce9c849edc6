using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Npgsql;

namespace Proptexx.Core.Postgresql;

public static class ContextExtensions
{
    public static NpgsqlConnection OpenNpgsql(this ProptexxContext context)
        => context
            .GetService<NpgsqlDataSource>()
            .OpenConnection();

    public static ValueTask<NpgsqlConnection> OpenNpgsqlAsync(this ProptexxContext context,
        CancellationToken cancellationToken)
        => context
            .GetService<NpgsqlDataSource>()
            .OpenConnectionAsync(cancellationToken);

    public static NpgsqlConnection OpenNpgsql(this HttpContext context)
        => context
            .RequestServices.GetRequiredService<NpgsqlDataSource>()
            .OpenConnection();

    public static ValueTask<NpgsqlConnection> OpenNpgsqlAsync(this HttpContext context)
        => context
            .RequestServices.GetRequiredService<NpgsqlDataSource>()
            .OpenConnectionAsync(context.RequestAborted);
}
