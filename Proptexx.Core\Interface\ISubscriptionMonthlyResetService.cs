namespace Proptexx.Core.Services;

/// <summary>
/// Interface for subscription renewal background service
/// Handles automatic renewal and reset operations for different subscription types
/// </summary>
public interface IRenewalSubscriptionBackgroundService
{
    /// <summary>
    /// Process subscription renewals based on subscription types
    /// </summary>
    Task ProcessSubscriptionRenewalsAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Reset period usage for subscriptions that need it
    /// </summary>
    Task ResetPeriodUsageAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Update subscription statuses based on expiry dates
    /// </summary>
    Task UpdateSubscriptionStatusesAsync(CancellationToken cancellationToken = default);
} 