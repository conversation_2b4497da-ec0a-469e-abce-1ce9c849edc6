using System.Diagnostics;
using System.Text.Json;
using StackExchange.Redis;

namespace Proptexx.Worker;

public static class RedisQueueHelper
{
    public static async Task<List<T>> WaitForQueueItemsAsync<T>(IDatabase redis, string queueName, int popCount, CancellationToken cancellationToken)
    {
        var startedAt = Stopwatch.StartNew();
        List<T> result = [];

        while (true)
        {
            if (result.Count >= popCount) break;
            if (result.Count > 0 && startedAt.ElapsedMilliseconds > 2000) break;

            var values = await redis.ListRightPopAsync(queueName, popCount) ?? null;
            if (values is null || values.Length <= 0)
            {
                await Task.Delay(200, cancellationToken);
                continue;
            }

            for (var i = 0; i < values.Length; i++)
            {
                var value = values[i].ToString();
                if (string.IsNullOrWhiteSpace(value)) continue;
                var obj = JsonSerializer.Deserialize<T>(value);
                if (obj is null) continue;
                result.Add(obj);
            }
        }

        return result;
    }
}