using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class ClientSecret : IDbTable
{
    public Guid Id { get; set; } 

    public required Guid ClientId { get; init; }

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public DateTime? ExpiredAt { get; private set; }

    public long TotalUsage { get; set; } // Total usage since creation

    public string GetDbRef() => "core.client_secret";

    public ClientSecret SetExpired(DateTime expiredAt)
    {
        this.ExpiredAt = expiredAt;
        return this;
    }

    public ClientSecret RevokeExpired()
    {
        this.ExpiredAt = null;
        return this;
    }
}