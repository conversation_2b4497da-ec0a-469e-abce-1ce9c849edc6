﻿using System.ComponentModel.DataAnnotations;
using System.Text;
using System.Text.Json;
using Proptexx.AI;
using Proptexx.AI.Models.Flow;
using Proptexx.Core;
using Proptexx.Core.AI;
using Proptexx.Core.Attributes;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Resilience;
using Proptexx.Core.Services;
using StackExchange.Redis;
using static Proptexx.Core.Constants.WidgetConstants;

namespace Proptexx.Web.Command.Widget;

[RequiredService(ServiceNames.RealEstateWidget, ServiceNames.WidgetAccess, ErrorMessage = "Access denied: Real estate widget or widget access service required")]
public sealed class StagingOrRefurnishingV2Command : ICommand
{
    private static readonly Random _rnd = new();

    [Required]
    public required string ImageBase64 { get; init; }

    [Required]
    public required string RoomType { get; init; }

    [Required]
    public required string SceneType { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        var logger = context.GetService<ILogger<StagingOrRefurnishingV2Command>>();

        try
        {
            var workspaceId = context.User.GetWorkspaceGuid();
            var redis = context.GetService<IConnectionMultiplexer>().GetDatabase();

            var cacheKey = EncodingService.UniqueKey(this.ImageBase64, this.RoomType, this.SceneType, _rnd.Next(10000, 99999));
            var cachedImage = await CheckCacheAsync(redis, cacheKey);
            if (!string.IsNullOrWhiteSpace(cachedImage))
            {
                context.AddData("responseUrl", cachedImage);
                return;
            }

            var env = context.GetService<IHostEnvironment>();

            var payload = JsonSerializer.SerializeToDocument(new
            {
                this.ImageBase64,
                this.SceneType,
                this.RoomType
            });

            var response = await PollyRetryExtensions.GetDefaultRetryPolicy(logger, $"VirtualStagingOrRefurnishingV2 - ExecuteAsync").ExecuteAsync(() =>
            {
                return ((IModel)context.Services.GetModel<StagingOrRefunishingV2>()).InferAsync(new ModelContext
                {
                    Payload = payload,
                    ItemId = Guid.NewGuid().ToString(),
                    WorkspaceId = workspaceId.ToString(),
                    CancellationToken = context.CancellationToken
                });
            });

            if (response.Document is null)
            {
                throw new ApplicationException("Model returned empty result");
            }

            var imageUrl = PayloadService.GetRequiredString(response.Document, "imageUrl");
            context.AddData("responseUrl", imageUrl);

            var redisValue = JsonSerializer.Serialize(new GenerativeImageCacheItem
            {
                ImageUrl = imageUrl,
                CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            });

            await redis.HashSetAsync("widget_generative_cache", cacheKey, redisValue);
        }
        catch (ApplicationException e)
        {
            throw new CommandException(e.Message, e);
        }
        catch (Exception e)
        {
            logger.LogError(e, "Unable to generate output");
            throw new CommandException("Unable to generate output");
        }
    }

    private static async Task<string?> CheckCacheAsync(IDatabaseAsync redis, string key)
    {
        var redisValue = await redis.HashGetAsync("widget_generative_cache", key);

        if (!redisValue.HasValue) return null;

        var str = redisValue.ToString();
        var obj = JsonSerializer.Deserialize<GenerativeImageCacheItem>(str);
        return obj?.ImageUrl;
    }
}

