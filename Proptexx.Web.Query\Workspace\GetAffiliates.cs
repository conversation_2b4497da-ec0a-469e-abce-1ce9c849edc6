using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Attributes;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Workspace;

public sealed class GetAffiliates : IQuery
{
    [GuidNotEmpty] public Guid? WorkspaceId { get; init; }
    
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var workspaceId = WorkspaceId ?? context.User.GetWorkspaceGuid();
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var result = await npgsql.QueryAsync<AffiliateModel>(AffiliateModel.Sql, new { _workspace_id = workspaceId });
        return result;
    }

    public sealed class AffiliateModel
    {
        public required Guid Id { get; init; }
        
        public required string Name { get; init; }
        
        public required string Title { get; init; }
        
        public DateTime CreatedAt { get; init; }
        
        public static string Sql => @"
            select w.id,
                   w.name,
                   w.title,
                   w.created_at
            from core.workspace w
            where w.parent_id = :_workspace_id;
        ";
    }
}
