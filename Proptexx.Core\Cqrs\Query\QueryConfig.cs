namespace Proptexx.Core.Cqrs.Query;

public class QueryConfig
{
    private readonly Dictionary<string, IQueryHandler> _handlers = new();

    internal QueryConfig() {}

    public void AddHandler(IQueryHandler handler)
    {
        if (!_handlers.<PERSON>Add(handler.Identifier, handler))
        {
            throw new QueryException("Query handler already defined");
        }
    }

    internal IQueryHandler? GetHandler(string identifier) 
        => _handlers.GetValueOrDefault(identifier);
}