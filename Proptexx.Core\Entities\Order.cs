using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class Order : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; init; } = Guid.NewGuid();
    
    public Guid AccountId { get; init; }

    [IgnoreOnInsert] public int? OrderNbr { get; } = null!;
    
    public string? ReferencePerson { get; init; }
    
    public decimal TotalPrice { get; set; }
    
    public required string Currency { get; init; }
    
    public string? PaymentLink { get; set; }

    public DateTime? PaidAt { get; set; }

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public string GetDbRef() => "core.order";
}