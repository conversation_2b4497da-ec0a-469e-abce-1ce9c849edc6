using Microsoft.Extensions.Logging;
using Proptexx.Core.Http;
using Proptexx.Core.Services;
using StackExchange.Redis;

namespace Proptexx.AI.Models.CV;

public sealed class RoomSceneEmptinessDetector(
    IComputerVisionClient computerVisionClient,
    IImageAssessmentClient imageAssessmentClient,
    IConnectionMultiplexer connectionMultiplexer,
    ILoggerFactory loggerFactory)
    : PreProcessorForRoomScene(
        computerVisionClient,
        imageAssessmentClient,
        connectionMultiplexer,
        loggerFactory)
{
    public class Result : PreProcessorForRoomScene.Result
    {
    }
}