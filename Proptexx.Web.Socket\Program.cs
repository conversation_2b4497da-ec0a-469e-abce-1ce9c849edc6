
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.SignalR;
using Proptexx.Web;
using Proptexx.Web.Socket;

var builder = WebApplication.CreateBuilder(args);
builder.AddProptexxWeb();

builder.Services.AddSignalR(config =>
    {
    })
    .AddStackExchangeRedis("localhost:6379", options =>
    {
        options.Configuration.ChannelPrefix = "socket"; // Optional prefix for channel names
    });

builder.Services.AddSingleton<IUserIdProvider, ProptexxUserIdProvider>();

builder.Services.AddAuthentication(options =>
    {
        // Configure JWT or your preferred authentication scheme
    })
    .AddJwtBearer(options =>
    {
        options.Events = new JwtBearerEvents
        {
            OnMessageReceived = context =>
            {
                context.Token = context.Request.Query["access_token"];
                return Task.CompletedTask;
            },
            OnTokenValidated = context =>
            {
                return Task.CompletedTask;
            }
        };
    });

var app = builder.Build();
app.UseProptexxWeb();
app.MapHub<ProptexxHub>("/hub");
app.Run();