using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Stores;
using Proptexx.Core.Utils;

namespace Proptexx.Web.Command.Helpers;

public static class PortalRegister
{
    public static async Task ExecuteAsync(CommandContext context, string name, string email, string? phone, string password, Dictionary<string, object> additionalData)
    {
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        await using var trx = await npgsql.BeginTransactionAsync();

        try
        {
            string? company = null;
            if (additionalData.TryGetValue("company", out var tmpCompany))
            {
                company = tmpCompany as string;
            }

            if (await npgsql.Account().EmailExists(email))
            {
                throw new CommandException("Check your info and try again");
            }

            // Setup account
            var account = await OnboardHelper.OnboardAccount(npgsql, name, email, false, password, phone, false);

            // Setup workspace
            await OnboardHelper.CreateWorkspace(npgsql, account, company, null, null);

            // Sending welcome email and auth code email
            var otp = await OtpHelper.Create(context, email);
            await MessageHelper.OnPortalOnboarding(npgsql, email, account.FullName());
            await MessageHelper.SendOtp(npgsql, email, otp);

            await trx.CommitAsync(context.CancellationToken);
        }
        catch (Exception)
        {
            await trx.RollbackAsync(context.CancellationToken);
            throw;
        }
    }
}