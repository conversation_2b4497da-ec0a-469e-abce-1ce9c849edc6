using System.Net.Sockets;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Retry;

namespace Proptexx.Core.Resilience
{
    public static class PollyRetryExtensions
    {
        public static AsyncRetryPolicy GetDefaultRetryPolicy(
            ILogger logger,
            string operationDescription, // e.g., TaskId or a description of the operation
            int retryCount = 3,
            int delaySeconds = 2)
        {
            // Only retry on transient exceptions
            return Policy
                .Handle<HttpRequestException>()
                .Or<TimeoutException>()
                .Or<SocketException>()
                .Or<TaskCanceledException>(ex => !ex.CancellationToken.IsCancellationRequested) // Only retry if not explicitly cancelled
                                                                                                // Never retry on cancellation exceptions - they indicate intentional cancellation
                .WaitAndRetryAsync(
                    retryCount: retryCount,
                    sleepDurationProvider: attempt => TimeSpan.FromSeconds(Math.Pow(2, attempt) * delaySeconds), // Exponential backoff
                    onRetry: (exception, timespan, currentRetry, context) =>
                    {
                        // Log retries as Information instead of Warning to reduce log noise
                        logger?.LogInformation(exception,
                            "Retry {CurrentRetry}/{RetryCount} for {OperationDescription} after {Delay}s due to: {ExceptionType} - {ExceptionMessage}",
                            currentRetry, retryCount, operationDescription, timespan.TotalSeconds, exception.GetType().Name, exception.Message);
                    }
                );
        }

        public static Task<TResult> ExecuteWithRetryAsync<TResult>(
            this AsyncRetryPolicy policy,
            Func<Task<TResult>> action,
            Action<Exception, TimeSpan, int, Context> onRetry)
        {
            return policy.ExecuteAsync(async () =>
            {
                // It's better to pass onRetry directly to the policy definition
                // but if we need to execute something before each attempt (not just on retry),
                // this is a place. For now, we'll rely on the onRetry in the policy.
                return await action();
            });
        }
    }
}