using System.ComponentModel.DataAnnotations;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Entities;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Utils;

namespace Proptexx.Web.Command.Workspace;

public sealed class AddWidgetUrl : ICommand
{
    [Required]
    public required string AbsoluteUrl { get; init; }
    
    public async Task ExecuteAsync(CommandContext context)
    {
        var urlInfo = UrlInfo.Parse(this.AbsoluteUrl);

        if (!context.User.TryGetWorkspaceGuid(out var workspaceId))
        {
            throw new UnauthorizedAccessException();
        }

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);

        var domain = new Domain
        {
            WorkspaceId = workspaceId.Value,
            Hostname = urlInfo.Hostname,
            MatchStrategy = MatchStrategy.Start,
            Path = urlInfo.Path,
            QueryParams = urlInfo.QueryParams,
            InputValue = urlInfo.InputString
        };

        await npgsql.InsertAsync(domain);
    }
}