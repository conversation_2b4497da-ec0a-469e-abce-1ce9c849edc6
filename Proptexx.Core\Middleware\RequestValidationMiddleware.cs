using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Auth;
using Proptexx.Core.DTOs;
using Proptexx.Core.Interface;
using Proptexx.Core.Services;
using System.IdentityModel.Tokens.Jwt;
using System.Text.Json;
using System.Text;
using static Proptexx.Core.Constants.SubscriptionConstants;
using Proptexx.Core.Constants;
using Dapper;

namespace Proptexx.Core.Middleware;

/// <summary>
/// Middleware for validating API requests based on subscription status and quota
/// Supports OneTime and PayAsYouGo subscription validation with special handling for widget commands
/// </summary>
public class RequestValidationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestValidationMiddleware> _logger;
    private readonly HashSet<string> _pathsToValidate;
    private readonly RequestValidationOptions _options;

    // Static JWT handler to avoid repeated allocations
    private static readonly JwtSecurityTokenHandler JwtHandler = new();

    // Default paths that should be validated for API key authentication
    private static readonly HashSet<string> DefaultPathsToValidate = new(StringComparer.OrdinalIgnoreCase)
    {
        "/api",
        "/cv",
        "/gen", 
        "/chat",
        "/batch",
        "/flow",
        "/widget",
        "/_command"
    };

    // Widget commands that require expensive operations (higher quota validation)
    private static readonly HashSet<string> ExpensiveWidgetCommands = new(StringComparer.OrdinalIgnoreCase)
    {
        "widget.IndoorStagingOrRefurnishing",
        "widget.StagingOrRefurnishingV2Command", 
        "widget.ProcessRefurnishingProductReplacement",
        "widget.SubmitImages",
    };

    // Widget commands that are authentication related (may have different validation)
    private static readonly HashSet<string> AuthWidgetCommands = new(StringComparer.OrdinalIgnoreCase)
    {
        "widget.Login",
        "widget.Register",
        "widget.SetPassword"
    };

    // Widget commands that are lightweight (chat/messaging)
    private static readonly HashSet<string> LightweightWidgetCommands = new(StringComparer.OrdinalIgnoreCase)
    {
        "widget.SendMessage",
        "widget.SendChat",
        "widget.UpdateProfile",
        "widget.SubmitFeedback"
    };

    public RequestValidationMiddleware(
        RequestDelegate next,
        ILogger<RequestValidationMiddleware> logger,
        RequestValidationOptions? options = null)
    {
        _next = next;
        _logger = logger;
        _options = options ?? new RequestValidationOptions();
        
        // Combine default paths with additional paths from options
        _pathsToValidate = new HashSet<string>(DefaultPathsToValidate, StringComparer.OrdinalIgnoreCase);
        foreach (var path in _options.PathsToValidate)
        {
            _pathsToValidate.Add(path);
        }
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            // Skip validation for excluded paths
            if (ShouldSkipValidation(context))
            {
                await _next(context);
                return;
            }

            // Resolve scoped services from HttpContext
            var usageService = context.RequestServices.GetRequiredService<ISubscriptionUsageService>();
            var validationService = context.RequestServices.GetRequiredService<ISubscriptionValidationService>();
            var subscriptionService = context.RequestServices.GetRequiredService<ISubscriptionService>();

            // Extract authentication information from request
            var (apiKey, clientSecretId, authenticationType) = ExtractAuthenticationInfo(context);
            
            // Validate authentication type and perform initial validation
            SubscriptionValidationResult? validationResult = await validationService.ValidateAsync(apiKey, context.RequestAborted);
           

            // Parse widget command if this is a command request
            var widgetCommandInfo = await ParseWidgetCommandAsync(context);

            // Apply widget-specific validation for API key authentication
            if (widgetCommandInfo != null && validationResult != null)
            {
                var widgetValidationResult = await ValidateWidgetCommandAsync(validationResult, widgetCommandInfo, context.RequestAborted);
                if (!widgetValidationResult.IsValid)
                {
                    await WriteValidationFailureResponse(context, widgetValidationResult);
                    return;
                }
            }

            // Classify request for usage tracking
            var classification = ClassifyRequest(context, widgetCommandInfo);
            
            // Store validation info for later use
            StoreValidationInfo(context, apiKey, clientSecretId, authenticationType, classification, widgetCommandInfo, validationResult);

            if (_options.DetailedLogging)
            {
                _logger.LogInformation("API validation successful for {Path} with classification {Classification}, AuthType: {AuthenticationType}, ClientSecretId: {ClientSecretId}", 
                    context.Request.Path, classification.Type, authenticationType, clientSecretId ?? "N/A");
            }

            // Continue to next middleware
            await _next(context);

            // Increment usage after successful request processing (only for API key authentication)
            if (ShouldIncrementUsage(context))
            {
                await IncrementUsageAsync(usageService, subscriptionService, apiKey, classification, clientSecretId, context.RequestAborted);

            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in RequestValidationMiddleware for path: {Path}", context.Request.Path);
            
            // Don't block the request, but log the error
            await _next(context);
        }
    }

    /// <summary>
    /// Determines if validation should be skipped for the current request
    /// </summary>
    private bool ShouldSkipValidation(HttpContext context)
    {
        var path = context.Request.Path.Value ?? string.Empty;
        
        // Skip validation for OPTIONS requests (CORS preflight)
        if (context.Request.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
        {
            return true;
        }

        // Only validate paths that are in the _pathsToValidate list
        if (!_pathsToValidate.Any(pathToValidate => path.StartsWith(pathToValidate, StringComparison.OrdinalIgnoreCase)))
        {
            return true;
        }

        return false;
    }

    /// <summary>
    /// Extracts authentication information from the request
    /// Supports both Bearer token and API key authentication, similar to AuthHandler
    /// 
    /// For API key authentication: Uses ApiKeyService.ParseApiKey() to extract clientSecretId
    /// For Bearer token authentication: Parses JWT token directly to extract clientSecretId from claims
    /// 
    /// Returns (apiKey, clientSecretId, authenticationType)
    /// </summary>
    private static (string apiKey, string clientSecretId, string authenticationType) ExtractAuthenticationInfo(HttpContext context)
    {
        var headers = context.Request.Headers;

        // Check Authorization header first
        if (headers.TryGetValue("Authorization", out var authorization))
        {
            var authHeaderValue = authorization.ToString().AsSpan();

            // Find the space separator efficiently
            var spaceIndex = authHeaderValue.IndexOf(' ');
            if (spaceIndex > 0 && spaceIndex < authHeaderValue.Length - 1)
            {
                var scheme = authHeaderValue[..spaceIndex];
                var value = authHeaderValue[(spaceIndex + 1)..].Trim();

                // Handle Bearer token authentication
                if (scheme.Equals("Bearer", StringComparison.OrdinalIgnoreCase))
                {
                    var bearerToken = value.ToString();
                    var clientSecretId = ExtractClientSecretIdFromBearerToken(context, bearerToken);
                    var apiKey = string.IsNullOrEmpty(clientSecretId) ? string.Empty : ApiKeyService.CreateApiKey(clientSecretId);
                    return (apiKey, clientSecretId ?? string.Empty, "Bearer");
                }

                // Handle API key in Authorization header
                if (scheme.Equals("ApiKey", StringComparison.OrdinalIgnoreCase))
                {
                    var apiKey = value.ToString();
                    var clientSecretId = ExtractClientSecretIdFromApiKey(apiKey);
                    return (apiKey, clientSecretId ?? string.Empty, "ApiKey");
                }
            }
        }
        // Check direct apiKey header
        else if (headers.TryGetValue("apiKey", out var apiKeyHeader))
        {
            var apiKey = apiKeyHeader.ToString().Trim();
            if (!string.IsNullOrEmpty(apiKey))
            {
                var clientSecretId = ExtractClientSecretIdFromApiKey(apiKey);
                return (apiKey, clientSecretId ?? string.Empty, "ApiKey");
            }
        }

        return (string.Empty, string.Empty, "None");
    }

    /// <summary>
    /// Extracts clientSecretId from API key using ApiKeyService
    /// Similar to how AuthHandler parses API keys
    /// </summary>
    private static string? ExtractClientSecretIdFromApiKey(string apiKey)
    {
        // Fast path: check for null/empty/whitespace first
        if (string.IsNullOrWhiteSpace(apiKey))
        {
            return null;
        }

        // Fast path: check for common invalid values using spans to avoid allocations
        var apiKeySpan = apiKey.AsSpan();
        if (apiKeySpan.Equals("undefined", StringComparison.OrdinalIgnoreCase) ||
            apiKeySpan.Equals("null", StringComparison.OrdinalIgnoreCase))
        {
            return null;
        }

        try
        {
            // Use ApiKeyService.ParseApiKey to extract clientSecretId
            return ApiKeyService.ParseApiKey(apiKey);
        }
        catch (Exception)
        {
            // If parsing fails, return null
            return null;
        }
    }

    /// <summary>
    /// Extracts clientSecretId from Bearer token by parsing the JWT token
    /// This method parses the token directly to extract claims without requiring full authentication
    /// </summary>
    private static string? ExtractClientSecretIdFromBearerToken(HttpContext context, string bearerToken)
    {
        // Fast path: basic validation before expensive JWT parsing
        if (string.IsNullOrWhiteSpace(bearerToken))
        {
            return null;
        }

        try
        {
            // Fast token validation before parsing
            if (!JwtHandler.CanReadToken(bearerToken))
            {
                return null;
            }

            var jwtToken = JwtHandler.ReadJwtToken(bearerToken);
            
            // More efficient claim lookup using direct enumeration
            foreach (var claim in jwtToken.Claims)
            {
                if (claim.Type == "clientSecretId")
                {
                    return claim.Value;
                }
            }
            
            return null;
        }
        catch (Exception)
        {
            // If token parsing fails, return null
            return null;
        }
    }

    /// <summary>
    /// Extracts clientSecretId from the authenticated user's claims
    /// Used as fallback when Bearer token parsing doesn't work
    /// </summary>
    private static string? ExtractClientSecretIdFromClaims(HttpContext context)
    {
        // If user is authenticated and has claims, try to get clientSecretId
        if (context.User?.Identity?.IsAuthenticated == true)
        {
            return context.User.TryGetClientSecretId();
        }

        return null;
    }

    /// <summary>
    /// Parses widget command information from request body if this is a command request
    /// </summary>
    private static async Task<WidgetCommandInfo?> ParseWidgetCommandAsync(HttpContext context)
    {
        var path = context.Request.Path.Value ?? string.Empty;
        
        // Only parse for command requests
        if (!path.Equals("/_command", StringComparison.OrdinalIgnoreCase) || 
            !context.Request.Method.Equals("POST", StringComparison.OrdinalIgnoreCase))
        {
            return null;
        }

        try
        {
            // Enable buffering to allow multiple reads of the request body
            context.Request.EnableBuffering();
            
            // Read the request body
            var body = await new StreamReader(context.Request.Body, Encoding.UTF8).ReadToEndAsync();
            context.Request.Body.Position = 0; // Reset position for next middleware

            if (string.IsNullOrEmpty(body))
            {
                return null;
            }

            // Parse the command definition
            var commandDefinition = JsonSerializer.Deserialize<CommandDefinition>(body, new JsonSerializerOptions 
            { 
                PropertyNameCaseInsensitive = true 
            });

            if (commandDefinition?.Identifier == null)
            {
                return null;
            }

            return new WidgetCommandInfo
            {
                Target = commandDefinition.Identifier,
                IsExpensive = ExpensiveWidgetCommands.Contains(commandDefinition.Identifier),
                IsAuthentication = AuthWidgetCommands.Contains(commandDefinition.Identifier),
                IsLightweight = LightweightWidgetCommands.Contains(commandDefinition.Identifier),
                Payload = commandDefinition.Payload
            };
        }
        catch (Exception)
        {
            // If we can't parse the command, continue with normal validation
            return null;
        }
    }

    /// <summary>
    /// Validates widget-specific command requirements
    /// </summary>
    private async Task<SubscriptionValidationResult> ValidateWidgetCommandAsync(
        SubscriptionValidationResult baseValidation, 
        WidgetCommandInfo commandInfo, 
        CancellationToken cancellationToken)
    {
        // For authentication commands, they might need special handling
        if (commandInfo.IsAuthentication)
        {
            if (_options.DetailedLogging)
            {
                _logger.LogInformation("Processing authentication widget command: {Target}", commandInfo.Target);
            }
            // Auth commands might be allowed with different rules
            // For now, use base validation
            return baseValidation;
        }

        // For expensive commands, ensure subscription has enough quota remaining
        if (commandInfo.IsExpensive)
        {
            if (_options.DetailedLogging)
            {
                _logger.LogInformation("Processing expensive widget command: {Target}", commandInfo.Target);
            }

            // Check if subscription is valid first
            if (!baseValidation.IsValid)
            {
                return baseValidation;
            }

            // For expensive operations, we need a minimum quota buffer
            const int minimumQuotaForExpensiveOperation = 10;
            
            if (baseValidation.RemainingQuota < minimumQuotaForExpensiveOperation)
            {
                var reason = $"Insufficient quota for expensive operation '{commandInfo.Target}'. " +
                           $"Minimum {minimumQuotaForExpensiveOperation} requests required, " +
                           $"but only {baseValidation.RemainingQuota} remaining.";
                
                if (_options.DetailedLogging)
                {
                    _logger.LogWarning("Expensive widget command blocked due to insufficient quota: {Target}, " +
                                     "Required: {MinQuota}, Available: {RemainingQuota}", 
                                     commandInfo.Target, minimumQuotaForExpensiveOperation, baseValidation.RemainingQuota);
                }

                return new SubscriptionValidationResult
                {
                    IsValid = false,
                    Reason = reason,
                    Subscription = baseValidation.Subscription,
                    UsagePercentage = baseValidation.UsagePercentage,
                    RemainingQuota = baseValidation.RemainingQuota
                };
            }

            // For PayAsYouGo subscriptions with unlimited quota (quota = 0), allow expensive operations
            if (baseValidation.Subscription?.Type?.Equals(SubscriptionTypes.PayAsYouGo, StringComparison.OrdinalIgnoreCase) == true &&
                baseValidation.Subscription.Quota == 0)
            {
                if (_options.DetailedLogging)
                {
                    _logger.LogInformation("Expensive widget command allowed for unlimited PayAsYouGo subscription: {Target}", 
                                         commandInfo.Target);
                }
                return baseValidation;
            }

            if (_options.DetailedLogging)
            {
                _logger.LogInformation("Expensive widget command validation passed: {Target}, " +
                                     "RemainingQuota: {RemainingQuota}", 
                                     commandInfo.Target, baseValidation.RemainingQuota);
            }
        }

        // For lightweight commands, use base validation as-is
        if (commandInfo.IsLightweight && _options.DetailedLogging)
        {
            _logger.LogInformation("Processing lightweight widget command: {Target}", commandInfo.Target);
        }

        return baseValidation;
    }

    /// <summary>
    /// Classifies the request for usage tracking and monitoring
    /// </summary>
    private static RequestClassification ClassifyRequest(HttpContext context, WidgetCommandInfo? widgetCommandInfo)
    {
        var path = context.Request.Path.Value ?? string.Empty;

        if (path.StartsWith("/_command") && widgetCommandInfo?.Target.StartsWith("widget.", StringComparison.OrdinalIgnoreCase) == true)
        {
            return new RequestClassification(
                WidgetConstants.HandlerTypes.CommandHandler,
                widgetCommandInfo?.Target??string.Empty,
                path);
        }
 
        if (path.StartsWith("/batch", StringComparison.OrdinalIgnoreCase))
        {
            return new RequestClassification(
                WidgetConstants.HandlerTypes.BatchHandler,
                WidgetConstants.SubTypes.Batch,
                path);
        }

        if (path.StartsWith("/seq/", StringComparison.OrdinalIgnoreCase))
        {
            return new RequestClassification(
                WidgetConstants.HandlerTypes.SequentialHandler,
                WidgetConstants.SubTypes.Sequential,
                path);
        }

        if (path.Contains("virtual-staging", StringComparison.OrdinalIgnoreCase))
        {
            return new RequestClassification(
                "Generative.VirtualStaging",
                WidgetConstants.SubTypes.Model,
                path);
        }

        if (path.Contains("virtual-refurnishing", StringComparison.OrdinalIgnoreCase))
        {
            return new RequestClassification(
                "Generative.VirtualRefurnishing",
                WidgetConstants.SubTypes.Model,
                path);
        }

        if (path.StartsWith("/cv/", StringComparison.OrdinalIgnoreCase))
        {
            return new RequestClassification(
                "ComputerVision.General",
                WidgetConstants.SubTypes.Model,
                path);
        }

        if (path.StartsWith("/gen/", StringComparison.OrdinalIgnoreCase))
        {
            return new RequestClassification(
                "Generative.General",
                WidgetConstants.SubTypes.Model,
                path);
        }

        return new RequestClassification(
            WidgetConstants.HandlerTypes.ModelHandler,
            WidgetConstants.SubTypes.Model,
            path);
    }

 

    /// <summary>
    /// Writes an unauthorized response
    /// </summary>
    private static async Task WriteUnauthorizedResponse(HttpContext context, string message)
    {
        context.Response.StatusCode = 401;
        context.Response.ContentType = "application/json";

        var response = new
        {
            error = "Unauthorized",
            message = message,
            timestamp = DateTime.UtcNow.ToString("O")
        };

        var json = JsonSerializer.Serialize(response);
        await context.Response.WriteAsync(json);
    }

    /// <summary>
    /// Writes a validation failure response with detailed information
    /// </summary>
    private static async Task WriteValidationFailureResponse(HttpContext context, SubscriptionValidationResult validationResult)
    {
        // Determine appropriate HTTP status code based on validation failure reason
        context.Response.StatusCode = DetermineStatusCode(validationResult.Reason);
        context.Response.ContentType = "application/json";

        var response = new
        {
            error = "ValidationFailed",
            message = validationResult.Reason,
            details = new
            {
                subscriptionType = validationResult.Subscription?.Type,
                subscriptionStatus = validationResult.Subscription?.Status,
                quota = validationResult.Subscription?.Quota ?? 0,
                totalUsage = validationResult.Subscription?.TotalUsage ?? 0,
                remainingQuota = validationResult.RemainingQuota,
                usagePercentage = Math.Round(validationResult.UsagePercentage, 2),
                isDemo = validationResult.Subscription?.IsDemo ?? false,
                planId = validationResult.Subscription?.PlanId,
                domain = validationResult.Subscription?.Domain,
                createdAt = validationResult.Subscription?.CreatedAt,
                updatedAt = validationResult.Subscription?.UpdatedAt
            },
            timestamp = DateTime.UtcNow.ToString("O")
        };

        var json = JsonSerializer.Serialize(response);
        await context.Response.WriteAsync(json);
    }

    /// <summary>
    /// Determines the appropriate HTTP status code based on validation failure reason
    /// </summary>
    private static int DetermineStatusCode(string reason)
    {
        // Convert reason to lowercase for case-insensitive matching
        var lowerReason = reason.ToLowerInvariant();

        return lowerReason switch
        {
            // Expired subscription scenarios
            var r when r.Contains("expired") => 403, // Forbidden
            var r when r.Contains("api key has expired") => 401, // Unauthorized
            
            // Quota-related scenarios
            var r when r.Contains("quota exceeded") => 429, // Too Many Requests
            var r when r.Contains("insufficient quota") => 429, // Too Many Requests
            var r when r.Contains("quota") => 429, // Too Many Requests (general quota issues)
            
            // Authentication and authorization scenarios
            var r when r.Contains("invalid api key") => 401, // Unauthorized
            var r when r.Contains("api key is required") => 401, // Unauthorized
            var r when r.Contains("no active subscription") => 404, // Not Found
            var r when r.Contains("subscription not found") => 404, // Not Found
            
            // Subscription type issues
            var r when r.Contains("unsupported subscription type") => 400, // Bad Request
            var r when r.Contains("requires a valid quota") => 400, // Bad Request
            
            // Validation errors
            var r when r.Contains("validation error") => 500, // Internal Server Error
            
            // Default for any other validation failure
            _ => 400 // Bad Request
        };
    }

    /// <summary>
    /// Writes an internal error response
    /// </summary>
    private static async Task WriteErrorResponse(HttpContext context, string message)
    {
        context.Response.StatusCode = 500;
        context.Response.ContentType = "application/json";

        var response = new
        {
            error = "InternalServerError",
            message = message,
            timestamp = DateTime.UtcNow.ToString("O")
        };

        var json = JsonSerializer.Serialize(response);
        await context.Response.WriteAsync(json);
    }

    /// <summary>
    /// Increments usage for a successful request
    /// Ensures a PayAsYouGo subscription exists before incrementing usage
    /// </summary>
    private async Task IncrementUsageAsync(ISubscriptionUsageService usageService, ISubscriptionService subscriptionService, string apiKey, RequestClassification classification, string? clientSecretId, CancellationToken cancellationToken)
    {
        try
        {
            // Ensure a PayAsYouGo subscription exists for this API key
            var subscription = await subscriptionService.EnsureClientSubscriptionExists(apiKey, cancellationToken: cancellationToken);
            if (subscription == null)
            {
                _logger.LogWarning("Failed to ensure subscription exists for API key, skipping usage increment. ClientSecretId: {ClientSecretId}", clientSecretId ?? "N/A");
                return;
            }

            if (_options.DetailedLogging)
            {
                _logger.LogInformation("Subscription ensured for API key. Type: {Type}, ClientSecretId: {ClientSecretId}", 
                    subscription.Type, clientSecretId ?? "N/A");
            }

            // Increment usage using the existing method
            var dtoClassification = new RequestClassification(
                classification.Type,
                classification.Target,
                classification.Identifier
            );
            await usageService.IncrementUsageForRequestTypeAsync(apiKey, dtoClassification, cancellationToken);

            if (_options.DetailedLogging)
            {
                _logger.LogInformation("Usage incremented after successful {Classification} request, ClientSecretId: {ClientSecretId}",
                    classification.Type, clientSecretId ?? "N/A");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing usage for {Classification} request, ClientSecretId: {ClientSecretId}", 
                classification.Type, clientSecretId ?? "N/A");
        }
    }

    /// <summary>
    /// Stores validation information in HttpContext.Items for later use
    /// </summary>
    private static void StoreValidationInfo(
        HttpContext context,
        string? apiKey,
        string? clientSecretId,
        string authenticationType,
        RequestClassification classification,
        WidgetCommandInfo? widgetCommandInfo,
        SubscriptionValidationResult? validationResult)
    {
        context.Items["ApiKey"] = apiKey;
        context.Items["ClientSecretId"] = clientSecretId;
        context.Items["AuthenticationType"] = authenticationType;
        context.Items["RequestClassification"] = classification;
        context.Items["WidgetCommandInfo"] = widgetCommandInfo;
        
        // Store validation result only for API key authentication
        if (authenticationType == "ApiKey" && validationResult != null)
        {
            context.Items["ValidationResult"] = validationResult;
        }
    }

    /// <summary>
    /// Determines if usage should be incremented based on response status
    /// </summary>
    private bool ShouldIncrementUsage(HttpContext context)
    {
        var statusCode = context.Response.StatusCode;
        
        // Increment for successful responses (2xx)
        if (statusCode >= 200 && statusCode < 300)
        {
            return true;
        }
        
        // Optionally increment for error responses if configured
        return _options.IncrementUsageOnError;
    }
}

/// <summary>
/// Widget command information extracted from request
/// </summary>
public class WidgetCommandInfo
{
    public required string Target { get; init; }
    public bool IsExpensive { get; init; }
    public bool IsAuthentication { get; init; }
    public bool IsLightweight { get; init; }
    public System.Text.Json.Nodes.JsonNode? Payload { get; init; }
}

/// <summary>
/// Command definition structure for parsing requests
/// </summary>
public class CommandDefinition
{
    public required string Identifier { get; init; }
    public string? Handler { get; init; }
    public System.Text.Json.Nodes.JsonNode? Payload { get; init; }
}


/// <summary>
/// Extension methods for adding the request validation middleware
/// </summary>
public static class RequestValidationMiddlewareExtensions
{
    /// <summary>
    /// Adds the API key validation middleware to the pipeline
    /// </summary>
    public static IApplicationBuilder UseApiKeyUsageValidation(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<RequestValidationMiddleware>();
    }

    /// <summary>
    /// Adds the API key validation middleware with custom configuration
    /// </summary>
    public static IApplicationBuilder UseApiKeyValidation(this IApplicationBuilder builder, Action<RequestValidationOptions> configure)
    {
        var options = new RequestValidationOptions();
        configure(options);
        
        return builder.UseMiddleware<RequestValidationMiddleware>(options);
    }
}

/// <summary>
/// Configuration options for the request validation middleware
/// </summary>
public class RequestValidationOptions
{
    /// <summary>
    /// Additional paths to validate (will be added to the default paths)
    /// Default paths: /api, /cv, /gen, /chat, /batch, /widget, /_command
    /// </summary>
    public List<string> PathsToValidate { get; set; } = new();

    /// <summary>
    /// Whether to increment usage for non-2xx responses
    /// </summary>
    public bool IncrementUsageOnError { get; set; } = false;

    /// <summary>
    /// Whether to log detailed validation information
    /// </summary>
    public bool DetailedLogging { get; set; } = false;
}
 