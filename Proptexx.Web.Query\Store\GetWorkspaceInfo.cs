using Proptexx.Core;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Query;

namespace Proptexx.Web.Query.Store;

public class GetWorkspaceInfo : IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        return new
        {
            callerId = context.User.TryGetCallerId(),
            workspaceId = context.User.GetWorkspaceId(),
            workspaceName = context.User.GetWorkspaceName(),
        };
    }
}