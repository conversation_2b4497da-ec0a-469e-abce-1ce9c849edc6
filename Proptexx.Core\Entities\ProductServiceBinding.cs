using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class ProductServiceBinding : IDbTable
{
    public Guid ProductId { get; init; }
    
    public required string ServiceId { get; init; }

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public string GetDbRef() => "core.product_service_binding";
}