ARG REGISTRY=proptexx.azurecr.io
FROM ${REGISTRY}/proptexx.server.sdk:latest AS sdk

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
LABEL author="Proptexx <<EMAIL>>"
HEALTHCHECK --interval=30s --timeout=15s --start-period=5s --retries=3 CMD curl -f http://localhost/health || exit 1
WORKDIR /app
EXPOSE 80
COPY --from=sdk /src/Proptexx.Web.Api/bin/Release/net9.0/publish .
ENTRYPOINT [ "dotnet", "Proptexx.Web.Api.dll" ]
