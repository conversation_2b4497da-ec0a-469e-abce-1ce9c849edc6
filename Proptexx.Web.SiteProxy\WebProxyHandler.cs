using HtmlAgilityPack;

namespace Proptexx.Web.SiteProxy;

public sealed class WebProxyHandler
{
    private static HttpClient StaticHttpClient { get; }

    static WebProxyHandler()
    {
        StaticHttpClient = new HttpClient();
    }

    public async Task InvokeAsync(HttpContext ctx)
    {
        if (ctx.Request.Query.TryGetValue("url", out var url))
        {
            ctx.Response.StatusCode = StatusCodes.Status400BadRequest;
            await ctx.Response.WriteAsync("URL parameter is required");
            return;
        }

        var targetUri = new Uri(url.ToString());
        var response = await StaticHttpClient.GetAsync(targetUri);

        if (response.IsSuccessStatusCode)
        {
            var contentType = response.Content.Headers.ContentType?.MediaType;
            if (!string.IsNullOrWhiteSpace(contentType) && contentType.StartsWith("text/html"))
            {
                var content = await response.Content.ReadAsStringAsync();

                var doc = new HtmlDocument();
                doc.LoadHtml(content);

                var proxyUri = new Uri("https://customer.dev.local/proxy?url=");
                var sourceBase = new Uri($"https://{targetUri.Host}");
                RewriteUrlsToProxy(doc, proxyUri, sourceBase);

                var modifiedContent = doc.DocumentNode.OuterHtml;
                await ctx.Response.WriteAsync(modifiedContent);
            }
            else
            {
                var rawContent = await response.Content.ReadAsByteArrayAsync();
                ctx.Response.ContentType = contentType;
                await ctx.Response.Body.WriteAsync(rawContent);
            }
        }
        else
        {
            ctx.Response.StatusCode = (int)response.StatusCode;
            await ctx.Response.WriteAsync($"Error fetching content: {response.ReasonPhrase}");
        }
    }

    private static void RewriteUrlsToProxy(HtmlDocument doc, Uri proxyUri, Uri sourceBase)
    {
        var elementsToCheck = new Dictionary<string, string>(){
            ["script"] = "src",
            ["link"] = "href"
        };

        foreach (var (tag, attrName) in elementsToCheck)
        {
            var elements = doc.DocumentNode.SelectNodes($"//{tag}");
            foreach (var element in elements)
            {
                var attrValue = element.GetAttributeValue(attrName, null);

                try
                {
                    if (Uri.IsWellFormedUriString(attrValue, UriKind.Relative))
                    {
                        var absoluteUri = new Uri(sourceBase, attrValue).ToString();
                        var proxyUrl = proxyUri.AbsoluteUri + Uri.EscapeDataString(absoluteUri);
                        element.SetAttributeValue(attrName, proxyUrl);
                    }
                    else if (Uri.IsWellFormedUriString(attrValue, UriKind.Absolute))
                    {
                        var absoluteUri = new Uri(attrValue);

                        if (absoluteUri.Host.Equals(sourceBase.Host, StringComparison.OrdinalIgnoreCase))
                        {
                            var proxyUrl = proxyUri.AbsoluteUri + Uri.EscapeDataString(absoluteUri.AbsoluteUri);
                            element.SetAttributeValue(attrName, proxyUrl);
                        }
                    }
                    else
                    {
                        element.SetAttributeValue(attrName, "#");
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine($"Error rewriting URL: {e}");
                    element.SetAttributeValue(attrName, "#");
                }
            }
        }
    }
}