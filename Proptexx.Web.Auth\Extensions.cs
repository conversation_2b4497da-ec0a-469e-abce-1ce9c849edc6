using Proptexx.Web.Auth.Auth;
using Proptexx.Web.Auth.Resolvers;

namespace Proptexx.Web.Auth;

public static class Extensions
{
    public static IServiceCollection AddAuthResolvers(this IServiceCollection services)
    {
        services.AddSingleton<IClientResolver, ClientResolver>();
        services.AddSingleton<ISessionResolver, SessionResolver>();
        services.AddSingleton<IScopeResolverFactory, ScopeResolverFactory>();
        services.AddKeyedSingleton<IScopeResolver, LoginResolver>("login");
        services.AddKeyedSingleton<IScopeResolver, CodeResolver>("code");
        services.AddKeyedSingleton<IScopeResolver, WorkspaceResolver>("workspace");
        services.AddKeyedSingleton<IScopeResolver, ChangeAccountResolver>("change-account");
        services.AddKeyedSingleton<IScopeResolver, WidgetResolver>("widget");
        services.AddKeyedSingleton<IScopeResolver, OtpResolver>("otp");
        services.AddKeyedSingleton<IScopeResolver, StoreResolver>("store");
        return services;
    }
}