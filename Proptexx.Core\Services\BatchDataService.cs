using StackExchange.Redis;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Proptexx.Core.Services;

public class BatchDataService
{
    private readonly IConnectionMultiplexer _redis;
    private readonly IDatabase _database;
    private readonly ILogger<BatchDataService> _logger;

    public BatchDataService(IConnectionMultiplexer redis, ILogger<BatchDataService> logger)
    {
        _redis = redis;
        _database = redis.GetDatabase();
        _logger = logger;
    }

    // Store batch metadata for BigQuery export
    public async Task StoreBatchMetadataAsync(string batchId, object batchData)
    {
        var key = $"batch_metadata:{batchId}";
        var json = JsonConvert.SerializeObject(batchData);
        await _database.StringSetAsync(key, json, TimeSpan.FromDays(30)); // Keep for 30 days
    }

    // Store task metadata for BigQuery export
    public async Task StoreTaskMetadataAsync(string taskId, object taskData)
    {
        var key = $"task_metadata:{taskId}";
        var json = JsonConvert.SerializeObject(taskData);
        await _database.StringSetAsync(key, json, TimeSpan.FromDays(30)); // Keep for 30 days
    }

    // Store task result for accumulation and BigQuery export
    public async Task StoreTaskResultAsync(string taskId, object resultData)
    {
        var key = $"task_result:{taskId}";
        var json = JsonConvert.SerializeObject(resultData);
        await _database.StringSetAsync(key, json, TimeSpan.FromDays(30)); // Keep for 30 days
    }

    // Get all batch data for BigQuery export
    public async Task<BatchExportData> GetBatchDataForExportAsync(string batchId)
    {
        var exportData = new BatchExportData
        {
            BatchId = batchId,
            Tasks = new List<TaskExportData>(),
            Results = new List<ResultExportData>()
        };

        // Get batch metadata
        var batchKey = $"batch_metadata:{batchId}";
        var batchJson = await _database.StringGetAsync(batchKey);
        if (batchJson.HasValue && !string.IsNullOrEmpty(batchJson))
        {
            exportData.BatchMetadata = JsonConvert.DeserializeObject(batchJson!);
        }

        // Get all task IDs for this batch from Redis sets
        var batchTasksKey = $"batch_tasks:{batchId}";
        var taskIds = await _database.SetMembersAsync(batchTasksKey);

        foreach (var taskId in taskIds)
        {
            var taskIdString = taskId.ToString();
            
            // Get task metadata
            var taskKey = $"task_metadata:{taskIdString}";
            var taskJson = await _database.StringGetAsync(taskKey);
            if (taskJson.HasValue && !string.IsNullOrEmpty(taskJson))
            {
                var taskData = new TaskExportData
                {
                    TaskId = taskIdString,
                    Metadata = JsonConvert.DeserializeObject(taskJson!)
                };
                exportData.Tasks.Add(taskData);
            }

            // Get task result
            var resultKey = $"task_result:{taskIdString}";
            var resultJson = await _database.StringGetAsync(resultKey);
            if (resultJson.HasValue && !string.IsNullOrEmpty(resultJson))
            {
                var resultData = new ResultExportData
                {
                    TaskId = taskIdString,
                    Result = JsonConvert.DeserializeObject(resultJson!)
                };
                exportData.Results.Add(resultData);
            }
        }

        return exportData;
    }

    // Track task IDs for a batch
    public async Task AddTaskToBatchAsync(string batchId, string taskId)
    {
        var key = $"batch_tasks:{batchId}";
        await _database.SetAddAsync(key, taskId);
        await _database.KeyExpireAsync(key, TimeSpan.FromDays(30));
    }

    // Check if all tasks in a batch are completed
    public async Task<bool> IsBatchCompleteAsync(string batchId)
    {
        var batchTasksKey = $"batch_tasks:{batchId}";
        var taskIds = await _database.SetMembersAsync(batchTasksKey);

        foreach (var taskId in taskIds)
        {
            var resultKey = $"task_result:{taskId}";
            var hasResult = await _database.KeyExistsAsync(resultKey);
            if (!hasResult)
            {
                return false; // At least one task is not completed
            }
        }

        return true; // All tasks have results
    }

    // Get the count of completed tasks vs total tasks for a batch
    public async Task<(int completed, int total)> GetBatchProgressAsync(string batchId)
    {
        var batchTasksKey = $"batch_tasks:{batchId}";
        var taskIds = await _database.SetMembersAsync(batchTasksKey);
        var total = taskIds.Length;
        var completed = 0;

        foreach (var taskId in taskIds)
        {
            var resultKey = $"task_result:{taskId}";
            var hasResult = await _database.KeyExistsAsync(resultKey);
            if (hasResult)
            {
                completed++;
            }
        }

        return (completed, total);
    }

    // Clean up Redis data after BigQuery export
    public async Task CleanupBatchDataAsync(string batchId)
    {
        var tasks = new List<Task>();

        // Delete batch metadata
        tasks.Add(_database.KeyDeleteAsync($"batch_metadata:{batchId}"));

        // Get all task IDs and delete their data
        var batchTasksKey = $"batch_tasks:{batchId}";
        var taskIds = await _database.SetMembersAsync(batchTasksKey);

        foreach (var taskId in taskIds)
        {
            tasks.Add(_database.KeyDeleteAsync($"task_metadata:{taskId}"));
            tasks.Add(_database.KeyDeleteAsync($"task_result:{taskId}"));
        }

        // Delete the batch tasks set
        tasks.Add(_database.KeyDeleteAsync(batchTasksKey));

        await Task.WhenAll(tasks);
        _logger.LogInformation("Cleaned up Redis data for batch {BatchId}", batchId);
    }
}

// DTOs for BigQuery export
public class BatchExportData
{
    public required string BatchId { get; set; }
    public object? BatchMetadata { get; set; }
    public required List<TaskExportData> Tasks { get; set; }
    public required List<ResultExportData> Results { get; set; }
}

public class TaskExportData
{
    public required string TaskId { get; set; }
    public object? Metadata { get; set; }
}

public class ResultExportData
{
    public required string TaskId { get; set; }
    public object? Result { get; set; }
}
