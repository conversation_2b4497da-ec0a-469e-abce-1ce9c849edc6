using Proptexx.AI;
using Proptexx.AI.Widget;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Cqrs.Command.Handlers;
using Proptexx.Core.Redis;
using Proptexx.Core.Services;
using Proptexx.Stripe;
using Proptexx.Web;
using Proptexx.Web.Command;

var builder = WebApplication.CreateBuilder(args);
builder.AddProptexxWeb();
builder.RegisterStripeApiKey();

builder.Services.AddModels();
builder.Services.AddChatServices();
builder.Services.AddCommands(command =>
    command.AddHandler(new ObjectCommandHandler(registry => registry.AddObjectCommands())));

builder.Services.AddScoped<WidgetService>();
builder.Services.AddTransient<WidgetStore>();
builder.Services.AddScoped<RequiredServiceValidationService>();

var app = builder.Build();
SubscriptionServiceAccess.Configure(app.Services);
app.UseProptexxWeb(enableApiKeyUsageValidation:true);
app.MapGet("/", () => "Proptexx | Command");
app.MapPost("/_command", (CommandHttpHandler handler, HttpContext context) => handler.InvokeAsync(context));
app.Run();
