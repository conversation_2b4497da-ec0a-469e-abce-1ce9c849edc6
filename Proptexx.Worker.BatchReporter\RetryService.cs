using StackExchange.Redis;

namespace Proptexx.Worker.BatchReporter;

public sealed class RetryService : BackgroundService
{
    private readonly ILogger<RetryService> _logger;
    private readonly IConnectionMultiplexer _redis;
    private readonly IConfiguration _config;

    public RetryService(
        ILogger<RetryService> logger,
        IConnectionMultiplexer redis,
        IConfiguration config)
    {
        _logger = logger;
        _redis = redis;
        _config = config;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        const long maxToMove = 100;
        var intervalMs = _config.GetValue("retryCheckIntervalMs", 30000);
        var redis = _redis.GetDatabase();

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                var retryListLength = await redis.ListLengthAsync("batch_callbacks_retry", CommandFlags.PreferReplica);
                if (retryListLength <= 0) continue;

                var toMove = Math.Min(maxToMove, retryListLength);
                _logger.LogInformation("Retry queue contains {NumItems} or more. Start move..", toMove);

                var batch = redis.CreateBatch();
                var tasks = new List<Task<RedisValue>>();

                for (var i = 0; i < toMove; i++)
                {
                    var task = batch.ListRightPopLeftPushAsync("batch_callbacks_retry", "batch_callbacks");
                    tasks.Add(task);
                }

                batch.Execute();
                await Task.WhenAll(tasks);

                var moved = tasks.Count(task => !task.Result.IsNullOrEmpty);
                _logger.LogInformation("Moved {Count} items...", moved);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error moving items from retry queue");
            }
            finally
            {
                await Task.Delay(intervalMs, stoppingToken);
            }
        }
    }
}