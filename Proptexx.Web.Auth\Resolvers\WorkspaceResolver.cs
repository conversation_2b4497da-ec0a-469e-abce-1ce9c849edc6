using Dapper;
using Npgsql;
using Proptexx.Core.Auth;
using Proptexx.Core.Entities;
using Proptexx.Core.Postgresql.Builder;
using Proptexx.Web.Auth.Auth;

namespace Proptexx.Web.Auth.Resolvers;

public sealed class WorkspaceResolver : IScopeResolver<WorkspaceResolver.WorkspacePayload>
{
    private readonly NpgsqlDataSource _dataSource;

    public WorkspaceResolver(NpgsqlDataSource dataSource)
    {
        _dataSource = dataSource;
    }

    public async Task ResolveAsync(ScopeContext context, WorkspacePayload payload, CancellationToken cancellationToken)
    {
        if (!payload.WorkspaceId.HasValue || payload.WorkspaceId.Value.Equals(Guid.Empty))
        {
            throw new NullReferenceException(nameof(payload.WorkspaceId));
        }

        if (!context.Identity.TryGetCallerId(out var callerId))
        {
            throw new UnauthorizedAccessException();
        }

        var accountId = Guid.Parse(callerId);

        await using var npgsql = await _dataSource.OpenConnectionAsync(cancellationToken);
        var workspaceId = await LoginHelper.SetWorkspaceClaimsAsync(context, npgsql, accountId, payload.WorkspaceId);
        var account = await npgsql.QueryFirstAsync<Account>("select a.* from core.account a where a.id = :_account_id", new { _account_id = accountId });
        account.WorkspaceId = workspaceId;
        await npgsql.UpdateAsync(account, "WorkspaceId");
    }

    public class WorkspacePayload
    {
        public Guid? WorkspaceId { get; init; }
    }
}