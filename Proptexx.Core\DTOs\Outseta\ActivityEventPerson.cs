using System;
using System.Text.Json.Serialization;

namespace Proptexx.Core.DTOs.Outseta // Changed namespace
{
    public class ActivityEventPerson
    {
        public string? Email { get; set; } 
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public bool PasswordMustChange { get; set; }
        public string? PhoneMobile { get; set; }
        public string? PhoneWork { get; set; }
        public string? Language { get; set; } 
        public string? IPAddress { get; set; } 
        public string? Referer { get; set; } 
        public string? UserAgent { get; set; } 
        public string? FullName { get; set; }
        public bool HasLoggedIn { get; set; }
        public int OAuthIntegrationStatus { get; set; }
        public bool OptInToEmailList { get; set; }
        public string? UserAgentPlatformBrowser { get; set; }
        public bool HasUnsubscribed { get; set; }
        public bool IsConnectedToDiscord { get; set; }
        public bool SchemaLessDataLoaded { get; set; }
        public string? Uid { get; set; } 
        [JsonPropertyName("_objectType")]
        public string? ObjectType { get; set; }
        public DateTime Created { get; set; }
        public DateTime Updated { get; set; }
    }
}
