using Proptexx.AI.Widget;
using Proptexx.Core.Extensions;
using Proptexx.Core.Redis;
using Proptexx.Web;
using Proptexx.Web.Auth;
using Proptexx.Web.Auth.Handlers;
using Proptexx.Web.Auth.OAuth;
using Proptexx.Web.Auth.OAuth.Google;
using Proptexx.Web.Auth.OAuth.Microsoft;

var builder = WebApplication.CreateBuilder(args);
builder.AddProptexxWeb();
builder.Services.AddScoped<AuthHandler>();
builder.Services.AddScoped<OAuthLinkHandler>();
builder.Services.AddScoped<OAuthCallbackHandler>();
builder.Services.AddScoped<RefreshHandler>();
builder.Services.AddScoped<SignoutHandler>();
builder.Services.AddScoped<FallbackHandler>();

builder.Services.AddSingleton<IClientSecretStore, ClientSecretStore>();
builder.Services.AddAuthResolvers();

builder.Services.AddTransient<WidgetService>();
builder.Services.AddTransient<WidgetStore>();
builder.Services.AddSingleton<IOAuthProviderFactory, OAuthProviderFactory>();
builder.Services.AddKeyedSingleton<IOAuthProvider, GoogleOAuthProvider>("google");
builder.Services.AddKeyedSingleton<IOAuthProvider, MicrosoftOAuthProvider>("microsoft");

var app = builder.Build();
app.UseProptexxWeb(false);
app.MapGet("/", () => "Proptexx | Auth");
app.MapPost("/_auth", (AuthHandler handler, HttpContext context) => handler.InvokeAsync(context));
app.MapPost("/_oauth/link", (OAuthLinkHandler handler, HttpContext context) => handler.InvokeAsync(context));
app.MapGet("/_oauth/callback", (OAuthCallbackHandler handler, HttpContext context) => handler.InvokeAsync(context));
app.MapPost("/_refresh", (RefreshHandler handler, HttpContext context) => handler.InvokeAsync(context));

// Temporary redirect POST requests on /_command to command service
app.MapPost("/_command", async (HttpContext context) =>
{
    var client = new HttpClient();
    client.Timeout = TimeSpan.FromMinutes(3);
    var requestMessage = new HttpRequestMessage(HttpMethod.Post, "https://command.proptexx.com/_command");
    requestMessage.Content = new StreamContent(context.Request.Body);
    
    if (context.Request.Headers.TryGetValue("Authorization", out var authHeader))
    {
        requestMessage.Headers.TryAddWithoutValidation("Authorization", authHeader.ToString());
    }

    if (context.Request.Headers.TryGetValue("apiKey", out var apiKey))
    {
        requestMessage.Headers.TryAddWithoutValidation("apiKey", apiKey.ToString());
    }

    var responseMessage = await client.SendAsync(requestMessage);

    context.Response.StatusCode = (int)responseMessage.StatusCode;

    await responseMessage.Content.CopyToAsync(context.Response.Body);
    await context.Response.Body.FlushAsync();
});

// Temporary redirect POST requests on /_query to query service
app.MapPost("/_query", async (HttpContext context) =>
{
    var client = new HttpClient();
    client.Timeout = TimeSpan.FromMinutes(3);
    var requestMessage = new HttpRequestMessage(HttpMethod.Post, "https://query.proptexx.com/_query");
    requestMessage.Content = new StreamContent(context.Request.Body);
    
    if (context.Request.Headers.TryGetValue("Authorization", out var authHeader))
    {
        requestMessage.Headers.TryAddWithoutValidation("Authorization", authHeader.ToString());
    }

    if (context.Request.Headers.TryGetValue("apiKey", out var apiKey))
    {
        requestMessage.Headers.TryAddWithoutValidation("apiKey", apiKey.ToString());
    }

    var responseMessage = await client.SendAsync(requestMessage);

    context.Response.StatusCode = (int)responseMessage.StatusCode;

    await responseMessage.Content.CopyToAsync(context.Response.Body);
    await context.Response.Body.FlushAsync();
});


app.MapDelete("/_auth", (SignoutHandler handler, HttpContext context) => handler.InvokeAsync(context));
app.MapFallback((FallbackHandler handler, HttpContext context) => handler.InvokeAsync(context));
app.Run();