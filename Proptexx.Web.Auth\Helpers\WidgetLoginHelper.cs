using System.Security.Cryptography;
using Proptexx.Web.Auth.Auth;
using Proptexx.Web.Auth.Resolvers;

namespace Proptexx.Web.Auth.Helpers;

public static class WidgetLoginHelper
{
    public static async Task LoginAsync(ScopeContext context, LoginPayload payload)
    {
        // var username = payload.GetUsername();
        // var password = payload.Password ?? throw new UnauthorizedAccessException();
        //
        // var clientId = context.User.GetClientId();
        // var redis = context.GetService<IConnectionMultiplexer>().GetDatabase();
        //
        // var fieldsToGet = new RedisValue[] { "workspaceId" };
        // var values = await redis.HashGetAsync($"client_service:{clientId}", fieldsToGet);
        //
        // if (values[0].IsNullOrEmpty || !Guid.TryParse(values[0], out var workspaceId))
        // {
        //     throw new UnauthorizedAccessException();
        // }
        //
        // await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        // var param = new { _username = username.Trim().ToLowerInvariant(), _workspace_id = workspaceId };
        // var account = await npgsql.QueryFirstOrDefaultAsync<LoginModel>(LoginModel.Sql, param);
        // if (account is null) throw new CommandException("Invalid credentials");
        //
        // if (payload.AuthType != "oauth" && !VerifyPassword(account, password))
        // {
        //     throw new CommandException("Invalid credentials");
        // }
        //
        // context.AddClaim("accountId", account.Id.ToString());
        // context.AddClaim("fullName", $"{account.FirstName} {account.FamilyName}");
        // if (account.IsRoot) context.AddClaim("isRoot", "1");
        //
        // if (!account.RegisteredInWorkspace)
        // {
        //     await npgsql.Account().JoinCluster(account.Id, workspaceId, "Lead", true);
        // }
    }

    private static bool VerifyPassword(LoginModel model, string password)
    {
        // Retrieve the stored hash and salt from the AccountSecret object
        const int iterations = 10000;
        const int hashSizeInBytes = 64; // 256 bits
        var storedSalt = Convert.FromBase64String(model.Salt);

        // Generate the hash using the incoming password and retrieved salt
        var pbkdf2Verification = new Rfc2898DeriveBytes(password, storedSalt, iterations, HashAlgorithmName.SHA512);
        var generatedHash = pbkdf2Verification.GetBytes(hashSizeInBytes);

        // Convert the generated hash to a base64 string for comparison
        var generatedHashString = Convert.ToBase64String(generatedHash);

        // Compare the generated hash with the stored hash
        return generatedHashString.Equals(model.Hash);
    }
    
    private class LoginModel
    {
        public required Guid Id { get; init; }

        public required string FirstName { get; init; }

        public required string FamilyName { get; init; }
        
        public bool IsRoot { get; init; }

        public required string Hash { get; init; }

        public required string Salt { get; init; }
        
        public bool RegisteredInWorkspace { get; init; }
        
        public static string Sql => @"
            select a.id,
                   a.first_name,
                   a.family_name,
                   a.is_root,
                   acs.hash,
                   acs.salt,
                   (count(c.workspace_id) > 0) as registered_in_workspace
            from core.account a
            join core.account_secret acs on a.id = acs.account_id and acs.expired_at is null
            join core.account_email ace on a.id = ace.account_id
            left outer join core.cluster_account_binding cab on a.id = cab.account_id
            left outer join core.cluster c on cab.cluster_id = c.id and c.workspace_id = :_workspace_id and lower(c.name) = 'lead'
            where trim(lower(ace.email)) = :_username and a.cancelled_at is null
            group by a.id, a.first_name, a.family_name, acs.hash, acs.salt";
    }
}
