

### Authenticate
POST https://auth.dev.local/_auth
Content-Type: application/json
Authorization: A<PERSON><PERSON><PERSON> ZTQwYzhmOTEtZjQ2Ni00YzFkLTlkYzctYmJmOGY2MjAyMjE4

{}

> {% client.global.set("accessToken", response.body.$accessToken) %}

### Call Batch API with two model requests, using the accessToken from previous request
POST https://api.dev.local/batch
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "items": [
    {
      "model": "flow/VirtualStagingOrRefurnishingIs",
      "config": {
        "image_url": "https://ansainteriors.com/wp-content/uploads/2022/03/interior-designer-in-delhi.jpg"
      }
    }
  ]
}
