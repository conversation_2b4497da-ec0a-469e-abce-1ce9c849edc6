using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations;

[Migration(250327002)]
public class Migrate250327002 : FluentMigrator.Migration
{
    public override void Up()
    {
        // Drop existing materialized views
        Execute.Sql(@"DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_dashboard_summary CASCADE;");
        Execute.Sql(@"DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_dashboard_summary_today CASCADE;");
        Execute.Sql(@"DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_dashboard_summary_past CASCADE;");

        Execute.Sql(@"DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_ai_requests_summary CASCADE;");
        Execute.Sql(@"DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_ai_requests_today CASCADE;");
        Execute.Sql(@"DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_ai_requests_past CASCADE;");

        Execute.Sql(@"DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_batch_requests_summary CASCADE;");
        Execute.Sql(@"DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_batch_requests_today CASCADE;");
        Execute.Sql(@"DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_batch_requests_past CASCADE;");

        // Dashboard Summary Views
        Execute.Sql(@"
            CREATE MATERIALIZED VIEW telemetry.mv_dashboard_summary_today AS
            SELECT a.created_at::date AS day,
                   COUNT(*) FILTER (WHERE a.is_api_request) AS total_api_requests,
                   COUNT(*) FILTER (WHERE a.is_ai_request) AS total_ai_requests,
                   COUNT(*) FILTER (WHERE a.is_batch) AS total_batch_requests,
                   ROUND(COALESCE(AVG(a.duration_ms), 0::double precision)::numeric, 1) AS average_request_duration,
                   COUNT(DISTINCT a.session_id) AS distinct_sessions
            FROM telemetry.api_logs a
            WHERE a.created_at >= date_trunc('day', now() AT TIME ZONE 'UTC')
              AND a.created_at < now() AT TIME ZONE 'UTC'
            GROUP BY a.created_at::date
            WITH NO DATA;
        ");

        Execute.Sql(@"CREATE UNIQUE INDEX idx_mv_dashboard_today_unique ON telemetry.mv_dashboard_summary_today(day);");

        Execute.Sql(@"
            CREATE MATERIALIZED VIEW telemetry.mv_dashboard_summary_past AS
            SELECT a.created_at::date AS day,
                   COUNT(*) FILTER (WHERE a.is_api_request) AS total_api_requests,
                   COUNT(*) FILTER (WHERE a.is_ai_request) AS total_ai_requests,
                   COUNT(*) FILTER (WHERE a.is_batch) AS total_batch_requests,
                   ROUND(COALESCE(AVG(a.duration_ms), 0::double precision)::numeric, 1) AS average_request_duration,
                   COUNT(DISTINCT a.session_id) AS distinct_sessions
            FROM telemetry.api_logs a
            WHERE a.created_at < date_trunc('day', now() AT TIME ZONE 'UTC')
            GROUP BY a.created_at::date
            WITH NO DATA;
        ");

        Execute.Sql(@"CREATE UNIQUE INDEX idx_mv_dashboard_past_unique ON telemetry.mv_dashboard_summary_past(day);");

        Execute.Sql(@"
            CREATE MATERIALIZED VIEW telemetry.mv_dashboard_summary AS
            SELECT day,
                   SUM(total_api_requests) AS total_api_requests,
                   SUM(total_ai_requests) AS total_ai_requests,
                   SUM(total_batch_requests) AS total_batch_requests,
                   ROUND(COALESCE(AVG(average_request_duration), 0::double precision)::numeric, 1) AS average_request_duration,
                   SUM(distinct_sessions) AS distinct_sessions
            FROM (
                SELECT * FROM telemetry.mv_dashboard_summary_today
                UNION ALL
                SELECT * FROM telemetry.mv_dashboard_summary_past
            ) AS combined
            GROUP BY day
            WITH NO DATA;
        ");

        Execute.Sql(@"CREATE UNIQUE INDEX idx_mv_dashboard_combined_unique ON telemetry.mv_dashboard_summary(day);");

        // AI Requests Summary Views
        Execute.Sql(@"
            CREATE MATERIALIZED VIEW telemetry.mv_ai_requests_today AS
            SELECT a.created_at::date AS day,
                   COALESCE(c.title, 'Unknown') AS client_name,
                   a.processed_endpoint AS endpoint,
                   COUNT(*) AS request_count
            FROM telemetry.api_logs a
            LEFT JOIN crm.workspace c ON a.workspace_id = c.id
            WHERE a.is_ai_request
              AND a.created_at >= date_trunc('day', now() AT TIME ZONE 'UTC')
              AND a.created_at < now() AT TIME ZONE 'UTC'
            GROUP BY a.created_at::date, c.title, a.processed_endpoint
            WITH NO DATA;
        ");

        Execute.Sql(@"CREATE UNIQUE INDEX idx_mv_ai_requests_today_unique ON telemetry.mv_ai_requests_today(day, client_name, endpoint);");

        Execute.Sql(@"
            CREATE MATERIALIZED VIEW telemetry.mv_ai_requests_past AS
            SELECT a.created_at::date AS day,
                   COALESCE(c.title, 'Unknown') AS client_name,
                   a.processed_endpoint AS endpoint,
                   COUNT(*) AS request_count
            FROM telemetry.api_logs a
            LEFT JOIN crm.workspace c ON a.workspace_id = c.id
            WHERE a.is_ai_request
              AND a.created_at < date_trunc('day', now() AT TIME ZONE 'UTC')
            GROUP BY a.created_at::date, c.title, a.processed_endpoint
            WITH NO DATA;
        ");

        Execute.Sql(@"CREATE UNIQUE INDEX idx_mv_ai_requests_past_unique ON telemetry.mv_ai_requests_past(day, client_name, endpoint);");

        Execute.Sql(@"
            CREATE MATERIALIZED VIEW telemetry.mv_ai_requests_summary AS
            SELECT * FROM telemetry.mv_ai_requests_today
            UNION ALL
            SELECT * FROM telemetry.mv_ai_requests_past
            WITH NO DATA;
        ");

        Execute.Sql(@"CREATE UNIQUE INDEX idx_mv_ai_requests_summary_unique ON telemetry.mv_ai_requests_summary(day, client_name, endpoint);");

        // Batch Requests Summary Views
        Execute.Sql(@"
            CREATE MATERIALIZED VIEW telemetry.mv_batch_requests_today AS
            SELECT e.created_at::date AS day,
                   COALESCE(c.title, 'Unknown') AS workspace_name,
                   COUNT(*) AS batch_count
            FROM telemetry.api_logs e
            LEFT JOIN crm.workspace c ON e.workspace_id = c.id
            WHERE e.is_batch
              AND e.created_at >= date_trunc('day', now() AT TIME ZONE 'UTC')
              AND e.created_at < now() AT TIME ZONE 'UTC'
            GROUP BY e.created_at::date, c.title
            WITH NO DATA;
        ");

        Execute.Sql(@"CREATE UNIQUE INDEX idx_mv_batch_requests_today_unique ON telemetry.mv_batch_requests_today(day, workspace_name);");

        Execute.Sql(@"
            CREATE MATERIALIZED VIEW telemetry.mv_batch_requests_past AS
            SELECT e.created_at::date AS day,
                   COALESCE(c.title, 'Unknown') AS workspace_name,
                   COUNT(*) AS batch_count
            FROM telemetry.api_logs e
            LEFT JOIN crm.workspace c ON e.workspace_id = c.id
            WHERE e.is_batch
              AND e.created_at < date_trunc('day', now() AT TIME ZONE 'UTC')
            GROUP BY e.created_at::date, c.title
            WITH NO DATA;
        ");

        Execute.Sql(@"CREATE UNIQUE INDEX idx_mv_batch_requests_past_unique ON telemetry.mv_batch_requests_past(day, workspace_name);");

        Execute.Sql(@"
            CREATE MATERIALIZED VIEW telemetry.mv_batch_requests_summary AS
            SELECT * FROM telemetry.mv_batch_requests_today
            UNION ALL
            SELECT * FROM telemetry.mv_batch_requests_past
            WITH NO DATA;
        ");

        Execute.Sql(@"CREATE UNIQUE INDEX idx_mv_batch_requests_summary_unique ON telemetry.mv_batch_requests_summary(day, workspace_name);");
    }

    public override void Down()
    {
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_dashboard_summary CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_dashboard_summary_today CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_dashboard_summary_past CASCADE;");

        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_ai_requests_summary CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_ai_requests_today CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_ai_requests_past CASCADE;");

        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_batch_requests_summary CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_batch_requests_today CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_batch_requests_past CASCADE;");
    }
}