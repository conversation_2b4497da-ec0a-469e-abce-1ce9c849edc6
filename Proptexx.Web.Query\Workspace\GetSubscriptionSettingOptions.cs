using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.DTOs;
using static Proptexx.Core.Constants.SubscriptionConstants;

namespace Proptexx.Web.Query.Workspace;

public sealed class GetSubscriptionSettingOptions : IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
 
        // Build subscription type options
        var settingOptions = new SubscriptionSettingOptions {
            SubscriptionTypes =
            [
                new() { Id = SubscriptionTypes.OneTime, Value = "One time" },
                new() { Id = SubscriptionTypes.PayAsYouGo, Value = "Pay as you go" },
                new() { Id = SubscriptionTypes.Period, Value = "Period" }
            ]
        };                     
        return await Task.FromResult(settingOptions);
    }
}