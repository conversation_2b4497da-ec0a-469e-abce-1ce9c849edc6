
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Npgsql;
using Proptexx.Core.Services;

public static class SubscriptionServiceAccess
{
    private static IServiceProvider? _serviceProvider;

    public static void Configure(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
    }

    public static ISubscriptionService Instance
    {
        get
        {
            if (_serviceProvider == null)
            {
                throw new InvalidOperationException("Service provider is not configured.");
            }

            return new SubscriptionService(
                _serviceProvider.GetRequiredService<ILogger<SubscriptionService>>(),
                _serviceProvider.GetRequiredService<NpgsqlDataSource>()
            );
        }
    }
}
