using Proptexx.Core.Postgresql.Builder;

namespace Proptexx.Core.Entities;

public class BatchFile : IDbTable
{
    public required string Id { get; init; }

    public required string MimeType { get; init; }

    public int Width { get; set; }

    public int Height { get; set; }
    
    public string? ErrorMessage { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public string GetDbRef() => "batching.batch_file";
}