<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <Authors>Proptexx</Authors>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <LangVersion>12</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Proptexx.Web.Webhook\Proptexx.Web.Webhook.csproj" />
        <ProjectReference Include="..\Proptexx.Web\Proptexx.Web.csproj" />
        <ProjectReference Include="..\Proptexx.AI\Proptexx.AI.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="wwwroot\" />
    </ItemGroup>

    <ItemGroup>
      <Content Update="wwwroot\index.html">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
      <Content Update="wwwroot\assets\index-BovkbvKB.css">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
      <Content Update="wwwroot\assets\index-CZ-Kfy9E.js">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
      <Content Update="wwwroot\favicon.png">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
    </ItemGroup>
    
</Project>
