### Authenticate
POST https://auth.dev.local/_auth
Content-Type: application/json
Authorization: <PERSON><PERSON><PERSON><PERSON> YzFkYTVmOWMtN2FhOS00ZTQ4LWFiMzItODE0MjVhZjA1Yjgw

{}

> {% client.global.set("accessToken", response.body.$accessToken) %}

### Onbaarding
POST https://partner.dev.local/onboarding/affiliate
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "identifier": "test-affiliate-1",
  "name": "Test affiliate #1",
  "contactName": "Are Benoni",
  "contactEmail": "<EMAIL>",
  "urls": []
}