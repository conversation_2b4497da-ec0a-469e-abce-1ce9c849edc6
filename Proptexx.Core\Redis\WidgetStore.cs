using System.Text.Json;
using Proptexx.Core.Entities;
using StackExchange.Redis;

namespace Proptexx.Core.Redis;

public sealed class WidgetStore : RedisHashStore<WidgetStore.WidgetModel>
{
    public WidgetStore(IConnectionMultiplexer connectionMultiplexer) 
        : base(connectionMultiplexer, "widget")
    {
    }

    protected override string ResolveKey(WidgetModel entry) => entry.ApiKey;
    
    public class WidgetModel
    {
        public required Guid WorkspaceId { get; set; }

        public required string ApiKey { get; init; }

        public required string WorkspaceName { get; set; }
        
        public required string ClientName { get; set; }
        
        public required string Services { get; set; }

        public required Dictionary<string, MatchStrategy> Domains { get; set; }
        
        public required WidgetOptions Options { get; set; }
    }
}