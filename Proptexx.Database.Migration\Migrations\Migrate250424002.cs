﻿using FluentMigrator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Proptexx.Database.Migration.Migrations
{
    [Migration(250424002)]
    public class Migrate250424002 : FluentMigrator.Migration
    {
        public override void Up()
        {
            Alter.Table("widget").InSchema("core")
                .AddColumn("opt_logo_url").AsString().Nullable();
        }

        public override void Down()
        {
            Delete.Column("opt_logo_url")
                .FromTable("widget").InSchema("core");
        }
    }
}
