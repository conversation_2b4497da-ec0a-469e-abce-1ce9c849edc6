using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class Widget : IDbTable
{
    [IgnoreOnUpdate] public required Guid WorkspaceId { get; init; }
    
    public string? Position { get; set; }
    
    public string? ImgUrlPattern { get; set; }
    
    public string? LogoUrl { get; set; }
    
    public string? Locale { get; set; }
    
    public string? CustomTrigger { get; set; }
    
    public bool? IsOpen { get; set; }
    
    public bool? SkipAuth { get; set; }
    
    public string? TriggerUrl { get; set; }
    
    public string? SignupCallbackUrl { get; set; }
    
    public bool SignupEmailAdmin { get; set; }
    
    public string? SignupEmail { get; set; }
    
    public string? PrimaryColor { get; set; }
    
    public string? LightColor { get; set; }
    
    public string? DarkColor { get; set; }
    
    public int Offset { get; set; }

    public int Elevation { get; set; } = 1000;
    public int? ScratchedImageMinWidth { get; set; }
    public int? ScratchedImageMinHeight { get; set; }
    public int? ScratchedImageMinAspectRatio { get; set; }
    public int? ScratchedImageMaxAspectRatio { get; set; }
    public long? ScratchedImageMinFileSizeInBytes { get; set; }
    public string? ScratchedImageAllowedFormats { get; set; }
    public string? ScratchedImageExcludedKeywords { get; set; }

    public string? ScopeSelector { get; set; }
    public string GetDbRef() => "core.widget";
}