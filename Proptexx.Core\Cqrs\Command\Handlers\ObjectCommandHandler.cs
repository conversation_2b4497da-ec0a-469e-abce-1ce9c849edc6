using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using Proptexx.Core.Json;
using Proptexx.Core.Services;
using Microsoft.Extensions.DependencyInjection;

namespace Proptexx.Core.Cqrs.Command.Handlers;

public sealed class ObjectCommandHandler : ICommandHandler
{
    private readonly TypeRegistry<ICommand> _registry;

    public ObjectCommandHandler(Action<TypeRegistry<ICommand>> registryConfig)
    {
        _registry = new TypeRegistry<ICommand>();
        registryConfig(_registry);
        Initialize();
    }

    public string Identifier => string.Empty;

    private void Initialize() => _registry.Initialize();

    public async Task<bool> HasAccessAsync(CommandContext ctx)
    {
        var type = _registry.Get(ctx.Definition.Identifier);
        if (type == null)
        {
            return false;
        }

        // Check for RequiredServiceAttribute
        var validationService = ctx.Services.GetService<RequiredServiceValidationService>();
        if (validationService != null)
        {
            return await validationService.ValidateAccessAsync(type, ctx.User, ctx.CancellationToken);
        }

        // Fallback to default behavior if service not available
        return true;
    }

    public async Task ExecuteAsync(CommandContext ctx)
    {
        var type = _registry.Get(ctx.Definition.Identifier)
                   ?? throw new CommandException("Command not found");

        var payloadObj = ctx.Definition.Payload?.Deserialize(type, JsonDefaults.JsonSerializerOptions);
        if (payloadObj is not ICommand command)
        {
            throw new CommandException("Command not parsable");
        }

        // Command validation
        Validate(command);
        
        // Execute command
        await command.ExecuteAsync(ctx);
    }

    private static void Validate(ICommand command)
    {
        var validationCtx = new ValidationContext(command);
        var validationResults = new List<ValidationResult>();
        var isValid = Validator.TryValidateObject(command, validationCtx, validationResults, validateAllProperties: true);

        if (!isValid)
        {
            var messages = validationResults.Select(x => x.ErrorMessage).ToList();
            var msg = string.Join(Environment.NewLine, messages);
            throw new ValidationException(msg);
        }
    }
}