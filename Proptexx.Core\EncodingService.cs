using System.Security.Cryptography;
using System.Text;

namespace Proptexx.Core;

public static class EncodingService
{
    private const string Delimiter = "|";
    
    public static string UniqueKey(params object[] values)
    {
        var concatenated = string.Join("|", values);
        var hashBytes = SHA256.HashData(Encoding.UTF8.GetBytes(concatenated));
        return Convert.ToBase64String(hashBytes);
    }

    public static string Encode(params string[] values)
    {
        var str = string.Join(Delimiter, values);
        var bytes = Encoding.UTF8.GetBytes(str);
        return Convert.ToBase64String(bytes)
            .Replace('+', '-')
            .Replace('/', '_')
            .TrimEnd('=');
    }

    public static string[] Decode(string key)
    {
        var paddedApiKey = key.Replace('-', '+').Replace('_', '/');

        var paddingNeeded = 4 - paddedApiKey.Length % 4;
        if (paddingNeeded < 4)
        {
            paddedApiKey = paddedApiKey.PadRight(paddedApiKey.Length + paddingNeeded, '=');
        }

        var bytes = Convert.FromBase64String(paddedApiKey);
        var decodedString = Encoding.UTF8.GetString(bytes);

        return decodedString.Split(Delimiter);
    }
}

