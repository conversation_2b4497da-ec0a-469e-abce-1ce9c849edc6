using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Proptexx.Core.HealthCheck;

public static class Extensions
{
    public static WebApplicationBuilder AddProptexxHealthChecks(this WebApplicationBuilder builder)
    {
        builder.Services.AddSingleton<IHealthCheck, PostgresHealthCheck>();
        builder.Services.AddSingleton<IHealthCheck, RedisHealthCheck>();

        builder.Services.AddHealthChecks()
            .AddCheck<PostgresHealthCheck>("PostgreSQL")
            .AddCheck<RedisHealthCheck>("Redis");
        
        return builder;
    }
}