using System.Text.Json.Serialization;

namespace Proptexx.Core.DTOs.Outseta // Changed namespace
{
    public class OutsetaData
    {
        public string? Uid { get; set; }
        public string? Name { get; set; }
        public MailingAddress? MailingAddress { get; set; }
        public int? AccountStage { get; set; } 
        public DateTime? Created { get; set; }
        public DateTime? Updated { get; set; }
        public List<PersonAccount>? PersonAccount { get; set; }
        public string? InvoiceNotes { get; set; }
        public bool? IsDemo { get; set; }
        public List<object>? StripeInvoices { get; set; } 
        public List<object>? StripePaymentMethods { get; set; } 
        public List<object>? StripeSubscriptions { get; set; } 
        public List<Subscription>? Subscriptions { get; set; }
        public List<object>? Deals { get; set; } 
        public List<object>? TaxIds { get; set; } 
        public string? TaxStatus { get; set; }
        public string? AccountStageLabel { get; set; }
        public Subscription? CurrentSubscription { get; set; }
        public bool? HasLoggedIn { get; set; }
        public Subscription? LatestSubscription { get; set; }
        public decimal? LifetimeRevenue { get; set; }
        public Person? PrimaryContact { get; set; }
        public bool? TaxIdIsInvalid { get; set; }
        public bool? IsLivemode { get; set; }
        public bool? SchemaLessDataLoaded { get; set; }
        public ActivityEventData? ActivityEventData { get; set; }
        [JsonPropertyName("_objectType")]
        public string? ObjectType { get; set; }
        public string? StoreUrl { get; set; }
        public string? Platform { get; set; }
        public int? Viewers { get; set; }
    }

    public class AccountsResponse
    {
        public Metadata? Metadata { get; set; }
        public List<OutsetaData>? Items { get; set; }
    }
}
