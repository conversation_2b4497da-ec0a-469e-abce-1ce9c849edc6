using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations;

[Migration(250417001)]
public class Migrate250417001 : FluentMigrator.Migration
{
    public override void Up()
    {
        // 1) Today's widget renders
        Execute.Sql(@"
                CREATE MATERIALIZED VIEW IF NOT EXISTS telemetry.mv_widget_renders_today AS
                SELECT
                    created_at::date                                                   AS day,
                    workspace_id,
                    REGEXP_REPLACE(
                        (request_body::json->'payload'->>'url'),
                        '^https?://(www\.)?',
                        ''
                    )                                                                   AS render_url,
                    COUNT(*)                                                           AS request_count
                FROM telemetry.api_logs
                WHERE
                    is_widget
                    AND identifier = 'widget.indoorStagingOrRefurnishing'
                    AND created_at::date = CURRENT_DATE
                GROUP BY day, workspace_id, render_url
                WITH NO DATA;
            ");

        // 2) Past widget renders
        Execute.Sql(@"
                CREATE MATERIALIZED VIEW IF NOT EXISTS telemetry.mv_widget_renders_past AS
                SELECT
                    created_at::date                                                   AS day,
                    workspace_id,
                    REGEXP_REPLACE(
                        (request_body::json->'payload'->>'url'),
                        '^https?://(www\.)?',
                        ''
                    )                                                                   AS render_url,
                    COUNT(*)                                                           AS request_count
                FROM telemetry.api_logs
                WHERE
                    is_widget
                    AND identifier = 'widget.indoorStagingOrRefurnishing'
                    AND created_at::date < CURRENT_DATE
                GROUP BY day, workspace_id, render_url
                WITH NO DATA;
            ");

        // 3) Combined view of today + past
        Execute.Sql(@"
                CREATE MATERIALIZED VIEW IF NOT EXISTS telemetry.mv_widget_renders_summary AS
                SELECT day, workspace_id, render_url, request_count
                FROM telemetry.mv_widget_renders_today

                UNION ALL

                SELECT day, workspace_id, render_url, request_count
                FROM telemetry.mv_widget_renders_past
                WITH NO DATA;
            ");

        // Indexes to support efficient querying
        Execute.Sql(@"
                CREATE INDEX IF NOT EXISTS idx_mv_widget_renders_today_day_ws_url
                  ON telemetry.mv_widget_renders_today (day, workspace_id, render_url);

                CREATE INDEX IF NOT EXISTS idx_mv_widget_renders_past_day_ws_url
                  ON telemetry.mv_widget_renders_past (day, workspace_id, render_url);

                CREATE INDEX IF NOT EXISTS idx_mv_widget_renders_summary_day_ws_url
                  ON telemetry.mv_widget_renders_summary (day, workspace_id, render_url);
            ");
    }

    public override void Down()
    {
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_widget_renders_summary;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_widget_renders_past;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_widget_renders_today;");
    }
}