using System.Text.Json.Serialization;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Http;
using Proptexx.Core.Services;
using StackExchange.Redis;

namespace Proptexx.AI.Models.CV;

public sealed class PostProcessor : BaseComputerVisionModel
{
    public PostProcessor(
        IComputerVisionClient computerVisionClient, 
        IImageAssessmentClient imageAssessmentClient,
        IConnectionMultiplexer connectionMultiplexer, 
        ILoggerFactory loggerFactory)
        : base("PostProcessor",
            computerVisionClient,
            imageAssessmentClient,
            connectionMultiplexer,
            loggerFactory)
    {
    }

    public sealed class Result
    {
        [JsonPropertyName("isEmptyLivingRoom")]
        public bool? IsEmptyLivingRoom { get; init; }

        [JsonPropertyName("isEmptyBedroom")]
        public bool? IsEmptyBedroom { get; init; }

        [JsonPropertyName("hasMoreThanOneToilet")]
        public bool? HasMoreThanOneToilet { get; init; }

        [JsonPropertyName("hasRealAnimal")]
        public bool? HasRealAnimal { get; init; }

        [JsonPropertyName("hasHorrorPoster")]
        public bool? HasHorrorPoster { get; init; }

        [JsonPropertyName("hasPerson")]
        public bool? HasPerson { get; init; }

        [JsonPropertyName("hasAbnormalSize")]
        public bool? HasAbnormalSize { get; init; }
    }
}