### Authenticate
# @name authen
POST https://auth.proptexx.com/_auth
Content-Type: application/json
Authorization: Api<PERSON>ey MDc4MTYzZDUtYjM2NS00MTFlLThlNDItZDRkYTMwNjhkYzQ5

{}

### Extract token using correct syntax for property with $ sign
@accessToken = {{authen.response.body.$.$accessToken}}

### Call Batch API with two model requests, using the accessToken from previous request
POST https://api.proptexx.com/batch
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "items": [
    {
      "model": "gen/GenerativeCompliancePrecheck",
      "config": {
        "image_url": "https://cdn-bnokp.nitrocdn.com/QNoeDwCprhACHQcnEmHgXDhDpbEOlRHH/assets/images/optimized/rev-7de7212/www.decorilla.com/online-decorating/wp-content/uploads/2021/12/How-to-decorate-a-small-living-room-by-Decorilla-designer-Mladen-C.jpg"
      }
    },
    {
      "model": "gen/GenerativeCompliancePrecheck",
      "config": {
        "image_url": "https://proptexxstorage1.blob.core.windows.net/cfe55da4-d418-4775-a236-220f66f52116/3452e0ad-ae03-4a8a-8f9e-26bfa4b42885_1750659957.jpg"
      }
    }
  ]
}

//{
//  "items": [
//    {
//      "model": "cv/AltTextGenerator",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/AnimalDetector",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/ApplianceFeatureDetector",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/ArchitectureDetector",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/EmptinessDetector",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/ExteriorFeatureDetector",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/FloorplanAnalyzer",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/FurnitureConditionDetector",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/ImageQualityAssess",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/ImageTamperingDetector",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/InteriorFeatureDetector",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/LicensePlateDetector",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/MaterialDetector",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/ObjectDetector",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/PersonDetector",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/PostProcessor",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/PreProcessorForObjects",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/PreProcessorForRoomScene",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/PropertyConditionDetector",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/SignDetector",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/ViewDetector",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/WatermarkLogoDetector",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    },
//    {
//      "model": "cv/WatermarkTextDetector",
//      "config": {
//        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
//      }
//    }
//  ]
//}

{
  "items": [
    {
      "model": "huspi/LightCondition",
      "config": {
        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
      }
    },
    {
      "model": "huspi/PropertyAmenitiesGeneration",
      "config": {
        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
      }
    },
    {
      "model": "huspi/PropertyCondition",
      "config": {
        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
      }
    },
    {
      "model": "huspi/PropertyDescriptionGenerator",
      "config": {
        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
      }
    },
    {
      "model": "huspi/PropertyLocationGeneration",
      "config": {
        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
      }
    },
    {
      "model": "huspi/PropertyQuality",
      "config": {
        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
      }
    },
    {
      "model": "huspi/RoomType",
      "config": {
        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
      }
    },
    {
      "model": "huspi/TitleGeneration",
      "config": {
        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
      }
    },
    {
      "model": "huspi/ViewAnalyzer",
      "config": {
        "image_url": "https://pictures.immobilienscout24.de/listings/71d0fdab-9f14-4581-8beb-ddc5ba471567-**********.jpg/ORIG/resize/1106x830%3E/format/webp/quality/73"
      }
    }
  ]
}


> {% client.global.set("batchId", response.body.id) %}


### Call Status API with the response id from previous call to get status of batch. Also using the accessToken from first request

#GET https://api.proptexx.com/status/{{batchId}}
#Content-Type: application/json
#Authorization: Bearer {{accessToken}}