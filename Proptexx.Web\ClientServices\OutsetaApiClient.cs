using Microsoft.Extensions.Options;
using Proptexx.Core.Entities.Outseta;
using Proptexx.Core.Options;
using Proptexx.Web.ClientServices.Interfaces;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Web;

namespace Proptexx.Web.Services
{


    public class OutsetaApiClient : IOutsetaApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly OutsetaOptions _options;
        private readonly JsonSerializerOptions _jsonSerializerOptions;

        public OutsetaApiClient(HttpClient httpClient, IOptions<OutsetaOptions> options)
        {
            _httpClient = httpClient;
            _options = options.Value;
            _httpClient.BaseAddress = new Uri(_options.BaseUrl.EndsWith("/") ? _options.BaseUrl : _options.BaseUrl + "/");
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Outseta", $"{_options.ApiKey}:{_options.SecretKey}");
            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            _jsonSerializerOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
            };
        }

        private string BuildQueryString(int limit, int offset, string? fields, string? orderBy, Dictionary<string, string>? filters)
        {
            var query = HttpUtility.ParseQueryString(string.Empty);
            query["limit"] = limit.ToString();
            query["offset"] = offset.ToString();
            if (!string.IsNullOrEmpty(fields))
            {
                query["fields"] = fields;
            }
            if (!string.IsNullOrEmpty(orderBy))
            {
                query["orderBy"] = orderBy;
            }
            if (filters != null)
            {
                foreach (var filter in filters)
                {
                    query[filter.Key] = filter.Value;
                }
            }
            return query.ToString() ?? string.Empty;
        }

        public async Task<PeopleResponse?> GetAllPeopleAsync(int limit = 100, int offset = 0, string? fields = null, string? orderBy = null, Dictionary<string, string>? filters = null)
        {
            var queryString = BuildQueryString(limit, offset, fields, orderBy, filters);
            var response = await _httpClient.GetAsync($"crm/people?{queryString}");
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadFromJsonAsync<PeopleResponse>(_jsonSerializerOptions);
        }

        public async Task<Person?> GetPersonAsync(string personUid, string? fields = null)
        {
            var queryString = string.IsNullOrEmpty(fields) ? string.Empty : $"?fields={fields}";
            var response = await _httpClient.GetAsync($"crm/people/{personUid}{queryString}");
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadFromJsonAsync<Person>(_jsonSerializerOptions);
        }

        public async Task<Person?> AddPersonAsync(Person person)
        {
            var response = await _httpClient.PostAsJsonAsync("crm/people", person, _jsonSerializerOptions);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadFromJsonAsync<Person>(_jsonSerializerOptions);
        }

        public async Task<Person?> UpdatePersonAsync(string personUid, Person person)
        {
            var response = await _httpClient.PutAsJsonAsync($"crm/people/{personUid}", person, _jsonSerializerOptions);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadFromJsonAsync<Person>(_jsonSerializerOptions);
        }

        public async Task DeletePersonAsync(string personUid)
        {
            var response = await _httpClient.DeleteAsync($"crm/people/{personUid}");
            response.EnsureSuccessStatusCode();
        }

        public async Task SetTemporaryPasswordAsync(string personUid, string temporaryPassword)
        {
            var payload = new { temporaryPassword };
            var response = await _httpClient.PutAsJsonAsync($"crm/people/{personUid}/temporary-password", payload, _jsonSerializerOptions);
            response.EnsureSuccessStatusCode();
        }

        public async Task InitiatePasswordResetAsync(string email)
        {
            var payload = new { Email = email };
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, "crm/people/initiate-password-reset")
            {
                Content = JsonContent.Create(payload, options: _jsonSerializerOptions)
            };
            // Do not add default Authorization header for this specific request as per "noauth" requirement.
            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        public async Task SendConfirmationEmailToPrimaryPersonAsync(string accountUid)
        {
            var response = await _httpClient.PutAsync($"crm/accounts/{accountUid}/send-confirmation-email/primary-person", null);
            response.EnsureSuccessStatusCode();
        }

        public async Task SendConfirmationEmailToSpecificPersonAsync(string accountUid, string personUid)
        {
            var response = await _httpClient.PutAsync($"crm/accounts/{accountUid}/people/{personUid}/send-confirmation-email", null);
            response.EnsureSuccessStatusCode();
        }

        public async Task SendConfirmationEmailToAllPeopleAsync(string accountUid)
        {
            var response = await _httpClient.PutAsync($"crm/accounts/{accountUid}/send-confirmation-email/all-people", null);
            response.EnsureSuccessStatusCode();
        }

        public async Task<Account?> GetAccountAsync(string accountUid, string? fields = null)
        {
            var queryString = string.IsNullOrEmpty(fields) ? string.Empty : $"?fields={fields}";
            var response = await _httpClient.GetAsync($"crm/accounts/{accountUid}{queryString}");
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadFromJsonAsync<Account>(_jsonSerializerOptions);
        }

        public async Task<AccountsResponse?> GetAllAccountsAsync(int limit = 100, int offset = 0, string? fields = null, string? orderBy = null, Dictionary<string, string>? filters = null)
        {
            var queryString = BuildQueryString(limit, offset, fields, orderBy, filters);
            var response = await _httpClient.GetAsync($"crm/accounts?{queryString}");
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadFromJsonAsync<AccountsResponse>(_jsonSerializerOptions);
        }

        // Added for posting custom activity / webhook data
        public async Task<HttpResponseMessage> PostCustomActivityAsync(string webhookUrl, object payload)
        {
            // The webhookUrl is expected to be a full URL.
            // The existing _httpClient can be used to make requests to arbitrary URLs
            // if they are absolute. If webhookUrl is relative, it would be relative to _httpClient.BaseAddress.
            var response = await _httpClient.PostAsJsonAsync(webhookUrl, payload, _jsonSerializerOptions);
            return response; // Return the full response for the caller to handle.
        }
    }
}
