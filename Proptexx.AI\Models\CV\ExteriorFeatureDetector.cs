using System.Text.Json.Serialization;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Http;
using Proptexx.Core.Services;
using StackExchange.Redis;

namespace Proptexx.AI.Models.CV;

public sealed class ExteriorFeatureDetector(
    IComputerVisionClient computerVisionClient,
    IImageAssessmentClient imageAssessmentClient,
    IConnectionMultiplexer connectionMultiplexer,
    ILoggerFactory loggerFactory)
    : BaseComputerVisionModel(
        "ExteriorFeatureDetector",
        computerVisionClient,
        imageAssessmentClient,
        connectionMultiplexer,
        loggerFactory)
{
    public sealed class Result
    {
        [JsonPropertyName("outdoor_space_type")]
        public string? OutdoorSpaceType { get; init; }
        
        [JsonPropertyName("type_of_building")]
        public string[]? TypeOfBuilding { get; init; }
        
        [JsonPropertyName("feature")]
        public string[]? Feature { get; init; }
        
        [JsonPropertyName("confidence")]
        public float[]? Confidence { get; init; }
    }
}