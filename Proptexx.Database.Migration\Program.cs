﻿using System.Diagnostics;
using System.Reflection;
using FluentMigrator.Runner;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Npgsql;
using Proptexx.Core.Configuration;
using Serilog;

string? databaseHost = null;
string? databaseName = null;
var builder = Host.CreateApplicationBuilder(args);
builder.AddProptexxConfiguration();
builder.AddProptexxLogging();

builder.Services.AddFluentMigratorCore()
    .ConfigureRunner(config =>
    {
        var connectionString = builder.Configuration.GetConnectionString("Postgresql");
        if (string.IsNullOrWhiteSpace(connectionString))
        {
            throw new Exception("The Postgres connection string is empty");
        }

        var connStrBuilder = new NpgsqlConnectionStringBuilder(connectionString);
        databaseHost = connStrBuilder.Host;
        databaseName = connStrBuilder.Database;

        var assembly = Assembly.GetExecutingAssembly();

        config.AddPostgres();
        config.WithGlobalConnectionString(connectionString);
        config.WithMigrationsIn(assembly);
        config.ScanIn(assembly);
    });

var host = builder.Build();
var config = host.Services.GetRequiredService<IConfiguration>();
var rollbackTo = config.GetValue<int?>("Rollback", null);
var stopwatch = Stopwatch.StartNew();

using var scope = host.Services.CreateScope();
var logger = scope.ServiceProvider.GetRequiredService<ILogger>();
var migrator = scope.ServiceProvider.GetRequiredService<IMigrationRunner>();
migrator.Processor.BeginTransaction();

try
{
    logger.Information($"Starting migration on {databaseName} @ {databaseHost}");

    //if (rollbackTo.HasValue)
    //{
    //    logger.Information($"Rolling back to migration '{rollbackTo.Value}' on {databaseName} @ {databaseHost}");

    //    migrator.RollbackToVersion(rollbackTo.Value);
    //    return;
    //}

    if (!migrator.HasMigrationsToApplyUp())
    {
        logger.Information("No migrations to apply..");
        return;
    }

    migrator.MigrateUp();
    migrator.Processor.CommitTransaction();
    logger.Information($"Migrations completed successfully");
}
catch (Exception e)
{
    logger.Error(e, $"Migration failed. Rolling back..");
    migrator.Processor.RollbackTransaction();
}
finally
{
    logger.Information($"Time taken: {stopwatch.ElapsedMilliseconds / 1000} seconds");
}
