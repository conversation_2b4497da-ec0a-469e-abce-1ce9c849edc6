--
-- PostgreSQL database dump
--

-- Dumped from database version 16.4
-- Dumped by pg_dump version 17.2

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: batching; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA batching;


--
-- Name: core; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA core;


--
-- Name: crm; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA crm;


--
-- Name: public; Type: SCHEMA; Schema: -; Owner: -
--

-- *not* creating schema, since initdb creates it


--
-- Name: telemetry; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA telemetry;


--
-- Name: widget; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA widget;


--
-- Name: citext; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS citext WITH SCHEMA public;


--
-- Name: EXTENSION citext; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION citext IS 'data type for case-insensitive character strings';



--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: get_batch_report(integer); Type: FUNCTION; Schema: batching; Owner: -
--

CREATE FUNCTION batching.get_batch_report(_limit_count integer) RETURNS TABLE(batch_id uuid, created_at timestamp without time zone, callback_url text, task_id uuid, model text, config jsonb, started_at timestamp without time zone, status integer, result jsonb, error_message text)
    LANGUAGE sql
AS $$
with
    filtered_batches as (
        select b.id
        from batching.batch b
                 left join batching.webhook_feedback wf on b.id = wf.batch_id and wf.sent_at > b.completed_at
        where b.callback_url is not null and b.completed_at is not null
        group by b.id
        having
            count(wf.id) = 0
            or (count(wf.id) between 1 and 3
            and count(case when wf.is_success then 1 end) = 0
            and max(wf.sent_at) < current_timestamp - interval '2 minutes'
            )
        limit _limit_count
    ),
    locked_batches as(
        select b1.id, b1.created_at, b1.callback_url
        from batching.batch b1
                 join filtered_batches fb on b1.id = fb.id
            for update skip locked
    )
select distinct on (t.id)
    lb.id as batch_id,
    lb.created_at,
    lb.callback_url,
    t.id as task_id,
    t.model,
    t.config,
    r.started_at,
    coalesce(r.status, 0) as status,
    r.output as result,
    r.error_message
from locked_batches lb
         join batching.task t on lb.id = t.batch_id
         left join batching.result r on t.id = r.task_id
order by t.id, r.started_at desc;
$$;


--
-- Name: queue_pop(integer); Type: FUNCTION; Schema: batching; Owner: -
--

CREATE FUNCTION batching.queue_pop(_limit_rows integer) RETURNS TABLE(id uuid, batch_id uuid, file_hash text, item_hash text, model text, config jsonb, mime_type text, status integer, result jsonb, error_message text, created_at timestamp without time zone, processed_at timestamp without time zone, completed_at timestamp without time zone, workspace_id uuid, callback_url text)
    LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
        WITH cte AS (
            SELECT bi.id AS batch_item_id,
                   bi.batch_id,
                   bi.file_hash,
                   bi.item_hash,
                   bi.model,
                   bi.config,
                   bi.mime_type,
                   bi.status,
                   bi.result,
                   bi.error_message,
                   bi.created_at,
                   bi.processed_at,
                   bi.completed_at,
                   b.workspace_id,
                   b.callback_url
            FROM batching.batch_item bi
                     JOIN batching.batch b ON bi.batch_id = b.id
            WHERE bi.status = 0
            ORDER BY b.created_at DESC
                FOR UPDATE SKIP LOCKED
            LIMIT _limit_rows
            )
            UPDATE batching.batch_item bitem
                SET status = 1,
                    processed_at = NOW() AT TIME ZONE 'UTC'
                WHERE bitem.id IN (SELECT batch_item_id FROM cte)
                RETURNING
                    bitem.id,
                    bitem.batch_id,
                    bitem.file_hash,
                    bitem.item_hash,
                    bitem.model,
                    bitem.config,
                    bitem.mime_type,
                    bitem.status,
                    bitem.result,
                    bitem.error_message,
                    bitem.created_at,
                    bitem.processed_at,
                    bitem.completed_at,
                    (SELECT cte.workspace_id FROM cte WHERE cte.batch_item_id = bitem.id) AS workspace_id,
                    (SELECT cte.callback_url FROM cte WHERE cte.batch_item_id = bitem.id) AS callback_url;
END;
$$;


--
-- Name: generate_api_key(uuid); Type: FUNCTION; Schema: crm; Owner: -
--

CREATE FUNCTION crm.generate_api_key(unique_id uuid) RETURNS text
    LANGUAGE plpgsql
AS $_$
DECLARE
    api_key TEXT;
BEGIN
    -- Convert UUID to text
    api_key := unique_id::TEXT;

    -- Encode to Base64 and make it URL-safe
    api_key := replace(replace(encode(convert_to(api_key, 'UTF8'), 'base64'), '+', '-'), '/', '_');

    -- Remove padding `=` characters
    api_key := regexp_replace(api_key, '=+$', '');

    RETURN api_key;
END;
$_$;


--
-- Name: parse_api_key(text); Type: FUNCTION; Schema: crm; Owner: -
--

CREATE FUNCTION crm.parse_api_key(api_key text) RETURNS uuid
    LANGUAGE plpgsql
AS $$
DECLARE
    padded_api_key TEXT;
    decoded_text TEXT;
    secret_id UUID;
BEGIN
    -- Replace `-` with `+` and `_` with `/` to make it standard Base64
    padded_api_key := replace(replace(api_key, '-', '+'), '_', '/');

    -- Add padding if necessary
    WHILE length(padded_api_key) % 4 <> 0 LOOP
            padded_api_key := padded_api_key || '=';
        END LOOP;

    -- Decode from Base64
    decoded_text := convert_from(decode(padded_api_key, 'base64'), 'UTF8');

    -- Convert decoded text back to UUID
    secret_id := decoded_text::UUID;

    RETURN secret_id;
END;
$$;


--
-- Name: set_order_nbr(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION core.set_order_nbr() RETURNS trigger
    LANGUAGE plpgsql
AS $$
BEGIN
    IF NEW.order_nbr IS NULL THEN
        NEW.order_nbr := nextval('core.order_nbr_seq');
    END IF;
    RETURN NEW;
END;
$$;


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: batch; Type: TABLE; Schema: batching; Owner: -
--

CREATE TABLE batching.batch (
                                id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                                workspace_id uuid NOT NULL,
                                account_id uuid,
                                created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                                callback_url text,
                                is_sync boolean DEFAULT false NOT NULL,
                                completed_at timestamp without time zone,
                                session public.citext,
                                label text
);


--
-- Name: batch_item; Type: TABLE; Schema: batching; Owner: -
--

CREATE TABLE batching.batch_item (
                                     id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                                     batch_id uuid NOT NULL,
                                     file_hash text,
                                     item_hash text NOT NULL,
                                     model text NOT NULL,
                                     config jsonb NOT NULL,
                                     mime_type text,
                                     status integer DEFAULT 0 NOT NULL,
                                     result jsonb,
                                     error_message text,
                                     created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                                     processed_at timestamp without time zone,
                                     completed_at timestamp without time zone
);


--
-- Name: request_meta; Type: TABLE; Schema: batching; Owner: -
--

CREATE TABLE batching.request_meta (
                                       id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                                       batch_id uuid NOT NULL,
                                       correlation_id text,
                                       client_ip text,
                                       user_agent text,
                                       referrer text,
                                       language text
);


--
-- Name: result; Type: TABLE; Schema: batching; Owner: -
--

CREATE TABLE batching.result (
                                 id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                                 task_id uuid NOT NULL,
                                 status integer NOT NULL,
                                 output jsonb,
                                 error_message text,
                                 started_at timestamp without time zone NOT NULL,
                                 completed_at timestamp without time zone NOT NULL,
                                 request_params jsonb,
                                 response_params jsonb,
                                 exception text
);


--
-- Name: task; Type: TABLE; Schema: batching; Owner: -
--

CREATE TABLE batching.task (
                               id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                               batch_id uuid NOT NULL,
                               created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                               model text NOT NULL,
                               config jsonb NOT NULL
);


--
-- Name: webhook_feedback; Type: TABLE; Schema: batching; Owner: -
--

CREATE TABLE batching.webhook_feedback (
                                           id uuid DEFAULT public.uuid_generate_v1() NOT NULL,
                                           batch_id uuid NOT NULL,
                                           is_success boolean DEFAULT false NOT NULL,
                                           callback_url text NOT NULL,
                                           response_text text,
                                           status_code integer,
                                           sent_at timestamp without time zone
);


--
-- Name: coupon; Type: TABLE; Schema: core; Owner: -
--

CREATE TABLE core.coupon (
                             id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                             created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                             code public.citext NOT NULL,
                             discount_type integer NOT NULL,
                             discount_value numeric(19,5) NOT NULL,
                             workspace_id uuid
);


--
-- Name: coupon_product_binding; Type: TABLE; Schema: core; Owner: -
--

CREATE TABLE core.coupon_product_binding (
                                             id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                                             created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                             coupon_id uuid NOT NULL,
                                             product_id uuid,
                                             valid_from timestamp without time zone,
                                             valid_to timestamp without time zone,
                                             created_by uuid NOT NULL
);


--
-- Name: domain; Type: TABLE; Schema: core; Owner: -
--

CREATE TABLE core.domain (
                             id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                             workspace_id uuid NOT NULL,
                             created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                             hostname public.citext NOT NULL,
                             path text,
                             input_value text,
                             match_strategy integer DEFAULT 0 NOT NULL,
                             path_match text,
                             updated_at timestamp without time zone,
                             query_params text
);


--
-- Name: order_nbr_seq; Type: SEQUENCE; Schema: core; Owner: -
--

CREATE SEQUENCE core.order_nbr_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: order; Type: TABLE; Schema: core; Owner: -
--

CREATE TABLE core."order" (
                              id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                              account_id uuid NOT NULL,
                              created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                              total_price numeric(19,5) NOT NULL,
                              currency text NOT NULL,
                              reference_person text,
                              paid_at timestamp without time zone,
                              payment_link text,
                              order_nbr integer DEFAULT nextval('core.order_nbr_seq'::regclass)
);


--
-- Name: order_line; Type: TABLE; Schema: core; Owner: -
--

CREATE TABLE core.order_line (
                                 id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                                 order_id uuid NOT NULL,
                                 workspace_id uuid NOT NULL,
                                 product_id uuid NOT NULL,
                                 created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL
);


--
-- Name: order_payment_history; Type: TABLE; Schema: core; Owner: -
--

CREATE TABLE core.order_payment_history (
                                            id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                                            order_id uuid NOT NULL,
                                            created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                                            amount numeric(19,5) NOT NULL,
                                            payment_channel text NOT NULL,
                                            payment_ref text NOT NULL
);


--
-- Name: product; Type: TABLE; Schema: core; Owner: -
--

CREATE TABLE core.product (
                              id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                              created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                              title text NOT NULL,
                              description text,
                              price_amount numeric(19,5) DEFAULT 0 NOT NULL,
                              currency character varying(3) NOT NULL,
                              config jsonb DEFAULT '{}'::jsonb NOT NULL,
                              updated_at timestamp without time zone,
                              payment_type text NOT NULL,
                              summary text,
                              content text,
                              workspace_id uuid,
                              active boolean DEFAULT true NOT NULL
);


--
-- Name: product_service_binding; Type: TABLE; Schema: core; Owner: -
--

CREATE TABLE core.product_service_binding (
                                              product_id uuid NOT NULL,
                                              service_id public.citext NOT NULL,
                                              created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL
);


--
-- Name: sale_channel; Type: TABLE; Schema: core; Owner: -
--

CREATE TABLE core.sale_channel (
                                   id uuid DEFAULT public.uuid_generate_v1() NOT NULL,
                                   workspace_id uuid NOT NULL,
                                   created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                                   name text NOT NULL,
                                   description text
);


--
-- Name: sale_channel_product_binding; Type: TABLE; Schema: core; Owner: -
--

CREATE TABLE core.sale_channel_product_binding (
                                                   sale_channel_id uuid NOT NULL,
                                                   product_id uuid NOT NULL
);


--
-- Name: service; Type: TABLE; Schema: core; Owner: -
--

CREATE TABLE core.service (
                              id public.citext NOT NULL,
                              title text NOT NULL,
                              description text,
                              service_type text NOT NULL,
                              credit_cost numeric(19,5) NOT NULL,
                              settings jsonb DEFAULT '{}'::jsonb NOT NULL,
                              active_from timestamp without time zone,
                              active_to timestamp without time zone
);


--
-- Name: service_type; Type: TABLE; Schema: core; Owner: -
--

CREATE TABLE core.service_type (
                                   id text NOT NULL,
                                   settings jsonb DEFAULT '{}'::jsonb NOT NULL
);


--
-- Name: widget; Type: TABLE; Schema: core; Owner: -
--

CREATE TABLE core.widget (
                             workspace_id uuid NOT NULL,
                             "position" text DEFAULT 'bottom-right'::text,
                             img_url_pattern text,
                             logo_url text,
                             locale text,
                             custom_trigger text,
                             is_open boolean,
                             skip_auth boolean,
                             trigger_url text,
                             signup_callback_url text,
                             signup_email_admin boolean DEFAULT true NOT NULL,
                             signup_email text,
                             primary_color text,
                             light_color text,
                             dark_color text,
                             "offset" integer DEFAULT 0 NOT NULL,
                             elevation integer DEFAULT 1000 NOT NULL,
                             use_ads boolean DEFAULT false NOT NULL,
                             customize_warning boolean DEFAULT false NOT NULL,
                             single_image boolean DEFAULT false NOT NULL,
                             layout text DEFAULT 'docked'::text NOT NULL
);


--
-- Name: workspace_service_binding; Type: TABLE; Schema: core; Owner: -
--

CREATE TABLE core.workspace_service_binding (
                                                workspace_id uuid NOT NULL,
                                                service_id public.citext NOT NULL,
                                                created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                                                expires_at timestamp without time zone
);


--
-- Name: account; Type: TABLE; Schema: crm; Owner: -
--

CREATE TABLE crm.account (
                             id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                             first_name text NOT NULL,
                             family_name text NOT NULL,
                             date_of_birth date,
                             gender integer,
                             language text,
                             workspace_id uuid,
                             created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                             updated_at timestamp without time zone,
                             cancelled_at timestamp without time zone,
                             cancellation_reason text,
                             is_root boolean DEFAULT false NOT NULL
);


--
-- Name: account_data; Type: TABLE; Schema: crm; Owner: -
--

CREATE TABLE crm.account_data (
                                  id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                                  account_id uuid NOT NULL,
                                  data_key public.citext NOT NULL,
                                  data_value text,
                                  created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                                  updated_at timestamp without time zone
);


--
-- Name: account_email; Type: TABLE; Schema: crm; Owner: -
--

CREATE TABLE crm.account_email (
                                   id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                                   account_id uuid NOT NULL,
                                   email public.citext NOT NULL,
                                   created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                                   verified_at timestamp without time zone
);


--
-- Name: account_id_lookup; Type: TABLE; Schema: crm; Owner: -
--

CREATE TABLE crm.account_id_lookup (
                                       old_id text NOT NULL,
                                       new_id uuid NOT NULL
);


--
-- Name: account_phone; Type: TABLE; Schema: crm; Owner: -
--

CREATE TABLE crm.account_phone (
                                   id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                                   account_id uuid NOT NULL,
                                   number public.citext NOT NULL,
                                   created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                                   verified_at timestamp without time zone
);


--
-- Name: account_secret; Type: TABLE; Schema: crm; Owner: -
--

CREATE TABLE crm.account_secret (
                                    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                                    account_id uuid NOT NULL,
                                    hash text NOT NULL,
                                    salt text NOT NULL,
                                    created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                                    expired_at timestamp without time zone
);


--
-- Name: client; Type: TABLE; Schema: crm; Owner: -
--

CREATE TABLE crm.client (
                            id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                            workspace_id uuid,
                            name public.citext NOT NULL,
                            description text,
                            issue_token boolean,
                            scopes text,
                            session_lifespan integer DEFAULT 10 NOT NULL,
                            created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                            updated_at timestamp without time zone,
                            cancelled_at timestamp without time zone,
                            cancellation_reason text
);


--
-- Name: client_secret; Type: TABLE; Schema: crm; Owner: -
--

CREATE TABLE crm.client_secret (
                                   id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                                   client_id uuid NOT NULL,
                                   created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                                   expired_at timestamp without time zone
);


--
-- Name: cluster; Type: TABLE; Schema: crm; Owner: -
--

CREATE TABLE crm.cluster (
                             id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                             parent_id uuid,
                             cluster_type_id uuid NOT NULL,
                             workspace_id uuid NOT NULL,
                             name public.citext NOT NULL,
                             created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                             updated_at timestamp without time zone
);


--
-- Name: cluster_account_binding; Type: TABLE; Schema: crm; Owner: -
--

CREATE TABLE crm.cluster_account_binding (
                                             cluster_id uuid NOT NULL,
                                             account_id uuid NOT NULL,
                                             created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                                             updated_at timestamp without time zone,
                                             verified_at timestamp without time zone,
                                             verified_by uuid
);


--
-- Name: cluster_tpl; Type: TABLE; Schema: crm; Owner: -
--

CREATE TABLE crm.cluster_tpl (
                                 id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                                 name text NOT NULL,
                                 created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                                 updated_at timestamp without time zone
);


--
-- Name: cluster_tpl_hierarchy; Type: TABLE; Schema: crm; Owner: -
--

CREATE TABLE crm.cluster_tpl_hierarchy (
                                           id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                                           cluster_tpl_type_id uuid NOT NULL,
                                           parent_id uuid
);


--
-- Name: cluster_tpl_type; Type: TABLE; Schema: crm; Owner: -
--

CREATE TABLE crm.cluster_tpl_type (
                                      id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                                      cluster_tpl_id uuid NOT NULL,
                                      name text NOT NULL,
                                      name_plural text NOT NULL,
                                      created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                                      updated_at timestamp without time zone
);


--
-- Name: form; Type: TABLE; Schema: crm; Owner: -
--

CREATE TABLE crm.form (
                          id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                          name public.citext NOT NULL,
                          description text,
                          created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                          updated_at timestamp without time zone
);


--
-- Name: form_entry; Type: TABLE; Schema: crm; Owner: -
--

CREATE TABLE crm.form_entry (
                                id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                                form_id uuid NOT NULL,
                                parent_id uuid,
                                order_nbr integer NOT NULL,
                                type integer NOT NULL,
                                name text NOT NULL,
                                title text NOT NULL,
                                format text,
                                description text,
                                created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                                updated_at timestamp without time zone
);


--
-- Name: message; Type: TABLE; Schema: crm; Owner: -
--

CREATE TABLE crm.message (
                             id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                             parent_id uuid,
                             sender_id uuid,
                             subject text NOT NULL,
                             content text,
                             is_rich boolean DEFAULT false NOT NULL,
                             is_hidden boolean DEFAULT false NOT NULL,
                             created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                             scheduled_at timestamp without time zone,
                             processed_at timestamp without time zone
);


--
-- Name: message_recipient; Type: TABLE; Schema: crm; Owner: -
--

CREATE TABLE crm.message_recipient (
                                       id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                                       message_id uuid NOT NULL,
                                       recipient_id uuid,
                                       recipient text NOT NULL,
                                       message_type integer DEFAULT 0 NOT NULL,
                                       status integer DEFAULT 0 NOT NULL,
                                       response text,
                                       created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                                       processed_at timestamp without time zone
);


--
-- Name: workspace; Type: TABLE; Schema: crm; Owner: -
--

CREATE TABLE crm.workspace (
                               id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                               parent_id uuid,
                               cluster_tpl_id uuid,
                               name public.citext NOT NULL,
                               created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                               updated_at timestamp without time zone,
                               founder_id uuid,
                               is_test_mode boolean DEFAULT false NOT NULL,
                               title public.citext NOT NULL
);


--
-- Name: workspace_data; Type: TABLE; Schema: crm; Owner: -
--

CREATE TABLE crm.workspace_data (
                                    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                                    workspace_id uuid NOT NULL,
                                    data_key public.citext NOT NULL,
                                    data_value text,
                                    created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                                    updated_at timestamp without time zone
);



--
-- Name: api_logs; Type: TABLE; Schema: telemetry; Owner: -
--

CREATE TABLE telemetry.api_logs (
                                    id integer NOT NULL,
                                    "timestamp" timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                                    endpoint text NOT NULL,
                                    headers text NOT NULL,
                                    request_body text,
                                    client_id uuid,
                                    account_id uuid,
                                    workspace_id uuid,
                                    session_id uuid,
                                    client_version text,
                                    http_method text,
                                    duration_ms double precision DEFAULT 0,
                                    created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL
);


--
-- Name: api_logs_id_seq; Type: SEQUENCE; Schema: telemetry; Owner: -
--

CREATE SEQUENCE telemetry.api_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: api_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: telemetry; Owner: -
--

ALTER SEQUENCE telemetry.api_logs_id_seq OWNED BY telemetry.api_logs.id;


--
-- Name: mv_avg_duration; Type: MATERIALIZED VIEW; Schema: telemetry; Owner: -
--

CREATE MATERIALIZED VIEW telemetry.mv_avg_duration AS
SELECT avg(duration_ms) AS avg_duration_ms,
       (created_at)::date AS day
FROM telemetry.api_logs
GROUP BY ((created_at)::date)
WITH NO DATA;


--
-- Name: mv_avg_requests_per_day; Type: MATERIALIZED VIEW; Schema: telemetry; Owner: -
--

CREATE MATERIALIZED VIEW telemetry.mv_avg_requests_per_day AS
SELECT ((count(*))::double precision / GREATEST(date_part('day'::text, (max(created_at) - min(created_at))), (1)::double precision)) AS avg_requests_per_day,
       (created_at)::date AS day
FROM telemetry.api_logs
GROUP BY ((created_at)::date)
WITH NO DATA;


--
-- Name: mv_avg_sessions_per_day; Type: MATERIALIZED VIEW; Schema: telemetry; Owner: -
--

CREATE MATERIALIZED VIEW telemetry.mv_avg_sessions_per_day AS
SELECT ((count(DISTINCT session_id))::double precision / NULLIF(date_part('day'::text, (max(created_at) - min(created_at))), (0)::double precision)) AS avg_sessions_per_day,
       (created_at)::date AS day
FROM telemetry.api_logs
GROUP BY ((created_at)::date)
WITH NO DATA;


--
-- Name: mv_batch_request_count; Type: MATERIALIZED VIEW; Schema: telemetry; Owner: -
--

CREATE MATERIALIZED VIEW telemetry.mv_batch_request_count AS
SELECT c.title AS workspace_name,
       count(*) AS batch_count,
       a.workspace_id,
       a.created_at
FROM (telemetry.api_logs a
    LEFT JOIN crm.workspace c ON ((a.workspace_id = c.id)))
WHERE (a.endpoint ~~ '%batch%'::text)
GROUP BY c.title, a.workspace_id, a.created_at
ORDER BY (count(*)) DESC
WITH NO DATA;


--
-- Name: mv_models_per_account_id; Type: MATERIALIZED VIEW; Schema: telemetry; Owner: -
--

CREATE MATERIALIZED VIEW telemetry.mv_models_per_account_id AS
WITH extracted AS (
    SELECT regexp_replace(regexp_replace(api_logs.endpoint, '^/cv|gen/'::text, ''::text), '^/'::text, ''::text) AS clean_endpoint,
           api_logs.client_id,
           api_logs.workspace_id,
           api_logs.account_id,
           api_logs.created_at
    FROM telemetry.api_logs
    WHERE ((api_logs.endpoint ~~ '/cv/%'::text) OR (api_logs.endpoint ~~ '/gen/%'::text))
)
SELECT c.title AS client_name,
       e.workspace_id,
       initcap(regexp_replace(regexp_replace(e.clean_endpoint, '([a-z])([A-Z])'::text, '\1 \2'::text, 'g'::text), '-'::text, ' '::text, 'g'::text)) AS endpoint,
       count(*) AS requestcount,
       e.created_at
FROM (extracted e
    LEFT JOIN crm.workspace c ON ((e.workspace_id = c.id)))
GROUP BY c.title, e.clean_endpoint, e.workspace_id, e.created_at
ORDER BY (count(*)) DESC
WITH NO DATA;


--
-- Name: mv_request_count_per_endpoint; Type: MATERIALIZED VIEW; Schema: telemetry; Owner: -
--

CREATE MATERIALIZED VIEW telemetry.mv_request_count_per_endpoint AS
WITH extracted AS (
    SELECT api_logs.workspace_id,
           api_logs.created_at,
           CASE
               WHEN ((api_logs.endpoint = '/_query'::text) AND (api_logs.request_body ~~ '%identifier%'::text)) THEN split_part(jsonb_extract_path_text((api_logs.request_body)::jsonb, VARIADIC ARRAY['_'::text, 'identifier'::text]), '.'::text, 2)
               ELSE api_logs.endpoint
               END AS formatted_endpoint
    FROM telemetry.api_logs
    WHERE ((api_logs.request_body !~~ '%dashboard.%'::text) AND (api_logs.endpoint !~~ '/gen/%'::text) AND (api_logs.endpoint !~~ '/cv/%'::text) AND (api_logs.endpoint !~ '^/_(?!query).*'::text) AND (api_logs.endpoint !~~ '%batch%'::text))
)
SELECT c.title AS workspace_name,
       e.workspace_id,
       e.created_at,
       initcap(regexp_replace(regexp_replace(replace(split_part(e.formatted_endpoint, '/'::text, array_length(string_to_array(e.formatted_endpoint, '/'::text), 1)), '-'::text, ' '::text), '([a-z])([A-Z])'::text, '\1 \2'::text, 'g'::text), '-'::text, ' '::text, 'g'::text)) AS endpoint,
       count(*) AS request_count
FROM (extracted e
    LEFT JOIN crm.workspace c ON ((e.workspace_id = c.id)))
WHERE (e.formatted_endpoint IS NOT NULL)
GROUP BY c.title, e.formatted_endpoint, e.workspace_id, e.created_at
ORDER BY (count(*)) DESC
WITH NO DATA;


--
-- Name: mv_total_requests_by_type; Type: MATERIALIZED VIEW; Schema: telemetry; Owner: -
--

CREATE MATERIALIZED VIEW telemetry.mv_total_requests_by_type AS
SELECT count(*) AS total_requests,
       CASE
           WHEN ((endpoint ~~ '/gen/%'::text) OR (endpoint ~~ '/cv/%'::text)) THEN 'AIRequests'::text
           WHEN (endpoint ~~ '%batch%'::text) THEN 'BatchRequests'::text
           ELSE 'APIRequests'::text
           END AS request_type,
       (created_at)::date AS day,
       workspace_id
FROM telemetry.api_logs
GROUP BY workspace_id,
         CASE
             WHEN ((endpoint ~~ '/gen/%'::text) OR (endpoint ~~ '/cv/%'::text)) THEN 'AIRequests'::text
             WHEN (endpoint ~~ '%batch%'::text) THEN 'BatchRequests'::text
             ELSE 'APIRequests'::text
             END, ((created_at)::date)
WITH NO DATA;


--
-- Name: image; Type: TABLE; Schema: widget; Owner: -
--

CREATE TABLE widget.image (
                              id text NOT NULL,
                              status integer DEFAULT 0 NOT NULL,
                              image_url text NOT NULL,
                              original_url text,
                              error_message text,
                              created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                              file_hash text,
                              mime_type text,
                              width integer,
                              height integer,
                              room_type text,
                              is_empty_room boolean
);


--
-- Name: listing; Type: TABLE; Schema: widget; Owner: -
--

CREATE TABLE widget.listing (
                                id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
                                created_at timestamp without time zone DEFAULT (now() AT TIME ZONE 'UTC'::text) NOT NULL,
                                image_id text NOT NULL,
                                workspace_id uuid NOT NULL,
                                listing_url text NOT NULL
);


--
-- Name: api_logs id; Type: DEFAULT; Schema: telemetry; Owner: -
--

ALTER TABLE ONLY telemetry.api_logs ALTER COLUMN id SET DEFAULT nextval('telemetry.api_logs_id_seq'::regclass);


--
-- Name: batch pk_batch; Type: CONSTRAINT; Schema: batching; Owner: -
--

ALTER TABLE ONLY batching.batch
    ADD CONSTRAINT pk_batch PRIMARY KEY (id);


--
-- Name: batch_item pk_batch_item; Type: CONSTRAINT; Schema: batching; Owner: -
--

ALTER TABLE ONLY batching.batch_item
    ADD CONSTRAINT pk_batch_item PRIMARY KEY (id);


--
-- Name: request_meta pk_request_meta; Type: CONSTRAINT; Schema: batching; Owner: -
--

ALTER TABLE ONLY batching.request_meta
    ADD CONSTRAINT pk_request_meta PRIMARY KEY (id);


--
-- Name: result pk_result; Type: CONSTRAINT; Schema: batching; Owner: -
--

ALTER TABLE ONLY batching.result
    ADD CONSTRAINT pk_result PRIMARY KEY (id);


--
-- Name: task pk_task; Type: CONSTRAINT; Schema: batching; Owner: -
--

ALTER TABLE ONLY batching.task
    ADD CONSTRAINT pk_task PRIMARY KEY (id);


--
-- Name: webhook_feedback pk_webhook_feedback; Type: CONSTRAINT; Schema: batching; Owner: -
--

ALTER TABLE ONLY batching.webhook_feedback
    ADD CONSTRAINT pk_webhook_feedback PRIMARY KEY (id);


--
-- Name: coupon pk_coupon; Type: CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.coupon
    ADD CONSTRAINT pk_coupon PRIMARY KEY (id);


--
-- Name: coupon_product_binding pk_coupon_product_binding; Type: CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.coupon_product_binding
    ADD CONSTRAINT pk_coupon_product_binding PRIMARY KEY (id);


--
-- Name: domain pk_domain; Type: CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.domain
    ADD CONSTRAINT pk_domain PRIMARY KEY (id);


--
-- Name: order pk_order; Type: CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core."order"
    ADD CONSTRAINT pk_order PRIMARY KEY (id);


--
-- Name: order_line pk_order_line; Type: CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.order_line
    ADD CONSTRAINT pk_order_line PRIMARY KEY (id);


--
-- Name: order_payment_history pk_order_payment_history; Type: CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.order_payment_history
    ADD CONSTRAINT pk_order_payment_history PRIMARY KEY (id);


--
-- Name: product pk_product; Type: CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.product
    ADD CONSTRAINT pk_product PRIMARY KEY (id);


--
-- Name: sale_channel pk_sale_channel; Type: CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.sale_channel
    ADD CONSTRAINT pk_sale_channel PRIMARY KEY (id);


--
-- Name: service pk_service; Type: CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.service
    ADD CONSTRAINT pk_service PRIMARY KEY (id);


--
-- Name: service_type pk_service_type; Type: CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.service_type
    ADD CONSTRAINT pk_service_type PRIMARY KEY (id);


--
-- Name: widget pk_widget; Type: CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.widget
    ADD CONSTRAINT pk_widget PRIMARY KEY (workspace_id);


--
-- Name: coupon_product_binding ux_coupon_product_binding; Type: CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.coupon_product_binding
    ADD CONSTRAINT ux_coupon_product_binding UNIQUE (coupon_id, product_id);


--
-- Name: order ux_order_order_nbr; Type: CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core."order"
    ADD CONSTRAINT ux_order_order_nbr UNIQUE (order_nbr);


--
-- Name: workspace_service_binding ux_workspace_service_binding; Type: CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.workspace_service_binding
    ADD CONSTRAINT ux_workspace_service_binding UNIQUE (workspace_id, service_id);


--
-- Name: account PK_account; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.account
    ADD CONSTRAINT "PK_account" PRIMARY KEY (id);


--
-- Name: account_email PK_account_email; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.account_email
    ADD CONSTRAINT "PK_account_email" PRIMARY KEY (id);


--
-- Name: account_id_lookup PK_account_id_lookup; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.account_id_lookup
    ADD CONSTRAINT "PK_account_id_lookup" PRIMARY KEY (old_id);


--
-- Name: account_phone PK_account_phone; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.account_phone
    ADD CONSTRAINT "PK_account_phone" PRIMARY KEY (id);


--
-- Name: account_secret PK_account_secret; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.account_secret
    ADD CONSTRAINT "PK_account_secret" PRIMARY KEY (id);


--
-- Name: client PK_client; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.client
    ADD CONSTRAINT "PK_client" PRIMARY KEY (id);


--
-- Name: client_secret PK_client_secret; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.client_secret
    ADD CONSTRAINT "PK_client_secret" PRIMARY KEY (id);


--
-- Name: cluster PK_cluster; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.cluster
    ADD CONSTRAINT "PK_cluster" PRIMARY KEY (id);


--
-- Name: cluster_tpl PK_cluster_tpl; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.cluster_tpl
    ADD CONSTRAINT "PK_cluster_tpl" PRIMARY KEY (id);


--
-- Name: cluster_tpl_hierarchy PK_cluster_tpl_hierarchy; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.cluster_tpl_hierarchy
    ADD CONSTRAINT "PK_cluster_tpl_hierarchy" PRIMARY KEY (id);


--
-- Name: cluster_tpl_type PK_cluster_tpl_type; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.cluster_tpl_type
    ADD CONSTRAINT "PK_cluster_tpl_type" PRIMARY KEY (id);


--
-- Name: workspace PK_workspace; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.workspace
    ADD CONSTRAINT "PK_workspace" PRIMARY KEY (id);


--
-- Name: account_data ix_account_data__account_id_data_key; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.account_data
    ADD CONSTRAINT ix_account_data__account_id_data_key UNIQUE (account_id, data_key);


--
-- Name: account_data pk_account_data; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.account_data
    ADD CONSTRAINT pk_account_data PRIMARY KEY (id);


--
-- Name: cluster_account_binding pk_cluster_account_binding; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.cluster_account_binding
    ADD CONSTRAINT pk_cluster_account_binding PRIMARY KEY (cluster_id, account_id);


--
-- Name: form pk_form; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.form
    ADD CONSTRAINT pk_form PRIMARY KEY (id);


--
-- Name: form_entry pk_form_entry; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.form_entry
    ADD CONSTRAINT pk_form_entry PRIMARY KEY (id);


--
-- Name: message pk_message; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.message
    ADD CONSTRAINT pk_message PRIMARY KEY (id);


--
-- Name: message_recipient pk_message_recipient; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.message_recipient
    ADD CONSTRAINT pk_message_recipient PRIMARY KEY (id);


--
-- Name: workspace_data pk_workspace_data; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.workspace_data
    ADD CONSTRAINT pk_workspace_data PRIMARY KEY (id);


--
-- Name: account_email ux_account_email__email; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.account_email
    ADD CONSTRAINT ux_account_email__email UNIQUE (email);


--
-- Name: account_phone ux_account_phone__number; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.account_phone
    ADD CONSTRAINT ux_account_phone__number UNIQUE (number);


--
-- Name: client ux_client_name; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.client
    ADD CONSTRAINT ux_client_name UNIQUE (workspace_id, name);


--
-- Name: client_secret ux_client_secret; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.client_secret
    ADD CONSTRAINT ux_client_secret UNIQUE (client_id, expired_at);


--
-- Name: cluster ux_cluster; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.cluster
    ADD CONSTRAINT ux_cluster UNIQUE (workspace_id, name);


--
-- Name: cluster_tpl_hierarchy ux_cluster_tpl_hierarchy; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.cluster_tpl_hierarchy
    ADD CONSTRAINT ux_cluster_tpl_hierarchy UNIQUE (cluster_tpl_type_id, parent_id);


--
-- Name: form_entry ux_form_entry; Type: CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.form_entry
    ADD CONSTRAINT ux_form_entry UNIQUE (form_id, parent_id, order_nbr);


--
-- Name: api_logs PK_api_logs; Type: CONSTRAINT; Schema: telemetry; Owner: -
--

ALTER TABLE ONLY telemetry.api_logs
    ADD CONSTRAINT "PK_api_logs" PRIMARY KEY (id);


--
-- Name: image pk_image; Type: CONSTRAINT; Schema: widget; Owner: -
--

ALTER TABLE ONLY widget.image
    ADD CONSTRAINT pk_image PRIMARY KEY (id);


--
-- Name: listing pk_request_meta; Type: CONSTRAINT; Schema: widget; Owner: -
--

ALTER TABLE ONLY widget.listing
    ADD CONSTRAINT pk_request_meta PRIMARY KEY (id);


--
-- Name: listing ux_listing; Type: CONSTRAINT; Schema: widget; Owner: -
--

ALTER TABLE ONLY widget.listing
    ADD CONSTRAINT ux_listing UNIQUE (image_id, listing_url, workspace_id);


--
-- Name: idx_batch_item_batch_id; Type: INDEX; Schema: batching; Owner: -
--

CREATE INDEX idx_batch_item_batch_id ON batching.batch_item USING btree (batch_id);


--
-- Name: idx_batch_item_status_0; Type: INDEX; Schema: batching; Owner: -
--

CREATE INDEX idx_batch_item_status_0 ON batching.batch_item USING btree (status) WHERE (status = 0);


--
-- Name: idx_batch_item_status_created_at; Type: INDEX; Schema: batching; Owner: -
--

CREATE INDEX idx_batch_item_status_created_at ON batching.batch_item USING btree (status, created_at);


--
-- Name: ix_batch_account_id; Type: INDEX; Schema: batching; Owner: -
--

CREATE INDEX ix_batch_account_id ON batching.batch USING btree (account_id);


--
-- Name: ix_batch_created_at; Type: INDEX; Schema: batching; Owner: -
--

CREATE INDEX ix_batch_created_at ON batching.batch USING btree (created_at);


--
-- Name: ix_batch_created_at_callback_completed; Type: INDEX; Schema: batching; Owner: -
--

CREATE INDEX ix_batch_created_at_callback_completed ON batching.batch USING btree (created_at, id) WHERE ((callback_url IS NOT NULL) AND (completed_at IS NOT NULL));


--
-- Name: ix_batch_item__batch_id; Type: INDEX; Schema: batching; Owner: -
--

CREATE INDEX ix_batch_item__batch_id ON batching.batch_item USING btree (batch_id);


--
-- Name: ix_batch_workspace_id; Type: INDEX; Schema: batching; Owner: -
--

CREATE INDEX ix_batch_workspace_id ON batching.batch USING btree (workspace_id);


--
-- Name: ix_result_task_id; Type: INDEX; Schema: batching; Owner: -
--

CREATE INDEX ix_result_task_id ON batching.result USING btree (task_id);


--
-- Name: ix_task_batch_id_created_at; Type: INDEX; Schema: batching; Owner: -
--

CREATE INDEX ix_task_batch_id_created_at ON batching.task USING btree (batch_id, created_at);


--
-- Name: ix_webhook_feedback_batch_id_sent_at; Type: INDEX; Schema: batching; Owner: -
--

CREATE INDEX ix_webhook_feedback_batch_id_sent_at ON batching.webhook_feedback USING btree (batch_id, sent_at);


--
-- Name: ux_coupon_code; Type: INDEX; Schema: core; Owner: -
--

CREATE UNIQUE INDEX ux_coupon_code ON core.coupon USING btree (code);


--
-- Name: IX_account_id_lookup_new_id; Type: INDEX; Schema: crm; Owner: -
--

CREATE UNIQUE INDEX "IX_account_id_lookup_new_id" ON crm.account_id_lookup USING btree (new_id);


--
-- Name: idx_api_logs_created_at; Type: INDEX; Schema: telemetry; Owner: -
--

CREATE INDEX idx_api_logs_created_at ON telemetry.api_logs USING btree (created_at);


--
-- Name: idx_api_logs_endpoint; Type: INDEX; Schema: telemetry; Owner: -
--

CREATE INDEX idx_api_logs_endpoint ON telemetry.api_logs USING btree (endpoint);


--
-- Name: idx_api_logs_timestamp; Type: INDEX; Schema: telemetry; Owner: -
--

CREATE INDEX idx_api_logs_timestamp ON telemetry.api_logs USING btree ("timestamp");


--
-- Name: order before_insert_order_nbr; Type: TRIGGER; Schema: core; Owner: -
--

CREATE TRIGGER before_insert_order_nbr BEFORE INSERT ON core."order" FOR EACH ROW EXECUTE FUNCTION core.set_order_nbr();


--
-- Name: batch_item fk_batch_item__batch; Type: FK CONSTRAINT; Schema: batching; Owner: -
--

ALTER TABLE ONLY batching.batch_item
    ADD CONSTRAINT fk_batch_item__batch FOREIGN KEY (batch_id) REFERENCES batching.batch(id) ON DELETE CASCADE;


--
-- Name: request_meta fk_request_meta__batch; Type: FK CONSTRAINT; Schema: batching; Owner: -
--

ALTER TABLE ONLY batching.request_meta
    ADD CONSTRAINT fk_request_meta__batch FOREIGN KEY (batch_id) REFERENCES batching.batch(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: result fk_result__task; Type: FK CONSTRAINT; Schema: batching; Owner: -
--

ALTER TABLE ONLY batching.result
    ADD CONSTRAINT fk_result__task FOREIGN KEY (task_id) REFERENCES batching.task(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: task fk_task__batch; Type: FK CONSTRAINT; Schema: batching; Owner: -
--

ALTER TABLE ONLY batching.task
    ADD CONSTRAINT fk_task__batch FOREIGN KEY (batch_id) REFERENCES batching.batch(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: webhook_feedback fk_webhook_feedback__batch; Type: FK CONSTRAINT; Schema: batching; Owner: -
--

ALTER TABLE ONLY batching.webhook_feedback
    ADD CONSTRAINT fk_webhook_feedback__batch FOREIGN KEY (batch_id) REFERENCES batching.batch(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: coupon fk_coupon__workspace; Type: FK CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.coupon
    ADD CONSTRAINT fk_coupon__workspace FOREIGN KEY (workspace_id) REFERENCES crm.workspace(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: coupon_product_binding fk_coupon_product_binding__coupon; Type: FK CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.coupon_product_binding
    ADD CONSTRAINT fk_coupon_product_binding__coupon FOREIGN KEY (coupon_id) REFERENCES core.coupon(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: coupon_product_binding fk_coupon_product_binding__product; Type: FK CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.coupon_product_binding
    ADD CONSTRAINT fk_coupon_product_binding__product FOREIGN KEY (product_id) REFERENCES core.product(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: domain fk_domain__workspace; Type: FK CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.domain
    ADD CONSTRAINT fk_domain__workspace FOREIGN KEY (workspace_id) REFERENCES crm.workspace(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: order fk_order__account; Type: FK CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core."order"
    ADD CONSTRAINT fk_order__account FOREIGN KEY (account_id) REFERENCES crm.account(id);


--
-- Name: order_line fk_order_line__order; Type: FK CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.order_line
    ADD CONSTRAINT fk_order_line__order FOREIGN KEY (order_id) REFERENCES core."order"(id) ON UPDATE CASCADE;


--
-- Name: order_line fk_order_line__product; Type: FK CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.order_line
    ADD CONSTRAINT fk_order_line__product FOREIGN KEY (product_id) REFERENCES core.product(id) ON UPDATE CASCADE;


--
-- Name: order_line fk_order_line__workspace; Type: FK CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.order_line
    ADD CONSTRAINT fk_order_line__workspace FOREIGN KEY (workspace_id) REFERENCES crm.workspace(id) ON UPDATE CASCADE;


--
-- Name: order_payment_history fk_order_payment_history__order; Type: FK CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.order_payment_history
    ADD CONSTRAINT fk_order_payment_history__order FOREIGN KEY (order_id) REFERENCES core."order"(id);


--
-- Name: product fk_product__workspace; Type: FK CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.product
    ADD CONSTRAINT fk_product__workspace FOREIGN KEY (workspace_id) REFERENCES crm.workspace(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: product_service_binding fk_product_service_binding__product; Type: FK CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.product_service_binding
    ADD CONSTRAINT fk_product_service_binding__product FOREIGN KEY (product_id) REFERENCES core.product(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: product_service_binding fk_product_service_binding__service; Type: FK CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.product_service_binding
    ADD CONSTRAINT fk_product_service_binding__service FOREIGN KEY (service_id) REFERENCES core.service(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: sale_channel fk_sale_channel__workspace; Type: FK CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.sale_channel
    ADD CONSTRAINT fk_sale_channel__workspace FOREIGN KEY (workspace_id) REFERENCES crm.workspace(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: sale_channel_product_binding fk_sale_channel_product_binding__product; Type: FK CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.sale_channel_product_binding
    ADD CONSTRAINT fk_sale_channel_product_binding__product FOREIGN KEY (product_id) REFERENCES core.product(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: sale_channel_product_binding fk_sale_channel_product_binding__sale_channel; Type: FK CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.sale_channel_product_binding
    ADD CONSTRAINT fk_sale_channel_product_binding__sale_channel FOREIGN KEY (sale_channel_id) REFERENCES core.sale_channel(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: service fk_service__service_type; Type: FK CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.service
    ADD CONSTRAINT fk_service__service_type FOREIGN KEY (service_type) REFERENCES core.service_type(id) ON UPDATE CASCADE;


--
-- Name: widget fk_widget__workspace; Type: FK CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.widget
    ADD CONSTRAINT fk_widget__workspace FOREIGN KEY (workspace_id) REFERENCES crm.workspace(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: workspace_service_binding fk_workspace_service_binding__service; Type: FK CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.workspace_service_binding
    ADD CONSTRAINT fk_workspace_service_binding__service FOREIGN KEY (service_id) REFERENCES core.service(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: workspace_service_binding fk_workspace_service_binding__workspace; Type: FK CONSTRAINT; Schema: core; Owner: -
--

ALTER TABLE ONLY core.workspace_service_binding
    ADD CONSTRAINT fk_workspace_service_binding__workspace FOREIGN KEY (workspace_id) REFERENCES crm.workspace(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: account_data fk_account_data__account; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.account_data
    ADD CONSTRAINT fk_account_data__account FOREIGN KEY (account_id) REFERENCES crm.account(id) ON DELETE CASCADE;


--
-- Name: account_email fk_account_email__account_id; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.account_email
    ADD CONSTRAINT fk_account_email__account_id FOREIGN KEY (account_id) REFERENCES crm.account(id) ON DELETE CASCADE;


--
-- Name: account_id_lookup fk_account_id_lookup__account; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.account_id_lookup
    ADD CONSTRAINT fk_account_id_lookup__account FOREIGN KEY (new_id) REFERENCES crm.account(id) ON DELETE CASCADE;


--
-- Name: account_phone fk_account_phone__account_id; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.account_phone
    ADD CONSTRAINT fk_account_phone__account_id FOREIGN KEY (account_id) REFERENCES crm.account(id) ON DELETE CASCADE;


--
-- Name: account_secret fk_account_secret__account_id; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.account_secret
    ADD CONSTRAINT fk_account_secret__account_id FOREIGN KEY (account_id) REFERENCES crm.account(id) ON DELETE CASCADE;


--
-- Name: client fk_client__workspace; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.client
    ADD CONSTRAINT fk_client__workspace FOREIGN KEY (workspace_id) REFERENCES crm.workspace(id) ON DELETE CASCADE;


--
-- Name: client_secret fk_client_secret__client; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.client_secret
    ADD CONSTRAINT fk_client_secret__client FOREIGN KEY (client_id) REFERENCES crm.client(id) ON DELETE CASCADE;


--
-- Name: cluster fk_cluster__cluster_id; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.cluster
    ADD CONSTRAINT fk_cluster__cluster_id FOREIGN KEY (parent_id) REFERENCES crm.cluster(id) ON DELETE CASCADE;


--
-- Name: cluster fk_cluster__cluster_tpl_type_id; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.cluster
    ADD CONSTRAINT fk_cluster__cluster_tpl_type_id FOREIGN KEY (cluster_type_id) REFERENCES crm.cluster_tpl_type(id) ON DELETE CASCADE;


--
-- Name: cluster fk_cluster__workspace_id; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.cluster
    ADD CONSTRAINT fk_cluster__workspace_id FOREIGN KEY (workspace_id) REFERENCES crm.workspace(id) ON DELETE CASCADE;


--
-- Name: cluster_account_binding fk_cluster_account_binding__account_id; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.cluster_account_binding
    ADD CONSTRAINT fk_cluster_account_binding__account_id FOREIGN KEY (account_id) REFERENCES crm.account(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: cluster_account_binding fk_cluster_account_binding__cluster_id; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.cluster_account_binding
    ADD CONSTRAINT fk_cluster_account_binding__cluster_id FOREIGN KEY (cluster_id) REFERENCES crm.cluster(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: cluster_tpl_hierarchy fk_cluster_tpl_hierarchy_cluster_tpl_type_id__cluster_tpl_type_; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.cluster_tpl_hierarchy
    ADD CONSTRAINT fk_cluster_tpl_hierarchy_cluster_tpl_type_id__cluster_tpl_type_ FOREIGN KEY (cluster_tpl_type_id) REFERENCES crm.cluster_tpl_type(id) ON DELETE CASCADE;


--
-- Name: cluster_tpl_hierarchy fk_cluster_tpl_hierarchy_parent_id__cluster_tpl_type_id; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.cluster_tpl_hierarchy
    ADD CONSTRAINT fk_cluster_tpl_hierarchy_parent_id__cluster_tpl_type_id FOREIGN KEY (parent_id) REFERENCES crm.cluster_tpl_type(id) ON DELETE CASCADE;


--
-- Name: cluster_tpl_type fk_cluster_tpl_type__cluster_tpl_id; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.cluster_tpl_type
    ADD CONSTRAINT fk_cluster_tpl_type__cluster_tpl_id FOREIGN KEY (cluster_tpl_id) REFERENCES crm.cluster_tpl(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: form_entry fk_form_entry__form_id; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.form_entry
    ADD CONSTRAINT fk_form_entry__form_id FOREIGN KEY (form_id) REFERENCES crm.form(id) ON DELETE CASCADE;


--
-- Name: form_entry fk_form_entry__parent_id; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.form_entry
    ADD CONSTRAINT fk_form_entry__parent_id FOREIGN KEY (parent_id) REFERENCES crm.form_entry(id);


--
-- Name: message fk_message__account; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.message
    ADD CONSTRAINT fk_message__account FOREIGN KEY (sender_id) REFERENCES crm.account(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: message fk_message__message; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.message
    ADD CONSTRAINT fk_message__message FOREIGN KEY (parent_id) REFERENCES crm.message(id) ON UPDATE SET NULL ON DELETE SET NULL;


--
-- Name: message_recipient fk_message_recipient__account; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.message_recipient
    ADD CONSTRAINT fk_message_recipient__account FOREIGN KEY (recipient_id) REFERENCES crm.account(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: message_recipient fk_message_recipient__message; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.message_recipient
    ADD CONSTRAINT fk_message_recipient__message FOREIGN KEY (message_id) REFERENCES crm.message(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: workspace fk_workspace__account; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.workspace
    ADD CONSTRAINT fk_workspace__account FOREIGN KEY (founder_id) REFERENCES crm.account(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: workspace fk_workspace__cluster_tpl; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.workspace
    ADD CONSTRAINT fk_workspace__cluster_tpl FOREIGN KEY (cluster_tpl_id) REFERENCES crm.cluster_tpl(id) ON UPDATE SET NULL ON DELETE SET NULL;


--
-- Name: workspace fk_workspace__workspace; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.workspace
    ADD CONSTRAINT fk_workspace__workspace FOREIGN KEY (parent_id) REFERENCES crm.workspace(id) ON UPDATE SET NULL ON DELETE SET NULL;


--
-- Name: workspace_data fk_workspace_data__workspace; Type: FK CONSTRAINT; Schema: crm; Owner: -
--

ALTER TABLE ONLY crm.workspace_data
    ADD CONSTRAINT fk_workspace_data__workspace FOREIGN KEY (workspace_id) REFERENCES crm.workspace(id) ON DELETE CASCADE;


--
-- Name: listing fk_listing__image; Type: FK CONSTRAINT; Schema: widget; Owner: -
--

ALTER TABLE ONLY widget.listing
    ADD CONSTRAINT fk_listing__image FOREIGN KEY (image_id) REFERENCES widget.image(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: listing fk_listing__workspace; Type: FK CONSTRAINT; Schema: widget; Owner: -
--

ALTER TABLE ONLY widget.listing
    ADD CONSTRAINT fk_listing__workspace FOREIGN KEY (workspace_id) REFERENCES crm.workspace(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--

