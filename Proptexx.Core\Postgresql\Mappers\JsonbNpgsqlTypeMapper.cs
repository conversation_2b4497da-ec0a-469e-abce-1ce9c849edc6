using System.Text.Json;
using Npgsql;
using NpgsqlTypes;

namespace Proptexx.Core.Postgresql.Mappers;

public abstract class JsonbRequiredNpgsqlTypeMapper<T> : NpgsqlTypeMapper<T>
{
    private readonly JsonSerializerOptions _jsonSerializerOptions;

    protected JsonbRequiredNpgsqlTypeMapper(JsonSerializerOptions jsonSerializerOptions)
    {
        _jsonSerializerOptions = jsonSerializerOptions;
    }

    public override T? Parse(object value)
    {
        if (value is not string str)
        {
            throw new InvalidOperationException($"Value is not a string");
        }

        var result = JsonSerializer.Deserialize<T>(str, _jsonSerializerOptions);
        return result;
    }

    protected override void SetValue(NpgsqlParameter parameter, T? value)
    {
        if (value is null) throw new NullReferenceException("Value is null");
        parameter.NpgsqlDbType = NpgsqlDbType.Jsonb;
        parameter.Value = JsonSerializer.Serialize(value, _jsonSerializerOptions);
    }
}

public abstract class JsonbNpgsqlTypeMapper<T> : NpgsqlTypeMapper<T> where T : class
{
    private readonly bool _allowNulls;
    private readonly JsonSerializerOptions _jsonSerializerOptions;

    protected JsonbNpgsqlTypeMapper(bool allowNulls, JsonSerializerOptions jsonSerializerOptions)
    {
        _allowNulls = allowNulls;
        _jsonSerializerOptions = jsonSerializerOptions;
    }

    public override T? Parse(object value)
    {
        if (value is not string str)
        {
            if (_allowNulls) return null;
            throw new InvalidOperationException($"Value is not a string");
        }

        var result = JsonSerializer.Deserialize<T>(str, _jsonSerializerOptions);
        if (result is null && !_allowNulls)
        {
            throw new InvalidOperationException("Failed to parse JSON. Result is null");
        }

        return result;
    }

    protected override void SetValue(NpgsqlParameter parameter, T? value)
    {
        if (value is null && !_allowNulls) throw new NullReferenceException("Value is null");
        parameter.NpgsqlDbType = NpgsqlDbType.Jsonb;
        parameter.Value = JsonSerializer.Serialize(value, _jsonSerializerOptions);
    }
}