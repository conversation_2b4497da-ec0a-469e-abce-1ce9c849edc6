<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <Authors>Proptexx</Authors>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <LangVersion>12</LangVersion>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>
    
    <ItemGroup>
        <ProjectReference Include="..\Proptexx.Web\Proptexx.Web.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

    <PropertyGroup>
        <PlaywrightPlatform>linux</PlaywrightPlatform>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.Playwright" Version="1.51.0" />
    </ItemGroup>

</Project>
