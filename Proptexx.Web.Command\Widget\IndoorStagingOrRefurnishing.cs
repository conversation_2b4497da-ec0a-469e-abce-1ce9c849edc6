using System.ComponentModel.DataAnnotations;
using System.Text;
using System.Text.Json;
using Proptexx.AI;
using Proptexx.AI.Models.Flow;
using Proptexx.Core;
using Proptexx.Core.AI;
using Proptexx.Core.Attributes;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Resilience;
using Proptexx.Core.Services;
using StackExchange.Redis;
using static Proptexx.Core.Constants.WidgetConstants;

namespace Proptexx.Web.Command.Widget;

[RequiredService(ServiceNames.EcommerceWidget, ServiceNames.WidgetAccess, ErrorMessage = "Access denied: Real estate widget or widget access service required")]
public sealed class IndoorStagingOrRefurnishing : ICommand
{
    private static Random _rnd = new();

    [Required]
    public required string ImageUrl { get; init; }

    [Required]
    public required string RoomType { get; init; }

    [Required]
    public required string ArchitectureStyle { get; init; }

    public string? Url { get; init; }

    public int? Seed { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        var logger = context.GetService<ILogger<IndoorStagingOrRefurnishing>>();

        try
        {
            var workspaceId = context.User.GetWorkspaceGuid();
            var redis = context.GetService<IConnectionMultiplexer>().GetDatabase();

            var cacheKey = EncodingService.UniqueKey(this.ImageUrl, this.RoomType, this.ArchitectureStyle, this.Seed ?? _rnd.Next(10000, 99999));
            var cachedImageUrl = await CheckCacheAsync(redis, cacheKey);
            if (!string.IsNullOrWhiteSpace(cachedImageUrl))
            {
                context.AddData("responseUrl", cachedImageUrl);
                return;
            }

            var env = context.GetService<IHostEnvironment>();
            IModel model;
            // if (env.EnvironmentName.Equals("Development", StringComparison.OrdinalIgnoreCase) ||
            //     env.EnvironmentName.Equals("Staging", StringComparison.OrdinalIgnoreCase))
            // {
            //     // Use the new flow
            //     model = context.Services.GetModel<VirtualStagingProductPlacement>();
            // }
            // else
            // {
                // Use the old flow
                model = context.Services.GetModel<VirtualStagingOrRefurnishing>();
            // }

            var payload = JsonSerializer.SerializeToDocument(new
            {
                this.ImageUrl,
                this.ArchitectureStyle,
                this.RoomType,
                this.Seed
            });

            var response = await PollyRetryExtensions.GetDefaultRetryPolicy(logger, $"IndoorStagingOrRefurnishing - IndoorStagingOrRefurnishing").ExecuteAsync(() =>
            {
                return model.InferAsync(new ModelContext
                {
                    Payload = payload,
                    ItemId = Guid.NewGuid().ToString(),
                    WorkspaceId = workspaceId.ToString(),
                    CancellationToken = context.CancellationToken
                });
            });

            if (response.Document is null)
            {
                throw new ApplicationException("Model returned empty result");
            }

            var imageUrl = PayloadService.GetRequiredString(response.Document, "imageUrl");
            context.AddData("responseUrl", imageUrl);

            var redisValue = JsonSerializer.Serialize(new GenerativeImageCacheItem
            {
                ImageUrl = imageUrl,
                CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            });

            await redis.HashSetAsync("widget_generative_cache", cacheKey, redisValue);
        }
        catch (ApplicationException e)
        {
            throw new CommandException(e.Message, e);
        }
        catch (Exception e)
        {
            logger.LogError(e, "Unable to generate output");
            throw new CommandException("Unable to generate output");
        }
    }

    private static async Task<string?> CheckCacheAsync(IDatabaseAsync redis, string key)
    {
        var redisValue = await redis.HashGetAsync("widget_generative_cache", key);

        if (!redisValue.HasValue) return null;

        var str = redisValue.ToString();
        var obj = JsonSerializer.Deserialize<GenerativeImageCacheItem>(str);
        return obj?.ImageUrl;
    }
}

public class GenerativeImageCacheItem
{
    public required string ImageUrl { get; init; }

    public long CreatedAt { get; init; }
}