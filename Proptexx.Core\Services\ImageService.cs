namespace Proptexx.Core.Services;

public class ImageAssessment
{
    public required string ImageUrl { get; set; }

    public required string Base64String { get; init; }

    public required string MimeType { get; init; }

    public required string FileHash { get; set; }

    public int Width { get; init; }

    public int Height { get; init; }
}

public class ImageService
{
    public static async Task<string> DownloadImageAsBase64Async(
        IHttpClientFactory httpClientFactory,
        string imageUrl,
        CancellationToken cancellationToken)
    {
        var httpClient = httpClientFactory.CreateClient();
        var imageBytes = await httpClient.GetByteArrayAsync(imageUrl, cancellationToken);
        return Convert.ToBase64String(imageBytes);
    }
}