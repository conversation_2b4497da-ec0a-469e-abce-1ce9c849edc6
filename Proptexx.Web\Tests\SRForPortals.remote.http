### Authenticate
POST https://auth.proptexx.com/_auth
Content-Type: application/json
Authorization: Api<PERSON>ey YjJhYTg0OTUtZGU2YS00OGQ2LWE4ZTQtMDQzODA0ZWFlNGE1

{}

> {% client.global.set("accessToken", response.body.$accessToken) %}

### Call Batch API for model flow/virtual-staging-or-refurnishing-for-portals, using the accessToken from previous request
POST https://api.proptexx.com/batch
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "items": [
    {
      "model": "flow/virtual-staging-or-refurnishing-for-portals",
      "config": {
        "image_url": "https://ansainteriors.com/wp-content/uploads/2022/03/interior-designer-in-delhi.jpg"
      }
    }
  ]
}

> {% client.global.set("batchId", response.body.id) %}


### Call Status API with the response id from previous call to get status of batch. Also using the accessToken from first request

#GET https://api.proptexx.com/status/{{batchId}}
#Content-Type: application/json
#Authorization: Bearer {{accessToken}}