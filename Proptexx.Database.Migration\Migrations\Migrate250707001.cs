using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations
{
    [Migration(250707001)]
    public class CreateSubscriptionTableAndMigrateData : FluentMigrator.Migration
    {
        public override void Up()
        {
            // Step 1: Add total_usage column to existing client_secret table
            Alter.Table("client_secret").InSchema("core")
                .AddColumn("total_usage").AsCustom("BIGINT").NotNullable().WithDefaultValue(0);

            // Create index for the new total_usage column
            Create.Index("ix_client_secret_total_usage")
                .OnTable("client_secret").InSchema("core")
                .OnColumn("total_usage");

            // Add check constraint for total_usage
            Execute.Sql(@"
                ALTER TABLE core.client_secret 
                ADD CONSTRAINT chk_client_secret_total_usage_positive 
                CHECK (total_usage >= 0);
            ");

            // Add comment for the new column
            Execute.Sql(@"
                COMMENT ON COLUMN core.client_secret.total_usage IS 'Total usage since creation';
            ");

            // Step 2: Create the unified subscription table with simplified structure matching the entity
            Create.Table("client_subscription").InSchema("core")
                // Primary Key
                .WithColumn("id").AsGuid().PrimaryKey().WithDefault(SystemMethods.NewGuid)
                
                // Client Information  
                .WithColumn("client_secret_id").AsGuid().NotNullable()
                    .ForeignKey("fk_client_subscription_client_secret_id", "core", "client_secret", "id")
                .WithColumn("is_demo").AsBoolean().NotNullable().WithDefaultValue(false)
                
                // Subscription Information
                .WithColumn("type").AsString(20).NotNullable() // "Period", "OneTime", "PayAsYouGo"
                
                // Unified Usage & Limits System
                .WithColumn("quota").AsCustom("BIGINT").NotNullable().WithDefaultValue(0) // Period quota for subscriptions
                .WithColumn("total_usage").AsCustom("BIGINT").NotNullable().WithDefaultValue(0) // Current period usage
                
                // Status & Lifecycle
                .WithColumn("status").AsString(20).NotNullable() // "Active", "Inactive", "Suspended", "Expired"
                
                // Audit fields
                .WithColumn("created_at").AsCustom("TIMESTAMP WITH TIME ZONE").NotNullable().WithDefault(SystemMethods.CurrentUTCDateTime)
                .WithColumn("updated_at").AsCustom("TIMESTAMP WITH TIME ZONE").Nullable()
                
                // Optional fields
                .WithColumn("notes").AsCustom("TEXT").Nullable()
                .WithColumn("plan_id").AsString(50).Nullable()
                .WithColumn("domain").AsString(255).Nullable()
                .WithColumn("allowed_paths").AsCustom("TEXT").Nullable()
                
                // Widget-specific fields (migrated from widget_clients table)
                .WithColumn("outseta_account_id").AsString(255).Nullable();

            // Step 2: Create indexes for better performance
            Create.Index("ix_client_subscription_client_secret_id")
                .OnTable("client_subscription").InSchema("core")
                .OnColumn("client_secret_id");
                
            Create.Index("ix_client_subscription_type")
                .OnTable("client_subscription").InSchema("core")
                .OnColumn("type");
                
            Create.Index("ix_client_subscription_status")
                .OnTable("client_subscription").InSchema("core")
                .OnColumn("status");
                
            Create.Index("ix_client_subscription_plan_id")
                .OnTable("client_subscription").InSchema("core")
                .OnColumn("plan_id");

            Create.Index("ix_client_subscription_outseta_account_id")
                .OnTable("client_subscription").InSchema("core")
                .OnColumn("outseta_account_id");

            Create.Index("ix_client_subscription_total_usage")
                .OnTable("client_subscription").InSchema("core")
                .OnColumn("total_usage");

            Create.Index("ix_client_subscription_quota")
                .OnTable("client_subscription").InSchema("core")
                .OnColumn("quota");

            // Step 3: Add check constraints
            Execute.Sql(@"
                ALTER TABLE core.client_subscription 
                ADD CONSTRAINT chk_client_subscription_type 
                CHECK (type IN ('Period', 'OneTime', 'PayAsYouGo'));
                
                ALTER TABLE core.client_subscription 
                ADD CONSTRAINT chk_client_subscription_status 
                CHECK (status IN ('Active', 'Inactive', 'Suspended', 'Expired'));
                
                -- Add constraint to ensure non-negative values for usage and quota
                ALTER TABLE core.client_subscription 
                ADD CONSTRAINT chk_client_subscription_usage_quota_positive 
                CHECK (quota >= 0 AND total_usage >= 0);
            ");

            // Step 4: Add comprehensive comments for documentation
            Execute.Sql(@"
                COMMENT ON TABLE core.client_subscription IS 'Unified table for managing all types of subscriptions: Real Estate Widgets, Ecommerce Widgets, Enterprise APIs, and Other Services';
                COMMENT ON COLUMN core.client_subscription.client_secret_id IS 'Foreign key to the client (tenant) this subscription belongs to';
                COMMENT ON COLUMN core.client_subscription.is_demo IS 'Indicates if this is a demo subscription';
                COMMENT ON COLUMN core.client_subscription.type IS 'Type of subscription: Period, OneTime, or PayAsYouGo';
                COMMENT ON COLUMN core.client_subscription.quota IS 'Period quota for subscriptions';
                COMMENT ON COLUMN core.client_subscription.total_usage IS 'Current period usage for subscriptions';
                COMMENT ON COLUMN core.client_subscription.status IS 'Status: Active, Inactive, Suspended, or Expired';
                COMMENT ON COLUMN core.client_subscription.created_at IS 'Date when the subscription was created (UTC timestamp)';
                COMMENT ON COLUMN core.client_subscription.updated_at IS 'Date when the subscription was last updated (UTC timestamp)';
                COMMENT ON COLUMN core.client_subscription.notes IS 'Optional notes for this subscription';
                COMMENT ON COLUMN core.client_subscription.plan_id IS 'Associated subscription plan ID';
                COMMENT ON COLUMN core.client_subscription.domain IS 'Allowed domain for widget subscriptions';
                COMMENT ON COLUMN core.client_subscription.allowed_paths IS 'Allowed paths for widget subscriptions';
                COMMENT ON COLUMN core.client_subscription.outseta_account_id IS 'Outseta account ID for ecommerce widget subscriptions';
            ");
            
            // Step 5: Create additional PostgreSQL-specific optimizations
            Execute.Sql(@"
                -- Enable row level security (RLS) for future use
                ALTER TABLE core.client_subscription ENABLE ROW LEVEL SECURITY;
                
                -- Create a function to automatically update updated_at timestamp
                CREATE OR REPLACE FUNCTION core.update_client_subscription_updated_at()
                RETURNS TRIGGER AS $$
                BEGIN
                    NEW.updated_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC';
                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;
                
                -- Create trigger to automatically update updated_at
                CREATE TRIGGER tr_client_subscription_updated_at
                    BEFORE UPDATE ON core.client_subscription
                    FOR EACH ROW
                    EXECUTE FUNCTION core.update_client_subscription_updated_at();
                
                -- Create function to increment usage for both client_secret and client_subscription (by client_secret_id)
                CREATE OR REPLACE FUNCTION core.increment_usage(
                    p_client_secret_id UUID
                )
                RETURNS VOID AS $$
                BEGIN
                    -- Increment lifetime usage in client_secret (for API key lifetime tracking)
                    UPDATE core.client_secret 
                    SET total_usage = total_usage + 1
                    WHERE id = p_client_secret_id;
                    
                    -- Increment current period usage in client_subscription (for current period tracking)
                    UPDATE core.client_subscription 
                    SET total_usage = total_usage + 1
                    WHERE client_secret_id = p_client_secret_id;
                    
                    -- Check if any rows were affected
                    IF NOT FOUND THEN
                        RAISE EXCEPTION 'Client secret or subscription not found';
                    END IF;
                END;
                $$ LANGUAGE plpgsql;
                
                -- Create function to increment usage for both client_secret and client_subscription (by api_key)
                CREATE OR REPLACE FUNCTION core.increment_usage_by_api_key(
                    p_api_key TEXT
                )
                RETURNS VOID AS $$
                DECLARE
                    v_client_secret_id UUID;
                BEGIN
                    -- Get client_secret_id from api_key using parse_api_key function
                    v_client_secret_id := core.parse_api_key(p_api_key);
                    
                    IF v_client_secret_id IS NULL THEN
                        RAISE EXCEPTION 'API key not found';
                    END IF;
                    
                    -- Call the main increment function
                    PERFORM core.increment_usage(v_client_secret_id);
                END;
                $$ LANGUAGE plpgsql;
                
                -- Create function for just client_secret increment (by client_secret_id)
                CREATE OR REPLACE FUNCTION core.increment_client_secret_usage(
                    p_client_secret_id UUID
                )
                RETURNS VOID AS $$
                BEGIN
                    -- Increment lifetime usage in client_secret only
                    UPDATE core.client_secret 
                    SET total_usage = total_usage + 1
                    WHERE id = p_client_secret_id;
                    
                    -- Check if any rows were affected
                    IF NOT FOUND THEN
                        RAISE EXCEPTION 'Client secret not found';
                    END IF;
                END;
                $$ LANGUAGE plpgsql;
                
                -- Create function for just client_secret increment (by api_key)
                CREATE OR REPLACE FUNCTION core.increment_client_secret_usage_by_api_key(
                    p_api_key TEXT
                )
                RETURNS VOID AS $$
                DECLARE
                    v_client_secret_id UUID;
                BEGIN
                    -- Get client_secret_id from api_key using parse_api_key function
                    v_client_secret_id := core.parse_api_key(p_api_key);
                    
                    IF v_client_secret_id IS NULL THEN
                        RAISE EXCEPTION 'API key not found';
                    END IF;
                    
                    -- Call the main increment function
                    PERFORM core.increment_client_secret_usage(v_client_secret_id);
                END;
                $$ LANGUAGE plpgsql;
                
                -- Create function for just subscription increment (by client_secret_id)
                CREATE OR REPLACE FUNCTION core.increment_subscription_usage(
                    p_client_secret_id UUID
                )
                RETURNS VOID AS $$
                BEGIN
                    -- Increment current period usage in client_subscription only
                    UPDATE core.client_subscription 
                    SET total_usage = total_usage + 1
                    WHERE client_secret_id = p_client_secret_id;
                    
                    -- Check if any rows were affected
                    IF NOT FOUND THEN
                        RAISE EXCEPTION 'Subscription not found';
                    END IF;
                END;
                $$ LANGUAGE plpgsql;
                
                -- Create function for just subscription increment (by api_key)
                CREATE OR REPLACE FUNCTION core.increment_subscription_usage_by_api_key(
                    p_api_key TEXT
                )
                RETURNS VOID AS $$
                DECLARE
                    v_client_secret_id UUID;
                BEGIN
                    -- Get client_secret_id from api_key using parse_api_key function
                    v_client_secret_id := core.parse_api_key(p_api_key);
                    
                    IF v_client_secret_id IS NULL THEN
                        RAISE EXCEPTION 'API key not found';
                    END IF;
                    
                    -- Call the main increment function
                    PERFORM core.increment_subscription_usage(v_client_secret_id);
                END;
                $$ LANGUAGE plpgsql;
                
                -- Create view for total usage by client_id
                CREATE OR REPLACE VIEW core.client_usage_summary AS
                SELECT 
                    cs.client_id,
                    COUNT(cs.id) as total_secrets,
                    SUM(cs.total_usage) as total_usage
                FROM core.client_secret cs
                GROUP BY cs.client_id;
                
                -- Create function to get total usage by client_id
                CREATE OR REPLACE FUNCTION core.get_client_total_usage(
                    p_client_id UUID
                )
                RETURNS TABLE(
                    client_id UUID,
                    total_secrets BIGINT,
                    total_usage BIGINT
                ) AS $$
                BEGIN
                    RETURN QUERY
                    SELECT 
                        v.client_id,
                        v.total_secrets,
                        v.total_usage
                    FROM core.client_usage_summary v
                    WHERE v.client_id = p_client_id;
                END;
                $$ LANGUAGE plpgsql;
                
                -- Create function to get total usage by client_id (returns JSON)
                CREATE OR REPLACE FUNCTION core.get_client_total_usage_json(
                    p_client_id UUID
                )
                RETURNS JSON AS $$
                DECLARE
                    result JSON;
                BEGIN
                    SELECT json_build_object(
                        'client_id', v.client_id,
                        'total_secrets', v.total_secrets,
                        'total_usage', v.total_usage
                    ) INTO result
                    FROM core.client_usage_summary v
                    WHERE v.client_id = p_client_id;
                    
                    RETURN COALESCE(result, '{}'::JSON);
                END;
                $$ LANGUAGE plpgsql;
                
                -- Create function to get all clients usage summary
                CREATE OR REPLACE FUNCTION core.get_all_clients_usage()
                RETURNS TABLE(
                    client_id UUID,
                    total_secrets BIGINT,
                    total_usage BIGINT
                ) AS $$
                BEGIN
                    RETURN QUERY
                    SELECT 
                        v.client_id,
                        v.total_secrets,
                        v.total_usage
                    FROM core.client_usage_summary v
                    ORDER BY v.total_usage DESC;
                END;
                $$ LANGUAGE plpgsql;
                
                -- Create function to get top clients by usage
                CREATE OR REPLACE FUNCTION core.get_top_clients_by_usage(
                    p_limit INTEGER DEFAULT 10
                )
                RETURNS TABLE(
                    client_id UUID,
                    total_secrets BIGINT,
                    total_usage BIGINT
                ) AS $$
                BEGIN
                    RETURN QUERY
                    SELECT 
                        v.client_id,
                        v.total_secrets,
                        v.total_usage
                    FROM core.client_usage_summary v
                    ORDER BY v.total_usage DESC
                    LIMIT p_limit;
                END;
                $$ LANGUAGE plpgsql;
                
                -- Create helper function to generate API key from client_secret_id
                CREATE OR REPLACE FUNCTION core.get_api_key_from_client_secret_id(
                    p_client_secret_id UUID
                )
                RETURNS TEXT AS $$
                DECLARE
                    v_api_key TEXT;
                BEGIN
                    -- Generate API key using generate_api_key function
                    SELECT core.generate_api_key(p_client_secret_id) INTO v_api_key;
                    
                    IF v_api_key IS NULL THEN
                        RAISE EXCEPTION 'Unable to generate API key for client_secret_id: %', p_client_secret_id;
                    END IF;
                    
                    RETURN v_api_key;
                END;
                $$ LANGUAGE plpgsql;
                
                -- Create helper function to get client_secret_id from API key
                CREATE OR REPLACE FUNCTION core.get_client_secret_id_from_api_key(
                    p_api_key TEXT
                )
                RETURNS UUID AS $$
                DECLARE
                    v_client_secret_id UUID;
                BEGIN
                    -- Parse API key to get client_secret_id
                    v_client_secret_id := core.parse_api_key(p_api_key);
                    
                    IF v_client_secret_id IS NULL THEN
                        RAISE EXCEPTION 'Unable to parse API key: %', p_api_key;
                    END IF;
                    
                    RETURN v_client_secret_id;
                END;
                $$ LANGUAGE plpgsql;
            ");
        }

        public override void Down()
        {
            // Step 1: Drop all indexes from client_subscription table
            Delete.Index("ix_client_subscription_quota").OnTable("client_subscription").InSchema("core");
            Delete.Index("ix_client_subscription_total_usage").OnTable("client_subscription").InSchema("core");
            Delete.Index("ix_client_subscription_outseta_account_id").OnTable("client_subscription").InSchema("core");
            Delete.Index("ix_client_subscription_plan_id").OnTable("client_subscription").InSchema("core");
            Delete.Index("ix_client_subscription_status").OnTable("client_subscription").InSchema("core");
            Delete.Index("ix_client_subscription_type").OnTable("client_subscription").InSchema("core");
            Delete.Index("ix_client_subscription_client_secret_id").OnTable("client_subscription").InSchema("core");
            
            // Step 2: Drop index from client_secret table
            Delete.Index("ix_client_secret_total_usage").OnTable("client_secret").InSchema("core");
            
            // Step 3: Drop triggers and functions
            Execute.Sql(@"
                DROP TRIGGER IF EXISTS tr_client_subscription_updated_at ON core.client_subscription;
                DROP FUNCTION IF EXISTS core.update_client_subscription_updated_at();
                DROP FUNCTION IF EXISTS core.increment_usage(UUID);
                DROP FUNCTION IF EXISTS core.increment_usage_by_api_key(TEXT);
                DROP FUNCTION IF EXISTS core.increment_client_secret_usage(UUID);
                DROP FUNCTION IF EXISTS core.increment_client_secret_usage_by_api_key(TEXT);
                DROP FUNCTION IF EXISTS core.increment_subscription_usage(UUID);
                DROP FUNCTION IF EXISTS core.increment_subscription_usage_by_api_key(TEXT);
                DROP FUNCTION IF EXISTS core.get_client_total_usage(UUID);
                DROP FUNCTION IF EXISTS core.get_client_total_usage_json(UUID);
                DROP FUNCTION IF EXISTS core.get_all_clients_usage();
                DROP FUNCTION IF EXISTS core.get_top_clients_by_usage(INTEGER);
                DROP FUNCTION IF EXISTS core.get_api_key_from_client_secret_id(UUID);
                DROP FUNCTION IF EXISTS core.get_client_secret_id_from_api_key(TEXT);
                DROP VIEW IF EXISTS core.client_usage_summary;
            ");
            
            // Step 4: Drop check constraints
            Execute.Sql(@"
                ALTER TABLE core.client_subscription 
                DROP CONSTRAINT IF EXISTS chk_client_subscription_type;
                
                ALTER TABLE core.client_subscription 
                DROP CONSTRAINT IF EXISTS chk_client_subscription_status;
                
                ALTER TABLE core.client_subscription 
                DROP CONSTRAINT IF EXISTS chk_client_subscription_usage_quota_positive;
                
                ALTER TABLE core.client_secret 
                DROP CONSTRAINT IF EXISTS chk_client_secret_total_usage_positive;
            ");
            
            // Step 5: Drop the client_subscription table and remove total_usage column from client_secret
            Delete.Table("client_subscription").InSchema("core");
            
            // Remove total_usage column from client_secret table using raw SQL
            Execute.Sql(@"
                ALTER TABLE core.client_secret 
                DROP COLUMN IF EXISTS total_usage;
            ");
        }
    }
} 