using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations
{
    [Migration(250327001)]
    public class Migrate250327001 : FluentMigrator.Migration
    {
        public override void Up()
        {
            // 1. Create materialized view for Today's Data (from midnight UTC until now)
            Execute.Sql(@"
                CREATE MATERIALIZED VIEW IF NOT EXISTS telemetry.mv_api_requests_today
                TABLESPACE pg_default
                AS
                SELECT 
                    workspace_id,
                    processed_endpoint,
                    created_at::date AS day,
                    COUNT(*) AS request_count
                FROM telemetry.api_logs
                WHERE 
                    is_api_request
                    AND processed_endpoint IS NOT NULL
                    AND created_at >= CURRENT_DATE  -- today's data (midnight to now)
                GROUP BY workspace_id, processed_endpoint, created_at::date
                WITH NO DATA;
            ");

            // 2. Create materialized view for Past Data (before today)
            Execute.Sql(@"
                CREATE MATERIALIZED VIEW IF NOT EXISTS telemetry.mv_api_requests_past
                TAB<PERSON><PERSON><PERSON><PERSON> pg_default
                AS
                SELECT 
                    workspace_id,
                    processed_endpoint,
                    created_at::date AS day,
                    COUNT(*) AS request_count
                FROM telemetry.api_logs
                WHERE 
                    is_api_request
                    AND processed_endpoint IS NOT NULL
                    AND created_at < CURRENT_DATE  -- data before today
                GROUP BY workspace_id, processed_endpoint, created_at::date
                WITH NO DATA;
            ");

            // 3. Create Combined materialized view (union of today's and past data)
            Execute.Sql(@"
                CREATE MATERIALIZED VIEW IF NOT EXISTS telemetry.mv_api_requests_summary
                TABLESPACE pg_default
                AS
                SELECT 
                    workspace_id,
                    processed_endpoint,
                    day,
                    SUM(request_count) AS request_count
                FROM (
                    SELECT * FROM telemetry.mv_api_requests_past
                    UNION ALL
                    SELECT * FROM telemetry.mv_api_requests_today
                ) AS combined
                GROUP BY workspace_id, processed_endpoint, day
                WITH NO DATA;
            ");

            // Create indexes for mv_api_requests_today
            Execute.Sql(@"
                CREATE INDEX IF NOT EXISTS idx_mv_api_requests_today_day 
                    ON telemetry.mv_api_requests_today (day);
                CREATE INDEX IF NOT EXISTS idx_mv_api_requests_today_ws_endpoint 
                    ON telemetry.mv_api_requests_today (workspace_id, processed_endpoint);
            ");

            // Create indexes for mv_api_requests_past
            Execute.Sql(@"
                CREATE INDEX IF NOT EXISTS idx_mv_api_requests_past_day 
                    ON telemetry.mv_api_requests_past (day);
                CREATE INDEX IF NOT EXISTS idx_mv_api_requests_past_ws_endpoint 
                    ON telemetry.mv_api_requests_past (workspace_id, processed_endpoint);
            ");

            // Create indexes for mv_api_requests_summary
            Execute.Sql(@"
                CREATE INDEX IF NOT EXISTS idx_mv_api_requests_summary_day 
                    ON telemetry.mv_api_requests_summary (day);
                CREATE INDEX IF NOT EXISTS idx_mv_api_requests_summary_ws_endpoint 
                    ON telemetry.mv_api_requests_summary (workspace_id, processed_endpoint);
            ");

            // 1. Create materialized view for Today's AI Requests (from midnight UTC until now)
            Execute.Sql(@"
                CREATE MATERIALIZED VIEW IF NOT EXISTS telemetry.mv_ai_requests_today
                TABLESPACE pg_default
                AS
                SELECT 
                    workspace_id,
                    processed_endpoint,
                    created_at::date AS day,
                    COUNT(*) AS request_count
                FROM telemetry.api_logs
                WHERE 
                    is_ai_request
                    AND processed_endpoint IS NOT NULL
                    AND created_at >= CURRENT_DATE
                GROUP BY workspace_id, processed_endpoint, created_at::date
                WITH NO DATA;
            ");

            // 2. Create materialized view for Past AI Requests (data before today)
            Execute.Sql(@"
                CREATE MATERIALIZED VIEW IF NOT EXISTS telemetry.mv_ai_requests_past
                TABLESPACE pg_default
                AS
                SELECT 
                    workspace_id,
                    processed_endpoint,
                    created_at::date AS day,
                    COUNT(*) AS request_count
                FROM telemetry.api_logs
                WHERE 
                    is_ai_request
                    AND processed_endpoint IS NOT NULL
                    AND created_at < CURRENT_DATE
                GROUP BY workspace_id, processed_endpoint, created_at::date
                WITH NO DATA;
            ");

            // 3. Create the combined materialized view by unioning both views
            Execute.Sql(@"
                CREATE MATERIALIZED VIEW IF NOT EXISTS telemetry.mv_ai_requests_summary
                TABLESPACE pg_default
                AS
                SELECT 
                    workspace_id,
                    processed_endpoint,
                    day,
                    SUM(request_count) AS request_count
                FROM (
                    SELECT * FROM telemetry.mv_ai_requests_past
                    UNION ALL
                    SELECT * FROM telemetry.mv_ai_requests_today
                ) AS combined
                GROUP BY workspace_id, processed_endpoint, day
                WITH NO DATA;
            ");

            // Create indexes for the Today view
            Execute.Sql(@"
                CREATE INDEX IF NOT EXISTS idx_mv_ai_requests_today_day 
                    ON telemetry.mv_ai_requests_today (day);
                CREATE INDEX IF NOT EXISTS idx_mv_ai_requests_today_ws_endpoint 
                    ON telemetry.mv_ai_requests_today (workspace_id, processed_endpoint);
            ");

            // Create indexes for the Past view
            Execute.Sql(@"
                CREATE INDEX IF NOT EXISTS idx_mv_ai_requests_past_day 
                    ON telemetry.mv_ai_requests_past (day);
                CREATE INDEX IF NOT EXISTS idx_mv_ai_requests_past_ws_endpoint 
                    ON telemetry.mv_ai_requests_past (workspace_id, processed_endpoint);
            ");

            // Create indexes for the Combined view
            Execute.Sql(@"
                CREATE INDEX IF NOT EXISTS idx_mv_ai_requests_summary_day 
                    ON telemetry.mv_ai_requests_summary (day);
                CREATE INDEX IF NOT EXISTS idx_mv_ai_requests_summary_ws_endpoint 
                    ON telemetry.mv_ai_requests_summary (workspace_id, processed_endpoint);
            ");

            // 1. Create materialized view for Batch Requests Today (from midnight UTC until now)
            Execute.Sql(@"
                CREATE MATERIALIZED VIEW IF NOT EXISTS telemetry.mv_batch_requests_today
                TABLESPACE pg_default
                AS
                SELECT 
                    workspace_id,
                    created_at::date AS day,
                    COUNT(*) AS batch_count
                FROM telemetry.api_logs
                WHERE 
                    is_batch
                    AND created_at >= CURRENT_DATE  -- Data from today (midnight to now)
                GROUP BY workspace_id, created_at::date
                WITH NO DATA;
            ");

            // Create indexes for today's view
            Execute.Sql(@"
                CREATE INDEX IF NOT EXISTS idx_mv_batch_today_day 
                    ON telemetry.mv_batch_requests_today (day);
                CREATE INDEX IF NOT EXISTS idx_mv_batch_today_ws 
                    ON telemetry.mv_batch_requests_today (workspace_id);
            ");

            // 2. Create materialized view for Batch Requests Past (before today)
            Execute.Sql(@"
                CREATE MATERIALIZED VIEW IF NOT EXISTS telemetry.mv_batch_requests_past
                TABLESPACE pg_default
                AS
                SELECT 
                    workspace_id,
                    created_at::date AS day,
                    COUNT(*) AS batch_count
                FROM telemetry.api_logs
                WHERE 
                    is_batch
                    AND created_at < CURRENT_DATE  -- Data before today
                GROUP BY workspace_id, created_at::date
                WITH NO DATA;
            ");

            // Create indexes for past view
            Execute.Sql(@"
                CREATE INDEX IF NOT EXISTS idx_mv_batch_past_day 
                    ON telemetry.mv_batch_requests_past (day);
                CREATE INDEX IF NOT EXISTS idx_mv_batch_past_ws 
                    ON telemetry.mv_batch_requests_past (workspace_id);
            ");

            // 3. Create combined materialized view for Batch Requests
            Execute.Sql(@"
                CREATE MATERIALIZED VIEW IF NOT EXISTS telemetry.mv_batch_requests_summary
                TABLESPACE pg_default
                AS
                SELECT 
                    workspace_id,
                    day,
                    SUM(batch_count) AS batch_count
                FROM (
                    SELECT * FROM telemetry.mv_batch_requests_past
                    UNION ALL
                    SELECT * FROM telemetry.mv_batch_requests_today
                ) AS combined
                GROUP BY workspace_id, day
                WITH NO DATA;
            ");

            // Create indexes for combined view
            Execute.Sql(@"
                CREATE INDEX IF NOT EXISTS idx_mv_batch_summary_day 
                    ON telemetry.mv_batch_requests_summary (day);
                CREATE INDEX IF NOT EXISTS idx_mv_batch_summary_ws 
                    ON telemetry.mv_batch_requests_summary (workspace_id);
            ");
        }

        public override void Down()
        {
            Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_summary;");
            Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_past;");
            Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_api_requests_today;"); 
            Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_ai_requests_summary;");
            Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_ai_requests_past;");
            Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_ai_requests_today;");
            Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_batch_requests_summary;");
            Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_batch_requests_past;");
            Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_batch_requests_today;");
        }
    }
}
