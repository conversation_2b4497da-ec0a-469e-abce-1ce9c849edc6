using Dapper;
using Npgsql;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Entities;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Stores;
using Proptexx.Core.Utils;

namespace Proptexx.Core.Services;

public static class OrderManager
{
    public static async Task<Guid> CreateOrderAsync(
        NpgsqlConnection connection, Guid accountId, Guid workspaceId, Guid productId)
    {
        var product = await connection.Product().GetProduct(productId);
        if (product is null) throw new CommandException("Product not found");
        var orderId = await CreateOrderAsync(connection, accountId, workspaceId, product.Id, product.PriceAmount, product.Currency);
        return orderId;
    }

    public static async Task<Guid> CreateOrderAsync(NpgsqlConnection connection, Guid accountId, Guid workspaceId, Guid productId, decimal orderPrice, string currency)
    {
        const string sql = "select * from core.account a where a.id = :_account_id";
        var account = await connection.QueryFirstAsync<Account>(sql, new { _account_id = accountId });

        var order = new Order
        {
            AccountId = account.Id,
            ReferencePerson = account.FullName(),
            TotalPrice = orderPrice,
            Currency = currency
        };

        var orderLine = new OrderLine
        {
            OrderId = order.Id,
            ProductId = productId,
            WorkspaceId = workspaceId
        };

        await connection.InsertAsync(order);
        await connection.InsertAsync(orderLine);
        return order.Id;
    }

    public static async Task CreateOrderAsync(NpgsqlConnection connection, Guid newOrderId, Guid accountId, string referencePerson, Guid workspaceId, string? paymentLink, IEnumerable<Guid> productIds)
    {
        decimal totalPrice = 0;
        string? currency = null;
        
        List<OrderLine> orderLines = [];
        foreach (var productId in productIds)
        {
            var product = await connection.QueryFirstAsync<Product>("select * from core.product p where p.id = :_product_id", new { _product_id = productId });

            if (currency is not null && !product.Currency.Equals(currency))
            {
                throw new ApplicationException("Not possible to mix products of different currencies");
            }
            
            currency = product.Currency;
            totalPrice += product.PriceAmount;

            orderLines.Add(new OrderLine
            {
                OrderId = newOrderId,
                ProductId = product.Id,
                WorkspaceId = workspaceId,
            });
        }

        if (currency is null) throw new NullReferenceException(nameof(currency));

        var order = new Order
        {
            Id = newOrderId,
            AccountId = accountId,
            ReferencePerson = referencePerson,
            TotalPrice = totalPrice,
            Currency = currency,
            PaymentLink = paymentLink
        };
        
        await connection.InsertAsync(order);
        await connection.InsertAsync(orderLines);
    }

    public static async Task<OrderModel> GetOrderAsync(NpgsqlConnection npgsql, Guid orderId)
    {
        // TODO: For now it will work to just output the firstOrDefault to get workspace_id,
        // but this needs to be fixed to support an order with muliple workspaces (it thats needed later) 
        var order = await npgsql.QueryFirstOrDefaultAsync<OrderModel>(OrderModel.Sql, new { _order_id = orderId })
                    ?? throw new NullReferenceException("Order not found");

        return order;
    }

    public static async Task RecordPaymentAsync(NpgsqlConnection npgsql, Guid orderId, decimal amount, string? paymentChannel, string? paymentReference, CancellationToken contextRequestAborted)
    {
        var orderPayment = new OrderPaymentHistory
        {
            OrderId = orderId,
            Amount = amount,
            PaymentChannel = paymentChannel,
            PaymentRef = paymentReference
        };

        await npgsql.InsertAsync(orderPayment);
    }

    public static async Task MarkAsPaidAsync(NpgsqlConnection connection, Guid orderId)
    {
        var order = await connection.Order().GetOrder(orderId);
        order.PaidAt = DateTime.UtcNow;
        await connection.UpdateAsync(order, "PaidAt");
    }

    public static async Task EnableServicesAsync(
        NpgsqlConnection npgsql, Guid orderId, CancellationToken cancellationToken)
    {
        var order = await GetOrderAsync(npgsql, orderId);
        await EnableServicesAsync(npgsql, order.WorkspaceId, order.Id, cancellationToken);
    }

    public static async Task EnableServicesAsync(
        NpgsqlConnection npgsql, Guid workspaceId, Guid orderId, CancellationToken cancellationToken)
    {
        var existingServices = await npgsql.ListQueryAsync<WorkspaceServiceBinding>(
            "select wsb.* from core.workspace_service_binding wsb where wsb.workspace_id = :_workspace_id", 
            new { _workspace_id = workspaceId });
        
        IEnumerable<IRequestedService> requestedServices = await npgsql
            .QueryAsync<ServiceModel>(ServiceModel.Sql, new { _order_id = orderId });
        await EnableServicesAsync(npgsql, existingServices, requestedServices.ToArray());
    }

    public static async Task EnableServicesAsync(NpgsqlConnection npgsql, Guid workspaceId, IEnumerable<IRequestedService> requestedServices)
    {
        var existingServices = await npgsql.ListQueryAsync<WorkspaceServiceBinding>(
            "select wsb.* from core.workspace_service_binding wsb where wsb.workspace_id = :_workspace_id", 
            new { _workspace_id = workspaceId });

        await EnableServicesAsync(npgsql, existingServices, requestedServices);
    }

    public static async Task EnableServicesAsync(
        NpgsqlConnection npgsql, IList<WorkspaceServiceBinding> existingServices, IEnumerable<IRequestedService> requestedServices)
    {
        List<WorkspaceServiceBinding> records = [];
        foreach (var serviceModel in requestedServices)
        {
            var record = existingServices.FirstOrDefault(x =>
                x.ServiceId.Equals(serviceModel.ServiceId) &&
                x.WorkspaceId.Equals(serviceModel.WorkspaceId)
            ) ?? new WorkspaceServiceBinding
            {
                WorkspaceId = serviceModel.WorkspaceId,
                ServiceId = serviceModel.ServiceId,
                ExpiresAt = serviceModel.ExpiresAt
            };
            var expiresAt = serviceModel.ExpiresAt ?? DateTime.UtcNow.AddMonths(1);

            if (serviceModel.ServiceId.Equals("real-estate-widget"))
            {
                await AccessKeyHelper.CreateWidgetKey(
                    npgsql,
                    serviceModel.WorkspaceId,
                    expiresAt,
                    "real-estate-widget",
                    "System generated real-estate widget key",
                    "widget login otp"
                );
            }
            else if (serviceModel.ServiceId.Equals("ecommerce-widget"))
            {
                 await AccessKeyHelper.CreateWidgetKey(
                    npgsql,
                    serviceModel.WorkspaceId,
                    expiresAt,
                    "ecommerce-widget",
                    "System generated ecommerce widget key",
                    "ecommerce widget login otp"
                );
            }
            else if (serviceModel.ServiceId.Equals("widget-access"))
            {
                await AccessKeyHelper.CreateWidgetKey(
                    npgsql,
                    serviceModel.WorkspaceId,
                    null,
                    "widget",
                    "System generated widget key",
                    "widget login otp"
                );
            }

            record.ExpiresAt = (record.ExpiresAt ?? DateTime.UtcNow).AddMonths(1);
            records.Add(record);
        }

        var onConflict = new UpsertOnConflictHandler(ConflictType.Constraint, "ux_workspace_service_binding", ["expires_at"]);
        var inserts = await npgsql.InsertAsync(records, onConflict);
    }

    public sealed class ServiceModel : IRequestedService
    {
        public Guid WorkspaceId { get; init; }
        
        public Guid ProductId { get; init; }
        
        public required string ServiceId { get; init; }
        
        public DateTime? ExpiresAt { get; }

        public static string Sql => @"
            select ol.workspace_id,
                   ol.product_id,
                   psb.service_id
            from core.order_line ol
            join core.order o on ol.order_id = o.id
            join core.product p on ol.product_id = p.id
            join core.product_service_binding psb on p.id = psb.product_id
            where ol.order_id = :_order_id and o.paid_at is not null;
        ";
    }

    public class OrderModel : Order
    {
        public Guid WorkspaceId { get; init; }

        public static string Sql => @"
            select o.*,
                   ol.workspace_id
            from core.order o
            join core.order_line ol on o.id = ol.order_id
            where o.id = :_order_id;
        ";
    }
}

public interface IRequestedService
{
    Guid WorkspaceId { get; }

    string ServiceId { get; }
    
    public DateTime? ExpiresAt { get; }
}

public class RequestedService : IRequestedService
{
    public Guid WorkspaceId { get; init; }

    public required string ServiceId { get; init; }

    public DateTime? ExpiresAt { get; set; }
}