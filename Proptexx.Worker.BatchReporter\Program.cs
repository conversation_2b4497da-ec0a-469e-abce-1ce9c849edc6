using Proptexx.Worker;
using Proptexx.Worker.BatchReporter;
using Proptexx.Core.Services;
using Proptexx.Core.BigQuery;

var builder = Host.CreateApplicationBuilder(args);
builder.AddProptexxWorker();

builder.Services.AddScoped<BatchDataService>();
builder.Services.AddScoped<BigQueryWebhookFeedbackExporter>();

builder.Services.AddHostedService<RetryService>();
builder.Services.AddHostedService<ReporterService>();

var app = builder.Build();
app.Run();