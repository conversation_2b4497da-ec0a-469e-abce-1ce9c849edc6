﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Proptexx.Core.Configuration;


namespace Proptexx.Core.Extensions
{
    public static class ServiceCollectionExtentions
    {
        public static IServiceCollection AddSmtpSettings(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<SmtpSettings>(configuration.GetSection("SMTP"));
            return services;
        }
    }
}
