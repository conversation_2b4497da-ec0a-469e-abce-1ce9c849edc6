using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Proptexx.Core.Entities.Outseta
{
    public class Plan
    {
        [JsonPropertyName("_objectType")]
        public string? ObjectType { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public PlanFamily? PlanFamily { get; set; } // Assumes PlanFamily class is defined elsewhere
        public int? AccountRegistrationMode { get; set; }
        public bool? IsQuantityEditable { get; set; }
        public int? MinimumQuantity { get; set; }
        public int? MaximumPeople { get; set; } // Nullable int
        public decimal? MonthlyRate { get; set; }
        public decimal? AnnualRate { get; set; }
        public decimal? QuarterlyRate { get; set; }
        public decimal? OneTimeRate { get; set; }
        public decimal? SetupFee { get; set; }
        public bool? SkipSetupFeeOnPlanChange { get; set; }
        public bool? IsTaxable { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsPerUser { get; set; }
        public bool? RequirePaymentInformation { get; set; }
        public int? TrialPeriodDays { get; set; }
        public DateTime? TrialUntilDate { get; set; } // Nullable DateTime
        public int? ExpiresAfterMonths { get; set; }
        public DateTime? ExpirationDate { get; set; } // Nullable DateTime
        public string? PostLoginPath { get; set; }
        public string? StripeTaxCodeId { get; set; } // Nullable string
        public string? UnitOfMeasure { get; set; }
        public List<object>? PlanAddOns { get; set; } // Define if structure is known, using List<object> for now
        public List<object>? ContentGroups { get; set; } // Define if structure is known, using List<object> for now
        public int? NumberOfSubscriptions { get; set; } // Nullable int
        public object? ActivityEventData { get; set; } // Define if structure is known
        public string? Uid { get; set; }
        public DateTime? Created { get; set; }
        public DateTime? Updated { get; set; }
    }

    public class PlansResponse
    {
        // Assuming Metadata class exists elsewhere or to be defined.
        // If Metadata class is defined in another file and namespace, add the appropriate using directive.
        // For example: using Proptexx.Core.Entities.Common;
        public Metadata? Metadata { get; set; } 
        public List<Plan>? Items { get; set; }
    }
}
