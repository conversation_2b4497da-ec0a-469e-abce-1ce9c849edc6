namespace Proptexx.Core.Postgresql.Builder.Predicate;

public sealed class PredicateBuilderConfig
{
    private readonly List<IMethodCallHandler> _handlers = [];

    internal IEnumerable<IMethodCallHandler> Handlers => _handlers.AsEnumerable();

    internal static ColumnNameRenderer ColumnNameRender { get; private set; } = val => val;

    internal string ParameterPrefix { get; private set; } = "@";

    public PredicateBuilderConfig ConfigureHandlers(Action<List<IMethodCallHandler>> handlerFn)
    {
        handlerFn(_handlers);
        return this;
    }

    public PredicateBuilderConfig SetParameterPrefix(string prefix)
    {
        ParameterPrefix = prefix;
        return this;
    }

    public PredicateBuilderConfig SetColumnNameRenderer(ColumnNameRenderer columnNameFn)
    {
        ColumnNameRender = columnNameFn;
        return this;
    }
    
    public static PredicateBuilderConfig Create(Action<PredicateBuilderConfig> configAction)
    {
        var config = new PredicateBuilderConfig();
        configAction(config);
        return config;
    }
}