using System;
using System.Text.Json.Serialization;

namespace Proptexx.Core.Entities.Outseta
{
    public class ActivityEventPersonAccountItem
    {
        public ActivityEventPerson? Person { get; set; }
        // The "Account" part of PersonAccount within ActivityEventData is not detailed in the example,
        // so we'll omit it or use a generic object if needed later.
        public bool IsPrimary { get; set; }
        public bool ReceiveInvoices { get; set; }
        public string? Uid { get; set; } // Not present in example, but typical for PersonAccount
        [JsonPropertyName("_objectType")]
        public string? ObjectType { get; set; }
        public DateTime Created { get; set; }
        public DateTime Updated { get; set; }
    }
}
