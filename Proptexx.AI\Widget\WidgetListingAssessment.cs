using System.Text.Json.Serialization;
using Proptexx.Core.Services;

namespace Proptexx.AI.Widget;

public class WidgetListingEntry
{
    public List<WidgetListingAssessment> Images { get; init; } = [];

    internal string[] GetImageUrls()
    {
        return Images.Select(x => x.Url).ToArray();
    }
}

public class WidgetListingAssessment : IBatchImageStatus
{
    public required string Url { get; init; }
    
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public int? Width { get; set; }
    
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public int? Height { get; set; }
    
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? MimeType { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? Type { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? RoomType { get; set; }
    
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public bool? IsEmptyRoom { get; set; }
    
    public int Status { get; set; }

    public long ProcessedAt { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? Error { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? Exception { get; set; }
}