﻿using Proptexx.Web.Command.Widget;
using StackExchange.Redis;
using System.Text.Json;

namespace Proptexx.Web.Command.Helpers
{
    public static class RedisHelpers
    {

        public static async Task<string?> CheckCacheAsync(IDatabaseAsync redis, string cacheName, string key)
        {
            var redisValue = await redis.HashGetAsync(cacheName, key);

            if (!redisValue.HasValue) return null;

            var str = redisValue.ToString();
            var obj = JsonSerializer.Deserialize<GenerativeImageCacheItem>(str);
            return obj?.ImageUrl;
        }
    }
}
