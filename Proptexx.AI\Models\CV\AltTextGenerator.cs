using Microsoft.Extensions.Logging;
using Proptexx.Core.Http;
using Proptexx.Core.Services;
using StackExchange.Redis;

namespace Proptexx.AI.Models.CV;

public sealed class AltTextGenerator(
    IComputerVisionClient computerVisionClient, 
    IImageAssessmentClient imageAssessmentClient,
    IConnectionMultiplexer connectionMultiplexer,
    ILoggerFactory loggerFactory) 
    : BaseComputerVisionModel(
        "AltTextGenerator",
        computerVisionClient, 
        imageAssessmentClient,
        connectionMultiplexer,
        loggerFactory)
{
    public sealed class Result
    {
        public string? Description { get; init; }
    }
}