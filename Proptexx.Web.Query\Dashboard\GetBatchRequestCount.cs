﻿using Dapper;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Dashboard
{
    public sealed class GetBatchRequestCount : BaseFilter, IQuery
    {
        public async Task<object?> ExecuteAsync(QueryContext context)
        {
            await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);


            // Query the materialized view instead of dynamically querying the logs table
            var result = await npgsql.QueryAsync<BatchRequestModel>(Sql, GetParameters());
            return result;
        }

        public sealed class BatchRequestModel
        {
            public required string WorkspaceName { get; init; }
            public int BatchCount { get; init; }
        }

        // Update the SQL query to use the materialized view instead of querying the logs directly
        public static string Sql => @"
                   SELECT 
                COALESCE(c.title, 'Unknown') AS WorkspaceName,  
                SUM(v.batch_count) AS BatchCount
            FROM telemetry.mv_batch_requests_summary v
            LEFT JOIN core.workspace c ON v.workspace_id = c.id  
            WHERE 
                v.day BETWEEN @StartDate::date AND @EndDate::date
                AND (NULLIF(@ids, ARRAY[]::UUID[]) IS NULL OR v.workspace_id = ANY(@ids))
            GROUP BY c.title
            ORDER BY BatchCount DESC;";

    }
}
