using Proptexx.Core.Extensions;
using StackExchange.Redis;

namespace Proptexx.Core.Redis;

public abstract class RedisHashStore<T> where T : class
{
    private readonly string _hashKey;
    private readonly IDatabase _redis;

    protected RedisHashStore(IConnectionMultiplexer connectionMultiplexer, string hashKey)
    {
        _redis = connectionMultiplexer.GetDatabase();
        _hashKey = hashKey;
    }

    protected abstract string ResolveKey(T entry);

    public void Persist(IEnumerable<T> entries) => Persist(entries.ToArray());

    public void Persist(params T[] entries)
    {
        var batch = _redis.CreateBatch();

        foreach (var item in entries)
        {
            var key = ResolveKey(item).Trim();
            _ = batch.HashSetObjectAsync(_hashKey, key, item);
        }

        batch.Execute();
    }

    public Task<T?> GetEntryAsync(string key)
    {
        return this._redis.HashGetObjectAsync<T>(_hashKey, key);
    }

    public async Task<IEnumerable<T>> GetEntriesAsync(string[] keys)
    {
        var parsed = keys.Select(x => new RedisValue(x)).ToArray();
        return await _redis.HashGetObjectsAsync<T>(_hashKey, parsed);
    }
}