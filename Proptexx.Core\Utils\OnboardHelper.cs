using Dapper;
using Npgsql;
using Proptexx.Core.Entities;
using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Stores;

namespace Proptexx.Core.Utils;

public static class AccessKeyHelper
{
    public static async Task CreateWidgetKey(
        NpgsqlConnection npgsql,
        Guid workspaceId,
        DateTime? expiredAt,
        string clientName,
        string description,
        string scopes)
    {
        var x = await npgsql.QueryFirstOrDefaultAsync<long>(@"
            select count(*)
            from core.client c
            where c.workspace_id = :_workspace_id and c.name = :_client_name", 
            new { _workspace_id = workspaceId, _client_name = clientName });

        if (x > 0) return;

        var client = new Client
        {
            Name = clientName,
            Description = description,
            WorkspaceId = workspaceId,
            Scopes = scopes,
            IssueToken = false,
            SessionLifespan = 600
        };

        var clientSecret = new ClientSecret { Id = Guid.NewGuid(), ClientId = client.Id };

        if (expiredAt != null)
        {
            clientSecret.SetExpired((DateTime)expiredAt);
        }

        await npgsql.InsertAsync(client);
        await npgsql.InsertAsync(clientSecret);
    }
}

public static class OnboardHelper
{
    public static async Task<Account> OnboardAccount(NpgsqlConnection npgsql, string fullName, string email, bool emailVerified, string password, string? phone, bool phoneVerified = false)
    {
        var account = await npgsql.Account().GetAccountIdByEmailAsync(email);
        if (account is not null) return account;

        var (firstName, lastName) = NameHelper.ParseFullName(fullName);
        account = await npgsql.Account().Create(firstName, lastName);
        await npgsql.Account().AddEmail(account.Id, email, emailVerified);
        await npgsql.Account().SetSecret(account.Id, password);

        if (!string.IsNullOrWhiteSpace(phone))
        {
            await npgsql.Account().AddPhone(account.Id, phone, phoneVerified);
        }

        return account;
    }
    public static async Task AddDomain(NpgsqlConnection npgsql, Guid workspaceId, string hostname, string path = "/", string? inputValue = null, MatchStrategy matchStrategy = MatchStrategy.Start)
    {
        var domain = new Domain
        {
            WorkspaceId = workspaceId,
            Hostname = hostname,
            Path = path,
            InputValue = inputValue ?? $"https://{hostname}",
            MatchStrategy = matchStrategy
        };
        await npgsql.InsertAsync(domain);
    }
    public static async Task<Workspace> CreateWorkspace(NpgsqlConnection npgsql, Account founder, string? workspaceTitle, Guid? parentId, List<UrlInfo>? domains)
    {
        var title = NameHelper.ParseWorkspaceTitle(founder.FirstName, workspaceTitle);

        const string clusterTplName = "default";
        var clusterTpl = await npgsql.QueryFirstAsync<ClusterTpl>(
            "select ct.* from core.cluster_tpl ct where ct.name = :_name",
            new { _name = clusterTplName });
        
        var workspace = await npgsql.Workspace().Create(title, clusterTpl.Id, parentId, domains ?? [], founder.Id);
        var adminClusterId = await CreateClustersForWorkspace(npgsql, workspace.Id, clusterTpl.Id);
        await npgsql.Account().JoinCluster(founder.Id, adminClusterId, true);
        return workspace;
    }

    private static async Task<Guid> CreateClustersForWorkspace(NpgsqlConnection npgsql, Guid workspaceId, Guid clusterTplId)
    {
        var clusterTypes = await npgsql.QueryAsync<ClusterTplType>(
            "select * from core.cluster_tpl_type ctt where ctt.cluster_tpl_id = :_cluster_tpl_id",
            new { _cluster_tpl_id = clusterTplId });

        Guid? adminClusterId = null;
        foreach (var ct in clusterTypes)
        {
            var clusterId = await npgsql.Cluster().Create(workspaceId, ct.Name, ct.Id);
            if (ct.Name.Equals("Administrator", StringComparison.OrdinalIgnoreCase))
            {
                adminClusterId = clusterId;
            }
        }

        if (!adminClusterId.HasValue)
        {
            throw new NullReferenceException("ClusterIdForAdmin");
        }

        return adminClusterId.Value;
    }
}