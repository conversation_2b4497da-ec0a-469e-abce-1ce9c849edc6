using Microsoft.Extensions.Logging;
using Proptexx.Core.Http;
using Proptexx.Core.Services;
using StackExchange.Redis;

namespace Proptexx.AI.Models.CV;

public sealed class ArchitectureDetector(
    IComputerVisionClient computerVisionClient,
    IImageAssessmentClient imageAssessmentClient,
    IConnectionMultiplexer connectionMultiplexer,
    ILoggerFactory loggerFactory)
    : BaseComputerVisionModel(
        "ArchitectureDetector",
        computerVisionClient,
        imageAssessmentClient,
        connectionMultiplexer,
        loggerFactory)
{
    public sealed class Result
    {
        public string[]? Architecture { get; init; }
        
        public float[]? Confidence { get; init; }
    }
}