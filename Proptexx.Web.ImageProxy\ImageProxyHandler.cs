using Microsoft.Playwright;
using Six<PERSON>abors.ImageSharp;
using SixLabors.ImageSharp.Formats;

namespace Proptexx.Web.ImageProxy;

public sealed class ImageProxyHandler
{
    private readonly IHttpClientFactory _httpClientFactory;

    public ImageProxyHandler(IHttpClientFactory httpClientFactory)
    {
        _httpClientFactory = httpClientFactory;
    }
    
    public async Task InvokeAsync(HttpContext context)
    {
        string? contentType = null;
        byte[]? imageBytes = null;

        try
        {
            if (!context.Request.Query.TryGetValue("url", out var url))
            {
                throw new ApplicationException("Parameter url was not provided");
            }

            var imageUrl = url.ToString();

            if (string.IsNullOrWhiteSpace(imageUrl))
            {
                throw new ApplicationException("Parameter url was not provided");
            }

            imageBytes = await DownloadImageAsync(context, imageUrl);
        }
        catch (TimeoutException ex)
        {
            context.Response.StatusCode = StatusCodes.Status504GatewayTimeout;
        }
        catch (ApplicationException ex)
        {
            context.Response.StatusCode = StatusCodes.Status415UnsupportedMediaType;
        }
        catch (Exception e)
        {
            context.Response.StatusCode = StatusCodes.Status400BadRequest;
        }
        finally
        {
            context.Response.ContentType = contentType;

            if (imageBytes is null)
            {
                context.Response.StatusCode = StatusCodes.Status400BadRequest;
            }
            else
            {
                await context.Response.Body.WriteAsync(imageBytes, context.RequestAborted);
            }
        }
    }

    private async Task<byte[]> DownloadImageAsync(HttpContext context, string url)
    {
        using var httpClient = _httpClientFactory.CreateClient();
        httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");

        var response = await httpClient.GetAsync(url, context.RequestAborted);
        response.EnsureSuccessStatusCode();

        byte[] bytes;
        var mediaType = response.Content.Headers.ContentType?.MediaType;
        if (mediaType is not null && mediaType.StartsWith("image/"))
        {
            bytes = await response.Content.ReadAsByteArrayAsync(context.RequestAborted);
        }
        else
        {
            bytes = await FetchWithPlaywrightAsync(context.RequestServices, url);
        }

        AssessImage(bytes);
        return bytes;
    }

    private static IImageFormat AssessImage(byte[] imageBytes)
    {
        if (imageBytes.Length > 10_000_000) // 10 MB
        {
            throw new ApplicationException("Image is too large");
        }

        var format = Image.DetectFormat(imageBytes) ?? null;
        if (format is null)
        {
            throw new ApplicationException("Unsupported image format");
        }

        return format;
    }
    private static async Task<byte[]> FetchWithPlaywrightAsync(IServiceProvider services, string imageUrl)
    {
        // Resolve your shared singleton PlaywrightBrowser
        var playwrightService = services.GetRequiredService<PlaywrightBrowser>();

        if (playwrightService.Browser is null)
        {
            throw new InvalidOperationException("Playwright browser is not initialized.");
        }

        var context = await playwrightService.Browser.NewContextAsync(new()
        {
            BypassCSP = true,
            JavaScriptEnabled = true
        });

        var page = await context.NewPageAsync();

        try
        {
            Console.WriteLine("Navigating to image URL...");
            var response = await page.GotoAsync(imageUrl, new PageGotoOptions
            {
                WaitUntil = WaitUntilState.NetworkIdle,
                Timeout = 15000
            });

            // Wait for any bot protection redirect JS
            await page.WaitForTimeoutAsync(5000);

            // Re-navigate in case the challenge changed cookies, URL, etc.
            Console.WriteLine("Attempting final image load...");
            var imageResponse = await page.GotoAsync(imageUrl, new PageGotoOptions
            {
                WaitUntil = WaitUntilState.NetworkIdle,
                Timeout = 10000
            });

            if (imageResponse == null || !imageResponse.Ok)
            {
                throw new Exception($"Image request failed: Status {imageResponse?.Status}");
            }

            var contentType = imageResponse.Headers["content-type"];
            if (!contentType.StartsWith("image/"))
            {
                throw new Exception($"Expected image content, got '{contentType}'");
            }

            var body = await imageResponse.BodyAsync();
            return body.ToArray();
        }
        catch (Exception ex)
        {
            Console.WriteLine("Playwright failed to fetch image: " + ex.Message);
            throw;
        }
        finally
        {
            await context.CloseAsync();
        }
    }

}