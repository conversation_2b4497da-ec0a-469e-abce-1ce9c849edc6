using System.Net.Http.Headers;
using System.Text.Json.Serialization;
using Proptexx.Core.Extensions;

namespace Proptexx.Web.Auth.OAuth.Google;

public sealed class GoogleOAuthProvider : IOAuthProvider
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly GoogleOAuthOptions _options;

    public GoogleOAuthProvider(IConfiguration configuration, IHttpClientFactory httpClientFactory)
    {
        _options = configuration.GetRequiredSection<GoogleOAuthOptions>("google");
        _httpClientFactory = httpClientFactory;
    }

    public Task<string> GenerateAuthLinkAsync(string state)
    {
        const string responseType = "code";

        var url = $"{_options.AuthorityUri}" +
                  $"?client_id={Uri.EscapeDataString(_options.ClientId)}" +
                  $"&redirect_uri={Uri.EscapeDataString(_options.RedirectUri)}" +
                  $"&response_type={responseType}" +
                  $"&scope={Uri.EscapeDataString(_options.Scope)}" +
                  $"&state={Uri.EscapeDataString(state)}";

        return Task.FromResult(url);
    }

    public async Task<TokenResponse> ExchangeCodeForTokenAsync(string code)
    {
        using var httpClient = _httpClientFactory.CreateClient();
        using var tokenRequest = new FormUrlEncodedContent(new[]
        {
            new KeyValuePair<string, string>("code", code),
            new KeyValuePair<string, string>("client_id", _options.ClientId),
            new KeyValuePair<string, string>("client_secret", _options.ClientSecret),
            new KeyValuePair<string, string>("redirect_uri", _options.RedirectUri),
            new KeyValuePair<string, string>("grant_type", "authorization_code")
        });

        var response = await httpClient.PostAsync(_options.AccessTokenUri, tokenRequest);

        if (!response.IsSuccessStatusCode)
        {
            throw new Exception($"Token request failed: {response.StatusCode}");
        }

        var result = await response.Content.ReadFromJsonAsync<TokenResponse>()
                     ?? throw new NullReferenceException("Unable to parse token response");

        return result;
    }

    public async Task<UserInfo> FetchUserInfoAsync(string accessToken)
    {
        using var httpClient = _httpClientFactory.CreateClient();
        var request = new HttpRequestMessage(HttpMethod.Get, _options.UserInfoUri);
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

        var response = await httpClient.SendAsync(request);

        if (!response.IsSuccessStatusCode)
        {
            throw new Exception($"User info request failed: {response.StatusCode}");
        }

        var userInfo = await response.Content.ReadFromJsonAsync<GoogleUserInfo>()
                       ?? throw new InvalidOperationException("Unable to deserialize user information");

        return new UserInfo
        {
            Id = userInfo.Sub,
            Email = userInfo.Email,
            Locale = "en",
            Name = userInfo.Name,
            Picture = userInfo.Picture,
            EmailVerified = userInfo.EmailVerified,
            FamilyName = userInfo.FamilyName,
            GivenName = userInfo.GivenName
        };
    }
    
    private sealed class GoogleUserInfo
    {
        [JsonPropertyName("sub")]
        public required string Sub { get; init; }

        [JsonPropertyName("name")]
        public string? Name { get; init; }

        [JsonPropertyName("given_name")]
        public string? GivenName { get; init; }

        [JsonPropertyName("family_name")]
        public string? FamilyName { get; init; }

        [JsonPropertyName("picture")]
        public string? Picture { get; init; }

        [JsonPropertyName("email")]
        public string? Email { get; init; }

        [JsonPropertyName("email_verified")]
        public bool? EmailVerified { get; init; }

        [JsonPropertyName("hd")]
        public string? Hd { get; init; }
    }
}
