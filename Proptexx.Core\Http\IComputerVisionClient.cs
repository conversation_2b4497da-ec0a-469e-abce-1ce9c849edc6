using System.Net.Http.Json;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Services;

namespace Proptexx.Core.Http;

public interface IComputerVisionClient
{
    Task<JsonDocument> SendAsync(string endpoint, string modelName, string imageUrl, string? base64Image, string mimeType, CancellationToken cancellationToken);
    
    Task<JsonDocument> SendLegacyAsync(string modelEndpoint, object pl, CancellationToken cancellationToken);
}

public sealed class ComputerVisionHttpClient(HttpClient httpClient, ILogger<ComputerVisionHttpClient> logger) 
    : BaseHttpClient(httpClient, logger), IComputerVisionClient
{
    public async Task<JsonDocument> SendAsync(string endpoint, string modelName, string imageUrl, string? base64Image, string mimeType, CancellationToken cancellationToken)
    {
        var image = base64Image ?? imageUrl;
        using var content = JsonContent.Create(new
        {
            image,
            processor_name = modelName
        });

        var document = await PostJsonAsync(endpoint, content, cancellationToken);

        var modelInternalStatusCode = PayloadService.GetOptionalInt32(document, "status_code");
        if (modelInternalStatusCode is >= 400)
        {
            var modelInternalDetail = PayloadService.GetOptionalString(document, "detail");
            throw new ApplicationException(modelInternalDetail ?? "An internal model error was thrown");
        }

        return document;
    }

    public async Task<JsonDocument> SendLegacyAsync(string modelEndpoint, object payload, CancellationToken cancellationToken)
    {
        using var content = JsonContent.Create(payload);
        return await PostJsonAsync(modelEndpoint, content, cancellationToken);
    }
}