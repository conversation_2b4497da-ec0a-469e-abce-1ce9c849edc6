﻿using FluentMigrator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Proptexx.Database.Migration.Migrations
{
    [Migration(250424001)]
    public class Migrate250424001 : FluentMigrator.Migration
    {
        public override void Up()
        {
            Alter.Table("widget").InSchema("core")
                .AddColumn("customize_logo_url").AsString().Nullable();
        }

        public override void Down()
        {
            Delete.Column("customize_logo_url")
                .FromTable("widget").InSchema("core");
        }
    }
}
