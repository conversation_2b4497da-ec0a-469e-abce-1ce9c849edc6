# Logging configuration
Logging__LogLevel__Default=Information
Logging__LogLevel__Microsoft.AspNetCore=Warning

# Allowed Hosts
AllowedHosts=*

# Connection Strings
ConnectionStrings__Redis=
ConnectionStrings__MongoDB=
ConnectionStrings__Postgresql=
ConnectionStrings__PgBatch=
ConnectionStrings__AzureStorage=

# Centralized API Key
CentralizedApiKey=64ddc73ce7f4771179c3d67f

# Proptexx Configuration
Proptexx__Audience=proptexx.com
Proptexx__Issuer=proptexx.com
Proptexx__CallerIdentifier=$accountId
Proptexx__Cookie__Domain=.proptexx.com
Proptexx__SecurityKey=  # Representing null as an empty string
Proptexx__AccessTokenTtl=7200
Proptexx__SessionTokenTtl=7200
Proptexx__CommandUrl=   # Representing null as an empty string
Proptexx__QueryUrl=     # Representing null as an empty string

# OAuth Configuration
Google__ClientId=
Google__ClientSecret=
Google__OAuthUri=https://accounts.google.com/o/oauth2/v2/auth
Google__AccessTokenUri=https://oauth2.googleapis.com/token
Google__RedirectUri=

# Google Configuration
GoogleG__Base64EncodedJsonCredentials=
GoogleG__ProjectId=proptexx-dev
GoogleG__Location=europe-west3
GoogleG__Publisher=google
GoogleG__Model=gemini-1.5-flash-001

# Stripe Configuration
Stripe__ApiKey=  # Empty string
Stripe__WebhookSecret=  # Empty string
Stripe__SuccessUrl=  # Empty string
Stripe__CanceledUrl=  # Empty string
