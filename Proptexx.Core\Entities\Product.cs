using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class Product : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; init; } = Guid.NewGuid();
    
    public Guid? WorkspaceId { get; init; }

    public required string Title { get; set; }

    public string? Description { get; set; }
    
    public string? Summary { get; set; }
    
    public string? Content { get; set; }

    public required string PaymentType { get; init; }
    
    public decimal PriceAmount { get; set; }
    
    public required string Currency { get; set; }

    public required ProductConfig Config { get; init; }

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }

    public string GetDbRef() => "core.product";
}

public class ProductConfig
{
    public ProductConfig(string configType)
    {
        ConfigType = configType;
    }
    
    public string ConfigType { get; }
}

public class ProductWidgetAccessConfig : ProductConfig
{
    public ProductWidgetAccessConfig() : base("widget-access")
    {
    }

    public string? Interval { get; set; }
}
public class ProductEcommerceWidgetConfig : ProductConfig
{
    public ProductEcommerceWidgetConfig() : base("ecommerce-widget")
    {
    }

    public string? Interval { get; set; }
}
public class ProductRealEstateWidgetConfig : ProductConfig
{
    public ProductRealEstateWidgetConfig() : base("real-estate-widget")
    {
    }

    public string? Interval { get; set; }
}