using System.Text.Json;
using Proptexx.Core.Http;

namespace Proptexx.AI.Models.CV;

public sealed class WatermarkCostarDetection : LegacyComputerVisionModel
{
    public WatermarkCostarDetection(IComputerVisionClient computerVisionClient)  
        : base(computerVisionClient)
    {
    }

    protected override JsonDocument? ParseModelResponse(JsonDocument doc, string inputImageUrl, string modelEndpoint)
    {
        var computerVision = doc.RootElement.GetPropertyOrDefault("computer_vision");

        var propertyMapping = new Dictionary<string, string>
        {
            { "result", "output" },
            { "bbox", "bboxes" },
            { "score", "scores" }
        };

        return computerVision?.GetPropertiesToJsonDocument(propertyMapping);
    }

    protected override string GetModelEndpoint(string workspaceId)
        => "http://preprocessing.prod.ai.proptexx.com/watermark-costar-detection/predict";
}