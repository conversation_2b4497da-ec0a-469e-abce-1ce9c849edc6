namespace Proptexx.Web.Auth.OAuth;

public interface IOAuthProviderFactory
{
    IOAuthProvider GetProvider(string providerName);
}

public sealed class OAuthProviderFactory : IOAuthProviderFactory
{
    private readonly IServiceProvider _services;

    public OAuthProviderFactory(IServiceProvider services)
    {
        _services = services;
    }

    public IOAuthProvider GetProvider(string providerName)
    {
        if (string.IsNullOrWhiteSpace(providerName))
            throw new ArgumentNullException(nameof(providerName));

        var key = providerName.ToLowerInvariant();
        var provider = _services.GetKeyedService<IOAuthProvider>(key)
            ?? throw new InvalidOperationException($"OAuth provider '{providerName}' is not supported.");

        return provider;
    }
}
