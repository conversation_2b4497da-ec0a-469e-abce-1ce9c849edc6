using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.System;

public sealed class GetWebsites : IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        context.User.EnsureRootAccess();

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var enumerable = await npgsql.QueryAsync<WebsiteModel>(WebsiteModel.Sql);
        return enumerable;
    }

    public class WebsiteModel
    {
        public Guid Id { get; init; }
    
        public required string Slug { get; init; }
    
        public required string Title { get; init; }
    
        public DateTime CreatedAt { get; init; }
    
        public int NumDocuments { get; init; }
    
        public static string Sql => @"
        select w.id,
               w.slug,
               w.title,
               w.created_at,
               count(d.id) as num_documents
        from core.website w
        left outer join core.document d on w.id = d.website_id
        group by w.id;
    ";
    }
}