using System.Text.Json;
using Proptexx.Core.Json;
using Proptexx.Web.Auth.Auth;
using StackExchange.Redis;

namespace Proptexx.Web.Auth.Resolvers;

public sealed class SessionResolver : ISessionResolver
{
    private readonly IDatabase _redis;

    public SessionResolver(IConnectionMultiplexer connectionMultiplexer)
    {
        _redis = connectionMultiplexer.GetDatabase();
    }

    public async Task PersistAsync(string sessionId, TimeSpan maxAge, SessionPayload payload)
    {
        var redisKey = $"session:{sessionId}";
        var value = JsonSerializer.Serialize(payload, JsonDefaults.CompactOptions);
        await _redis.StringSetAsync(redisKey, value, maxAge);
    }

    public async Task<SessionPayload?> RetriveAsync(string sessionId)
    {
        var redisKey = $"session:{sessionId}";
        var redisValue = await _redis.StringGetAsync(redisKey);
        if (!redisValue.HasValue || redisValue.IsNullOrEmpty) return null;
        return JsonSerializer.Deserialize<SessionPayload>(redisValue.ToString(), JsonDefaults.CompactOptions);
    }

    public async Task DeleteAsync(string sessionId)
    {
        var redisKey = $"session:{sessionId}";
        await _redis.KeyDeleteAsync(redisKey);
    }
}