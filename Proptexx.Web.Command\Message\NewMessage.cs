using System.ComponentModel.DataAnnotations;
using System.Data;
using Dapper;
using Proptexx.Core;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Messaging;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Command.Message;

public sealed class NewMessage : ICommand
{
    [Required]
    public required string Receiver { get; init; }

    [Required, MaxLength(50)]
    public required string Subject { get; init; }

    public string? Content { get; init; }

    public bool? IsRich { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        var senderId = Guid.Parse(context.User.GetCallerId());

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var receiverIds = await GetReceiverIds(npgsql, this.Receiver);

        await using var trx = await npgsql.BeginTransactionAsync(context.CancellationToken);

        try
        {
            var recipients = receiverIds
                .Select(r => new InternalRecipientOption(r))
                .ToArray();

            var options = new MessageCreationOptions()
                .SetSender(senderId)
                .SetBody(this.Content, false)
                .AddRecipients(recipients);

            await MessageService.CreateAndSendAsync(npgsql, this.Subject, options);
            await trx.CommitAsync(context.CancellationToken);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            await trx.RollbackAsync(context.CancellationToken);
            throw;
        }
    }

    private static async Task<IEnumerable<Guid>> GetReceiverIds(IDbConnection npgsql, string receiver)
    {
        if (receiver.Equals("support", StringComparison.OrdinalIgnoreCase))
        {
            return await npgsql.QueryAsync<Guid>("select a.id from core.account a where a.is_root");
        }

        if (!Guid.TryParse(receiver, out var receiverId))
        {
            throw new CommandException("The receiver was not identified");
        }

        return await npgsql.QueryAsync<Guid>(
            "select a.id from core.account a where a.id = :_account_id", 
            new {_account_id = receiverId});
    }
}