using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Entities;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Workspace;

public sealed class GetWidgetUrls : IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var workspaceId = context.User.GetWorkspaceGuid();

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var result = await npgsql.QueryAsync<UrlFilterModel>(UrlFilterModel.Sql, new { _workspace_id = workspaceId });
        return result;
    }

    public sealed class UrlFilterModel
    {
        public Guid Id { get; init; }
    
        public required string UrlFilter { get; init; }
    
        public MatchStrategy MatchStrategy { get; init; }
        
        public DateTime CreatedAt { get; init; }

        public static string Sql => @"
            select d.id,
                   d.input_value as url_filter,
                   d.match_strategy,
                   d.created_at
            from core.domain d
            where d.workspace_id = :_workspace_id
            order by d.match_strategy, d.input_value
        ";
    }
}
