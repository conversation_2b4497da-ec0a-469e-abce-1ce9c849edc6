namespace Proptexx.Web.Auth.Handlers;

public sealed class FallbackHandler
{
    private readonly IWebHostEnvironment _environment;

    public FallbackHandler(IWebHostEnvironment environment)
    {
        _environment = environment;
    }

    public Task InvokeAsync(HttpContext context)
    {
        if (HttpMethods.IsGet(context.Request.Method))
        {
            var file = Path.Combine(_environment.WebRootPath, "index.html");
            context.Response.ContentType = "text/html";
            return context.Response.SendFileAsync(file);
        }

        context.Response.StatusCode = StatusCodes.Status404NotFound;
        return context.Response.WriteAsJsonAsync(new { error = "Invalid endpoint" });
    }
}