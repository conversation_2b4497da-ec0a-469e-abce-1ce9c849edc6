namespace Proptexx.Core.DTOs;

/// <summary>
/// Result of usage check for subscriptions
/// </summary>
public record UsageCheckResult
{
    /// <summary>
    /// Whether the subscription is valid for usage
    /// </summary>
    public bool IsValid { get; init; }
    
    /// <summary>
    /// Human-readable reason for the validation result
    /// </summary>
    public string Reason { get; init; } = string.Empty;
    
    /// <summary>
    /// Error code if validation failed
    /// </summary>
    public UsageCheckErrorCode ErrorCode { get; init; }
    
    /// <summary>
    /// Type of subscription (Period, OneTime, PayAsYouGo)
    /// </summary>
    public string Type { get; init; } = string.Empty;
    
    /// <summary>
    /// Total quota allocated to the subscription
    /// </summary>
    public long Quota { get; init; }
    
    /// <summary>
    /// Current usage count
    /// </summary>
    public long TotalUsage { get; init; }
    
    /// <summary>
    /// Remaining quota available
    /// </summary>
    public long RemainingQuota { get; init; }
    
    /// <summary>
    /// Usage percentage (0-100)
    /// </summary>
    public double UsagePercentage { get; init; }
    
    /// <summary>
    /// Whether the subscription has expired
    /// </summary>
    public bool IsExpired { get; init; }
    
    /// <summary>
    /// Days until expiry (null if no expiry date or already expired)
    /// </summary>
    public int? DaysUntilExpiry { get; init; }
}

/// <summary>
/// Error codes for usage validation
/// </summary>
public enum UsageCheckErrorCode
{
    /// <summary>
    /// No error
    /// </summary>
    None = 0,
    
    /// <summary>
    /// Subscription not found or inactive
    /// </summary>
    SubscriptionNotFound = 1001,
    
    /// <summary>
    /// Subscription has expired
    /// </summary>
    SubscriptionExpired = 1002,
    
    /// <summary>
    /// Quota has been exceeded
    /// </summary>
    QuotaExceeded = 1003,
    
    /// <summary>
    /// General validation error
    /// </summary>
    ValidationError = 9999
} 