using System.Text.Json.Serialization;
using Npgsql;
using Proptexx.Core;
using Proptexx.Core.Json;
using Proptexx.Core.Stores;
using Proptexx.Web.Api.Helpers;
using Proptexx.Web.Api.Responses;
using StackExchange.Redis;

namespace Proptexx.Web.Api.Handlers;

public sealed class StatusHandler
{
    private readonly NpgsqlDataSource _dataSource;
    private readonly IDatabase _redis;

    public StatusHandler(NpgsqlDataSource dataSource, IConnectionMultiplexer connectionMultiplexer)
    {
        _dataSource = dataSource;
        _redis = connectionMultiplexer.GetDatabase();
    }
    
    public async Task InvokeAsync(HttpContext context)
    {
        var response = new StatusApiResponse();

        try
        {
            var batchId = ParseBatchId(context);
            var label = ParseLabel(context);
            if (!batchId.HasValue) throw new ApplicationException("Invalid parameters");

            var workspaceId = context.User.GetWorkspaceGuid();

            await using var npgsql = await _dataSource.OpenConnectionAsync(context.RequestAborted);
            var enumerable = await BatchStore.GetBatchResults(npgsql, [batchId.Value]);
            if (!enumerable.Any()) throw new KeyNotFoundException("Not found");

            response.Success(batchId, label, enumerable);
            context.Response.StatusCode = StatusCodes.Status200OK;

            // Calculate batch time by last completed item
            var lastItem = enumerable
               .Where(x => x.ResultCompletedAt.HasValue)
               .OrderBy(x => x.ResultCompletedAt)
               .LastOrDefault();
            if (lastItem != null && lastItem.BatchCreatedAt.HasValue && lastItem.ResultCompletedAt.HasValue)
            {
                var duration = lastItem.ResultCompletedAt.Value - lastItem.BatchCreatedAt.Value;
                response.TimeTaken = $"{duration.Days} days, {duration.Hours} hours, {duration.Minutes} minutes, {duration.Seconds} seconds"; ;
            }
            
            response.ReceivedAt = enumerable.FirstOrDefault()?.BatchCreatedAt;
        }
        catch (KeyNotFoundException e)
        {
            context.Response.StatusCode = StatusCodes.Status404NotFound;
            response.Fail(e.Message);
        }
        catch (ApplicationException e)
        {
            context.Response.StatusCode = StatusCodes.Status400BadRequest;
            response.Fail(e.Message);
        }
        catch (UnauthorizedAccessException e)
        {
            context.Response.StatusCode = StatusCodes.Status401Unauthorized;
            response.Fail(e.Message);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            context.Response.StatusCode = StatusCodes.Status500InternalServerError;
            response.Fail("The operation exited with an error. We have been notified and are looking into it");
        }
        finally
        {
            await context.Response.WriteAsJsonAsync(response, JsonDefaults.JsonSerializerOptions);
        }
    }

    private static Guid? ParseBatchId(HttpContext context)
    {
        Guid? result = null;
        if (context.Request.RouteValues.TryGetValue("id", out var tmpBatchId) &&
            Guid.TryParse(tmpBatchId?.ToString(), out var batchId))
        {
            result = batchId;
        }

        return result;
    }

    private static string? ParseLabel(HttpContext context)
    {
        string? result = null;
        if (context.Request.Query.TryGetValue("label", out var label))
        {
            result = label.ToString();
        }

        return result;
    }
}

public class StatusApiResponse : ApiResponseStatus
{
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? Label { get; private set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public int? TotalCount { get; private set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public int? PendingCount { get; private set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public int? ErrorCount { get; private set; }

    public object? Results { get; protected set; }
    
    public void Success(Guid? batchId, string? label, IEnumerable<BatchResultModel> results)
    {
        var items = new List<object>();
        foreach (var x in results)
        {
            this.TotalCount++;
            
            switch (x.Status)
            {
                case 0:
                case 1:
                    this.PendingCount++;
                    break;
                case -1:
                    this.ErrorCount++;
                    break;
            }

            items.Add(new
            {
                status = BatchResultModel.GetStatusText(x.Status),
                model = x.Model,
                config = x.Config.RootElement,
                result = x.Result?.RootElement,
                error = x.ErrorMessage,
                taskDuration = x.TaskDuration
            });
        }

        this.Id = batchId;
        this.Label = label;
        this.Results = items;
    }
}