using System.ComponentModel.DataAnnotations;
using Proptexx.Core.Services;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Messaging;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Stores;
using StackExchange.Redis;

namespace Proptexx.Web.Command.Widget;

public sealed class Login : ICommand
{
    [Required] public required string Username { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        var workspaceId = context.User.GetWorkspaceGuid();

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        await using var trx = await npgsql.BeginTransactionAsync();

        try
        {
            if (!await npgsql.Account().EmailExistsOnWorkspace(workspaceId, this.Username))
            {
                throw new UnauthorizedAccessException("Invalid credentials");
            }

            var account = await npgsql.Account().GetAccountIdByEmailAsync(this.Username)
                          ?? throw new UnauthorizedAccessException("Invalid credentials");

            var database = context.GetService<IConnectionMultiplexer>().GetDatabase();
            var otp = PasswordService.GeneratePassword(5);
            var otpService = new RedisOtpService(database);
            await otpService.SetOtpAsync(account.Id.ToString(), otp);

            var (subject, body) = EmailTemplate.OnOtpRequested(otp);
            var opt = new MessageCreationOptions()
                .AddRecipient(new EmailRecipientOption(this.Username, account.Id))
                .SetBody(body, true);

            await MessageService.CreateAndSendAsync(npgsql, subject, opt);
            await trx.CommitAsync(context.CancellationToken);
            context.AddData("otpRef", account.Id);
        }
        catch (Exception)
        {
            await trx.RollbackAsync(context.CancellationToken);
            throw;
        }
    }
}