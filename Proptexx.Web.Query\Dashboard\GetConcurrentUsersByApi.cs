﻿using Dapper;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Dashboard;
public sealed class GetConcurrentUsersByApi : IQuery
{
    public required DateTime StartTime { get; init; }
    public required DateTime EndTime { get; init; }

    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var result = await npgsql.QueryAsync<ApiConcurrentUsersModel>(Sql, new { _start_time = StartTime, _end_time = EndTime });
        return result;
    }

    public sealed class ApiConcurrentUsersModel
    {
        public required string Endpoint { get; init; }
        public required int ActiveUsers { get; init; }
    }

    public static string Sql => @"
        SELECT endpoint, COUNT(DISTINCT session_id) AS active_users
        FROM telemetry.api_logs
        WHERE timestamp BETWEEN :_start_time AND :_end_time
        GROUP BY endpoint
        ORDER BY active_users DESC;
    ";
}
