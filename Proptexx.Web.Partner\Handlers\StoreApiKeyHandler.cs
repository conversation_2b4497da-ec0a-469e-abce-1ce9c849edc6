using System.Text.Json;
using Npgsql;
using Proptexx.Core;
using Proptexx.Core.Services;
using Proptexx.Core.Stores;

namespace Proptexx.Web.Partner.Handlers;

public sealed class StoreApiKeyHandler
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<StoreApiKeyHandler> _logger;
    private readonly NpgsqlDataSource _dataSource;

    public StoreApiKeyHandler(IConfiguration configuration, ILogger<StoreApiKeyHandler> logger, NpgsqlDataSource dataSource)
    {
        _configuration = configuration;
        _logger = logger;
        _dataSource = dataSource;
    }

    internal static Task InvokeAsync(StoreApiKeyHandler handler, HttpContext httpContext) => handler.InvokeAsync(httpContext);

    private async Task InvokeAsync(HttpContext httpContext)
    {
        try
        {
            using var doc = await JsonDocument.ParseAsync(httpContext.Request.Body);
            var userRef = PayloadService.GetRequiredString(doc, "userRef");

            await using var conn = await _dataSource.OpenConnectionAsync(httpContext.RequestAborted);
            var workspace = await conn.Workspace().GetByData("partnerId", userRef)
                ?? throw new ApplicationException("Unable to retrieve partner data");

            var secret = await conn.Client().GetSecret(workspace.Id, "store")
                ?? throw new ApplicationException("Unable to retrieve store secret");

            var apiKey = ApiKeyService.CreateApiKey(secret.Id);
            await httpContext.Response.WriteAsJsonAsync(new { apiKey });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception while retrieving store api key");
            httpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
        }
    }
}