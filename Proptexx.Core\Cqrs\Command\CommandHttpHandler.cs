using System.ComponentModel.DataAnnotations;
using System.Diagnostics;
using System.Text.Encodings.Web;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Json;

namespace Proptexx.Core.Cqrs.Command;

public class CommandHttpHandler
{
    private readonly CommandConfig _config;
    private readonly ILogger<CommandHttpHandler> _logger;

    private static readonly JsonSerializerOptions JsonSerializerOptions = new(JsonDefaults.JsonSerializerOptions)
    {
        Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping
    };

    public CommandHttpHandler(CommandConfig config, ILogger<CommandHttpHandler> logger)
    {
        _config = config;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext httpContext)
    {
        var response = new CommandResponse();
        JsonSerializerOptions? jsonSerializerOptions = null;

        try
        {
            var context = await CommandContext.FromHttpAsync(httpContext);
            var handler = _config.GetHandler(context.Definition.Handler ?? string.Empty)
                          ?? throw new CommandException("Command handler not found");

            if (!await handler.HasAccessAsync(context))
            {
                throw new CommandException("Unauthorized");
            }

            await handler.ExecuteAsync(context);
            jsonSerializerOptions = context.JsonSerializerOptions;
            response.Success(context);
        }
        catch (CommandException e)
        {
            response.Error(e.Message);
            httpContext.Response.StatusCode = StatusCodes.Status400BadRequest;
        }
        catch (UnauthorizedAccessException e)
        {
            response.Error(e.Message);
            httpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
        }
        catch (ValidationException e)
        {
            response.Error(e.Message);
            httpContext.Response.StatusCode = StatusCodes.Status403Forbidden;
        }
        catch (Exception e)
        {
            _logger.LogError(e, $"Exception in {nameof(CommandHttpHandler)}");
            response.Error("An error occured. We are notified and will look into it");
            httpContext.Response.StatusCode = StatusCodes.Status500InternalServerError;
        }
        finally
        {
            await httpContext.Response.WriteAsJsonAsync(response, jsonSerializerOptions ?? JsonSerializerOptions);
        }
    }

    private sealed class CommandResponse
    {
        private readonly Stopwatch _timer = Stopwatch.StartNew();

        public long ExecutionTime
            => _timer.ElapsedMilliseconds;

        public string? ErrorMessage { get; private set; }

        public bool IsSuccess { get; private set; }

        public void Success(CommandContext result)
        {
            Result = result.Data;
            IsSuccess = true;
            Completed();
        }

        public Dictionary<string, object>? Result { get; private set; }

        public void Error(string errorMessage)
        {
            ErrorMessage = errorMessage;
            Completed();
        }

        private void Completed()
        {
            _timer.Stop();
        }
    }
}