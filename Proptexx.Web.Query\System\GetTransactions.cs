using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.System;

public sealed class GetTransactions : IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        context.User.EnsureRootAccess();

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var enumerable = await npgsql.QueryAsync<TransactionModel>(TransactionModel.Sql, new { });
        return enumerable;
    }

    private sealed class TransactionModel
    {
        public Guid Id { get; init; }

        public required string PaymentChannel { get; init; }
        
        public required string PaymentRef { get; init; }
        
        public decimal Amount { get; init; }

        public DateTime CreatedAt { get; init; }

        public static string Sql => @"
            select oph.id,
                   oph.amount,
                   oph.payment_channel,
                   oph.payment_ref,
                   oph.created_at
            from core.order_payment_history oph
            join core.order o on oph.order_id = o.id
            order by oph.created_at;
        ";
    }
}