﻿namespace Proptexx.Core.Entities
{
    public class SubscriptionPlan
    {
        public string Uid { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string? Description { get; set; }
        public string PlanFamilyName { get; set; }
        public string PlanFamilyUid { get; set; }
        public bool IsQuantityEditable { get; set; }
        public decimal MonthlyRate { get; set; }
        public decimal AnnualRate { get; set; }
        public decimal QuarterlyRate { get; set; }
        public decimal OneTimeRate { get; set; }
        public decimal SetupFee { get; set; }
        public bool IsActive { get; set; }
        public bool IsPerUser { get; set; }
        public int TrialPeriodDays { get; set; }
        public DateTime? TrialUntilDate { get; set; }
        public string UnitOfMeasure { get; set; }
        public List<string> PlanAddOns { get; set; }
        public int NumberOfSubscriptions { get; set; }
        public int Quota { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
