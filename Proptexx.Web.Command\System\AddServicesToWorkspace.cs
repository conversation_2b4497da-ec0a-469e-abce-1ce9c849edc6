using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using Dapper;
using Proptexx.Core;
using Proptexx.Core.Attributes;
using Proptexx.Core.Auth;
using Proptexx.Core.Constants;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Entities;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Services;
using Proptexx.Core.Stores;
using Proptexx.Core.Utils;

namespace Proptexx.Web.Command.System;

public sealed class AddServicesToWorkspace : ICommand
{

    [Required, GuidNotEmpty]
    public required Guid WorkspaceId { get; init; }

    [Required, MinLength(1)]
    public required IEnumerable<string> Services { get; init; }

    public bool? IsDemo { get; init; }

    public SubscriptionDescriptor? Subscription { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        context.User.EnsureRootAccess();

        var accountId = Guid.Parse(context.User.GetCallerId());
        DateTime expiresAt = DateTime.UtcNow.Date;
        if (Subscription?.ExpireMode == "specific")
        {
            expiresAt = Subscription.Specific ?? throw new InvalidOperationException("ExpireDate must be set when ExpireMode is 'specific'.");
        }
        else
        {
            expiresAt = Subscription?.DurationPeriod?.GetEndDate() ?? throw new InvalidOperationException("ValidFor must be set to calculate the expiration date.");
        }
        var requestedServices = Services
    .Select(x => new RequestedService { WorkspaceId = WorkspaceId, ServiceId = x, ExpiresAt = expiresAt });
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        await OrderManager.EnableServicesAsync(npgsql, WorkspaceId, requestedServices);

        var workspaceInfo = await npgsql.QuerySingleOrDefaultAsync<dynamic>(@"
            SELECT name FROM core.workspace WHERE id = @WorkspaceId
        ", new { WorkspaceId });
        var workspaceName = workspaceInfo?.name as string;
        if (string.IsNullOrEmpty(workspaceName))
            throw new InvalidOperationException("Workspace not found or missing name.");
        var domains = new[] { $"{workspaceName}.com", $"www.{workspaceName}.com" };

        
        var existingDomains = await npgsql.QueryAsync<string>(@"
            SELECT hostname FROM core.domain WHERE workspace_id = @WorkspaceId
        ", new { WorkspaceId });

        foreach (var domain in domains)
        {
            if (!existingDomains.Contains(domain))
            {
                await OnboardHelper.AddDomain(npgsql, WorkspaceId, domain);
            }
        }

        foreach (var serviceId in Services)
        {
            string clientName = serviceId == WidgetConstants.ServiceNames.WidgetAccess ? "widget" : serviceId;
            var clientSecretId = await GetClientSecretId(npgsql, WorkspaceId, clientName);
            if (clientSecretId == null)
            {
                continue;
            }    

            var subscription = new ClientSubscription
            {
                ClientSecretId = clientSecretId.Value,
                IsDemo = IsDemo ?? false,
                Type = Subscription?.Type ?? "",
                Quota = Subscription?.Quota ?? 0,
                TotalUsage = 0,
                Status = "Active",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Notes = string.Empty,
                PlanId = null,
                Domain = string.Empty,
                AllowedPaths = "[]",
                OutsetaAccountId = null,
            };
            await SubscriptionServiceAccess.Instance.CreateAsync(subscription, context.CancellationToken);
        }

    }

    private static async Task<Guid?> GetClientSecretId(Npgsql.NpgsqlConnection npgsql, Guid workspaceId, string clientName)
    {
        var sql = @"
            SELECT cs.id AS ClientSecretId
            FROM core.client c
            INNER JOIN core.client_secret cs ON cs.client_id = c.id
            WHERE c.workspace_id = @WorkspaceId
              AND c.name = @ClientName
            ORDER BY cs.created_at DESC
            LIMIT 1;
        ";
        var info = await npgsql.QuerySingleOrDefaultAsync<dynamic>(sql, new { WorkspaceId = workspaceId, ClientName = clientName });
        return info?.clientsecretid as Guid?;
    }
}

public sealed class DurationPeriodDescriptor
{
    public required int Amount { get; init; }

    public required string Unit { get; init; }

    public DateTime? GetEndDate()
    {
        return this.Unit switch
        {
            "day" => DateTime.UtcNow.AddDays(Amount),
            "week" => DateTime.UtcNow.AddDays(Amount * 7),
            "month" => DateTime.UtcNow.AddMonths(Amount),
            "year" => DateTime.UtcNow.AddYears(Amount),
            _ => throw new ArgumentOutOfRangeException(nameof(Unit))
        };
    }
}
public class SubscriptionDescriptor
{
    public string? Type { get; init; }
    public int? Duration { get; init; }
    public int? Quota { get; init; }
    public DateTime? Specific { get; init; }
    public DurationPeriodDescriptor? DurationPeriod { get; init; }
    public string? ExpireMode { get; init; }
}
