### POST request to API proxy served at `api.proptexx.ai` 
POST https://api.proptexx.ai/detect-architecture
Content-Type: application/json
Authorization: ApiKey 812321093810230992

{
    "instances": [{
      "image_url": "https://repstaticneu.azureedge.net/images/7001/L/NWM/Small/687f7451-97d6-44fa-88be-83abc340c8ae-0215797b-2f3a-4b09-8d96-4099e59da1a4.jpg?1739974482565"
    }]
}

###

POST https://api.proptexx.ai/detect-features
Content-Type: application/json
Authorization: ApiKey 812321093810230992

{
  "instances": [{
    "image_url": "https://repstaticneu.azureedge.net/images/7001/L/NWM/Small/687f7451-97d6-44fa-88be-83abc340c8ae-0215797b-2f3a-4b09-8d96-4099e59da1a4.jpg?1739974482565"
  }]
}

###

POST https://api.proptexx.ai/detect-material
Content-Type: application/json
Authorization: ApiKey 812321093810230992

{
  "instances": [{
    "image_url": "https://repstaticneu.azureedge.net/images/7001/L/NWM/Small/687f7451-97d6-44fa-88be-83abc340c8ae-0215797b-2f3a-4b09-8d96-4099e59da1a4.jpg?1739974482565"
  }]
}

###

POST https://api.proptexx.ai/detect-room-type
Content-Type: application/json
Authorization: ApiKey 812321093810230992

{
  "instances": [{
    "image_url": "https://repstaticneu.azureedge.net/images/7001/L/NWM/Small/687f7451-97d6-44fa-88be-83abc340c8ae-0215797b-2f3a-4b09-8d96-4099e59da1a4.jpg?1739974482565"
  }]
}