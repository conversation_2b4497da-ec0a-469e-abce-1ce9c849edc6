using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Workspace;

public sealed class GetClients : IQuery
{
    public string? ServiceType { get; init; }
    
    public Guid? WorkspaceId { get; init; }

    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var workspaceId = WorkspaceId ?? context.User.GetWorkspaceGuid();
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var result = await npgsql.QueryAsync<ClientModel>(ClientModel.Sql, new { _workspace_id = workspaceId, _service_type = this.ServiceType });
        return result;
    }
}

public class ClientModel
{
    public required Guid ClientId { get; init; }
    
    public Guid? SecretId { get; init; }
    
    public required string Name { get; init; }
    
    public string? Description { get; init; }
    public string? ApiKey => this.SecretId.HasValue ? ApiKeyService.CreateApiKey(this.SecretId.Value) : null;

    public string? Scopes { get; init; }
    
    public DateTime CreatedAt { get; init; }

    public static string Sql => @"
        select c.id as client_id,
               c.name,
               c.description,
               c.scopes,
               cs.id as secret_id,
               cs.created_at
        from core.client c
        left outer join core.client_secret cs on c.id = cs.client_id
        where c.workspace_id = :_workspace_id 
          and c.cancelled_at is null 
          and (:_service_type is null or lower(c.name) = lower(:_service_type))
        order by cs.created_at;
    ";
}
