using System.Diagnostics;
using System.Text.Json.Serialization;

namespace Proptexx.Web.Api.Responses;

public class ApiResponse
{
    private readonly Stopwatch _stopwatch = Stopwatch.StartNew();

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public Guid? Id { get; protected set; }

    public string? Error { get; private set; }

    public DateTime ReceivedAt => DateTime.UtcNow;

    public long TimeTaken => _stopwatch.ElapsedMilliseconds;
    
    public virtual void Success(Guid id)
    {
        this.Id = id;
    }

    public void Fail(string errorMessage)
    {
        Id = null;
        Error = errorMessage;
    }
}