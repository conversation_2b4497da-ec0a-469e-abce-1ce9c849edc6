using Microsoft.Extensions.Logging;
using Proptexx.AI.Models.CV;
using Proptexx.AI.Services;
using Proptexx.Core.AI;
using Proptexx.Core.Http;
using Proptexx.Core.Services;
using StackExchange.Redis;

namespace Proptexx.AI.Models.Flow;

public sealed class ComplianceCheck : IModel
{
    private readonly IComputerVisionClient _computerVisionClient;
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly ILoggerFactory _loggerFactory;
    private readonly IImageAssessmentClient _imageAssessmentService;

    public ComplianceCheck(
        IComputerVisionClient computerVisionClient, 
        IImageAssessmentClient imageAssessmentService,
        IConnectionMultiplexer connectionMultiplexer, 
        ILoggerFactory loggerFactory)
    {
        _computerVisionClient = computerVisionClient;
        _imageAssessmentService = imageAssessmentService;
        _connectionMultiplexer = connectionMultiplexer;
        _loggerFactory = loggerFactory;
    }
    
    public async Task<ModelResponse> InferAsync(ModelContext context)
    {
        if (context.Payload is null)
        {
            throw new ApplicationException("Payload is empty");
        }

        var imageUrl = PayloadService.GetRequiredString(context.Payload, "imageUrl");
        var assessment = await _imageAssessmentService.InspectImageAsync(imageUrl, context.CancellationToken);

        var tasks = new List<Task<ModelResponse>>
        {
            new WatermarkTextDetector(_computerVisionClient, _imageAssessmentService, _connectionMultiplexer, _loggerFactory).InferAsync(context.WorkspaceId, assessment, context.CancellationToken),
            new WatermarkLogoDetector(_computerVisionClient, _imageAssessmentService, _connectionMultiplexer, _loggerFactory).InferAsync(context.WorkspaceId, assessment, context.CancellationToken),
            new PersonDetector(_computerVisionClient, _imageAssessmentService, _connectionMultiplexer, _loggerFactory).InferAsync(context.WorkspaceId, assessment, context.CancellationToken),
            new AnimalDetector(_computerVisionClient, _imageAssessmentService, _connectionMultiplexer, _loggerFactory).InferAsync(context.WorkspaceId, assessment, context.CancellationToken),
            new SignDetector(_computerVisionClient, _imageAssessmentService, _connectionMultiplexer, _loggerFactory).InferAsync(context.WorkspaceId, assessment, context.CancellationToken),
            new LicensePlateDetector(_computerVisionClient, _imageAssessmentService, _connectionMultiplexer, _loggerFactory).InferAsync(context.WorkspaceId, assessment, context.CancellationToken),
            new ImageTamperingDetector(_computerVisionClient, _imageAssessmentService, _connectionMultiplexer, _loggerFactory).InferAsync(context.WorkspaceId, assessment, context.CancellationToken)
        };

        var results = await Task.WhenAll(tasks);
        var result = await JsonDocumentService.MergeResponsesAsync(results);

        return new ModelResponse
        {
            Document = result
        };
    }
}