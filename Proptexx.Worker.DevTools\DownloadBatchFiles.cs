using Dapper;
using Microsoft.Extensions.DependencyInjection;
using Npgsql;
using Proptexx.Core.Http;
using Proptexx.Core.Storage;

namespace Proptexx.Worker.DevTools;

public static class DownloadBatchFiles
{
    private const string Sql = @"
        select t.config->>'imageUrl' as image_url
        from batching.batch b
        join batching.task t on b.id = t.batch_id
        join batching.result r on t.id = r.task_id
        where b.created_at > current_timestamp - interval '10 hours'
        group by image_url;";
    
    public static async Task ExecuteAsync(ServiceProvider services, CancellationToken cancellationToken)
    {
        var assessmentClient = services.GetRequiredService<IImageAssessmentClient>();
        var storage = services.GetRequiredService<IStorageService>();

        var i = 1;
        var containerName = "tempimages";
        await storage.CreateDirectoryAsync(containerName, cancellationToken);

        var urls = await GetUrlsFromStringAsync(services, cancellationToken);
        var chunks = urls.Chunk(100);

        foreach (var chunk in chunks)
        {
            foreach (var url in chunk)
            {
                try
                {
                    var fileName = $"image_{i}";
                    var assessment = await assessmentClient.InspectImageAsync(url, cancellationToken);
                    await storage.UploadImageAsync(containerName, fileName, assessment.Base64String, assessment.MimeType);
                    i++;
                }
                catch (Exception e)
                {
                    Console.WriteLine(e.Message);
                }
            }

            await Task.Delay(1000, cancellationToken);
        }
    }

    private static async Task<IEnumerable<string>> GetUrlsFromDbAsync(ServiceProvider services, CancellationToken cancellationToken)
    {
        var dataSource = services.GetRequiredService<NpgsqlDataSource>();
        var npgsql = await dataSource.OpenConnectionAsync(cancellationToken);
        var urls = await npgsql.QueryAsync<string>(Sql);
        return urls;
    }

    private static Task<IEnumerable<string>> GetUrlsFromStringAsync(ServiceProvider services, CancellationToken cancellationToken)
    {
        return Task.FromResult<IEnumerable<string>>([
            "https://pictures.immobilienscout24.de/listings/0307870f-7b2d-475c-a793-f600b125bdaa-**********.jpg/ORIG/resize/1106x830>/format/jpg"
        ]);
    }
}