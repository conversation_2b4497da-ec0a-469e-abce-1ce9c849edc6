using System.Net.Http.Json;
using System.Text.Json;
using Microsoft.Extensions.Logging;

namespace Proptexx.Core.Http;

public interface ITextualClient
{
    Task<JsonDocument> SendRequest(string uri, object value, CancellationToken cancellationToken);
}

public sealed class TextualHttpClient(HttpClient httpClient, ILogger<TextualHttpClient> logger) 
    : BaseHttpClient(httpClient, logger), ITextualClient
{
    public async Task<JsonDocument> SendRequest(string uri, object payload, CancellationToken cancellationToken)
    {
        using var content = JsonContent.Create(payload);
        var response = await PostJsonAsync(uri, content, cancellationToken);
        return response;
    }
}