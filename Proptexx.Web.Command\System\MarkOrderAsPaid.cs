using System.ComponentModel.DataAnnotations;
using Proptexx.Core;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Attributes;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Services;

namespace Proptexx.Web.Command.System;

public sealed class MarkOrderAsPaid : ICommand
{
    [Required, GuidNotEmpty]
    public required Guid OrderId { get; init; }

    [Required]
    public required string Channel { get; init; }

    [Required]
    public required string Reference { get; init; }

    [Required]
    public required PriceDescriptor AmountPaid { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        context.User.EnsureRootAccess();
        var callerId = Guid.Parse(context.User.GetCallerId());

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        await using var trx = await npgsql.BeginTransactionAsync(context.CancellationToken);

        try
        {
            await OrderManager.MarkAsPaidAsync(npgsql, OrderId);
            await OrderManager.RecordPaymentAsync(npgsql, OrderId, this.AmountPaid.Price, this.Channel, this.Reference, context.CancellationToken);
            await OrderManager.EnableServicesAsync(npgsql, OrderId, context.CancellationToken);
            await trx.CommitAsync(context.CancellationToken);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            await trx.RollbackAsync(context.CancellationToken);
            throw;
        }
    }
}
