using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Configuration;

namespace Proptexx.Core.Storage;

public sealed class AzureStorageService : IStorageService
{
    private readonly BlobServiceClient _blobServiceClient;

    public AzureStorageService(IConfiguration configuration)
    {
        var connStr = configuration.GetConnectionString("AzureStorage");
        connStr ??= "DefaultEndpointsProtocol=https;AccountName=proptexxstorage1;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net";
        _blobServiceClient = new BlobServiceClient(connStr);
    }

    public Task<string> UploadImageAsync(string containerName, string fileName, string base64Image, string contentType)
    {
        var imageBytes = Convert.FromBase64String(base64Image);
        return UploadImageAsync(containerName, fileName, imageBytes, contentType);
    }

    public async Task<string> UploadImageAsync(string containerName, string fileName, byte[] byteArrayImage, string contentType)
    {
        using var stream = new MemoryStream(byteArrayImage);
        var result = await UploadImageAsync(containerName, fileName, stream, contentType);
        return result;
    }

    public async Task<string> UploadImageAsync(string containerName, string fileName, Stream imageStream, string contentType)
    {
        imageStream.Position = 0;
        var containerClient = _blobServiceClient.GetBlobContainerClient(containerName);
        await containerClient.CreateIfNotExistsAsync(PublicAccessType.Blob);

        var blobClient = containerClient.GetBlobClient(fileName);
        var headers = new BlobHttpHeaders { ContentType = contentType };
        await blobClient.UploadAsync(imageStream, headers);

        var result = blobClient.Uri.ToString();
        return result;
    }

    public async Task CreateDirectoryAsync(string directoryName, CancellationToken cancellationToken = default)
    {
        var containerClient = _blobServiceClient.GetBlobContainerClient(directoryName);
        await containerClient.CreateIfNotExistsAsync(PublicAccessType.Blob, cancellationToken: cancellationToken);
    }

    // public static string GenerateSasTokenUrl(string accountName, string accountKey, string containerName, string blobName)
    // {
    //     const string permissions = "rwdlaciytfx"; // Permissions as per the Azure portal example
    //     var startTime = DateTime.UtcNow.AddMinutes(-5).ToString("yyyy-MM-ddTHH:mm:ssZ"); // Allow some clock skew
    //     var expiryTime = DateTime.UtcNow.AddMinutes(60).ToString("yyyy-MM-ddTHH:mm:ssZ");
    //
    //     // Canonicalized resource: /blob/accountname/containername/blobname
    //     var canonicalizedResource = $"/blob/{accountName}/{containerName}/{blobName}";
    //
    //     // String-to-sign format for Blob Service SAS
    //     var stringToSign = $"{permissions}\n" +  // Permissions
    //                        $"{startTime}\n" +   // Start time
    //                        $"{expiryTime}\n" +  // Expiry time
    //                        $"{canonicalizedResource}\n" + // Canonicalized resource
    //                        "\n" +               // Identifier (optional, empty in this case)
    //                        "\n" +               // IP address (optional, empty in this case)
    //                        "https\n" +          // Protocol
    //                        "2021-04-10\n";      // API version
    //
    //     var signature = CreateHmacSignature(stringToSign, accountKey);
    //
    //     // Construct SAS token
    //     var sasToken = $"sp={permissions}&st={Uri.EscapeDataString(startTime)}&se={Uri.EscapeDataString(expiryTime)}&spr=https&sv=2021-04-10&sr=b&sig={Uri.EscapeDataString(signature)}";
    //     var fullUrl = $"https://{accountName}.blob.core.windows.net/{containerName}/{blobName}?{sasToken}";
    //     return fullUrl;
    // }
    //
    // private static string CreateHmacSignature(string stringToSign, string accountKey)
    // {
    //     var key = Convert.FromBase64String(accountKey);
    //     using var hmacSha256 = new HMACSHA256(key);
    //     var dataToHmac = Encoding.UTF8.GetBytes(stringToSign);
    //     var hash = hmacSha256.ComputeHash(dataToHmac);
    //     return Convert.ToBase64String(hash);
    // }
}
