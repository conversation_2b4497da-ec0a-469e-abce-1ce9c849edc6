using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations
{
    [Migration(250707002)]
    public class AddSubscriptionPlanForeignKey : FluentMigrator.Migration
    {
        public override void Up()
        {
            // Add foreign key constraint between client_subscription.plan_id and subscription_plan.uid
            // The plan_id column is already nullable, so we just need to add the constraint
            Create.ForeignKey("fk_client_subscription_plan_id")
                .FromTable("client_subscription").InSchema("core").ForeignColumn("plan_id")
                .ToTable("subscription_plan").InSchema("core").PrimaryColumn("uid");

            // Add comment for the foreign key relationship
            Execute.Sql(@"
                COMMENT ON CONSTRAINT fk_client_subscription_plan_id ON core.client_subscription 
                IS 'Foreign key to subscription_plan.uid - nullable relationship';
            ");
        }

        public override void Down()
        {
            // Drop the foreign key constraint
            Delete.ForeignKey("fk_client_subscription_plan_id")
                .OnTable("client_subscription").InSchema("core");
        }
    }
} 