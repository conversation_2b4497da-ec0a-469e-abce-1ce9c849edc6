using System.Text.Json;
using Npgsql;
using Proptexx.AI;
using Proptexx.Core;
using Proptexx.Core.Entities;
using Proptexx.Core.Json;
using Proptexx.Web.Api.Helpers;
using Proptexx.Web.Api.Responses;
using StackExchange.Redis;

namespace Proptexx.Web.Api.Handlers;

internal sealed class SequentialHandler
{
    private readonly IServiceProvider _services;
    private readonly ILogger<SequentialHandler> _logger;
    private readonly NpgsqlDataSource _dataSource;
    private readonly IDatabase _redis;

    public SequentialHandler(
        IServiceProvider services,
        ILogger<SequentialHandler> logger,
        NpgsqlDataSource dataSource,
        IConnectionMultiplexer connectionMultiplexer)
    {
        _services = services;
        _logger = logger;
        _dataSource = dataSource;
        _redis = connectionMultiplexer.GetDatabase();
    }
    
    public async Task InvokeAsync(HttpContext context)
    {
        var batchId = Guid.NewGuid();
        var response = new SequentialApiResponse();

        try
        {
            if (!context.Request.RouteValues.TryGetValue("id", out var tmpSeqId) ||
                !Guid.TryParse(tmpSeqId?.ToString(), out var id))
            {
                throw new ApplicationException("Sequence `id` is missing");
            }

            var workspaceId = context.User.GetWorkspaceGuid();
            var workspaceName = context.User.TryGetWorkspaceName() ?? workspaceId.ToString();
            var doc = await JsonDocument.ParseAsync(context.Request.Body);
            var values = await _redis.SetMembersAsync($"sequence:{id}");
            
            List<JsonElement> elements;
            if (doc.RootElement.ValueKind == JsonValueKind.Array)
            {
                elements = doc.RootElement.EnumerateArray().ToList();
            }
            else if (doc.RootElement.ValueKind == JsonValueKind.Object)
            {
                elements = [doc.RootElement];
            }
            else
            {
                throw new ApplicationException("The request body must be either an object or array");
            }
            
            var i = 0;
            var batchTasks = new List<BatchTask>();
            foreach (var v in values)
            {
                if (v.IsNullOrEmpty) continue;
                var modelName = v.ToString();

                if (!_services.HasModel(modelName))
                {
                    throw new ApplicationException($"Requested model '{modelName}' is not found");
                }

                foreach (var el in elements)
                {
                    var batchTask = BatchHelper.ParsePayloadForBatchItem(batchId, modelName, el);
                    batchTasks.Add(batchTask);
                }

                i++;
            }

            var batch = new Batch
            {
                Id = batchId,
                WorkspaceId = workspaceId,
                AccountId = context.User.TryGetCallerGuid(),
                CallbackUrl = BatchHelper.GetCallbackUrl(context),
                IsSync = false
            };
            
            // var meta = new BatchRequestMeta
            // {
            //     BatchId = batch.Id,
            //     Language = context.Request.Headers.AcceptLanguage,
            //     Referrer = context.Request.Headers.Referer,
            //     ClientIp = context.Connection.RemoteIpAddress?.ToString(),
            //     CorrelationId = context.TraceIdentifier,
            //     UserAgent = context.Request.Headers.UserAgent
            // };

            await using var npgsql = await _dataSource.OpenConnectionAsync(context.RequestAborted);
            await BatchHelper.PersistBatchAsync(_logger, npgsql, batch, batchTasks, context.RequestAborted);
            await BatchHelper.PushToRedisAsync(_redis, workspaceName, batch, batchTasks, null);

            context.Response.StatusCode = StatusCodes.Status200OK;
            response.Success(batchId);
        }
        catch (ApplicationException e)
        {
            context.Response.StatusCode = StatusCodes.Status400BadRequest;
            response.Fail(e.Message);
        }
        catch (UnauthorizedAccessException e)
        {
            context.Response.StatusCode = StatusCodes.Status401Unauthorized;
            response.Fail(e.Message);
        }
        catch (JsonException)
        {
            context.Response.StatusCode = StatusCodes.Status400BadRequest;
            response.Fail("Unable to parse the request payload");
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            context.Response.StatusCode = StatusCodes.Status500InternalServerError;
            response.Fail("The operation exited with an error. We have been notified and are looking into it");
        }
        finally
        {
            await context.Response.WriteAsJsonAsync(response, JsonDefaults.JsonSerializerOptions);
        }
    }
}

public sealed class SequentialApiResponse : ApiResponse
{
}

/*
 * 
   RedisValue[] list =
   [
       new RedisValue("cv/AltTextGenerator"),
       new RedisValue("cv/AnimalDetector"),
       new RedisValue("cv/ApplianceFeatureDetector"),
       new RedisValue("cv/ArchitectureDetector"),
       new RedisValue("cv/EmptinessDetector"),
       new RedisValue("cv/ExteriorFeatureDetector"),
       new RedisValue("cv/FurnitureConditionDetector"),
       new RedisValue("cv/ImageQualityAssess"),
       new RedisValue("cv/ImageTamperingDetector"),
       new RedisValue("cv/InteriorFeatureDetector"),
       new RedisValue("cv/LicensePlateDetector"),
       new RedisValue("cv/MaterialDetector"),
       new RedisValue("cv/ObjectDetector"),
       new RedisValue("cv/PersonDetector"),
       new RedisValue("cv/PropertyConditionDetector"),
       new RedisValue("cv/RoomSceneAnalyzer"),
       new RedisValue("cv/RoomSceneEmptinessDetector"),
       new RedisValue("cv/SignDetector"),
       new RedisValue("cv/ViewDetector"),
       new RedisValue("cv/WatermarkLogoDetector"),
       new RedisValue("cv/WatermarkTextDetector")
   ];
 */