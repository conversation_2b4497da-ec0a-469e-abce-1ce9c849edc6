using Microsoft.Extensions.Logging;
using Proptexx.Core.Interface;
using Proptexx.Core.Constants;
using Npgsql;
using Dapper;
using Proptexx.Core.Entities;

namespace Proptexx.Core.Services;

/// <summary>
/// Service for validating subscription API keys
/// Implements ISubscriptionValidationService interface
/// Provides comprehensive validation for OneTime and PayAsYouGo subscription types
/// </summary>
public class SubscriptionValidationService : ISubscriptionValidationService
{
    private readonly ILogger<SubscriptionValidationService> _logger;
    private readonly ISubscriptionService _subscriptionService;
    private readonly ISubscriptionUsageService _subscriptionUsageService;
    private readonly NpgsqlDataSource _dataSource;

    public SubscriptionValidationService(
        ILogger<SubscriptionValidationService> logger,
        ISubscriptionService subscriptionService,
        ISubscriptionUsageService subscriptionUsageService,
        NpgsqlDataSource dataSource)
    {
        _logger = logger;
        _subscriptionService = subscriptionService;
        _subscriptionUsageService = subscriptionUsageService;
        _dataSource = dataSource;
    }

    /// <summary>
    /// Comprehensive API key validation for OneTime and PayAsYouGo subscriptions
    /// 
    /// For OneTime subscriptions:
    /// - Quota is required (quota > 0)
    /// - Usage must be <= quota
    /// - Expiry date validation if present (null expiry is allowed)
    /// 
    /// For PayAsYouGo subscriptions:
    /// - Quota is optional (can be 0 for unlimited)
    /// - If quota > 0, usage must be <= quota
    /// - Expiry date validation if present
    /// - Both quota and expiry can be enforced simultaneously
    /// </summary>
    public async Task<SubscriptionValidationResult> ValidateAsync(string apiKey, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Validating API key: {ApiKey}", apiKey);

            // Step 1: Parse and validate API key format
            if (string.IsNullOrWhiteSpace(apiKey))
            {
                return CreateFailureResult("API key is required", null);
            }

            // Get client_secret_id from API key
            var clientSecretId = await GetClientSecretIdFromApiKeyAsync(apiKey, cancellationToken);
            if (clientSecretId == null)
            {
                _logger.LogWarning("Invalid API key format or key not found: {ApiKey}", apiKey);
                return CreateFailureResult("Invalid API key", null);
            }

            // Step 2: Get subscription and client_secret data
            const string sql = @"
                SELECT 
                    cs.id as subscription_id,
                    cs.type as subscription_type,
                    cs.quota,
                    cs.total_usage,
                    cs.status as subscription_status,
                    cs.is_demo,
                    cs.plan_id,
                    cs.created_at as subscription_created_at,
                    cs.updated_at as subscription_updated_at,
                    cs.notes,
                    cs.domain,
                    cs.allowed_paths,
                    cs.outseta_account_id,
                    client_secret.id as client_secret_id,
                    client_secret.expired_at as client_expired_at,
                    client_secret.total_usage as client_total_usage,
                    client_secret.created_at as client_created_at
                FROM core.client_subscription cs
                JOIN core.client_secret ON cs.client_secret_id = client_secret.id
                WHERE cs.client_secret_id = @ClientSecretId
                AND cs.status = @ActiveStatus
                ORDER BY cs.created_at DESC
                LIMIT 1";

            await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
            var data = await connection.QuerySingleOrDefaultAsync<dynamic>(sql, new { 
                ClientSecretId = clientSecretId,
                ActiveStatus = SubscriptionConstants.Status.Active 
            });

            if (data == null)
            {
                _logger.LogWarning("No active subscription found for API key: {ApiKey}. Attempting to auto-create PayAsYouGo subscription...", apiKey);
                var created = await _subscriptionService.EnsureClientSubscriptionExists(apiKey, cancellationToken: cancellationToken);
                if (created != null)
                {
                    // Try to fetch again after creation
                    data = await connection.QuerySingleOrDefaultAsync<dynamic>(sql, new { 
                        ClientSecretId = clientSecretId,
                        ActiveStatus = SubscriptionConstants.Status.Active 
                    });
                    if (data == null)
                    {
                        _logger.LogError("Failed to fetch subscription after auto-creation for API key: {ApiKey}", apiKey);
                        return CreateFailureResult("Failed to create PayAsYouGo subscription", null);
                    }
                }
                else
                {
                    return CreateFailureResult("No active subscription found and failed to create PayAsYouGo", null);
                }
            }

            // Step 3: Create subscription entity for response
            var subscription = CreateSubscriptionEntity(data);

            // Step 4: Check client_secret expiry first
            DateTime? clientExpiresAt = data.client_expired_at;
            if (clientExpiresAt.HasValue && clientExpiresAt.Value <= DateTime.UtcNow)
            {
                _logger.LogWarning("API key has expired: {ApiKey}, ExpiredAt: {ExpiredAt}", apiKey, clientExpiresAt.Value);
                return CreateFailureResult("API key has expired", subscription);
            }

            // Step 5: Validate based on subscription type
            string subscriptionType = data.subscription_type?.ToString() ?? "";
            var validationResult = subscriptionType.ToLower() switch
            {
                "onetime" => await ValidateOneTimeSubscriptionAsync(data, subscription, cancellationToken),
                "payasyougo" => await ValidatePayAsYouGoSubscriptionAsync(data, subscription, cancellationToken),
                _ => CreateFailureResult($"Unsupported subscription type: {subscriptionType}", subscription)
            };

            if (validationResult.IsValid)
            {
                _logger.LogDebug("API key validation successful: {ApiKey}, Type: {Type}", apiKey, subscriptionType);
            }
            else
            {
                _logger.LogWarning("API key validation failed: {ApiKey}, Reason: {Reason}", apiKey, (string)validationResult.Reason);
             }

            return validationResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating API key: {ApiKey}", apiKey);
            return CreateFailureResult("Validation error occurred", null);
        }
    }

    /// <summary>
    /// Validate OneTime subscription
    /// Requirements:
    /// - Quota is required (quota > 0)
    /// - Usage must be <= quota
    /// - Expiry date validation if present
    /// </summary>
    private async Task<SubscriptionValidationResult> ValidateOneTimeSubscriptionAsync(
        dynamic data, 
        ClientSubscription subscription, 
        CancellationToken cancellationToken)
    {
        long quota = data.quota ?? 0;
        long usage = data.total_usage ?? 0;
        DateTime? clientExpiresAt = data.client_expired_at;

        // OneTime subscriptions require quota > 0
        if (quota <= 0)
        {
            return CreateFailureResult("OneTime subscription requires a valid quota", subscription);
        }

        // Check quota vs usage
        if (usage >= quota)
        {
            double usagePercentage = (double)usage / quota * 100;
            int remainingQuota = (int)Math.Max(0, quota - usage);
            return new SubscriptionValidationResult
            {
                IsValid = false,
                Reason = $"Quota exceeded: {usage}/{quota} requests used",
                Subscription = subscription,
                UsagePercentage = usagePercentage,
                RemainingQuota = remainingQuota
            };
        }

        // Check expiry if present (null expiry is allowed for OneTime)
        if (clientExpiresAt.HasValue && clientExpiresAt.Value <= DateTime.UtcNow)
        {
            return CreateFailureResult("Subscription has expired", subscription);
        }

        // Validation successful
        double validUsagePercentage = (double)usage / quota * 100;
        int validRemainingQuota = (int)(quota - usage);

        return new SubscriptionValidationResult
        {
            IsValid = true,
            Reason = "Valid OneTime subscription",
            Subscription = subscription,
            UsagePercentage = validUsagePercentage,
            RemainingQuota = validRemainingQuota
        };
    }

    /// <summary>
    /// Validate PayAsYouGo subscription
    /// Requirements:
    /// - Quota is optional (can be 0 for unlimited)
    /// - If quota > 0, usage must be <= quota
    /// - Expiry date validation if present
    /// - Both quota and expiry can be enforced simultaneously
    /// </summary>
    private async Task<SubscriptionValidationResult> ValidatePayAsYouGoSubscriptionAsync(
        dynamic data, 
        ClientSubscription subscription, 
        CancellationToken cancellationToken)
    {
        long quota = data.quota ?? 0;
        long usage = data.total_usage ?? 0;
        DateTime? clientExpiresAt = data.client_expired_at;

        // Check expiry if present
        if (clientExpiresAt.HasValue && clientExpiresAt.Value <= DateTime.UtcNow)
        {
            return CreateFailureResult("PayAsYouGo subscription has expired", subscription);
        }

        // Check quota if it's set (quota = 0 means unlimited for PayAsYouGo)
        if (quota > 0 && usage >= quota)
        {
            double usagePercentage = (double)usage / quota * 100;
            int remainingQuota = (int)Math.Max(0, quota - usage);
            return new SubscriptionValidationResult
            {
                IsValid = false,
                Reason = $"PayAsYouGo quota exceeded: {usage}/{quota} requests used",
                Subscription = subscription,
                UsagePercentage = usagePercentage,
                RemainingQuota = remainingQuota
            };
        }

        // Validation successful
        double validUsagePercentage = quota > 0 ? (double)usage / quota * 100 : 0;
        int validRemainingQuota = quota > 0 ? (int)(quota - usage) : int.MaxValue;

        return new SubscriptionValidationResult
        {
            IsValid = true,
            Reason = quota > 0 ? "Valid PayAsYouGo subscription with quota" : "Valid PayAsYouGo subscription (unlimited)",
            Subscription = subscription,
            UsagePercentage = validUsagePercentage,
            RemainingQuota = validRemainingQuota
        };
    }

    /// <summary>
    /// Get client_secret_id from API key using ApiKeyService
    /// </summary>
    private async Task<Guid?> GetClientSecretIdFromApiKeyAsync(string apiKey, CancellationToken cancellationToken)
    {
        try
        {
            // Use ApiKeyService.ParseApiKey for parsing
            var secretIdString = ApiKeyService.ParseApiKey(apiKey);
            if (string.IsNullOrWhiteSpace(secretIdString))
            {
                return null;
            }

            if (Guid.TryParse(secretIdString, out var secretId))
            {
                // Verify the API key exists in database
                const string sql = "SELECT COUNT(*) FROM core.client_secret WHERE id = @SecretId";
                await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
                var count = await connection.ExecuteScalarAsync<int>(sql, new { SecretId = secretId });
                
                return count > 0 ? secretId : null;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing API key: {ApiKey}", apiKey);
            return null;
        }
    }

    /// <summary>
    /// Create ClientSubscription entity from dynamic data
    /// </summary>
    private static ClientSubscription CreateSubscriptionEntity(dynamic data)
    {
        return new ClientSubscription
        {
            Id = data.subscription_id,
            ClientSecretId = data.client_secret_id,
            IsDemo = data.is_demo ?? false,
            Type = data.subscription_type?.ToString() ?? "",
            Quota = data.quota ?? 0,
            TotalUsage = data.total_usage ?? 0,
            Status = data.subscription_status?.ToString() ?? "",
            CreatedAt = data.subscription_created_at ?? DateTime.UtcNow,
            UpdatedAt = data.subscription_updated_at ?? DateTime.UtcNow,
            Notes = data.notes?.ToString(),
            PlanId = data.plan_id?.ToString(),
            Domain = data.domain?.ToString(),
            AllowedPaths = data.allowed_paths?.ToString(),
            OutsetaAccountId = data.outseta_account_id?.ToString()
        };
    }

    /// <summary>
    /// Create failure validation result
    /// </summary>
    private static SubscriptionValidationResult CreateFailureResult(string reason, ClientSubscription? subscription)
    {
        return new SubscriptionValidationResult
        {
            IsValid = false,
            Reason = reason,
            Subscription = subscription,
            UsagePercentage = 0.0,
            RemainingQuota = 0
        };
    }
} 