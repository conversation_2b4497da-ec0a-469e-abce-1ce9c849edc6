﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Proptexx.Core.Helpers
{
    public static class ObjectHelper
    {
        public static IDictionary<string, object> ToDictionary(this object obj)
        {
            if (obj == null)
                throw new ArgumentNullException(nameof(obj));

            return obj.GetType()
                      .GetProperties()
                      .Where(prop => prop.CanRead)
                      .ToDictionary(
                          prop => prop.Name,
                          prop => prop.GetValue(obj, null) ?? DBNull.Value
                      );
        }
    }
}
