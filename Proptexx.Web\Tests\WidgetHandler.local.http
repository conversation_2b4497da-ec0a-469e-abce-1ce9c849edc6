
### Authenticate
# @name authen
POST https://auth.dev.local/_auth
Content-Type: application/json
Authorization: ApiKey MDEwNzVkMDktMmI4OC00ZjM1LWJlMTYtODVmN2M4NjE2YjYx

{}

### Extract token using correct syntax for property with $ sign
@accessToken = {{authen.response.body.$.$accessToken}}


###
POST https://api.dev.local/widget/render
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "apiKey": "MDEwNzVkMDktMmI4OC00ZjM1LWJlMTYtODVmN2M4NjE2YjYx",
  "pageUrl": "https://portal.dev.local/products"
}


###
GET https://api.dev.local/widget/install-snippet?apiKey=MDEwNzVkMDktMmI4OC00ZjM1LWJlMTYtODVmN2M4NjE2YjYx
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{}