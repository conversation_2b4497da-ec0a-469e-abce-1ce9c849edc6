using System.Security.Cryptography;
using System.Text;

namespace Proptexx.Core.Services;

public static class PasswordService
{
    private const int FormatSaltSizeInBytes = 16; // 128 bits
    private const int FormatIterations = 10000;
    private const int FormatHashSizeInBytes = 64; // 256 bits

    public static string AlphanumericChars = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789";
    public static HashAlgorithmName HashAlgorithm = HashAlgorithmName.SHA512;
    
    public static (string hash, string salt) GenerateSecret(int length = 8)
    {
        var secret = GeneratePassword(length);
        return GenerateSecret(secret);
    }

    public static (string hash, string salt) GenerateSecret(string? passwd)
    {
        // Generate a random salt
        var salt = new byte[FormatSaltSizeInBytes];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(salt);

        // Generate the hash using PBKDF2
        var password = passwd ?? GeneratePassword(8);
        var pbkdf2 = new Rfc2898DeriveBytes(password, salt, FormatIterations, HashAlgorithm);
        var hash = pbkdf2.GetBytes(FormatHashSizeInBytes);

        return (Convert.ToBase64String(hash), Convert.ToBase64String(salt));
    }

    public static string GeneratePassword(int passwordLength)
    {
        var password = new StringBuilder();

        using var rng = RandomNumberGenerator.Create();
        var randomBytes = new byte[passwordLength];
        rng.GetBytes(randomBytes);

        for (var i = 0; i < passwordLength; i++)
        {
            var index = randomBytes[i] % AlphanumericChars.Length;
            password.Append(AlphanumericChars[index]);
        }

        return password.ToString();
    }

    public static bool VerifyPassword(string password, string hash, string salt)
    {
        try
        {
            var storedSalt = Convert.FromBase64String(salt);

            // Generate the hash using the incoming password and retrieved salt
            using var pbkdf2Verification = new Rfc2898DeriveBytes(password, storedSalt, FormatIterations, HashAlgorithmName.SHA512);
            var generatedHash = pbkdf2Verification.GetBytes(FormatHashSizeInBytes);

            // Convert the generated hash to a base64 string for comparison
            var generatedHashString = Convert.ToBase64String(generatedHash);

            // Compare the generated hash with the stored hash
            return generatedHashString.Equals(hash);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }

        return false;
    }
}