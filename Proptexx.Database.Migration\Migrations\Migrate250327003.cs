using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations;

[Migration(250327003)]
public class Migrate250327003 : FluentMigrator.Migration
{
    public override void Up()
    {
        // === Update AI Requests Materialized Views ===
        // Drop existing AI views
        Execute.Sql(@"DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_ai_requests_summary CASCADE;");
        Execute.Sql(@"DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_ai_requests_today CASCADE;");
        Execute.Sql(@"DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_ai_requests_past CASCADE;");

        // Recreate AI Requests Today with workspace_id
        Execute.Sql(@"
            CREATE MATERIALIZED VIEW telemetry.mv_ai_requests_today AS
            SELECT 
                a.workspace_id,
                a.created_at::date AS day,
                COALESCE(c.title, 'Unknown') AS client_name,
                a.processed_endpoint AS endpoint,
                COUNT(*) AS request_count
            FROM telemetry.api_logs a
            LEFT JOIN crm.workspace c ON a.workspace_id = c.id
            WHERE a.is_ai_request
              AND a.created_at >= date_trunc('day', now() AT TIME ZONE 'UTC')
              AND a.created_at < now() AT TIME ZONE 'UTC'
            GROUP BY a.workspace_id, a.created_at::date, c.title, a.processed_endpoint
            WITH NO DATA;
        ");
        Execute.Sql(@"CREATE UNIQUE INDEX idx_mv_ai_requests_today_unique ON telemetry.mv_ai_requests_today(day, client_name, endpoint, workspace_id);");

        // Recreate AI Requests Past with workspace_id
        Execute.Sql(@"
            CREATE MATERIALIZED VIEW telemetry.mv_ai_requests_past AS
            SELECT 
                a.workspace_id,
                a.created_at::date AS day,
                COALESCE(c.title, 'Unknown') AS client_name,
                a.processed_endpoint AS endpoint,
                COUNT(*) AS request_count
            FROM telemetry.api_logs a
            LEFT JOIN crm.workspace c ON a.workspace_id = c.id
            WHERE a.is_ai_request
              AND a.created_at < date_trunc('day', now() AT TIME ZONE 'UTC')
            GROUP BY a.workspace_id, a.created_at::date, c.title, a.processed_endpoint
            WITH NO DATA;
        ");
        Execute.Sql(@"CREATE UNIQUE INDEX idx_mv_ai_requests_past_unique ON telemetry.mv_ai_requests_past(day, client_name, endpoint, workspace_id);");

        // Recreate Combined AI Requests view with workspace_id
        Execute.Sql(@"
            CREATE MATERIALIZED VIEW telemetry.mv_ai_requests_summary AS
            SELECT 
                workspace_id,
                day,
                client_name,
                endpoint,
                SUM(request_count) AS request_count
            FROM (
                SELECT * FROM telemetry.mv_ai_requests_today
                UNION ALL
                SELECT * FROM telemetry.mv_ai_requests_past
            ) AS combined
            GROUP BY workspace_id, day, client_name, endpoint
            WITH NO DATA;
        ");
        Execute.Sql(@"CREATE UNIQUE INDEX idx_mv_ai_requests_summary_unique ON telemetry.mv_ai_requests_summary(day, client_name, endpoint, workspace_id);");


        // === Update Batch Requests Materialized Views ===
        // Drop existing Batch views
        Execute.Sql(@"DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_batch_requests_summary CASCADE;");
        Execute.Sql(@"DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_batch_requests_today CASCADE;");
        Execute.Sql(@"DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_batch_requests_past CASCADE;");

        // Recreate Batch Requests Today with workspace_id
        Execute.Sql(@"
            CREATE MATERIALIZED VIEW telemetry.mv_batch_requests_today AS
            SELECT 
                e.workspace_id,
                e.created_at::date AS day,
                COALESCE(c.title, 'Unknown') AS workspace_name,
                COUNT(*) AS batch_count
            FROM telemetry.api_logs e
            LEFT JOIN crm.workspace c ON e.workspace_id = c.id
            WHERE e.is_batch
              AND e.created_at >= date_trunc('day', now() AT TIME ZONE 'UTC')
              AND e.created_at < now() AT TIME ZONE 'UTC'
            GROUP BY e.workspace_id, e.created_at::date, c.title
            WITH NO DATA;
        ");
        Execute.Sql(@"CREATE UNIQUE INDEX idx_mv_batch_requests_today_unique ON telemetry.mv_batch_requests_today(day, workspace_name, workspace_id);");

        // Recreate Batch Requests Past with workspace_id
        Execute.Sql(@"
            CREATE MATERIALIZED VIEW telemetry.mv_batch_requests_past AS
            SELECT 
                e.workspace_id,
                e.created_at::date AS day,
                COALESCE(c.title, 'Unknown') AS workspace_name,
                COUNT(*) AS batch_count
            FROM telemetry.api_logs e
            LEFT JOIN crm.workspace c ON e.workspace_id = c.id
            WHERE e.is_batch
              AND e.created_at < date_trunc('day', now() AT TIME ZONE 'UTC')
            GROUP BY e.workspace_id, e.created_at::date, c.title
            WITH NO DATA;
        ");
        Execute.Sql(@"CREATE UNIQUE INDEX idx_mv_batch_requests_past_unique ON telemetry.mv_batch_requests_past(day, workspace_name, workspace_id);");

        // Recreate Combined Batch Requests view with workspace_id
        Execute.Sql(@"
            CREATE MATERIALIZED VIEW telemetry.mv_batch_requests_summary AS
            SELECT 
                workspace_id,
                day,
                workspace_name,
                SUM(batch_count) AS batch_count
            FROM (
                SELECT * FROM telemetry.mv_batch_requests_today
                UNION ALL
                SELECT * FROM telemetry.mv_batch_requests_past
            ) AS combined
            GROUP BY workspace_id, day, workspace_name
            WITH NO DATA;
        ");
        Execute.Sql(@"CREATE UNIQUE INDEX idx_mv_batch_requests_summary_unique ON telemetry.mv_batch_requests_summary(day, workspace_name, workspace_id);");
    }

    public override void Down()
    {
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_ai_requests_summary CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_ai_requests_today CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_ai_requests_past CASCADE;");

        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_batch_requests_summary CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_batch_requests_today CASCADE;");
        Execute.Sql("DROP MATERIALIZED VIEW IF EXISTS telemetry.mv_batch_requests_past CASCADE;");
    }
}