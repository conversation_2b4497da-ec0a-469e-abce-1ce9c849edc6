using Npgsql;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Redis;

namespace Proptexx.Worker.DataSync;

public static class AccountSync
{
    private const string Sql = """
                               select a.id,
                                      a.first_name,
                                      a.family_name,
                                      a.cancelled_at,
                                      a.is_root,
                                      sec.hash,
                                      sec.salt
                               from core.account a
                               left outer join (
                                   select sec.* 
                                   from core.account_secret sec 
                                   where sec.expired_at is null or sec.expired_at > current_timestamp
                                   order by sec.account_id, sec.expired_at nulls first, sec.created_at desc
                                   limit 1
                               ) as sec on a.id = sec.account_id
                               where a.cancelled_at is null or a.cancelled_at > current_timestamp
                               """;

    internal static async Task SyncAsync(IServiceProvider services, CancellationToken cancellationToken)
    {
        var dataSource = services.GetRequiredService<NpgsqlDataSource>();
        var accountStore = services.GetRequiredService<AccountHashStore>();

        await using var npgsql = await dataSource.OpenConnectionAsync(cancellationToken);
        var accounts = await npgsql.ListQueryAsync<AccountHashStore.AccountModel>(Sql);
        accountStore.Persist(accounts);
    }
}