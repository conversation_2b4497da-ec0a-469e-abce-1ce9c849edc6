using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class AccountData : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; } = Guid.NewGuid();

    public required Guid AccountId { get; init; }

    public required string DataKey { get; init; }
    
    public string? DataValue { get; init; }

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;
    
    public string GetDbRef() => "core.account_data";
}