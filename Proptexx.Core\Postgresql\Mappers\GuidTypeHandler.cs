using Npgsql;
using NpgsqlTypes;

namespace Proptexx.Core.Postgresql.Mappers;

public class GuidTypeHandler : NpgsqlTypeMapper<Guid>
{
    public override Guid Parse(object value)
    {
        if (value is not Guid guid || value is DBNull)
        {
            throw new InvalidOperationException("Cannot convert NULL to a Guid");
        }

        if (guid.Equals(Guid.Empty))
        {
            throw new InvalidOperationException("Cannot convert empty GUID from the database");
        }

        return guid;
    }
    
    protected override void SetValue(NpgsqlParameter parameter, Guid value)
    {
        parameter.Value = value;
        parameter.NpgsqlDbType = NpgsqlDbType.Uuid;
    }
}