using Proptexx.Core.DTOs.Outseta;

namespace Proptexx.Core.DTOs;

public class OutsetaAccountData
{
    public string CompanyName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public string? Phone { get; set; }
    public string? StoreUrl { get; set; }
    public string? Platform { get; set; }
    public int? Viewers { get; set; }
    public string? PlanName { get; set; }
    public string? PlanUid { get; set; }
    public string? AccountUid { get; set; }
}
