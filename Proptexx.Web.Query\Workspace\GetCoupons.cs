using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Attributes;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Workspace;

public sealed class GetCoupons : IQuery
{
    [GuidNotEmpty] public Guid? WorkspaceId { get; init; }
    
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var workspaceId = WorkspaceId ?? context.User.GetWorkspaceGuid();
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var result = await npgsql.QueryAsync<CouponModel>(CouponModel.Sql, new { _workspace_id = workspaceId });
        return result;
    }

    private sealed class CouponModel
    {
        public Guid Id { get; init; }
        
        public int DiscountType { get; init; }

        public int DiscountValue { get; init; }
        
        public DateTime CreatedAt { get; init; }

        public static string Sql => @"
            select c.id,
                   c.discount_type,
                   c.discount_value::int as discount_value,
                   c.created_at
            from core.coupon c
            order by c.created_at desc;
        ";
    }
}