### Authenticate
# @name authen
POST https://auth.dev.local/_auth
Content-Type: application/json
Authorization: ApiKey MDEwNzVkMDktMmI4OC00ZjM1LWJlMTYtODVmN2M4NjE2YjYx

{}

### Extract token using correct syntax for property with $ sign
@accessToken = {{authen.response.body.$.$accessToken}}

### Call Model with accessToken from previous request
POST https://api.dev.local/flow/virtual-staging-or-refurnishing
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "imageUrl": "https://static.proptexx.com/appartments/listing1/livingroom.jpg"
}

### Success
POST https://api.dev.local/flow/virtual-staging-or-refurnishing
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "imageUrl": "https://static.proptexx.com/appartments/listing1/livingroom.jpg",
  "room_Type": "Living rOOm",
  "architecture_style": "modErn"
}

### Fail
POST https://api.dev.local/flow/virtual-staging-or-refurnishing
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "imageUrl": "https://static.proptexx.com/appartments/listing1/kitchen.jpg",
  "room_Type": "DinIng rOOm",
  "architecture_style": "modErn"
}

### IS24
POST https://api.dev.local/flow/virtual-staging-or-refurnishing-is
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "imageUrl": "https://static.proptexx.com/appartments/listing1/livingroom.jpg"
}