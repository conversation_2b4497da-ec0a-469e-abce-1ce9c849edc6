using Dapper;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Entities;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Docs;

public class GetApiList : IQuery
{
    public string? StartsWith { get; init; }

    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);

        var param = new { _starts_with = StartsWith };
        var serviceList = await npgsql
            .QueryAsync<Service>(@"
                select s.id,
                       s.service_type,
                       s.title,
                       s.description,
                       s.credit_cost,
                       (s.settings || st.settings) as settings
                from core.service s
                join core.service_type st on s.service_type = st.id
                where service_type in ('gen', 'cv', 'flow')
                  and (:_starts_with is null or starts_with(s.id, :_starts_with))
                  and (active_from is null or current_timestamp >= active_from)
                  and (active_to is null or current_timestamp <= active_to)
                order by title", param);

        return serviceList;
    }
}