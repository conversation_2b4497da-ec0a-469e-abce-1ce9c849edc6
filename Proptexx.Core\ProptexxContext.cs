using System.Security.Claims;
using Microsoft.Extensions.DependencyInjection;

namespace Proptexx.Core;

public abstract class ProptexxContext
{
    protected ProptexxContext(IServiceProvider services, ClaimsPrincipal user)
    {
        Id = Guid.NewGuid().ToString();
        Services = services;
        User = user;
    }

    public string Id { get; }
    
    public IServiceProvider Services { get; }
    
    public ClaimsPrincipal User { get; }

    public required CancellationToken CancellationToken { get; init; }

    public T GetService<T>() where T : notnull => this.Services.GetRequiredService<T>();
    
    public T? GetOptionalService<T>() => this.Services.GetService<T>();
}