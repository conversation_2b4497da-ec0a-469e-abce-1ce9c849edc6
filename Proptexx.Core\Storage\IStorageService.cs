namespace Proptexx.Core.Storage;

public interface IStorageService
{
    Task CreateDirectoryAsync(string directoryName, CancellationToken cancellationToken);
    
    Task<string> UploadImageAsync(string containerName, string fileName, string base64Image, string contentType);
    
    Task<string> UploadImageAsync(string containerName, string fileName, byte[] byteArrayImage, string contentType);

    Task<string> UploadImageAsync(string containerName, string fileName, Stream imageStream, string contentType);
}