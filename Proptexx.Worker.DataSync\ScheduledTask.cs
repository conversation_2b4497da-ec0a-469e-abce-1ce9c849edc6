using System.Diagnostics;

namespace Proptexx.Worker.DataSync;

internal class ScheduledTask
{
    private readonly TimeSpan _interval;
    private readonly Func<IServiceProvider, CancellationToken, Task> _action;

    public string Name { get; set; }

    public DateTime? LastRun { get; private set; }

    public ScheduledTask(string name, float intervalMin,
        Func<IServiceProvider, CancellationToken, Task> action) 
        : this(name, TimeSpan.FromMinutes(intervalMin), action)
    { }

    public ScheduledTask(string name, TimeSpan interval, Func<IServiceProvider, CancellationToken, Task> action)
    {
        Name = name;
        _interval = interval;
        _action = action;
    }

    public bool ShouldRun()
    {
        if (!LastRun.HasValue)  return true;
        return (DateTime.UtcNow - LastRun) >= _interval;
    }

    public async Task<(string name, long timeTaken, Exception? exception)> RunAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        var sw = Stopwatch.StartNew();
        Exception? exception = null;

        try
        {
            await _action(serviceProvider, cancellationToken);
        }
        catch (Exception ex)
        {
            // ignored
            exception = ex;
        }
        finally
        {
            LastRun = DateTime.UtcNow;
        }

        return (this.Name, sw.ElapsedMilliseconds, exception);
    }
}