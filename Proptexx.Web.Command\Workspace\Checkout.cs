using System.ComponentModel.DataAnnotations;
using Dapper;
using Proptexx.Core;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Attributes;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Services;
using Proptexx.Stripe;

namespace Proptexx.Web.Command.Workspace;

public sealed class Checkout : ICommand
{
    [Required, GuidNotEmpty]
    public required Guid ProductId { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        var accountId = Guid.Parse(context.User.GetCallerId());
        var workspaceId = context.User.GetWorkspaceGuid();
        var configuration = context.GetService<IConfiguration>();

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);

        var product = await npgsql.QueryFirstOrDefaultAsync<ProductModel>(ProductModel.Sql, new { _workspace_id = workspaceId, _product_id = this.ProductId })
                      ?? throw new CommandException("The selected product is not available");

        await using var transaction = await npgsql.BeginTransactionAsync(context.CancellationToken);

        try
        {
            string? paymentLink = null;
            if (product.PaymentType.Equals("free", StringComparison.InvariantCulture))
            {
                var orderId = await OrderManager.CreateOrderAsync(npgsql, accountId, workspaceId, product.Id, product.PriceAmount, product.Currency);
                await OrderManager.RecordPaymentAsync(npgsql, orderId, 0m, "free", null, context.CancellationToken);
                await OrderManager.MarkAsPaidAsync(npgsql, orderId);
                await OrderManager.EnableServicesAsync(npgsql, orderId, context.CancellationToken);
            }
            else
            {
                var orderId = Guid.NewGuid();
                var account = await npgsql.QueryFirstAsync<Core.Entities.Account>(
                    @"select a.* from core.account a where a.id = :_account_id", 
                    new { _account_id = accountId });

                var refPerson = account.FullName();
                var session = await StripeManager.CreateCheckoutSessionAsync(configuration, product, orderId, context.CancellationToken);
                await OrderManager.CreateOrderAsync(npgsql, orderId, accountId, refPerson, workspaceId, session.Url, new []{product.Id});
                paymentLink = session.Url;
            }

            await transaction.CommitAsync(context.CancellationToken);

            if (!string.IsNullOrWhiteSpace(paymentLink))
            {
                context.AddData("redirectTo", paymentLink);
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            await transaction.RollbackAsync(context.CancellationToken);
            throw;
        }
    }
}