using System;
using System.Text.Json.Serialization;

namespace Proptexx.Core.DTOs.Outseta // Changed namespace
{
    public class PlanFamily
    {
        public string? Name { get; set; }
        public bool IsActive { get; set; }
        public bool IsDefault { get; set; }
        public bool SchemaLessDataLoaded { get; set; }
        public string? Uid { get; set; }
        [JsonPropertyName("_objectType")]
        public string? ObjectType { get; set; }
        public DateTime Created { get; set; }
        public DateTime Updated { get; set; }
    }
}
