using Microsoft.Extensions.DependencyInjection;

namespace Proptexx.Core.Cqrs.Command;

public static class Extensions
{
    public static IServiceCollection AddCommands(this IServiceCollection services, Action<CommandConfig> configFn)
    {
        var config = new CommandConfig();
        configFn(config);
        services.AddSingleton(config);
        services.AddScoped<CommandHttpHandler>();
        return services;
    }
}