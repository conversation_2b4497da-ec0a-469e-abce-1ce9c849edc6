using System.ComponentModel.DataAnnotations;
using Proptexx.Core.Services;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Attributes;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Utils;

namespace Proptexx.Web.Command.System;

public sealed class CreateWorkspace : ICommand
{
    [Required, MinLength(4)]
    public required string Name { get; init; }
    
    [GuidNotEmpty] public Guid? ParentId { get; init; }

    [Required]
    public required OwnerDescriptor Owner { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        context.User.EnsureRootAccess();

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        await using var trx = await npgsql.BeginTransactionAsync(context.CancellationToken);

        try
        {
            var password = PasswordService.GeneratePassword(6);
            var account = await OnboardHelper.OnboardAccount(npgsql, Owner.FullName, Owner.Email, true, password, Owner.Phone, true);
            var workspace = await OnboardHelper.CreateWorkspace(npgsql, account, this.Name, this.ParentId, []);
            await trx.CommitAsync(context.CancellationToken);
            context.AddData("workspaceId", workspace.Id);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            await trx.RollbackAsync(context.CancellationToken);
            throw;
        }
    }
}

public class OwnerDescriptor
{
    public required string FullName { get; init; }

    public required string Email { get; init; }

    public string? Phone { get; init; }
}
