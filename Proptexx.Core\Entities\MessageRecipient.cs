using Proptexx.Core.Messaging;
using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class MessageRecipient : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; } = Guid.NewGuid();

    public required Guid MessageId { get; init; }
    
    public Guid? RecipientId { get; init; }
    
    public required string Recipient { get; init; }

    public required MessageType MessageType { get; init; }

    public RecipientStatus Status { get; private set; } = RecipientStatus.Pending;
    
    public string? Response { get; init; }
    
    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public DateTime? ProcessedAt { get; private set; }

    public string GetDbRef() => "core.message_recipient";

    public MessageRecipient SetStatus(RecipientStatus status, DateTime? processedAt = null)
    {
        this.Status = status;
        this.ProcessedAt = processedAt is { Kind: DateTimeKind.Utc } ? processedAt : DateTime.UtcNow;
        return this;
    }
}