using System.Security.Claims;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Proptexx.Core;
using Proptexx.Core.Options;

namespace Proptexx.Web;

public sealed class AuthenticationMiddleware : IMiddleware
{
    private readonly ILogger<AuthenticationMiddleware> _logger;
    private readonly ProptexxOptions _proptexxOptions;

    public AuthenticationMiddleware(
        ILogger<AuthenticationMiddleware> logger,
        IOptions<ProptexxOptions> proptexxOptions)
    {
        _logger = logger;
        _proptexxOptions = proptexxOptions.Value;
    }

    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        try
        {
            var identity = new ProptexxIdentity(context.User.Claims, _proptexxOptions.CallerIdentifier, null);
            context.User = new ClaimsPrincipal(identity);
            await next(context);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "An exception occurred");
            context.Response.StatusCode = StatusCodes.Status500InternalServerError;
        }
    }
}