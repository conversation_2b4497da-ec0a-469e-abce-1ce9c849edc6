using Dapper;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Command.Account;

public sealed class OAuthDisconnect : ICommand
{
    public string? Provider { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        var provider = Provider?.Trim().ToLowerInvariant() ?? throw new NullReferenceException("provider");
        var accountId = Guid.Parse(context.User.GetCallerId());
        var dataKey = $"{provider}_id";
        
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        await npgsql.ExecuteAsync(@"
            delete from core.account_data ad where ad.account_id = :_account_id and ad.data_key = :_data_key; 
        ", new { _account_id = accountId, _data_key = dataKey });
    }
}