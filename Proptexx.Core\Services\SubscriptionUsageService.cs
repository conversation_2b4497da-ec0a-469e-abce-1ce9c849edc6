﻿using Microsoft.Extensions.Logging;
using Npgsql;
using Dapper;
using Proptexx.Core.DTOs;
using Proptexx.Core.Constants;
using Proptexx.Core.Interface;

namespace Proptexx.Core.Services
{
    public class SubscriptionUsageService : ISubscriptionUsageService
    {
        private readonly ILogger<SubscriptionUsageService> _logger;
        private readonly NpgsqlDataSource _dataSource;

        public SubscriptionUsageService(
            ILogger<SubscriptionUsageService> logger,
            NpgsqlDataSource dataSource)
        {
            _logger = logger;
            _dataSource = dataSource;
        }

        /// <summary>
        /// Enhanced validation that checks subscription quota, expiry dates, and request type-specific rules
        /// </summary>
        public async Task<EnhancedValidationResult> ValidateRequestWithQuotaAndExpiryAsync(
            string apiKey, 
            RequestClassification requestType, 
            CancellationToken cancellationToken)
        {
            try
            {
                // Get client secret ID from API key
                var clientSecretId = await GetClientSecretIdFromApiKeyAsync(apiKey, cancellationToken);
                if (clientSecretId == null)
                {
                    return EnhancedValidationResult.Invalid("Invalid API key", RequestValidationErrorCode.InvalidApiKey);
                }

                // Query subscription and client_secret data
                const string sql = @"
            SELECT 
                cs.type as subscription_type,
                cs.quota,
                cs.total_usage,
                cs.status as subscription_status,
                cs.client_secret_id,
                cs.is_demo,
                cs.plan_id,
                cs.created_at as subscription_created_at,
                cs.updated_at as subscription_updated_at,
                client_secret.expired_at as client_expired_at,
                client_secret.total_usage as client_total_usage,
                client_secret.created_at as client_created_at,
                -- Widget client data for widget-specific validation
                wc.quota as widget_quota,
                wc.renders_used as widget_renders_used,
                wc.expired_date as widget_expired_date,
                wc.start_date as widget_start_date
            FROM core.client_subscription cs
            JOIN core.client_secret ON cs.client_secret_id = client_secret.id
            LEFT JOIN core.widget_clients wc ON wc.api_key = @ApiKey
            WHERE cs.client_secret_id = @ClientSecretId
            AND cs.status = 'Active'
            ORDER BY cs.created_at DESC
            LIMIT 1";

                await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
                var data = await connection.QuerySingleOrDefaultAsync<dynamic>(sql, new { 
                    ClientSecretId = clientSecretId, 
                    ApiKey = apiKey 
                });

                if (data == null)
                {
                    return EnhancedValidationResult.Invalid("No active subscription found", RequestValidationErrorCode.SubscriptionNotFound);
                }

                // Check client_secret expiry
                DateTime? clientExpiresAt = data.client_expired_at;
                if (clientExpiresAt.HasValue && clientExpiresAt.Value <= DateTime.UtcNow)
                {
                    return EnhancedValidationResult.Invalid("API key has expired", RequestValidationErrorCode.ApiKeyExpired);
                }

                // Calculate effective expiry and days until expiry
                var expiryInfo = CalculateExpiryInfo(data, requestType);
                if (expiryInfo.IsExpired)
                {
                    return EnhancedValidationResult.Invalid(expiryInfo.ExpiryReason, RequestValidationErrorCode.SubscriptionExpired);
                }

                // Check quota based on request type
                var quotaValidation = ValidateQuotaForRequestType(data, requestType);
                if (!quotaValidation.IsValid)
                {
                    return EnhancedValidationResult.Invalid(
                        quotaValidation.Reason, 
                        quotaValidation.ErrorCode,
                        quotaValidation.UsagePercentage,
                        quotaValidation.RemainingQuota,
                        data.subscription_status?.ToString(),
                        expiryInfo.EffectiveExpiryDate,
                        expiryInfo.DaysUntilExpiry);
                }

                // Return successful validation result
                return EnhancedValidationResult.Valid(
                    subscription: CreateSubscriptionInfo(data),
                    usagePercentage: quotaValidation.UsagePercentage,
                    remainingQuota: quotaValidation.RemainingQuota,
                    subscriptionStatus: data.subscription_status?.ToString(),
                    expiresAt: expiryInfo.EffectiveExpiryDate,
                    daysUntilExpiry: expiryInfo.DaysUntilExpiry);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during enhanced validation for API key: {ApiKey}", apiKey);
                return EnhancedValidationResult.Invalid("Validation error", RequestValidationErrorCode.ValidationError);
            }
        }

        /// <summary>
        /// Increments usage based on request type
        /// </summary>
        public async Task<bool> IncrementUsageForRequestTypeAsync(
            string apiKey, 
            RequestClassification requestType, 
            CancellationToken cancellationToken)
        {
            try
            {
                return await IncrementWidgetUsageAsync(apiKey, cancellationToken);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error incrementing usage for request type: {RequestType}", requestType.Type);
                return false;
            }
        }

        /// <summary>
        /// Gets client secret ID from API key
        /// </summary>
        public async Task<Guid?> GetClientSecretIdFromApiKeyAsync(string apiKey, CancellationToken cancellationToken)
        {
            try
            {
                const string sql = @"
                SELECT cs.id 
                FROM core.client_secret cs
                JOIN core.client c ON cs.client_id = c.id
                WHERE core.generate_api_key(cs.id) = @ApiKey
                AND (cs.expired_at IS NULL OR cs.expired_at > CURRENT_TIMESTAMP)
                LIMIT 1";

                await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
                return await connection.QuerySingleOrDefaultAsync<Guid?>(sql, new { ApiKey = apiKey });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting client secret ID for API key: {ApiKey}", apiKey);
                return null;
            }
        }

        /// <summary>
        /// Gets comprehensive subscription information
        /// </summary>
        public async Task<SubscriptionInfo?> GetSubscriptionInfoAsync(string apiKey, CancellationToken cancellationToken)
        {
            try
            {
                var clientSecretId = await GetClientSecretIdFromApiKeyAsync(apiKey, cancellationToken);
                if (clientSecretId == null) return null;

                const string sql = @"
                SELECT 
                    cs.type as subscription_type,
                    cs.quota,
                    cs.total_usage,
                    cs.status as subscription_status,
                    cs.is_demo,
                    cs.plan_id,
                    client_secret.expired_at,
                    wc.quota as widget_quota,
                    wc.renders_used as widget_renders_used,
                    wc.expired_date as widget_expired_date
                FROM core.client_subscription cs
                JOIN core.client_secret ON cs.client_secret_id = client_secret.id
                LEFT JOIN core.widget_clients wc ON wc.api_key = @ApiKey
                WHERE cs.client_secret_id = @ClientSecretId
                AND cs.status = 'Active'
                ORDER BY cs.created_at DESC
                LIMIT 1";

                await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
                var data = await connection.QuerySingleOrDefaultAsync<dynamic>(sql, new { 
                    ClientSecretId = clientSecretId, 
                    ApiKey = apiKey 
                });

                if (data == null) return null;

                return new SubscriptionInfo
                {
                    Type = data.subscription_type?.ToString(),
                    Status = data.subscription_status?.ToString(),
                    Quota = (long?)data.quota ?? 0,
                    TotalUsage = (long?)data.total_usage ?? 0,
                    IsDemo = (bool?)data.is_demo ?? false,
                    PlanId = data.plan_id?.ToString(),
                    ExpiresAt = data.expired_at,
                    WidgetQuota = data.widget_quota,
                    WidgetRendersUsed = data.widget_renders_used,
                    WidgetExpiresAt = data.widget_expired_date
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting subscription info for API key: {ApiKey}", apiKey);
                return null;
            }
        }

    

        #region Private Helper Methods - All subscription business logic

        /// <summary>
        /// Validates quota based on the specific request type
        /// </summary>
        private QuotaValidationResult ValidateQuotaForRequestType(dynamic data, RequestClassification requestType)
        {
            try
            {
                if (IsWidgetRequest(requestType))
                {
                    return ValidateWidgetQuota(data, requestType);
                }
                return ValidateSubscriptionQuota(data, requestType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating quota for request type: {RequestType}", requestType.Type);
                return new QuotaValidationResult
                {
                    IsValid = false,
                    Reason = "Quota validation error",
                    ErrorCode = RequestValidationErrorCode.ValidationError
                };
            }
        }

        /// <summary>
        /// Validates widget-specific quota
        /// </summary>
        private QuotaValidationResult ValidateWidgetQuota(dynamic data, RequestClassification requestType)
        {
            int widgetQuota = data.widget_quota ?? 0;
            int widgetUsage = data.widget_renders_used ?? 0;

            if (widgetQuota <= 0)
            {
                return QuotaValidationResult.Valid(0, 0); // Unlimited or not configured
            }

            int remainingQuota = Math.Max(0, widgetQuota - widgetUsage);
            double usagePercentage = widgetQuota > 0 ? (double)widgetUsage / widgetQuota * 100 : 0;

            if (widgetUsage >= widgetQuota)
            {
                return new QuotaValidationResult
                {
                    IsValid = false,
                    Reason = "Widget render quota exceeded",
                    ErrorCode = RequestValidationErrorCode.QuotaExceeded,
                    UsagePercentage = usagePercentage,
                    RemainingQuota = remainingQuota
                };
            }

            if (IsHighUsageWidgetOperation(requestType) && usagePercentage > 90)
            {
                return new QuotaValidationResult
                {
                    IsValid = false,
                    Reason = "Widget quota nearly exhausted for high-usage operations",
                    ErrorCode = RequestValidationErrorCode.QuotaNearLimit,
                    UsagePercentage = usagePercentage,
                    RemainingQuota = remainingQuota
                };
            }

            return QuotaValidationResult.Valid(usagePercentage, remainingQuota);
        }

        /// <summary>
        /// Validates subscription quota
        /// </summary>
        private QuotaValidationResult ValidateSubscriptionQuota(dynamic data, RequestClassification requestType)
        {
            long quota = data.quota ?? 0;
            long usage = data.total_usage ?? 0;
            string subscriptionType = data.subscription_type?.ToString() ?? "";

            if (quota <= 0 && subscriptionType != "PayAsYouGo")
            {
                return QuotaValidationResult.Valid(0, 0); // Unlimited or not configured
            }

            long remainingQuota = Math.Max(0, quota - usage);
            double usagePercentage = quota > 0 ? (double)usage / quota * 100 : 0;

            if (subscriptionType == "PayAsYouGo")
            {
                return QuotaValidationResult.Valid(usagePercentage, remainingQuota);
            }

            if (usage >= quota)
            {
                return new QuotaValidationResult
                {
                    IsValid = false,
                    Reason = $"Subscription quota exceeded ({subscriptionType})",
                    ErrorCode = RequestValidationErrorCode.QuotaExceeded,
                    UsagePercentage = usagePercentage,
                    RemainingQuota = remainingQuota
                };
            }

            if (IsHighUsageWidgetOperation(requestType) && usagePercentage > 95)
            {
                return new QuotaValidationResult
                {
                    IsValid = false,
                    Reason = "Quota nearly exhausted for resource-intensive operations",
                    ErrorCode = RequestValidationErrorCode.QuotaNearLimit,
                    UsagePercentage = usagePercentage,
                    RemainingQuota = remainingQuota
                };
            }

            return QuotaValidationResult.Valid(usagePercentage, remainingQuota);
        }

        /// <summary>
        /// Calculates expiry information for different request types
        /// </summary>
        private ExpiryInfo CalculateExpiryInfo(dynamic data, RequestClassification requestType)
        {
            DateTime? clientExpiresAt = data.client_expired_at;
            DateTime? widgetExpiresAt = data.widget_expired_date;
            
            if (IsWidgetRequest(requestType) && widgetExpiresAt.HasValue)
            {
                if (widgetExpiresAt.Value <= DateTime.UtcNow)
                {
                    return new ExpiryInfo
                    {
                        IsExpired = true,
                        ExpiryReason = "Widget subscription has expired",
                        EffectiveExpiryDate = widgetExpiresAt.Value
                    };
                }
                
                return new ExpiryInfo
                {
                    IsExpired = false,
                    EffectiveExpiryDate = widgetExpiresAt.Value,
                    DaysUntilExpiry = (int)(widgetExpiresAt.Value - DateTime.UtcNow).TotalDays
                };
            }
            
            if (clientExpiresAt.HasValue)
            {
                return new ExpiryInfo
                {
                    IsExpired = false,
                    EffectiveExpiryDate = clientExpiresAt.Value,
                    DaysUntilExpiry = (int)(clientExpiresAt.Value - DateTime.UtcNow).TotalDays
                };
            }
            
            return new ExpiryInfo { IsExpired = false };
        }

        /// <summary>
        /// Creates subscription info object from dynamic data
        /// </summary>
        private object CreateSubscriptionInfo(dynamic data)
        {
            return new
            {
                Type = data.subscription_type?.ToString(),
                Status = data.subscription_status?.ToString(),
                Quota = (long?)data.quota ?? 0,
                TotalUsage = (long?)data.total_usage ?? 0,
                IsDemo = (bool?)data.is_demo ?? false,
                PlanId = data.plan_id?.ToString()
            };
        }

        /// <summary>
        /// Increments widget-specific usage using the new DB function
        /// </summary>
        private async Task<bool> IncrementWidgetUsageAsync(string apiKey, CancellationToken cancellationToken)
        {
            try
            {
                const string sql = @"SELECT core.increment_usage_by_api_key(@ApiKey)";
                await using var connection = await _dataSource.OpenConnectionAsync(cancellationToken);
                await connection.ExecuteAsync(sql, new { ApiKey = apiKey });
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error incrementing usage for API key via increment_usage_by_api_key: {ApiKey}", apiKey);
                return false;
            }
        }

        /// <summary>
        /// Determines if this is a widget-related request
        /// </summary>
        private static bool IsWidgetRequest(RequestClassification requestType) => requestType.Type == WidgetConstants.HandlerTypes.CommandHandler && requestType.Target.StartsWith("widget.");

        /// <summary>
        /// Determines if this is a high-usage widget operation (AI model intensive)
        /// </summary>
        private static bool IsHighUsageWidgetOperation(RequestClassification requestType)
        {
             return WidgetConstants.ExpensiveRequestTargets.Contains(requestType.Target.ToLowerInvariant());
        }

 
        #endregion
    }
}
