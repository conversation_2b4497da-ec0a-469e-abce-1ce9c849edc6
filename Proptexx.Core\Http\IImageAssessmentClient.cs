using System.Security.Cryptography;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Services;
using SixLabors.ImageSharp;

namespace Proptexx.Core.Http;

public interface IImageAssessmentClient
{
    Task<ImageAssessment> InspectImageAsync(string imageUrl, CancellationToken cancellationToken = default);
}

public sealed class ImageAssessmentHttpClient(HttpClient httpClient, ILogger<ImageAssessmentHttpClient> logger) 
    : BaseHttpClient(httpClient, logger), IImageAssessmentClient
{
    public async Task<ImageAssessment> InspectImageAsync(string imageUrl, CancellationToken cancellationToken = default)
    {
        var imageBytes = await GetAsByteArrayAsync(imageUrl, true, cancellationToken);
        return InspectImage(imageUrl, imageBytes);
    }

    private ImageAssessment InspectImage(string imageUrl, byte[] imageBytes)
    {
        var image = Image.Identify(imageBytes);
        return AssessImage(imageUrl, image, imageBytes);
    }

    private static ImageAssessment AssessImage(string imageUrl, ImageInfo image, byte[] imageBytes)
    {
        if (image == null)
        {
            throw new ApplicationException("Could not read image metadata");
        }

        var mimeType = image.Metadata.DecodedImageFormat?.DefaultMimeType;
        if (string.IsNullOrWhiteSpace(mimeType) || !ValidateMimeType(mimeType))
        {
            throw new ApplicationException($"Unsupported MIME type: {mimeType}");
        }

        return new ImageAssessment
        {
            ImageUrl = imageUrl,
            Base64String = Convert.ToBase64String(imageBytes),
            FileHash = ComputeHash(imageBytes),
            MimeType = mimeType,
            Width = image.Width,
            Height = image.Height
        };
    }

    private static string ComputeHash(byte[] byteArray)
    {
        var hashBytes = SHA256.HashData(byteArray);
        return Convert.ToHexString(hashBytes);
    }

    private static bool ValidateMimeType(string? mimeType)
    {
        return mimeType is not null && (mimeType.StartsWith("text/html") ||
               AcceptedMimeTypes.Contains(mimeType, StringComparer.OrdinalIgnoreCase));
    }
}