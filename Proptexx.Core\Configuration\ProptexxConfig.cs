using DotNetEnv;
using DotNetEnv.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;

namespace Proptexx.Core.Configuration;

public static class ProptexxConfig
{
    public static IHostApplicationBuilder AddProptexxConfiguration(this IHostApplicationBuilder appBuilder)
    {
        var dir = AppContext.BaseDirectory;
        var envName = appBuilder.Environment.EnvironmentName.ToLowerInvariant();

        List<string> envPaths =
        [
            Path.Combine(dir, ".env"),
            Path.Combine(dir, $".env.{envName}")
        ];

        var builder = new ConfigurationBuilder()
            .SetBasePath(AppContext.BaseDirectory)
            .AddDotNetEnvMulti(envPaths.ToArray(), new LoadOptions(false, true))
            .AddEnvironmentVariables();

        var configuration = builder.Build();
        appBuilder.Configuration.AddConfiguration(configuration);

        return appBuilder;
    }
}