using System.Text.Json;
using Proptexx.Core.Json;
using Npgsql;
using NpgsqlTypes;

namespace Proptexx.Core.Postgresql.Mappers;

public sealed class DictionaryTypeMapper : NpgsqlTypeMapper<IDictionary<string, object>>
{
    private readonly JsonSerializerOptions _jsonSettings = JsonDefaults.JsonSerializerOptions;

    public override IDictionary<string, object>? Parse(object value)
    {
        if (value is string str)
        {
            return JsonSerializer.Deserialize<Dictionary<string, object>>(str, _jsonSettings);
        }

        return null;
    }

    protected override void SetValue(NpgsqlParameter parameter, IDictionary<string, object>? value)
    {
        var serialized = value is null ? null : JsonSerializer.Serialize(value, _jsonSettings);
        parameter.NpgsqlDbType = NpgsqlDbType.Jsonb;
        parameter.Value = serialized;
    }
}