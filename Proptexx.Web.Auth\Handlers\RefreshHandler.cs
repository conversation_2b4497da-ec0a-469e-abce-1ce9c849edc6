using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.JsonWebTokens;
using Proptexx.Core;
using Proptexx.Core.Json;
using Proptexx.Core.Options;
using Proptexx.Web.Auth.Auth;

namespace Proptexx.Web.Auth.Handlers;

public sealed class RefreshHandler
{
    private readonly ILogger<RefreshHandler> _logger;
    private readonly IClientResolver _clientResolver;
    private readonly ISessionResolver _sessionResolver;
    private readonly ProptexxOptions _proptexxOptions;

    public RefreshHandler(
        ILogger<RefreshHandler> logger,
        IClientResolver clientResolver,
        ISessionResolver sessionResolver,
        IOptions<ProptexxOptions> proptexxOptions)
    {
        _logger = logger;
        _clientResolver = clientResolver;
        _sessionResolver = sessionResolver;
        _proptexxOptions = proptexxOptions.Value;
    }

    public async Task InvokeAsync(HttpContext httpContext)
    {
        try
        {
            if (!ApiKeyHelper.TryGetApiKey(httpContext, out var apiKey))
            {
                throw new UnauthorizedAccessException();
            }

            if (_proptexxOptions.Cookie is null || 
                !httpContext.Request.Cookies.TryGetValue(_proptexxOptions.Cookie.SessionRefreshToken, out var refreshToken) ||
                string.IsNullOrWhiteSpace(refreshToken) || 
                !httpContext.Request.Host.HasValue)
            {
                throw new UnauthorizedAccessException();
            }

            var session = await _sessionResolver.RetriveAsync(refreshToken)
                ?? throw new UnauthorizedAccessException();

            _ = session.Claims.TryGetValue(_proptexxOptions.CallerIdentifier, out var accountId);

            var clientSecretId = ApiKeyService.ParseApiKey(apiKey);

            Dictionary<string, string> claims;
            if (clientSecretId != session.Claims[_proptexxOptions.ClientIdentifier])
            {
                var clientResult = await _clientResolver.ResolveAsync(clientSecretId, accountId)
                                   ?? throw new UnauthorizedAccessException();

                if (clientResult.IssueCookie is not true)
                {
                    httpContext.Response.Cookies.Delete(_proptexxOptions.Cookie.SessionRefreshToken);
                    throw new UnauthorizedAccessException();
                }

                claims = new Dictionary<string, string>(session.Claims.Where(x => x.Key.StartsWith('$')))
                {
                    [JwtRegisteredClaimNames.Jti] = httpContext.TraceIdentifier,
                    ["$sessionId"] = session.SessionId, 
                    [_proptexxOptions.ClientIdentifier] = clientSecretId
                };

                foreach (var (key, value) in clientResult.AdditionalClaims)
                {
                    claims.Add(key, value);
                }
            }
            else
            {
                claims = new Dictionary<string, string>(session.Claims)
                {
                    [JwtRegisteredClaimNames.Jti] = httpContext.TraceIdentifier,
                    ["$sessionId"] = session.SessionId,
                    [_proptexxOptions.ClientIdentifier] = clientSecretId
                };
            }

            if (httpContext.Request.Headers.TryGetValue("Client-Version", out var clientVersion))
            {
                claims["clientVersion"] = clientVersion.ToString();
            }

            await AuthHandler.PersistSessionAsync(httpContext, _sessionResolver, _proptexxOptions, true, session.SessionId, claims);
            var accessToken = AuthHandler.GenerateToken(_proptexxOptions, claims);
            var response = new Dictionary<string, object> { { "$accessToken", accessToken } };
            await httpContext.Response.WriteAsJsonAsync(response, JsonDefaults.CompactOptions);
        }
        catch (UnauthorizedAccessException)
        {
            httpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
        }
        catch (Exception e)
        {
            _logger.LogError(e, nameof(AuthHandler));
            httpContext.Response.StatusCode = StatusCodes.Status500InternalServerError;
        }
    }
}