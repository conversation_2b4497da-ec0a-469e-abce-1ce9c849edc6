﻿using System.IdentityModel.Tokens.Jwt;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Entities;
using StackExchange.Redis;

namespace Proptexx.Core.Telemetry
{
    public class TelemetryMiddleware
    {
        private const string ApiKeyIndicator = "apiKey"; // Lowercase key used to identify API key auth mode.
        private const string ApiKeyPrefix = "ApiKey ";
        private const string BearerPrefix = "Bearer ";

        private readonly RequestDelegate _next;
        private readonly ILogger<TelemetryMiddleware> _logger;
        private readonly IConnectionMultiplexer _redis;

        public TelemetryMiddleware(RequestDelegate next, ILogger<TelemetryMiddleware> logger, IConnectionMultiplexer redis)
        {
            _next = next;
            _logger = logger;
            _redis = redis;
        }

        public async Task Invoke(HttpContext context)
        {
            var request = context.Request;

            // Serialize request headers as JSON.
            var headers = JsonSerializer.Serialize(
                request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString()));

            var endpoint = request.Path.ToString();
            var requestBody = await ReadRequestBodyAsync(request);

            // Determine auth type and extract token.
            string token = null;
            string authType = null;
            var authHeader = request.Headers["Authorization"].FirstOrDefault();

            if (!string.IsNullOrWhiteSpace(authHeader))
            {
                if (authHeader.StartsWith(BearerPrefix, StringComparison.OrdinalIgnoreCase))
                {
                    token = authHeader.Substring(BearerPrefix.Length).Trim();
                    authType = "Bearer";
                }
                else if (authHeader.IndexOf(ApiKeyIndicator, StringComparison.OrdinalIgnoreCase) != -1)
                {
                    // When the header contains an API key, remove the prefix if present.
                    token = authHeader.StartsWith(ApiKeyPrefix, StringComparison.OrdinalIgnoreCase)
                        ? authHeader.Substring(ApiKeyPrefix.Length).Trim()
                        : authHeader.Trim();
                    authType = ApiKeyIndicator;
                }
                else
                {
                    token = authHeader.Trim();
                }
            }

            var startTime = DateTime.UtcNow;

            // Extract user information based on the auth type.
            UserInfo userInfo = authType switch
            {
                "Bearer" => ExtractUserInfoFromToken(token),
                ApiKeyIndicator => ExtractUserInfoFromApiKey(token),
                _ => new UserInfo()
            };

            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during request execution.");
                throw;
            }
            finally
            {
                var duration = (DateTime.UtcNow - startTime).TotalMilliseconds;
                var responseCode = context.Response.StatusCode;
                context.Items.TryGetValue("batchId", out var batchId);

                // Create and populate the telemetry entry.
                var logEntry = new TelemetryEntry
                {
                    Timestamp = DateTime.UtcNow,
                    Endpoint = endpoint,
                    Headers = headers,
                    Body = requestBody ?? "{}",
                    WorkspaceId = userInfo.WorkspaceId,
                    SessionId = userInfo.SessionId,
                    ClientVersion = userInfo.ClientVersion,
                    AccountId = userInfo.AccountId,
                    HttpMethod = request.Method,
                    ClientId = userInfo.ClientId,
                    DurationMs = duration,
                    ResponseCode = responseCode,
                    Identifier = batchId?.ToString(),
                    SecretId = userInfo.SecretId
                };

                // await SendToRedisAsync(logEntry);
            }
        }

        private static async Task<string?> ReadRequestBodyAsync(HttpRequest request)
        {
            if (request.ContentLength is null || request.ContentLength == 0)
                return null;

            request.EnableBuffering();
            using var reader = new StreamReader(request.Body, leaveOpen: true);
            var body = await reader.ReadToEndAsync();
            request.Body.Position = 0;
            return body;
        }

        private async Task SendToRedisAsync(TelemetryEntry logEntry)
        {
            var db = _redis.GetDatabase();
            var jsonLog = JsonSerializer.Serialize(logEntry);
            await db.ListRightPushAsync("telemetry_queue", jsonLog);
        }

        private static UserInfo ExtractUserInfoFromToken(string? token)
        {
            var userInfo = new UserInfo();

            if (string.IsNullOrEmpty(token))
                return userInfo;

            var jwtHandler = new JwtSecurityTokenHandler();
            if (!jwtHandler.CanReadToken(token))
                return userInfo;

            var jwtToken = jwtHandler.ReadJwtToken(token);

            if (Guid.TryParse(jwtToken.Claims.FirstOrDefault(c => c.Type == "workspaceId")?.Value, out var workspaceId))
                userInfo.WorkspaceId = workspaceId;

            if (Guid.TryParse(jwtToken.Claims.FirstOrDefault(c => c.Type == "$sessionId")?.Value, out var sessionId))
                userInfo.SessionId = sessionId;

            string[] accountIdTokens = new string[] { "accountId", "$accountId" };
            if (Guid.TryParse(jwtToken.Claims.FirstOrDefault(c => accountIdTokens.Contains(c.Type))?.Value, out var accountId))
                userInfo.AccountId = accountId;

            if (Guid.TryParse(jwtToken.Claims.FirstOrDefault(c => c.Type == "clientId")?.Value, out var clientId))
                userInfo.ClientId = clientId;

            userInfo.ClientVersion = jwtToken.Claims.FirstOrDefault(c => c.Type == "clientVersion")?.Value;

            return userInfo;
        }

        /// <summary>
        /// Extracts user information from an API key.
        /// </summary>
        private static UserInfo ExtractUserInfoFromApiKey(string apiKey)
        {
            var userInfo = new UserInfo();

            try
            {
                // Use the ApiKeyService to parse the API key.
                // For example, ParseApiKey might return a GUID string representing the secret.
                if (Guid.TryParse(ApiKeyService.ParseApiKey(apiKey), out var secretId))
                    userInfo.SecretId = secretId;
            }
            catch (Exception ex)
            {
                // If any error occurs, log or ignore as appropriate.
                // In a real implementation, consider logging the error.
            }

            return userInfo;
        }
    }
}
