using System.ComponentModel.DataAnnotations;
using Proptexx.AI.Widget;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Services;

namespace Proptexx.Web.Query.Widget;

public class GetImages : IQuery
{
    [Required] public required string Url { get; init; }

    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var batchId = Guid.NewGuid();
        var workspaceName = context.User.GetWorkspaceName();
        var widgetService = context.GetService<WidgetService>();
        var entry = await widgetService.GetEntry(workspaceName, this.Url);
        var images = entry.Images;
        foreach (var image in images) image.Exception = null;
        var status = BatchService.GetStatus(images);
        var entries = images.Where(x => x.Status == 2).ToList();
        var result = new { batchId, status, entries, images };
        return result;
    }
}