using System.Reflection;
using System.Text.Json;
using Proptexx.Core.Json;

namespace Proptexx.Core.AI;

public interface IModel
{
    Task<ModelResponse> InferAsync(ModelContext context);
}

public class ModelResponse
{
    public required JsonDocument? Document { get; init; }
}


public static class ModelResponseExtensions
{
    public static T? GetNullableResult<T>(this ModelResponse response) where T : class
    {
        return response.Document?.RootElement.Deserialize<T>(JsonDefaults.JsonSerializerOptions);
    }
    
    public static T GetRequiredResult<T>(this ModelResponse response) where T : class
    {
        var result = GetNullableResult<T>(response);
        if (result is null)
        {
            throw new NullReferenceException($"Unable to parse model response");
        }
        return result;
    }
}

public class ModelContext
{
    public required string ItemId { get; init; }

    public required string WorkspaceId { get; init; }

    public JsonDocument? Payload { get; init; }
    
    public CancellationToken CancellationToken { get; init; }
}

public interface IModelProvider
{
    void AddModels(Assembly assembly);

    IModel GetModel(string modelName);

    TModel GetModel<TModel>() where TModel : class, IModel;
}

public sealed class ModelProvider : IModelProvider
{
    public void AddModels(Assembly assembly)
    {
        var modelsNamespace = $"{assembly.GetName().Name}.Models";

        // Find all types in the assembly that implement IModel and are within the specified namespace
        var modelTypes = assembly
            .GetTypes()
            .Where(t => typeof(IModel).IsAssignableFrom(t) && t.Namespace != null && t.Namespace.StartsWith(modelsNamespace))
            .ToList();

        foreach (var modelType in modelTypes)
        {
            // // Convert the namespace and type name to the desired string path format
            // var paths = GetRelativePath(modelType, modelsNamespace);
            // ModelFactory.Add(paths.Item1, modelType);
            // ModelFactory.Add(paths.Item2, modelType);
            //
            // // Register the model type with the service collection as transient
            // services.AddTransient(modelType);
        }
    }

    public IModel GetModel(string modelName)
    {
        throw new NotImplementedException();
    }

    public TModel GetModel<TModel>() where TModel : class, IModel
    {
        throw new NotImplementedException();
    }
}