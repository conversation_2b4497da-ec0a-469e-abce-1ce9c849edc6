using Microsoft.Extensions.Hosting;
using Serilog;

namespace Proptexx.Core.Configuration;

public static class ProptexxLogging
{
    public static IHostApplicationBuilder AddProptexxLogging(this IHostApplicationBuilder builder)
    {
        var config = new LoggerConfiguration()
            .ReadFrom.Configuration(builder.Configuration)
            .Enrich.WithProperty("ApplicationName", builder.Environment.ApplicationName)
            .Enrich.WithProperty("Version", Environment.Version.ToString())
            .Enrich.WithProperty("Environment", builder.Environment.EnvironmentName)
            .Enrich.WithProperty("Hostname", Environment.MachineName)
            .MinimumLevel.Override("Npgsql", Serilog.Events.LogEventLevel.Warning)
            .MinimumLevel.Override("System.Net.Http.HttpClient", Serilog.Events.LogEventLevel.Warning);

        config.WriteTo.Console();

        Log.Logger = config.CreateLogger();
        builder.Services.AddSerilog(Log.Logger);
        return builder;
    }
}