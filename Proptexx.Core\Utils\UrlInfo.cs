namespace Proptexx.Core.Utils;

public sealed class UrlInfo
{
    private UrlInfo() {}

    public required string InputString { get; init; }

    public required string Hostname { get; init; }

    public required string AbsoluteUrl { get; init; }

    public string? Path { get; init; }

    public string? QueryParams { get; init; }

    public static UrlInfo Parse(string url)
    {
        if (!url.Contains("://"))
        {
            url = "https://" + url;
        }

        Uri uri;
        try
        {
            uri = new Uri(url);
        }
        catch (UriFormatException ex)
        {
            throw new ArgumentException("Provided string is not a valid URL", ex);
        }

        return new UrlInfo
        {
            Hostname = uri.Host,
            AbsoluteUrl = uri.AbsoluteUri,
            Path = uri.AbsolutePath,
            QueryParams = uri.Query,
            InputString = url
        };
    }

    
}