using Proptexx.AI;
using Proptexx.Web;
using Proptexx.Web.Api.Handlers;
using Proptexx.Core.Services;
using Proptexx.Core.BigQuery;

var builder = WebApplication.CreateBuilder(args);
builder.AddProptexxWeb();
builder.Services.AddModels();

builder.Services.AddScoped<BatchDataService>();
builder.Services.AddScoped<BigQueryBatchExporter>();
builder.Services.AddScoped<BatchHandler>();
builder.Services.AddScoped<StatusHandler>();
builder.Services.AddScoped<WidgetSyncHandler>();
builder.Services.AddScoped<SequentialHandler>();
builder.Services.AddScoped<ModelHandler>();
builder.Services.AddScoped<FallbackHandler>();
builder.Services.AddScoped<WidgetHandler>();
builder.Services.AddScoped<StoreHandler>();

var app = builder.Build();
app.UseProptexxWeb();

app.MapPost("/batch", (<PERSON><PERSON><PERSON><PERSON><PERSON> handler, HttpContext context) => handler.InvokeAsync(context));
app.MapPost("/widget/sync", (WidgetSyncHandler handler, HttpContext context) => handler.InvokeAsync(context));
app.MapPost("/widget/render", (WidgetHandler handler, HttpContext context) => handler.HandleRenderAsync(context));
app.MapGet("/widget/apikey", (WidgetHandler handler, HttpContext context) => handler.GetApiKeyAsync(context));
app.MapGet("/widget/install-snippet", (WidgetHandler handler, HttpContext context) => handler.HandleGetInstallSnippetAsync(context));
app.MapGet("/status/{id:guid?}", (StatusHandler handler, HttpContext context) => handler.InvokeAsync(context));
app.MapPost("/seq/{id?}", (SequentialHandler handler, HttpContext context) => handler.InvokeAsync(context));
app.MapPost("/store/signup", (StoreHandler handler, HttpContext context) => handler.InvokeAsync(context));
app.MapGet("/store/account", (StoreHandler handler, HttpContext context) => handler.CheckAccountExistsAsync(context));
app.MapGet("/store/api-key", (StoreHandler handler, HttpContext context) => handler.GetApiKeyByWorkspaceTitle(context));
app.MapPost("/{*any}", (ModelHandler handler, HttpContext context) => handler.InvokeAsync(context));
app.MapFallback((FallbackHandler handler, HttpContext context) => handler.InvokeAsync(context));
app.Run();