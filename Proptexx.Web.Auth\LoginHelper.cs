using System.Data;
using Dapper;
using Proptexx.Web.Auth.Auth;

namespace Proptexx.Web.Auth;

public static class <PERSON><PERSON><PERSON><PERSON>per
{
    internal static Task SetAccountClaimsAsync(ScopeContext context, Guid accountId, string fullName, string? email)
    {
        context.AddClaim("$accountId", accountId.ToString());
        context.AddClaim("accountId", accountId.ToString());
        context.AddClaim("$fullName", fullName);
        context.AddClaim("fullName", fullName);

        if (!string.IsNullOrWhiteSpace(email))
        {
            context.AddClaim("email", email);
        }

        return Task.CompletedTask;
    }

    internal static async Task<Guid?> SetWorkspaceClaimsAsync(ScopeContext context, IDbConnection conn, Guid accountId, Guid? workspaceId = null)
    {
        var workspace = await conn.QueryFirstOrDefaultAsync<WorkspaceModel>(WorkspaceModel.Sql, new { _account_id = accountId, _workspace_id = workspaceId });

        if (workspace is not null)
        {
            context.AddClaim("workspaceId", workspace.WorkspaceId.ToString());
            context.AddClaim("workspaceName", workspace.WorkspaceName);
            context.AddClaim("serviceTypes", workspace.ServiceTypes);
            context.AddClaim("services", workspace.Services);
        }
        else
        {
            context.RemoveClaim("workspaceId");
            context.RemoveClaim("workspaceName");
            context.RemoveClaim("serviceTypes");
            context.RemoveClaim("services");
        }

        return workspace?.WorkspaceId;
    }

    internal static Task SetSystemClaimsAsync(ScopeContext context, bool isRoot)
    {
        if (isRoot)
        {
            context.AddClaim("$isRoot", isRoot.ToString().ToLowerInvariant());
        }
        else
        {
            context.RemoveClaim("$isRoot");
        }

        return Task.CompletedTask;
    }
    
    private sealed class WorkspaceModel
    {
        public required Guid WorkspaceId { get; init; }
        
        public required string WorkspaceName { get; init; }

        public required string ServiceTypes { get; init; }
        
        public required string Services { get; init; }

        public static string Sql => @"
            select w.id as workspace_id,
                   w.name as workspace_name,
                   w.created_at,
                   coalesce(string_agg(distinct services.service_type::text, ' '), '') as service_types,
                   coalesce(string_agg(distinct services.service_id::text, ' '), '') as services
            from core.workspace w
                     join core.cluster c on w.id = c.workspace_id
                     join core.cluster_account_binding cab on c.id = cab.cluster_id
                     join core.account a on cab.account_id = a.id
                     left outer join (
                        select tsb.service_id,
                               tsb.workspace_id,
                               s.service_type
                        from core.workspace_service_binding tsb
                                 join core.service s on tsb.service_id = s.id
                        where tsb.expires_at is null
                           or tsb.expires_at > current_timestamp) as services on w.id = services.workspace_id
            where cab.account_id = :_account_id and lower(c.name) in ('administrator', 'member')
              and ((:_workspace_id is not null and :_workspace_id = w.id) or (:_workspace_id is null and coalesce(a.workspace_id, w.id) = w.id))
            group by w.id, w.name, w.created_at
            order by w.created_at
            limit 1;
        ";
    }
}