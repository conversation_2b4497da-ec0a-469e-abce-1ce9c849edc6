### Test All Widget Commands with RequiredServiceAttribute

### EXPENSIVE AI COMMANDS (require real-estate-widget or widget-access)

### 1. ProcessRefurnishingProductReplacement
POST https://command.dev.local/_command
Content-Type: application/json
Authorization: ApiKey YOUR_VALID_API_KEY_HERE

{
  "identifier": "widget.ProcessRefurnishingProductReplacement",
  "payload": {
    "RoomImage": "https://example.com/room.jpg",
    "ProductImageUrl": "https://example.com/product.jpg",
    "ProductName": "Modern Sofa",
    "ProductDescription": "A comfortable modern sofa"
  }
}

### 2. SubmitImages
POST https://command.dev.local/_command
Content-Type: application/json
Authorization: ApiKey YOUR_VALID_API_KEY_HERE

{
  "identifier": "widget.SubmitImages",
  "payload": {
    "Url": "https://example.com/listing",
    "Images": ["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
    "ForceReload": false
  }
}

### 3. StagingOrRefurnishingV2Command
POST https://command.dev.local/_command
Content-Type: application/json
Authorization: ApiKey YOUR_VALID_API_KEY_HERE

{
  "identifier": "widget.StagingOrRefurnishingV2Command",
  "payload": {
    "ImageBase64": "/9j/4AAQSkZJRgABAQEAAAAAAAD...",
    "RoomType": "living room",
    "SceneType": "indoor"
  }
}

### 4. IndoorStagingOrRefurnishing
POST https://command.dev.local/_command
Content-Type: application/json
Authorization: ApiKey YOUR_VALID_API_KEY_HERE

{
  "identifier": "widget.IndoorStagingOrRefurnishing",
  "payload": {
    "ImageUrl": "https://example.com/room.jpg",
    "RoomType": "living room",
    "ArchitectureStyle": "modern",
    "Url": "https://example.com/listing"
  }
}

### LIGHTWEIGHT COMMANDS (require real-estate-widget or widget-access)

### 5. SendMessage
POST https://command.dev.local/_command
Content-Type: application/json
Authorization: ApiKey YOUR_VALID_API_KEY_HERE

{
  "identifier": "widget.SendMessage",
  "payload": {
    "Message": "What's the best color for a living room?"
  }
}

### 6. SendChat
POST https://command.dev.local/_command
Content-Type: application/json
Authorization: ApiKey YOUR_VALID_API_KEY_HERE

{
  "identifier": "widget.SendChat",
  "payload": {
    "Message": "Hello, I need help with interior design"
  }
}

### 7. SubmitFeedback
POST https://command.dev.local/_command
Content-Type: application/json
Authorization: ApiKey YOUR_VALID_API_KEY_HERE

{
  "identifier": "widget.SubmitFeedback",
  "payload": {
    "Url": "https://example.com/listing",
    "Source": "widget",
    "Output": "generated_image_url",
    "Type": "positive",
    "Value": "Great result!"
  }
}

### 8. UpdateProfile
POST https://command.dev.local/_command
Content-Type: application/json
Authorization: ApiKey YOUR_VALID_API_KEY_HERE

{
  "identifier": "widget.UpdateProfile",
  "payload": {
    "FullName": "John Doe",
    "Email": "<EMAIL>"
  }
}

### AUTHENTICATION COMMANDS (require real-estate-widget or widget-access)

### 9. Login
POST https://command.dev.local/_command
Content-Type: application/json
Authorization: ApiKey YOUR_VALID_API_KEY_HERE

{
  "identifier": "widget.Login",
  "payload": {
    "Username": "<EMAIL>"
  }
}

### 10. SetPassword
POST https://command.dev.local/_command
Content-Type: application/json
Authorization: ApiKey YOUR_VALID_API_KEY_HERE

{
  "identifier": "widget.SetPassword",
  "payload": {}
}

### NEGATIVE TESTS (should fail with access denied)

### Test with invalid/restricted API key
POST https://command.dev.local/_command
Content-Type: application/json
Authorization: ApiKey INVALID_OR_RESTRICTED_API_KEY

{
  "identifier": "widget.ProcessRefurnishingProductReplacement",
  "payload": {
    "RoomImage": "https://example.com/room.jpg",
    "ProductImageUrl": "https://example.com/product.jpg",
    "ProductName": "Modern Sofa"
  }
}

### Expected Results:
# - All commands with valid API keys having real-estate-widget or widget-access service should succeed
# - Commands with invalid/restricted API keys should return "Access denied: Real estate widget or widget access service required"
# - Commands with valid API keys but missing required services should return the same access denied message

### Notes:
# - Replace YOUR_VALID_API_KEY_HERE with actual API keys that have the required services
# - Replace INVALID_OR_RESTRICTED_API_KEY with an API key that doesn't have the required services
# - The RequiredServiceAttribute now handles all access control automatically
# - No more manual service checking in individual commands 