using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.System;

public sealed class GetOrders : IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        context.User.EnsureRootAccess();
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var enumerable = await npgsql.QueryAsync<OrderModel>(OrderModel.Sql);
        return enumerable;
    }

    private sealed class OrderModel
    {
        public Guid OrderLineId { get; init; }
        
        public Guid OrderId { get; init; }
        
        public Guid ProductId { get; init; }
        
        public Guid WorkspaceId { get; init; }
        
        public int OrderNbr { get; init; }
        
        public required string WorkspaceName { get; init; }
        
        public required string ProductTitle { get; init; }
        
        public required decimal PriceAmount { get; init; }
        
        public required string Currency { get; init; }
        
        public string? Reference<PERSON>erson { get; init; }
        
        public DateTime? PaidAt { get; init; }
        
        public DateTime CreatedAt { get; init; }

        public static string Sql => @"
            select ol.id as order_line_id,
                   ol.order_id,
                   ol.product_id,
                   ol.workspace_id,
                   o.order_nbr,
                   t.name as workspace_name,
                   o.reference_person,
                   o.paid_at,
                   o.created_at,
                   p.title as product_title,
                   p.price_amount,
                   p.currency
            from core.order_line ol
            join core.workspace t on ol.workspace_id = t.id
            join core.order o on ol.order_id = o.id
            join core.product p on ol.product_id = p.id
            order by o.created_at desc;
        ";
    }
}
