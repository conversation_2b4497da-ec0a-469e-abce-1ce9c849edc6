using Dapper;
using Npgsql;
using Proptexx.Core.Entities;
using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Services;

namespace Proptexx.Core.Stores;

public class AccountStore
{
    private readonly NpgsqlConnection _connection;

    public AccountStore(NpgsqlConnection connection) => _connection = connection;

    public async Task<Account> Create(string firstName, string familyName)
    {
        var account = new Account
        {
            FirstName = firstName,
            FamilyName = familyName
        };

        await _connection.InsertAsync(account);
        return account;
    }

    public async Task AddPhone(Guid accountId, string number, bool isVerified)
    {
        if (string.IsNullOrWhiteSpace(number)) return;

        var phone = new AccountPhone
        {
            AccountId = accountId,
            Number = number,
        };

        if (isVerified) phone.Verify();

        await _connection.InsertAsync(phone);
    }

    public async Task AddEmail(Guid accountId, string address, bool isVerified)
    {
        if (string.IsNullOrWhiteSpace(address)) return;

        var email = new AccountEmail
        {
            AccountId = accountId,
            Email = address
        };

        if (isVerified) email.Verify();

        await _connection.InsertAsync(email);
    }

    public async Task AddData(Guid accountId, string dataKey, string dataValue)
    {
        var data = new AccountData
        {
            AccountId = accountId,
            DataKey = dataKey,
            DataValue = dataValue
        };

        await _connection.InsertAsync(data);
    }

    public async Task SetSecret(Guid accountId, string password)
    {
        if (string.IsNullOrWhiteSpace(password))
        {
            throw new NullReferenceException(nameof(password));
        }

        await _connection.ExecuteAsync(@"
            update core.account_secret 
            set expired_at = current_timestamp 
            where account_id = :_account_id and expired_at is null 
        ", new { _account_id = accountId });

        var (hash, salt) = PasswordService.GenerateSecret(password);
        var secret = new AccountSecret
        {
            AccountId = accountId,
            Hash = hash,
            Salt = salt
        };

        await _connection.InsertAsync(secret);
    }
    
    public async Task SetWorkspaceId(Guid accountId, Guid workspaceId)
    {
        await _connection.ExecuteAsync(@"
            update core.account 
            set workspace_id = :workspace_id 
            where id = :_account_id
        ", new { _account_id = accountId, workspace_id = workspaceId });
    }

    public async Task JoinCluster(Guid accountId, Guid clusterId, bool verifyNow = true)
    {
        var cab = new ClusterAccountBinding
        {
            AccountId = accountId,
            ClusterId = clusterId,
            VerifiedAt = verifyNow ? DateTime.UtcNow : null
        };

        var conflictHandler = new IgnoreOnConflictHandler(ConflictType.Constraint, "pk_cluster_account_binding");
        await _connection.InsertAsync(cab, conflictHandler);
    }

    public async Task JoinCluster(Guid accountId, Guid workspaceId, string? clusterTypeName, bool verifyNow = true)
    {
        if (string.IsNullOrWhiteSpace(clusterTypeName)) return;
        
        var clusterId = await _connection.Cluster()
            .GetClusterIdByClusterTypeName(workspaceId, clusterTypeName);

        if (!clusterId.HasValue) return;

        await JoinCluster(accountId, clusterId.Value, verifyNow);
    }

    public async Task Update(Guid accountId, string firstName, string familyName, string email)
    {
        var account = await this._connection.QueryFirstAsync<Account>("select a.* from core.account a where a.id = :_account_id", new { _account_id = accountId });
        account.FirstName = firstName;
        account.FamilyName = familyName;

        await this._connection.UpdateAsync(account);

        const string sql = "select * from core.account_email where account_id = :_account_id";
        var param = new { _account_id = accountId };
        var accountEmails = await this._connection.QueryAsync<AccountEmail>(sql, param);

        var accountEmail = accountEmails.MinBy(x => x.VerifiedAt);

        if (accountEmail is not null && !accountEmail.Email.Equals(email))
        {
            accountEmail.Update(email);
            await this._connection.UpdateAsync(accountEmail, "Email", "VerifiedAt");
        }
    }

    public async Task Update(Guid accountId, string firstName, string familyName, int? gender)
    {
        var account = await this._connection.QueryFirstAsync<Account>("select a.* from core.account a where a.id = :_account_id", new { _account_id = accountId });
        account.FirstName = firstName;
        account.FamilyName = familyName;
        account.Gender = gender;

        await this._connection.UpdateAsync(account);
        
        // var accountPhone = await this._connection.GetFirstOrDefaultAsync<AccountPhone>(
        //     x => x.AccountId == accountId);
        //
        // if (accountPhone is not null && !accountPhone.Number.Equals(phone))
        // {
        //     accountPhone.Update(phone);
        //     await this._connection.UpdateAsync(accountPhone, "Number", "VerifiedAt");
        // }
        //
        // var accountEmail = await this._connection.GetFirstOrDefaultAsync<AccountEmail>(
        //     x => x.AccountId == accountId);
        //
        // if (accountEmail is not null && !accountEmail.Email.Equals(email))
        // {
        //     accountEmail.Update(email);
        //     await this._connection.UpdateAsync(accountEmail, "Email", "VerifiedAt");
        // }
    }

    public async Task<bool> EmailExists(string email)
    {
        const string sql = @"
            select ae.account_id from core.account_email ae where trim(lower(ae.email)) = :_email;
        ";
 
        var param = new { _email = email.Trim().ToLowerInvariant() };
        var res = await _connection.QueryAsync(sql, param);
        return res.Any();
    }

    public async Task<bool> EmailExistsOnWorkspace(Guid workspaceId, string email)
    {
        const string sql = @"
            select ae.account_id
            from core.account_email ae
            join core.account a on ae.account_id = a.id
            join core.cluster_account_binding cab on a.id = cab.account_id
            join core.cluster c on cab.cluster_id = c.id and c.workspace_id = :_workspace_id
            where trim(lower(ae.email)) = :_email
            limit 1;
        ";
 
        var param = new { _email = email.ToLowerInvariant().Trim(), _workspace_id = workspaceId };
        var res = await _connection.QueryFirstOrDefaultAsync(sql, param);
        return res is not null;
    }

    public Task<Account?> GetAccountIdByEmailAsync(string email)
    {
        const string sql = @"
            select a.*
            from core.account a 
            join core.account_email ae on a.id = ae.account_id 
            where trim(lower(ae.email)) = :_email";

        return _connection.QueryFirstOrDefaultAsync<Account>(sql, new { _email = email.ToLowerInvariant().Trim() });
    }

    public async Task<string?> GetEmailByIdAsync(Guid accountId)
    {
        const string sql = @"
            select ae.email
            from core.account a
            join core.account_email ae on a.id = ae.account_id
            where a.id = :_account_id
            order by ae.created_at desc
            limit 1
        ";

        var x = await _connection.QueryFirstOrDefaultAsync<string>(sql, new { _account_id = accountId });
        return x;
    }

    public async Task<bool> PhoneExists(string number)
    {
        const string sql = @"
            select ae.account_id from core.account_phone ae where trim(lower(ae.number)) = :_email;
        ";
 
        var param = new { _email = number.Trim().ToLowerInvariant() };
        var res = await _connection.QueryAsync(sql, param);
        return res.Any();
    }
}