using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Proptexx.Core.DTOs.Outseta // Changed namespace
{
    public class ActivityEventData
    {
        public string? Name { get; set; }
        public string? InvoiceNotes { get; set; }
        public bool IsDemo { get; set; }
        public int AccountStage { get; set; }
        public List<ActivityEventPersonAccountItem>? PersonAccount { get; set; }
        public string? TaxStatus { get; set; }
        public string? AccountStageLabel { get; set; }
        public bool HasLoggedIn { get; set; }
        public decimal LifetimeRevenue { get; set; }
        public string? Nonce { get; set; }
        public ActivityEventPerson? PrimaryContact { get; set; }
        public bool TaxIdIsInvalid { get; set; }
        public bool IsLivemode { get; set; }
        public bool SchemaLessDataLoaded { get; set; }
        [JsonPropertyName("_objectType")]
        public string? ObjectType { get; set; }
        public DateTime Created { get; set; }
        public DateTime Updated { get; set; }
        public string? StoreUrl { get; set; }
        public string? Platform { get; set; }
        public int Viewers { get; set; }
    }
}
