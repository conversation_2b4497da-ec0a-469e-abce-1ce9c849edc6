using System.Net.Http.Json;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Proptexx.AI;
using Proptexx.Core.AI;
using Proptexx.Core.Http;
using Proptexx.Core.Services;
using Proptexx.Core.Storage;

namespace Proptexx.AI.Models.Gen;

public sealed class GenerativeCompliancePostcheck : BaseModel
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly string _cvModelEndpoint;
    private readonly string _preprocessingEndpoint;

    public GenerativeCompliancePostcheck(IHttpClientFactory httpClientFactory,
        IConfiguration configuration)
    {
        _httpClientFactory = httpClientFactory;

        _cvModelEndpoint = configuration.GetValue<string>("cv_model_dns")
                          ?? throw new NullReferenceException("cv_model_dns");
        _preprocessingEndpoint = configuration.GetValue<string>("preprocessing_model_dns")
                                 ?? throw new NullReferenceException("preprocessing_model_dns");
    }

    public override async Task<ModelResponse> InferAsync(ModelContext context)
    {
        if (context.Payload is null)
            throw new NullReferenceException(nameof(context.Payload));
        var imageOriginalUrl = PayloadService.GetRequiredString(context.Payload, "image_orginal_url");
        var imageGeneratedUrl = PayloadService.GetRequiredString(context.Payload, "image_genrated_url");
        var doc = await RunAsync(imageOriginalUrl, imageGeneratedUrl, context.CancellationToken);
        return new ModelResponse { Document = doc };
    }

    public async Task<JsonDocument> RunAsync(string imageOriginalUrl, string imageGeneratedUrl, CancellationToken cancellationToken = default)
    {
        var httpClient = _httpClientFactory.CreateClient();
        var reqBase64Img = await ImageService.DownloadImageAsBase64Async(_httpClientFactory, imageGeneratedUrl, cancellationToken);

        // 1. PostProcessorV4
        var postPayload = new { image = reqBase64Img, processor_name = "PostProcessorV4" };
        var postResp = await httpClient.PostAsJsonAsync($"{_cvModelEndpoint}/info/", postPayload, cancellationToken);
        postResp.EnsureSuccessStatusCode();
        var postJson = await postResp.Content.ReadFromJsonAsync<JsonElement>();

        // 2. Detect API
        var detectPayload = new { image_original = imageOriginalUrl, image_generated = imageGeneratedUrl };
        var detectResp = await httpClient.PostAsJsonAsync($"{_preprocessingEndpoint}/detect/", detectPayload, cancellationToken);
        detectResp.EnsureSuccessStatusCode();
        var detectJson = await detectResp.Content.ReadFromJsonAsync<JsonElement>();

        // Map filter (copy as is)
        var filter = postJson;

        // Map quality_checkers
        var deficiencies = detectJson.GetPropertyOrDefault("deficiencies") ?? new JsonElement();
        var deficiencyCauses = new List<string>();
        if (deficiencies.ValueKind == JsonValueKind.Object)
        {
            foreach (var prop in deficiencies.EnumerateObject())
            {
                if (prop.Value.ValueKind == JsonValueKind.True)
                {
                    deficiencyCauses.Add(prop.Name);
                }
            }
        }
        var totalScore = detectJson.GetPropertyOrDefault("total_score")?.GetDouble() ?? 0;
        var interpretation = detectJson.GetPropertyOrDefault("interpretation")?.GetString() ?? string.Empty;

        var qualityCheckers = new Dictionary<string, object?>
        {
            ["total_quality_score"] = totalScore,
            ["Total_quality_interpretation"] = interpretation,
            ["deficiency_causes"] = deficiencyCauses
        };

        var result = new Dictionary<string, object?>
        {
            ["filter"] = filter,
            ["quality_checkers"] = qualityCheckers
        };
        return JsonDocument.Parse(JsonSerializer.Serialize(result));
    }
}
