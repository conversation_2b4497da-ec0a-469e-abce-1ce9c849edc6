using System.Text.Json;
using Dapper;
using Npgsql;
using Proptexx.Core;
using Proptexx.Core.Entities;
using Proptexx.Core.Redis;

namespace Proptexx.Worker.DataSync;

public static class WidgetDataSync
{
    internal static async Task SyncAsync(IServiceProvider services, CancellationToken cancellationToken)
   {
        var dataSource = services.GetRequiredService<NpgsqlDataSource>();
        var widgetStore = services.GetRequiredService<WidgetStore>();

        await using var npgsql = await dataSource.OpenConnectionAsync(cancellationToken);
        var enumerable = await npgsql.QueryAsync<WidgetModel>(WidgetModel.Sql);

        var widgets = enumerable.Select(m => new WidgetStore.WidgetModel
        {
            ApiKey = m.ApiKey,
            WorkspaceId = m.WorkspaceId,
            WorkspaceName = m.WorkspaceName,
            ClientName = m.ClientName,
            Services = m.Services,
            Domains = ParseDomains(m.Domains),
            Options = new WidgetOptions
            {
                ImgUrlPattern = m.ImgUrlPattern,
                Layout = m.Layout,
                Locale = m.Locale,
                Position = m.Position,
                BackgroundColor = m.BackgroundColor,
                CustomTrigger = m.CustomTrigger,
                DarkColor = m.DarkColor,
                LightColor = m.LightColor,
                LogoUrl = m.LogoUrl,
                PrimaryColor = m.PrimaryColor,
                TriggerUrl = m.TriggerUrl,
                Elevation = m.Elevation,
                Offset = m.Offset,
                CustomizeWarning = m.CustomizeWarning,
                IsOpen = m.IsOpen,
                SingleImage = m.SingleImage,
                SkipAuth = m.SkipAuth,
                UseAds = m.UseAds,
                HideSidebar = m.HideSidebar,
                CustomizeLogoUrl = m.CustomizeLogoUrl,
                OptLogoUrl = m.OptLogoUrl,
                ScratchedImageMinWidth = m.ScratchedImageMinWidth,
                ScratchedImageMinHeight = m.ScratchedImageMinHeight,
                ScratchedImageMinAspectRatio = m.ScratchedImageMinAspectRatio,
                ScratchedImageMaxAspectRatio = m.ScratchedImageMaxAspectRatio,
                ScratchedImageMinFileSizeInBytes = m.ScratchedImageMinFileSizeInBytes,
                ScratchedImageAllowedFormats = m.ScratchedImageAllowedFormats,
                ScratchedImageExcludedKeywords = m.ScratchedImageExcludedKeywords,
                ScopeSelector = m.ScopeSelector
            }
        }).ToList();

        widgetStore.Persist(widgets);
    }

    private static Dictionary<string, MatchStrategy> ParseDomains(JsonDocument document)
    {
        var result = new Dictionary<string, MatchStrategy>();
        if (document.RootElement.ValueKind != JsonValueKind.Object)
        {
            return result;
        }

        var obj = document.RootElement.EnumerateObject();
        while (obj.MoveNext())
        {
            var key = obj.Current.Name;
            var value = (MatchStrategy)obj.Current.Value.GetInt32();
            result.Add(key, value);
        }

        return result;
    }

    public class WidgetModel : WidgetOptions
    {
        public Guid WorkspaceId { get; init; }

        public required string ApiKey { get; set; }

        public required string ClientName { get; set; }

        public required string WorkspaceName { get; set; }

        public required JsonDocument Domains { get; init; }

        public required string Services { get; init; }

        internal const string Sql = @"
            select core.generate_api_key(cs.id) as api_key,
                   c.name as client_name,
                   w.id as workspace_id,
                   w.name as workspace_name,
                   coalesce(jsonb_object_agg(d.input_value, d.match_strategy) filter ( where d.input_value is not null), '{}') as domains,
                   coalesce(string_agg(distinct tsb.service_id, ' '), '') as services,
                   wi.*
            from core.client_secret cs
            join core.client c on cs.client_id = c.id
            join core.workspace w on c.workspace_id = w.id
            left outer join core.widget wi on w.id = wi.workspace_id
            left outer join core.workspace_service_binding tsb on w.id = tsb.workspace_id and (tsb.expires_at is null or tsb.expires_at > current_timestamp)            
            left outer join (
                select distinct on (workspace_id, input_value)
                    workspace_id,
                    trim(lower(input_value)) as input_value,
                    match_strategy
                from core.domain
                where input_value is not null) d on w.id = d.workspace_id
            where lower(c.name) in ('real-estate-widget', 'widget', 'ecommerce-widget')
                  and (c.cancelled_at is null or c.cancelled_at > current_timestamp)
            group by w.id, c.id, cs.id, wi.workspace_id;
        ";
    }
}