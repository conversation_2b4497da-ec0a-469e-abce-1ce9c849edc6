using System.ComponentModel.DataAnnotations;
using System.Diagnostics;
using System.Text.Encodings.Web;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Json;

namespace Proptexx.Core.Cqrs.Query;

public class QueryHttpHandler
{
    private readonly QueryConfig _config;
    private readonly ILogger<QueryHttpHandler> _logger;

    private static readonly JsonSerializerOptions JsonSerializerOptions = new(JsonDefaults.JsonSerializerOptions)
    {
        Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping
    };

    public QueryHttpHandler(QueryConfig config, ILogger<QueryHttpHandler> logger)
    {
        _config = config;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext httpContext, RequestDelegate next)
    {
        var response = new QueryResponse();

        try
        {
            var context = await QueryMiddlewareContext.FromHttpAsync(httpContext);

            foreach (var (key, query) in context.Request)
            {
                var handler = _config.GetHandler(query.Handler ?? string.Empty)
                              ?? throw new QueryException("Query handler not found");

                var ctx = QueryContext.FromHttp(httpContext, query);
                
                if (!await handler.HasAccessAsync(ctx))
                {
                    throw new QueryException("Unauthorized");
                }

                var handlerResult = await handler.ExecuteAsync(ctx);

                // Keep like this until DoorInsider has migrated to newer version of sdk, then
                // can return only the first statement's body
                if (context.User.HasClientVersion()) 
                {
                    response.Results.Add(key, handlerResult.Result); // Correct moving forward
                }
                else
                {
                    response.Results.Add(key, handlerResult); // Deprecated, but held back for DoorInsider
                }
            }

            httpContext.Response.StatusCode = StatusCodes.Status200OK;
            response.Success();
        }
        catch (QueryException e)
        {
            response.Error(e.Message);
            httpContext.Response.StatusCode = StatusCodes.Status400BadRequest;
        }
        catch (ValidationException e)
        {
            response.Error(e.Message);
            httpContext.Response.StatusCode = StatusCodes.Status403Forbidden;
        }
        catch (UnauthorizedAccessException e)
        {
            response.Error(e.Message);
            httpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
        }
        catch (Exception e)
        {
            _logger.LogError(e, $"Exception in {nameof(QueryHttpHandler)}");
            response.Error("An error occured. We are notified and will look into it");
            httpContext.Response.StatusCode = StatusCodes.Status500InternalServerError;
        }
        finally
        {
            await httpContext.Response.WriteAsJsonAsync(response, JsonSerializerOptions);
        }
    }

    private class QueryResponse
    {
        private readonly Stopwatch _timer = Stopwatch.StartNew();
    
        public Dictionary<string, object?> Results { get; } = [];

        public long TotalTime => _timer.ElapsedMilliseconds; // Deprected - waiting for DoorInsider to bump SDK version
        
        public long TimeTaken => _timer.ElapsedMilliseconds;

        public string? ErrorMessage { get; private set; }

        public bool IsSuccess { get; private set; }

        public void Success()
        {
            IsSuccess = true;
            Completed();
        }

        public void Error(string errorMessage)
        {
            ErrorMessage = errorMessage;
            Completed();
        }

        private void Completed()
        {
            _timer.Stop();
        }
    }
}