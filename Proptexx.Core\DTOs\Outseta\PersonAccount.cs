using System;
using System.Text.Json.Serialization;

namespace Proptexx.Core.DTOs.Outseta // Changed namespace
{
    public class PersonAccount
    {
        public Person? Person { get; set; }
        public NestedAccountInfo? Account { get; set; } 
        public bool IsPrimary { get; set; }
        public bool ReceiveInvoices { get; set; }
        public string? Uid { get; set; }
        [JsonPropertyName("_objectType")]
        public string? ObjectType { get; set; }
        public DateTime Created { get; set; }
        public DateTime Updated { get; set; }
    }
}
