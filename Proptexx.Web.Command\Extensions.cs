﻿using System.Reflection;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Services;

namespace Proptexx.Web.Command;

public static class Extensions
{
    public static TypeRegistry<ICommand> AddObjectCommands(this TypeRegistry<ICommand> registry)
    {
        var assembly = Assembly.GetExecutingAssembly();
        registry.Add("account", assembly, "Account", true);
        registry.Add("message", assembly, "Message", true);
        registry.Add("workspace", assembly, "Workspace", true);
        registry.Add("widget", assembly, "Widget", true);
        registry.Add("service", assembly, "Service", true);
        registry.Add("system", assembly, "System", true);
        return registry;
    }
}