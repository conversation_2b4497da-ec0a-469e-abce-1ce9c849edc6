using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class AccountPhone : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; } = Guid.NewGuid();

    public required Guid AccountId { get; init; }
    
    public required string Number { get; set; }

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public DateTime? VerifiedAt { get; private set; }

    public string GetDbRef() => "core.account_phone";

    public void Update(string phoneNumber)
    {
        this.Number = phoneNumber;
        this.VerifiedAt = null;
    }

    public AccountPhone Verify()
    {
        this.VerifiedAt = DateTime.UtcNow;
        return this;
    }
}