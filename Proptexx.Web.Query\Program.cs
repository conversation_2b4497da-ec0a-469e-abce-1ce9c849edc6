using Proptexx.AI;
using Proptexx.AI.Widget;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Cqrs.Query.Handlers;
using Proptexx.Web;
using Proptexx.Web.Query;

var builder = WebApplication.CreateBuilder(args);
builder.AddProptexxWeb();
builder.Services.AddChatServices();
builder.Services.AddQueries(query =>
    query.AddHandler(new ObjectQueryHandler(registry => registry.AddObjectQueries())));

builder.Services.AddScoped<WidgetService>();

builder.Services.AddCors(cors =>
{
    cors.AddDefaultPolicy(p => p
        .SetIsOriginAllowed(_ => true)
        .AllowAnyHeader()
        .AllowAnyMethod()
        .AllowCredentials()
        .Build());
});

var app = builder.Build();
app.Map("", () => "Proptexx | Query");
app.UseProptexxWeb();
app.MapGet("/", () => "Proptexx | Query");
app.MapPost("/_query", (QueryHttpHandler handler, HttpContext context) => handler.InvokeAsync(context, _ => Task.CompletedTask));
app.Run();
