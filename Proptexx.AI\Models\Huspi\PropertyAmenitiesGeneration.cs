using System.Text.Json.Serialization;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Http;
using Proptexx.Core.Services;
using StackExchange.Redis;

namespace Proptexx.AI.Models.Huspi;

public sealed class PropertyAmenitiesGeneration(
    IComputerVisionClient computerVisionClient,
    IImageAssessmentClient imageAssessmentClient,
    IConnectionMultiplexer connectionMultiplexer,
    ILoggerFactory loggerFactory)
    : BaseComputerVisionModel(
        "PropertyAmenitiesGeneration",
        computerVisionClient,
        imageAssessmentClient,
        connectionMultiplexer,
        loggerFactory)
{
    public sealed class Result
    {
        [JsonPropertyName("propertyAmenities")]
        public string[]? PropertyAmenities { get; init; }
    }
}
