using System.Reflection;

namespace Proptexx.Core.Services;

public sealed class TypeRegistry<T>
{
    private readonly Dictionary<string, List<TypeScanner.Descriptor>> _descriptors =
        new(StringComparer.OrdinalIgnoreCase);

    private IDictionary<string, Type>? _registry;

    public TypeRegistry<T> Add(string? identifier, Assembly assembly, string? assemblyPath = null, bool searchRecursive = false)
    {
        identifier ??= "";

        var desc = new TypeScanner.Descriptor
        {
            Assembly = assembly,
            AssemblyPath = assemblyPath,
            SearchRecursive = searchRecursive
        };

        if (!_descriptors.ContainsKey(identifier))
        {
            _descriptors.Add(identifier, new List<TypeScanner.Descriptor>());
        }

        _descriptors[identifier].Add(desc);
        return this;
    }

    public TypeRegistry<T> Add(Assembly assembly, string? assemblyPath = null, bool searchRecursive = false)
        => Add(null, assembly, assemblyPath, searchRecursive);

    internal void Initialize()
    {
        _registry = TypeScanner.Scan<T>(_descriptors);
    }

    internal Type? Get(string name)
    {
        if (_registry is null) return null;
        if (string.IsNullOrWhiteSpace(name)) return null;

        var key = name.ToUpperInvariant().Trim();
        return !_registry.TryGetValue(key, out var value) ? null : value;
    }
}
