using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations
{
    [Migration(250321002)]
    public class Migrate250321002 : FluentMigrator.Migration
    {
        private const string TableName = "api_logs";
        private const string SchemaName = "telemetry";

        public override void Up()
        {
            Alter.Table(TableName)
                .InSchema(SchemaName)
                .AddColumn("is_batch").AsBoolean().NotNullable().WithDefaultValue(false)
                .AddColumn("is_api_request").AsBoolean().NotNullable().WithDefaultValue(false)
                .AddColumn("is_ai_request").AsBoolean().NotNullable().WithDefaultValue(false)
                .AddColumn("response_code").AsInt32().NotNullable().WithDefaultValue(200)
                .AddColumn("identifier").AsString(255).Nullable()  // Supports multiple identifiers
                .AddColumn("clean_endpoint").AsString(255).Nullable()
                .AddColumn("processed_endpoint").AsString(255).Nullable();

        }

        public override void Down()
        {
            Delete.Column("is_batch").FromTable(TableName).InSchema(SchemaName);
            Delete.Column("is_api_request").FromTable(TableName).InSchema(SchemaName);
            Delete.Column("is_ai_request").FromTable(TableName).InSchema(SchemaName);
            Delete.Column("response_code").FromTable(TableName).InSchema(SchemaName);
            Delete.Column("identifier").FromTable(TableName).InSchema(SchemaName);
            Delete.Column("clean_endpoint").FromTable(TableName).InSchema(SchemaName);
            Delete.Column("processed_endpoint").FromTable(TableName).InSchema(SchemaName);
        }
    }
}