using Proptexx.Core.Entities;

namespace Proptexx.Core.Services;

/// <summary>
/// Interface for subscription validation operations
/// </summary>
public interface ISubscriptionValidationService
{
    /// <summary>
    /// Validate API key and return validation result
    /// </summary>
    Task<SubscriptionValidationResult> ValidateAsync(string apiKey, CancellationToken cancellationToken = default);
}

/// <summary>
/// Result of subscription validation
/// </summary>
public record SubscriptionValidationResult
{
    public bool IsValid { get; init; }
    public string Reason { get; init; } = string.Empty;
    public ClientSubscription? Subscription { get; init; }
    public double UsagePercentage { get; init; }
    public int RemainingQuota { get; init; }
} 