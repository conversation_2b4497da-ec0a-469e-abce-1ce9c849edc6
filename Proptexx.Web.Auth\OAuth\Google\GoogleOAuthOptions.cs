﻿namespace Proptexx.Web.Auth.OAuth.Google
{
    public sealed class GoogleOAuthOptions
    {
        public required string ClientId { get; init; }

        public required string ClientSecret { get; init; }

        public required string AuthorityUri { get; init; }

        public required string AccessTokenUri { get; init; }
        
        public required string UserInfoUri { get; init; }

        public required string RedirectUri { get; init; }
        
        public required string Scope { get; init; }
        
        
    }
}
