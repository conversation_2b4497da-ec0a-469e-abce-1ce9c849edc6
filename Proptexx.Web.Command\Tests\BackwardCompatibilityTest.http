### Test Backward Compatibility of HasAccessAsync Changes

### 1. Test commands WITHOUT RequiredServiceAttribute (should work as before)
POST https://command.dev.local/_command
Content-Type: application/json
Authorization: ApiKey YOUR_API_KEY_HERE

{
  "identifier": "system.CreateCoupon",
  "payload": {
    "Code": "TEST2025",
    "Discount": 10
  }
}

### 2. Test commands WITH RequiredServiceAttribute (new functionality)
POST https://command.dev.local/_command
Content-Type: application/json
Authorization: ApiKey YOUR_API_KEY_HERE

{
  "identifier": "widget.Login",
  "payload": {
    "Username": "<EMAIL>"
  }
}

### 3. Test with invalid command identifier (should return false as before)
POST https://command.dev.local/_command
Content-Type: application/json
Authorization: ApiKey YOUR_API_KEY_HERE

{
  "identifier": "nonexistent.Command",
  "payload": {}
}

### Expected Results:
# 1. CreateCoupon: Should work if user has root access (no attribute validation)
# 2. widget.Login: Should validate RequiredServiceAttribute if service is registered
# 3. nonexistent.Command: Should return 401/403 as before 