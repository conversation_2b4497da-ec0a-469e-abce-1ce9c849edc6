using System.Security.Claims;
using System.Security.Principal;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Proptexx.Core.Auth;
using Proptexx.Core.Extensions;
using Proptexx.Core.Options;

namespace Proptexx.Core;

public class ProptexxIdentity : ClaimsIdentity
{
    public ProptexxIdentity(
        IDictionary<string, string> claims, 
        string callerIdentifier)
        : base(claims.ToClaims(), JwtBearerDefaults.AuthenticationScheme)
    {
        CallerIdentifier = callerIdentifier;
    }
    
    public ProptexxIdentity(
        IEnumerable<Claim> claims, 
        string callerIdentifier,
        bool? issueCookie)
        : base(claims, JwtBearerDefaults.AuthenticationScheme)
    {
        CallerIdentifier = callerIdentifier;
        IssueCookie = issueCookie;
    }

    public string CallerIdentifier { get; }

    public bool? IssueCookie { get; }

    public override bool IsAuthenticated =>
        base.IsAuthenticated && HasClaim(
            c => c.Type.Equals("$clientId", StringComparison.CurrentCultureIgnoreCase));

    public static ProptexxIdentity FromBearer(ProptexxOptions proptexxOptions, string accessToken, bool? issueCookie)
    {
        var claimsFromToken = AuthService.ClaimsFromToken(proptexxOptions, accessToken);
        return new ProptexxIdentity(claimsFromToken, proptexxOptions.CallerIdentifier, issueCookie);
    }
}

public sealed class ProptexxPrincipal : ClaimsPrincipal
{
    public ProptexxPrincipal(IIdentity identity) : base(identity)
    {
    }
}