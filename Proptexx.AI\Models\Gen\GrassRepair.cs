using System.Net.Http.Json;
using System.Text.Json;
using Google.Cloud.AIPlatform.V1;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Proptexx.Core.AI;
using Proptexx.Core.Http;
using Proptexx.Core.Services;
using Proptexx.Core.Storage;

namespace Proptexx.AI.Models.Gen;

public sealed class GrassRepair : ImageEnhancementModel
{
    private readonly IHostEnvironment _env;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IStorageService _storageService;
    private readonly string _cvModelEndpoint;
    private readonly string _aiModelEndpoint;

    public GrassRepair(IImageEnhancementClient imageEnhancementClient,
        IStorageService storageService,
        IHostEnvironment env,
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration)
        : base(imageEnhancementClient, storageService)
    {
        _env = env;
        _httpClientFactory = httpClientFactory;
        _storageService = storageService;

        _cvModelEndpoint = configuration.GetValue<string>("cv_model_dns")
                         ?? throw new NullReferenceException("cv_model_dns");
        _aiModelEndpoint = configuration.GetValue<string>("ai_model_dns")
                          ?? throw new NullReferenceException("ai_model_dns");
    }

    protected override string GetModelEndpoint(string workspaceId)
        => "http://preprocessing.prod.ai.proptexx.com/generative-image-enhancement-grass-repair/predict";

    protected override string? ParseModelResponse(JsonDocument doc, string inputImageUrl, string modelEndpoint)
    {
        return doc.RootElement
            .GetPropertyOrDefault("predictions", 0)?
            .GetPropertyOrDefault("result")?
            .GetPropertyOrDefault("predictions", 0)?
            .GetPropertyOrDefault("result")?
            .GetString();
    }

    public override async Task<ModelResponse> InferAsync(ModelContext context)
    {
        if (context.Payload is null)
        {
            throw new NullReferenceException(nameof(context.Payload));
        }

        var imageUrl = PayloadService.GetRequiredString(context.Payload, "imageUrl");
        var httpClient = _httpClientFactory.CreateClient();
        var reqBase64Img = await ImageService.DownloadImageAsBase64Async(
            _httpClientFactory,
            imageUrl,
            context.CancellationToken
        );

        // 1. RoomSceneProcessorV3
        var scenePayload = new
        {
            image = reqBase64Img,
            processor_name = "RoomSceneProcessorV3"
        };
        var sceneResp = await httpClient.PostAsJsonAsync($"{_cvModelEndpoint}/info/", scenePayload, context.CancellationToken);
        sceneResp.EnsureSuccessStatusCode();
        var sceneJson = await sceneResp.Content.ReadFromJsonAsync<JsonElement>();
        var scene = sceneJson.GetProperty("sceneTypes").GetProperty("sceneType").GetString()?.ToLowerInvariant();

        if (scene == "floor plan" || scene == "irrelevant" || scene == "indoor")
            throw new Exception("Scene is not suitable for grass repair.");

        // 2. PreProcessSkyGrassV3
        var grassPayload = new
        {
            image = reqBase64Img,
            processor_name = "PreProcessSkyGrassV3"
        };
        var grassResp = await httpClient.PostAsJsonAsync($"{_cvModelEndpoint}/info/", grassPayload, context.CancellationToken);
        grassResp.EnsureSuccessStatusCode();
        var grassJson = await grassResp.Content.ReadFromJsonAsync<JsonElement>();
        var hasGrass = grassJson.GetProperty("hasGrass").GetBoolean();

        if (!hasGrass)
            throw new Exception("No grass detected in image.");

        // 3. Call model endpoint
        var modelPayload = new
        {
            instances = new[]
            {
                    new { image_url = reqBase64Img, enhancement_level = 0.7 }
                }
        };
        var modelResp = await httpClient.PostAsJsonAsync($"{_aiModelEndpoint}/generative-image-enhancement-grass-repair/predict", modelPayload, context.CancellationToken);
        var modelJson = await modelResp.Content.ReadFromJsonAsync<JsonElement>();

        // upload image to storage
        const string contentType = "image/jpeg";
        var filename = $"{context.ItemId}_{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}.jpg";
        var imgUrl = string.Empty;

        if (modelJson.TryGetProperty("data", out var dataElem) &&
            dataElem.TryGetProperty("computer_vision", out var cvElem) &&
            cvElem.TryGetProperty("result", out var resBase64Img))
        {
            imgUrl = await _storageService.UploadImageAsync(context.WorkspaceId, filename, resBase64Img.ToString(), contentType);
        }

        return new GenerativeModelResponse
        {
            Base64Image = reqBase64Img,
            MimeType = "image/jpeg",
            RequestParams = new Dictionary<string, object?>
            {
                ["payload"] = modelPayload
            },
            OutputImageUrl = imgUrl,
            Document = JsonDocument.Parse($$"""{"imageUrl": "{{imgUrl}}"}""")
        };
    }
}