using Microsoft.Extensions.Logging;
using Proptexx.Core.Constants;
using static Proptexx.Core.Constants.SubscriptionConstants;
using Proptexx.Core.Entities;
using Proptexx.Core.Postgresql;
using Npgsql;
using Dapper;
using System.Data;

namespace Proptexx.Core.Services;

/// <summary>
/// Service for classifying subscription product services based on workspace analysis
/// </summary>
public class SubscriptionClassificationService : ISubscriptionClassificationService
{
    private readonly ILogger<SubscriptionClassificationService> _logger;
    private readonly NpgsqlDataSource _dataSource;

    public SubscriptionClassificationService(
        ILogger<SubscriptionClassificationService> logger,
        NpgsqlDataSource dataSource)
    {
        _logger = logger;
        _dataSource = dataSource;
    }

    /// <summary>
    /// Classify ecommerce widget type based on workspace analysis
    /// </summary>
    public async Task<string> ClassifyEcommerceWidgetTypeAsync(Guid workspaceId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Classifying ecommerce widget type for workspace {WorkspaceId}", workspaceId);
            using var connection = _dataSource.CreateConnection();

            // Check workspace service bindings for ecommerce-related services
            var hasEcommerceWidget = await connection.ExecuteScalarAsync<bool>(
                "SELECT EXISTS (SELECT 1 FROM workspace_service_bindings WHERE workspace_id = @WorkspaceId AND service_id = @EcommerceWidget)",
                new { WorkspaceId = workspaceId, EcommerceWidget = ServiceTypes.EcommerceWidget });

            if (hasEcommerceWidget)
            {
                _logger.LogDebug("Workspace {WorkspaceId} has ecommerce widget service", workspaceId);
                return ProductServices.EcommerceWidgets;
            }

            // Check for general widget services
            var hasWidgetService = await connection.ExecuteScalarAsync<bool>(
                "SELECT EXISTS (SELECT 1 FROM workspace_service_bindings WHERE workspace_id = @WorkspaceId AND (service_id = @Widget OR service_id = @WidgetAccess))",
                new { WorkspaceId = workspaceId, Widget = ServiceTypes.Widget, WidgetAccess = ServiceTypes.WidgetAccess });

            if (hasWidgetService)
            {
                _logger.LogDebug("Workspace {WorkspaceId} has general widget service", workspaceId);
                return ProductServices.RealEstateWidgets;
            }

            // Check for enterprise services
            var hasEnterpriseService = await connection.ExecuteScalarAsync<bool>(
                "SELECT EXISTS (SELECT 1 FROM workspace_service_bindings WHERE workspace_id = @WorkspaceId AND service_id = ANY(@EnterpriseServices))",
                new { WorkspaceId = workspaceId, EnterpriseServices = ServiceTypes.EnterpriseServices.ToArray() });

            if (hasEnterpriseService)
            {
                _logger.LogDebug("Workspace {WorkspaceId} has enterprise service", workspaceId);
                return ProductServices.EnterpriseApis;
            }

            // Default to Other Services
            _logger.LogDebug("Workspace {WorkspaceId} classified as Other Services", workspaceId);
            return ProductServices.OtherServices;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error classifying ecommerce widget type for workspace {WorkspaceId}", workspaceId);
            return ProductServices.OtherServices;
        }
    }

    /// <summary>
    /// Classify product services based on workspace analysis
    /// Returns a list of product services that the workspace should have access to
    /// </summary>
    public async Task<List<string>> ClassifyProductServicesAsync(Guid workspaceId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Classifying product services for workspace {WorkspaceId}", workspaceId);

            var productServices = new List<string>();
            using var connection = _dataSource.CreateConnection();

            // Check workspace service bindings
            var serviceBindings = (await connection.QueryAsync<string>(
                "SELECT service_id FROM workspace_service_bindings WHERE workspace_id = @WorkspaceId",
                new { WorkspaceId = workspaceId })).ToList();

            // Classify based on service types
            if (serviceBindings.Contains(ServiceTypes.EcommerceWidget))
            {
                productServices.Add(ProductServices.EcommerceWidgets);
            }

            if (serviceBindings.Contains(ServiceTypes.RealEstateWidget) ||
                serviceBindings.Contains(ServiceTypes.Widget) ||
                serviceBindings.Contains(ServiceTypes.WidgetAccess))
            {
                productServices.Add(ProductServices.RealEstateWidgets);
            }

            if (serviceBindings.Any(s => ServiceTypes.EnterpriseServices.Contains(s)))
            {
                productServices.Add(ProductServices.EnterpriseApis);
            }

            // If no specific services found, check for legacy API key clients
            if (!productServices.Any())
            {
                var hasLegacyApiKeyClients = await connection.ExecuteScalarAsync<bool>(
                    "SELECT EXISTS (SELECT 1 FROM widget_clients WHERE workspace_id = @WorkspaceId)",
                    new { WorkspaceId = workspaceId });

                if (hasLegacyApiKeyClients)
                {
                    _logger.LogDebug("Workspace {WorkspaceId} has legacy API key clients", workspaceId);
                    productServices.Add(ProductServices.RealEstateWidgets);
                }
            }

            // If still no services found, default to Other Services
            if (!productServices.Any())
            {
                _logger.LogDebug("Workspace {WorkspaceId} classified as Other Services", workspaceId);
                productServices.Add(ProductServices.OtherServices);
            }

            _logger.LogDebug("Workspace {WorkspaceId} classified with product services: {ProductServices}",
                workspaceId, string.Join(", ", productServices));

            return productServices;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error classifying product services for workspace {WorkspaceId}", workspaceId);
            return new List<string> { ProductServices.OtherServices };
        }
    }

    /// <summary>
    /// Get all available product services
    /// </summary>
    public string[] GetAvailableProductServices()
    {
        return ProductServices.All;
    }
} 