using System.ComponentModel.DataAnnotations;
using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.System;

public sealed class GetWebsite : IQuery
{
    [Required] public required Guid Id { get; init; }

    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        context.User.EnsureRootAccess();

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var item = await npgsql.QueryFirstOrDefaultAsync<WebsiteModel>(WebsiteModel.Sql, new { _id = this.Id });
        return item;
    }

    public class WebsiteModel
    {
        public Guid Id { get; init; }
    
        public required string Slug { get; init; }
    
        public required string Title { get; init; }
    
        public DateTime CreatedAt { get; init; }
    
        public int NumDocuments { get; init; }
    
        public static string Sql => @"
        select w.id,
               w.slug,
               w.title,
               w.created_at,
               count(d.id) as num_documents
        from core.website w
        left outer join core.document d on w.id = d.website_id
        where w.id = :_id
        group by w.id;
    ";
    }
}