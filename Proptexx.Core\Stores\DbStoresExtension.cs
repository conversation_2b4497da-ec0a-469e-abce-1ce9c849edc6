using Npgsql;

namespace Proptexx.Core.Stores;

public static class DbStoresExtension
{
    public static AccountStore Account(this NpgsqlConnection connection) => new(connection);
 
    public static WorkspaceStore Workspace(this NpgsqlConnection connection) => new(connection);
 
    public static ClusterStore Cluster(this NpgsqlConnection connection) => new(connection);
 
    public static ClientStore Client(this NpgsqlConnection connection) => new(connection);
    
    public static ProductStore Product(this NpgsqlConnection connection) => new(connection);
 
    public static OrderStore Order(this NpgsqlConnection connection) => new(connection);
}
