using System.Diagnostics.CodeAnalysis;
using System.Text.RegularExpressions;
using Proptexx.AI.Widget;
using Proptexx.Core;
using Proptexx.Core.Auth;
using Proptexx.Core.Entities;
using Proptexx.Core.Redis;
using Proptexx.Core.Services;
using Proptexx.Web.Auth.Auth;
using StackExchange.Redis;
using static Proptexx.Core.Constants.WidgetConstants;

namespace Proptexx.Web.Auth.Resolvers;

public sealed class WidgetResolver : IScopeResolver<WidgetPayload>
{
    private readonly IDatabase _redis;
    private readonly WidgetService _widgetService;
    private readonly WidgetStore _widgetStore;

    private readonly List<string> AllowedServices = [
        ServiceNames.Widget,
        ServiceNames.RealEstateWidget,
        ServiceNames.WidgetAccess
        ];

    public WidgetResolver(
        IConnectionMultiplexer connectionMultiplexer, 
        WidgetService widgetService,
        WidgetStore widgetStore)
    {
        _redis = connectionMultiplexer.GetDatabase();
        _widgetService = widgetService;
        _widgetStore = widgetStore;
    }

    public async Task ResolveAsync(ScopeContext context, WidgetPayload payload, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(payload.Identifier)) return;
        var secretId = context.Identity.GetClientIdentifier();
        var parsedId = Guid.Parse(secretId);
        var apiKey = ApiKeyService.CreateApiKey(parsedId);
        var item = await _widgetStore.GetEntryAsync(apiKey);
        var accessResult = VerifyWidgetAccess(secretId, payload.Identifier, item);
        context.AddData("options", accessResult.Options);

        // Legacy. Return batch in auth request for DoorInsider, until they upgrade their NPM package
        // and call the dedicated command/query to retrive the batch of images
        if (accessResult.WorkspaceName == "doorinsider" || accessResult.Options.SingleImage == true)
        {
            var batchId = BatchIdService.CreateBatchId(accessResult.WorkspaceId, payload.Identifier);
            var entry = await _widgetService.GetEntry(accessResult.WorkspaceName, payload.Identifier);
            var images = entry?.Images ?? [];
            foreach (var image in images) image.Exception = null;
            var status = BatchService.GetStatus(images);
            var entries = images.Where(x => x.Status == 2);
            var batch = new { batchId, status, entries, images };
            context.AddData("batch", batch);
        }
    }

    private WidgetAccessResult VerifyWidgetAccess(string clientId, string identifier, WidgetStore.WidgetModel? widgetModel)
    {
        if (widgetModel is null)
        {
            throw new UnauthorizedAccessException("No widget options");
        }

        // Check services
        if (widgetModel.Services is null || !AllowedServices.Any(service => widgetModel.Services.Contains(service, StringComparison.CurrentCultureIgnoreCase)))
        {
            throw new UnauthorizedAccessException($"Client {clientId}: No real-estate widget service");
        }

        if (!IsValidUri(identifier, out var refererUri))
        {
            throw new UnauthorizedAccessException($"Client {clientId}: Unable to parse identifier {identifier}");
        }

        // Check domains
        if (widgetModel.Domains is null)
        {
            throw new UnauthorizedAccessException($"Client {clientId}: No whitelist has been defined");
        }

        var urls = widgetModel.Domains;
        if (urls is null || urls.Count <= 0)
        {
            throw new UnauthorizedAccessException($"Client {clientId}: No whitelist has been defined");
        }

        var hasMatch = urls.Any(pair => IsMatch(refererUri, pair.Key, pair.Value));
        if (!hasMatch) throw new UnauthorizedAccessException($"Client {clientId}: No match for identifier");

        return new WidgetAccessResult
        {
            WorkspaceId = widgetModel.WorkspaceId,
            WorkspaceName = widgetModel.WorkspaceName,
            Options = widgetModel.Options
        };
    }

    private static bool IsMatch(Uri refererUri, string matchStr, MatchStrategy strategy)
    {
        return strategy switch
        {
            MatchStrategy.Exact => refererUri.AbsoluteUri.Equals(matchStr, StringComparison.OrdinalIgnoreCase),
            MatchStrategy.Start => refererUri.AbsoluteUri.StartsWith(matchStr, StringComparison.OrdinalIgnoreCase),
            MatchStrategy.Regex => Regex.IsMatch(refererUri.AbsoluteUri, matchStr, RegexOptions.IgnoreCase),
            _ => throw new ArgumentOutOfRangeException(nameof(strategy))
        };
    }

    private static bool IsValidUri(string uriStr, [NotNullWhen(true)] out Uri? result)
    {
        // Validate if the given string is a valid URI
        if (Uri.TryCreate(uriStr, UriKind.Absolute, out var result1)
            && (result1.Scheme == Uri.UriSchemeHttp || result1.Scheme == Uri.UriSchemeHttps))
        {
            result = result1;
            return true;
        };

        result = null;
        return false;
    }
}

internal class WidgetAccessResult
{
    public required Guid WorkspaceId { get; init; }

    public required string WorkspaceName { get; init; }

    public required WidgetOptions Options { get; init; }
}

public sealed class WidgetPayload
{
    public required string Identifier { get; init; }

    // public List<string>? Images { get; init; }

    // public int ScrapeAttempts { get; init; }
}
