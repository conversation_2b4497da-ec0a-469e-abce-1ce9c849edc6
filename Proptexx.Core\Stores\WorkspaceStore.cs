using System.Data;
using Dapper;
using Proptexx.Core.Entities;
using Proptexx.Core.Extensions;
using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Utils;

namespace Proptexx.Core.Stores;

public class WorkspaceStore
{
    private readonly IDbConnection _connection;

    public WorkspaceStore(IDbConnection connection) => _connection = connection;

    public async Task<Workspace> Create(string title, Guid clusterTplId, Guid? parentId, IEnumerable<UrlInfo> domains, Guid? founderId)
    {
        if (parentId.HasValue && parentId.Value.Equals(Guid.Empty))
        {
            throw new NullReferenceException("workspaceId");
        }

        var rnd = new Random();
        var name = $"{title.GetUriSlug(true)}-{rnd.Next(1000, 9999)}";
        var workspace = new Workspace
        {
            ParentId = parentId,
            Name = name,
            Title = title,
            ClusterTplId = clusterTplId,
            FounderId = founderId
        };

        await _connection.InsertAsync(workspace);

        var insertableDomains = domains
            .Select(d => new Domain
            {
                WorkspaceId = workspace.Id,
                Hostname = d.Hostname,
                Path = d.Path,
                InputValue = d.AbsoluteUrl,
                MatchStrategy = MatchStrategy.Start
            });

        await _connection.InsertAsync(insertableDomains);
        return workspace;
    }

    public async Task<bool> SetData(Guid workspaceId, string key, string value)
    {
        var workspace = new WorkspaceData
        {
            WorkspaceId = workspaceId,
            DataKey = key,
            DataValue = value
        };

        var inserts = await _connection.InsertAsync(workspace, new IgnoreOnConflictHandler(
            ConflictType.Constraint, "ix_workspace_data__workspace_id_data_key"));

        return inserts > 0;
    }

    public async Task<Workspace?> GetByTitle(string title)
    {
        const string sql = "select * from core.workspace w where w.title = :_title limit 1;";
        return await _connection.QueryFirstOrDefaultAsync<Workspace>(sql, new { _title = title });
    }

    public async Task<Workspace?> GetById(Guid id)
    {
        const string sql = "select * from core.workspace w where w.id = :_id limit 1;";
        return await _connection.QueryFirstOrDefaultAsync<Workspace>(sql, new { _id = id });
    }

    public Task<Workspace?> ByClientId(Guid clientId)
    {
        const string sql = @"
            select w.*
            from core.client c
                     join core.workspace w on c.workspace_id = w.id
            where c.id = :_client_id;
        ";

        return _connection.QueryFirstOrDefaultAsync<Workspace>(
            sql, new { _client_id = clientId });
    }

    public Task<Workspace?> GetByData(string key, string value, Guid? workspaceId = null, Guid? parentId = null)
    {
        const string sql = @"
            select w.*
            from core.workspace w
            join core.workspace_data wd on w.id = wd.workspace_id
            where (:_workspace_id is null or w.id = :_workspace_id) 
              and (:_parent_id is null or w.parent_id = :_parent_id)
              and wd.data_key = :_key 
              and wd.data_value = :_value;
        ";

        return _connection.QueryFirstOrDefaultAsync<Workspace>(
            sql, new
            {
                _key = key,
                _value = value,
                _workspace_id = workspaceId,
                _parent_id = parentId
            });
    }

    public async Task<List<AdminAccountModel>> GetAdmins(Guid workspaceId)
    {
        var param = new { _workspace_id = workspaceId };
        var result = await _connection
            .QueryAsync<AdminAccountModel>(AdminAccountModel.Sql, param);
        return result.ToList();
    }
}

public sealed class AdminAccountModel
{
    public Guid AccountId { get; init; }
    
    public required string FirstName { get; init; }
    
    public required string FamilyName { get; init; }
    
    public required string Email { get; init; }

    public static string Sql = @"
        select distinct on (a.id)
            a.id as account_id,
            a.first_name,
            a.family_name,
            ae.email
        from core.account a
        join core.account_email ae on a.id = ae.account_id and ae.verified_at is not null
        join core.cluster_account_binding cab on a.id = cab.account_id
        join core.cluster c on cab.cluster_id = c.id and lower(c.name) = 'administrator'
        where c.workspace_id = :_workspace_id
        group by a.id, a.first_name, a.id, a.family_name, ae.email;
    ";
}