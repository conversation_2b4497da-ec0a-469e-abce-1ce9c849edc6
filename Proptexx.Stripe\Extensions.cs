﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Stripe;

namespace Proptexx.Stripe;

public static class Extensions
{
    public static IHostApplicationBuilder RegisterStripeApiKey(this IHostApplicationBuilder builder)
    {
        StripeConfiguration.ApiKey = builder.Configuration.GetValue<string>("Stripe:ApiKey")
                                     ?? throw new NullReferenceException("Make sure all required env vars - including Stripe:ApiKey - are defined");

        return builder;
    }
}