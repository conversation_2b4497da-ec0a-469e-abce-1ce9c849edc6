using Proptexx.Core.Entities;
using Proptexx.Core.Services;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Interface;

namespace Proptexx.Core.Cqrs.Command;

/// <summary>
/// Command to create a PayAsYouGo subscription for a valid API key
/// </summary>
public class CreatePayAsYouGoSubscriptionCommand
{
    public required string ApiKey { get; set; }
    public string? ClientName { get; set; }
    public string? ContactName { get; set; }
    public string? ContactEmail { get; set; }
    public List<string>? ProductServices { get; set; }
}

/// <summary>
/// Handler for creating PayAsYouGo subscriptions
/// </summary>
public class CreatePayAsYouGoSubscriptionCommandHandler
{
    private readonly ISubscriptionUsageService _usageService;
    private readonly ILogger<CreatePayAsYouGoSubscriptionCommandHandler> _logger;

    public CreatePayAsYouGoSubscriptionCommandHandler(
        ISubscriptionUsageService usageService,
        ILogger<CreatePayAsYouGoSubscriptionCommandHandler> logger)
    {
        _usageService = usageService;
        _logger = logger;
    }

    public async Task<ClientSubscription?> Handle(CreatePayAsYouGoSubscriptionCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating PayAsYouGo subscription for API key: {ApiKey}", request.ApiKey);

        // Use the SubscriptionService for creation, not the UsageService
        if (_usageService is ISubscriptionService subscriptionService)
        {
            var result = await subscriptionService.CreatePayAsYouGoSubscriptionAsync(
                request.ApiKey,
                request.ClientName,
                request.ContactName,
                request.ContactEmail,
                request.ProductServices,
                cancellationToken);

            if (result != null)
            {
                _logger.LogInformation("Successfully created PayAsYouGo subscription {SubscriptionId} for API key {ApiKey}", 
                    result.Id, request.ApiKey);
            }
            else
            {
                _logger.LogWarning("Failed to create PayAsYouGo subscription for API key {ApiKey}", request.ApiKey);
            }
            return result;
        }
        else
        {
            _logger.LogError("ISubscriptionUsageService does not implement ISubscriptionService. Cannot create subscription.");
            return null;
        }
    }
} 