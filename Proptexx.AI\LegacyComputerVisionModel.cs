using System.Net.Http.Json;
using System.Text.Json;
using Proptexx.Core.AI;
using Proptexx.Core.Http;
using Proptexx.Core.Services;

namespace Proptexx.AI;

public abstract class LegacyComputerVisionModel : BaseModel
{
    private readonly IComputerVisionClient _computerVisionClient;

    protected LegacyComputerVisionModel(IComputerVisionClient computerVisionClient)
    {
        _computerVisionClient = computerVisionClient;
    }

    public override async Task<ModelResponse> InferAsync(ModelContext context)
    {
        if (context.Payload is null)
        {
            throw new NullReferenceException(nameof(context.Payload));
        }

        var imageUrl = PayloadService.GetRequiredString(context.Payload, "imageUrl");

        var ctx = new LegacyComputerVisionModelContext
        {
            ImageUrl = imageUrl,
            WorkspaceId = context.WorkspaceId,
            ItemId = context.ItemId,
            Payload = context.Payload,
            CancellationToken = context.CancellationToken
        };
        
        var document = await InferAsync(ctx);
        return document;
    }

    public async Task<LegacyComputerVisionModelResponse> InferAsync(LegacyComputerVisionModelContext context)
    {
        var modelEndpoint = this.GetModelEndpoint(context.WorkspaceId);

        var payload = new
        {
            instances = new List<object>{
                new 
                {
                    image_url = context.ImageUrl
                }
            }
        };

        var doc = await _computerVisionClient.SendLegacyAsync(modelEndpoint, payload, context.CancellationToken);
        var result = this.ParseModelResponse(doc, context.ImageUrl, modelEndpoint);

        return new LegacyComputerVisionModelResponse
        {
            Document = result
        };
    }

    protected abstract JsonDocument? ParseModelResponse(JsonDocument doc, string inputImageUrl, string modelEndpoint);

    protected abstract string GetModelEndpoint(string workspaceId);

    public sealed class Result
    {
        public string? ImageUrl { get; init; }
    }
}

public class LegacyComputerVisionModelContext : ModelContext
{
    public required string ImageUrl { get; init; }
}

public class LegacyComputerVisionModelResponse : ModelResponse
{
}
