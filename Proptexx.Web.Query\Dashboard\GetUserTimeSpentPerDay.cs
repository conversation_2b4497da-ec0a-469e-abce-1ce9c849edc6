﻿using Dapper;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Dashboard;

public sealed class GetUserTimeSpentPerDay : IQuery
{
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        var accountId = Guid.Parse(context.User.GetCallerId());
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);

        var result = await npgsql.QueryAsync<TimeSpentModel>(Sql, new { _account_id = accountId });

        return result;
    }

    public sealed class TimeSpentModel
    {
        public DateTime Date { get; init; }
        public double TotalMinutes { get; init; }
    }

    public static string Sql => @"
        WITH session_durations AS (
            SELECT 
                session_id, 
                DATE(timestamp) AS date,
                EXTRACT(EPOCH FROM (MAX(timestamp) - MIN(timestamp))) / 60 AS total_minutes
            FROM telemetry.api_logs
            WHERE account_id = :_account_id
              AND timestamp >= NOW() - INTERVAL '7 days'
            GROUP BY session_id, DATE(timestamp)
        )
        SELECT 
            date, 
            ROUND(SUM(total_minutes), 2) AS total_minutes
        FROM session_durations
        GROUP BY date
        ORDER BY date;
    ";
}
