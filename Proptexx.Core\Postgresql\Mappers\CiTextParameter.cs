using System.Data;
using Dapper;
using Npgsql;
using NpgsqlTypes;

namespace Proptexx.Core.Postgresql.Mappers;

public class CiTextParameter : SqlMapper.ICustomQueryParameter
{
    private readonly string _value;

    public CiTextParameter(string value)
    {
        _value = value;
    }

    public void AddParameter(IDbCommand command, string name)
    {
        command.Parameters.Add(new NpgsqlParameter
        {
            ParameterName = name,
            NpgsqlDbType = NpgsqlDbType.Citext,
            Value = _value
        });
    }
}