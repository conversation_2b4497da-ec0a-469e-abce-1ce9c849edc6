using System.Text.Json;
using System.Text.Json.Serialization;

namespace Proptexx.Core.Json;

public static class JsonDefaults
{
    private static readonly JsonSerializerOptions DefaultSerializerOptions = new(JsonSerializerDefaults.Web)
    {
        UnknownTypeHandling = JsonUnknownTypeHandling.JsonElement,
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        AllowTrailingCommas = true
    };

    public static JsonSerializerOptions JsonSerializerOptions => new(DefaultSerializerOptions);
    
    public static JsonSerializerOptions CompactOptions { get; } = new(JsonSerializerDefaults.Web)
    {
        UnknownTypeHandling = JsonUnknownTypeHandling.JsonElement,
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        AllowTrailingCommas = true,
        WriteIndented = false
    };

    public static void AddConverter(JsonConverter converter)
    {
        if (DefaultSerializerOptions.Converters.Contains(converter)) return;
        DefaultSerializerOptions.Converters.Add(converter);
    }

    public static bool RemoveConverter(JsonConverter converter)
    {
        return DefaultSerializerOptions.Converters.Contains(converter) && DefaultSerializerOptions.Converters.Remove(converter);
    }
}