using System.Text.Json;
using System.Text.Json.Serialization;
using Microsoft.Extensions.Logging;
using Proptexx.Core.Extensions;
using Proptexx.Core.Http;
using Proptexx.Core.Json;
using Proptexx.Core.Services;
using StackExchange.Redis;

namespace Proptexx.AI.Models.CV;

public class PreProcessorForRoomScene : BaseComputerVisionModel
{
    public PreProcessorForRoomScene(
        IComputerVisionClient computerVisionClient,
        IImageAssessmentClient imageAssessmentClient,
        IConnectionMultiplexer connectionMultiplexer,
        ILoggerFactory loggerFactory)
        : base(
            "PreProcessorForRoomScene",
            computerVisionClient,
            imageAssessmentClient,
            connectionMultiplexer,
            loggerFactory)
    {
    }

    protected override JsonDocument? ParseModelResponse(JsonDocument doc, string inputImageUrl, string modelEndpoint)
    {
        var temp = doc.Deserialize<TempResult>();
        if (temp is null) return null;

        var roomTypes = temp.RoomTypes.ToDictionary(x => x.RoomType, x => x.RoomTypeScore);
        var result = new Result
        {
            RoomTypes = roomTypes,
            IsEmptyRoom = temp.IsEmptyRoom,
            SceneTypes = [temp.SceneTypes.SceneType],
            SceneTypeConfidenceScores = [temp.SceneTypes.SceneTypeScore]
        };

        return result.ToJsonDocument(JsonDefaults.JsonSerializerOptions);
    }

    public class TempResult
    {
        [JsonPropertyName("isEmpty")]
        public bool IsEmptyRoom { get; set; }

        [JsonPropertyName("sceneTypes")]
        public SceneTypeModel SceneTypes { get; set; }

        [JsonPropertyName("roomTypes")]
        public RoomTypeModel[] RoomTypes { get; set; } = [];

        public class SceneTypeModel
        {
            [JsonPropertyName("sceneType")]
            public required string SceneType { get; set; }

            [JsonPropertyName("sceneTypeScore")]
            public required float SceneTypeScore { get; set; }
        }

        public class RoomTypeModel
        {
            [JsonPropertyName("roomType")]
            public required string RoomType { get; set; }

            [JsonPropertyName("roomTypeScore")]
            public required float RoomTypeScore { get; set; }
        }
    }

    public class Result
    {
        public bool IsEmptyRoom { get; set; }

        public string[] SceneTypes { get; set; } = [];

        public float[] SceneTypeConfidenceScores { get; set; } = [];

        public Dictionary<string, float> RoomTypes { get; set; } = [];
    }
}
