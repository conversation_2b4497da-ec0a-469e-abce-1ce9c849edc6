using System.Text.Json;
using System.Text.Json.Serialization;

namespace Proptexx.Core.Json;

public abstract class DiscriminatorJsonConverter<T> : JsonConverter<T>
    where T : class
{
    private readonly string _discriminator;
    private readonly bool _allowNulls;
    private readonly Dictionary<string, Type> _types;

    protected DiscriminatorJsonConverter(string discriminator, bool allowNulls, Dictionary<string, Type> types)
    {
        _discriminator = discriminator;
        _allowNulls = allowNulls;
        _types = types;
    }
    
    public override T? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        using var doc = JsonDocument.ParseValue(ref reader);
        var root = doc.RootElement;

        if (!root.TryGetProperty(_discriminator, out var typeElement))
            throw new JsonException("No type discriminator property found.");

        var discriminator = typeElement.GetString();

        if (discriminator is null)
        {
            if (_allowNulls) return null;
            throw new JsonException($"Unknown discriminator '{discriminator}'.");
        }

        if (!_types.TryGetValue(discriminator, out var targetType))
        {
            throw new JsonException($"Unknown discriminator '{discriminator}'.");
        }

        var result = JsonSerializer.Deserialize(root.GetRawText(), targetType, options) as T;

        if (result is null)
        {
            if (_allowNulls) return null;
            throw new JsonException($"Failed to deserialize JSON to {targetType.FullName}");
        }

        return result;
    }

    public override void Write(Utf8JsonWriter writer, T value, JsonSerializerOptions options)
    {
        JsonSerializer.Serialize(writer, value, value.GetType(), options);
    }
}