using System.ComponentModel.DataAnnotations;
using Proptexx.Core;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Attributes;
using Proptexx.Core.Entities;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Postgresql.Builder;

namespace Proptexx.Web.Command.System;

public sealed class CreateProduct : ICommand
{
    [GuidNotEmpty] public Guid? WorkspaceId { get; init; }

    [Required, <PERSON>Length(4), MaxLength(60)]
    public required string Title { get; init; }

    [MaxLength(500)]
    public string? Description { get; init; }
    
    [MaxLength(500)]
    public string? Summary { get; set; }
    
    [MaxLength(3000)]
    public string? Content { get; set; }
    
    [Required]
    public required string PaymentType { get; init; }

    public PriceDescriptor? Price { get; init; }

    public string? Interval { get; init; }

    [MinLength(1)]
    public required IEnumerable<string> Services { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        context.User.EnsureRootAccess();

        var accountId = Guid.Parse(context.User.GetCallerId());

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        await using var trx = await npgsql.BeginTransactionAsync(context.CancellationToken);

        try
        {
            var (price, currency) = ResolvePriceAndCurrency(this.PaymentType, this.Price);
            var interval = ResolveInterval(this.PaymentType, this.Interval);

            var product = new Product
            {
                Title = this.Title,
                Description = this.Description,
                Summary = this.Summary,
                Content = this.Content,
                PaymentType = this.PaymentType,
                PriceAmount = price,
                Currency = currency,
                WorkspaceId = WorkspaceId,
                Config = new ProductWidgetAccessConfig{Interval = interval}
            };

            await npgsql.InsertAsync(product);

            await npgsql.InsertAsync(this.Services
                .Select(service => new ProductServiceBinding { ServiceId = service, ProductId = product.Id })
                .ToList());

            await trx.CommitAsync(context.CancellationToken);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            await trx.RollbackAsync(context.CancellationToken);
            throw;
        }
    }

    private static string? ResolveInterval(string paymentType, string? intervalDescriptor)
    {
        if (!paymentType.Equals("recurring")) return null;
        if (intervalDescriptor is null) throw new CommandException("When choosing recurring payments, the interval needs to be defined");
        return intervalDescriptor;
    }

    private static (decimal price, string currency) ResolvePriceAndCurrency(string paymentType, PriceDescriptor? priceDescriptor)
    {
        if (paymentType.Equals("free"))
        {
            return (0m, string.Empty);
        }

        if (priceDescriptor?.Price is null or <= 0)
        {
            throw new CommandException("The price must be higher than 0");
        }

        return (priceDescriptor.Price, priceDescriptor.Currency);
    }
}

public class PriceDescriptor
{
    public required decimal Price { get; init; }
    
    public required string Currency { get; init; }
}

public sealed class IntervalDescriptor
{
    public required string Period { get; init; }
}

