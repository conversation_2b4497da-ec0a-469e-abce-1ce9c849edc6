using Microsoft.Extensions.DependencyInjection;
using Proptexx.Core.Interface;
using Proptexx.Core.Services;

namespace Proptexx.Core.Extensions;

/// <summary>
/// Extension methods for registering API Key management services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Register all Subscription management services
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddSubscriptionManagementServices(this IServiceCollection services)
    {
        // Register subscription services
        services.AddScoped<ISubscriptionService, SubscriptionService>();
        services.AddScoped<ISubscriptionValidationService, SubscriptionValidationService>();
        services.AddScoped<ISubscriptionClassificationService, SubscriptionClassificationService>();
        services.AddScoped<ISubscriptionUsageService, SubscriptionUsageService>();


        // Additional services
        services.AddScoped<IApiKeyGeneratorService, ApiKeyGeneratorService>();
        //services.AddScoped<IApiKeyRegistrationService, ApiKeyRegistrationService>();

        //// Background service for monthly reset
        //services.AddHostedService<RenewalSubscriptionBackgroundService>();
        //services.AddScoped<IRenewalSubscriptionBackgroundService>(provider => 
        //    provider.GetRequiredService<RenewalSubscriptionBackgroundService>());

        return services;
    }
     
} 