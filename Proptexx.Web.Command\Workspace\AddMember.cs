using System.ComponentModel.DataAnnotations;
using Proptexx.Core.Services;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Attributes;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Stores;
using Proptexx.Core.Utils;

namespace Proptexx.Web.Command.Workspace;

public sealed class AddMember : ICommand
{
    [Required, GuidNotEmpty]
    public required Guid WorkspaceId { get; init; }
    
    [Required, MinLength(4)]
    public required string FullName { get; init; }
    
    [Required, EmailAddress]
    public required string Email { get; init; }
    
    public string? Phone { get; init; }
    
    public required string Role { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        await using var trx = await npgsql.BeginTransactionAsync(context.CancellationToken);

        try
        {
            var callerId = Guid.Parse(context.User.GetCallerId());
            var password = PasswordService.GeneratePassword(6);
            var member = await OnboardHelper.OnboardAccount(npgsql, this.FullName, this.Email, false, password, this.Phone, false);
            await npgsql.Account().JoinCluster(member.Id, WorkspaceId, this.Role, true);
            await trx.CommitAsync(context.CancellationToken);
            context.AddData("memberId", member.Id);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            await trx.RollbackAsync(context.CancellationToken);
            throw;
        }
    }
}