﻿using Proptexx.Core.DTOs;
using Proptexx.Core.DTOs.Outseta;

namespace Proptexx.Core.Helpers
{
    public static class OutseraDataHelper
    {

        public static OutsetaAccountData? GetAccountData(this OutsetaData? payload)
        {
            if (payload == null)
            {
                return null;
            }
            var primaryContact = payload.PrimaryContact;
            var companyName = payload.Platform ?? payload.Name ?? "Unknown Company";
            var fullName = !string.IsNullOrWhiteSpace(primaryContact?.FullName)
                ? primaryContact.FullName
                : $"{primaryContact?.FirstName} {primaryContact?.LastName}".Trim();
            var phone = primaryContact?.PhoneMobile ?? primaryContact?.PhoneWork;
            var currentPlan = payload.CurrentSubscription?.Plan ?? payload.Subscriptions?.FirstOrDefault()?.Plan;

            return new OutsetaAccountData
            {
                CompanyName = companyName,
                Email = primaryContact?.Email ??string.Empty,
                FullName = fullName,
                Phone = phone,
                StoreUrl = payload.StoreUrl,
                Platform = payload.Platform,
                Viewers = payload.Viewers,
                PlanName = currentPlan?.Name,
                PlanUid = currentPlan?.Uid,
                AccountUid = payload.Uid
            };
        }
    }
}
