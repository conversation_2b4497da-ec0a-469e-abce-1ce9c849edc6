using Dapper;
using Microsoft.Extensions.DependencyInjection;
using Npgsql;
using Proptexx.Core.Entities;

public class SetFlagsOnTelemetryData
{
    public static async Task ExecuteAsync(ServiceProvider services, CancellationToken cancellationToken)
    {
        var dataSource = services.GetRequiredService<NpgsqlDataSource>();
        var current = new DateTime(2025, 3, 1);
        var connStr = dataSource.ConnectionString;
        Console.WriteLine(connStr);
        await using var conn = await dataSource.OpenConnectionAsync(cancellationToken);
        var updates = 0;
        while (true)
        {
            Console.WriteLine(current);
            var affected = await ProcessDateSpan(conn, current, cancellationToken);
            updates += affected;
            if (affected <= 0) break;
            await Task.Delay(10000, cancellationToken);
            current = current.AddDays(1);
        }
        Console.WriteLine($"Total updated entries: {updates}");
    }

    private static async Task<int> ProcessDateSpan(NpgsqlConnection conn, DateTime fromDate, CancellationToken cancellationToken)
    {
        var toDate = fromDate.AddDays(1);
        var logs = await conn.QueryAsync<TelemetryEntry>(@"
            select l.*
            from telemetry.api_logs l
            where l.created_at >= :_from_date and l.created_at < :_to_date and clean_endpoint is null
        ", new { _from_date = fromDate, _to_date = toDate }, commandTimeout: 60);

        var batchSize = 1000;
        var delayMs = 100;
        var updates = 0;

        foreach (var batch in logs.Chunk(batchSize))
        {
            var affected = await RunBatchAsync(conn, batch);
            updates += affected;

            Console.WriteLine($"Updated {affected} in batch, total so far: {updates}");

            // Optional: throttle slightly to protect DB
            await Task.Delay(delayMs, cancellationToken);
        }

        return updates;
    }

    private static Task<int> RunBatchAsync(NpgsqlConnection conn, TelemetryEntry[] batch)
    {
        // Create the parameter list and SQL VALUES string
        var valueLines = new List<string>();
        var parameters = new DynamicParameters();

        int i = 0;
        foreach (var item in batch)
        {
            item.SetFlags(); // pre-process values
            valueLines.Add($"(@id{i}, @is_batch{i}, @is_ai{i}, @is_api{i}, @clean{i}, @ident{i}, @proc{i})");

            parameters.Add($"id{i}", item.Id);
            parameters.Add($"is_batch{i}", item.IsBatch);
            parameters.Add($"is_ai{i}", item.IsAIRequest);
            parameters.Add($"is_api{i}", item.IsApiRequest);
            parameters.Add($"clean{i}", item.CleanEndpoint);
            parameters.Add($"ident{i}", item.Identifier);
            parameters.Add($"proc{i}", item.ProcessedEndpoint);

            i++;
        }

        var sql = $@"
                UPDATE telemetry.api_logs AS t
                SET
                    is_batch = v.is_batch,
                    is_ai_request = v.is_ai_request,
                    is_api_request = v.is_api_request,
                    clean_endpoint = v.clean_endpoint,
                    identifier = v.identifier,
                    processed_endpoint = v.processed_endpoint
                FROM (
                    VALUES {string.Join(",\n", valueLines)}
                ) AS v(id, is_batch, is_ai_request, is_api_request, clean_endpoint, identifier, processed_endpoint)
                WHERE t.id = v.id;
            ";

        return conn.ExecuteAsync(sql, parameters);
    }
}