

### Authenticate
POST https://auth.proptexx.com/_auth
Content-Type: application/json
Authorization: ApiKey YWFjMDFjNzQtODQ5ZC00OGRlLWI1NjAtYjVjMjkzZmQ0OTE1

{}

> {% client.global.set("accessToken", response.body.$accessToken) %}

### Call Status API with the response id from previous call to get status of batch. Also using the accessToken from first request

GET https://api.proptexx.com/status/78eb5a86-e9fe-4d55-8870-39661de121b6
Content-Type: application/json
Authorization: Bearer {{accessToken}}