using System.ComponentModel.DataAnnotations;
using Dapper;
using Proptexx.Core;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Command;
using Proptexx.Core.Attributes;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Services;
using Proptexx.Stripe;

namespace Proptexx.Web.Command.Workspace;

public sealed class CreateOrder : ICommand
{
    [Required, GuidNotEmpty]
    public required Guid WorkspaceId { get; init; }
    
    [Required]
    public required string InitiatedBy { get; init; }
    
    [Required, GuidNotEmpty]
    public required Guid Product { get; init; }
    
    public bool CreatePaymentLink { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        context.User.EnsureRootAccess();
        
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        await using var trx = await npgsql.BeginTransactionAsync(context.CancellationToken);

        try
        {
            var callerId = Guid.Parse(context.User.GetCallerId());
            var orderId = Guid.NewGuid();
            
            var configuration = context.GetService<IConfiguration>();
            var product = await npgsql.QueryFirstAsync<ProductModel>(ProductModel.Sql, new { _product_id = Product });

            string? paymentLink = null;
            if (this.CreatePaymentLink)
            {
                var session = await StripeManager.CreateCheckoutSessionAsync(configuration, product, orderId, context.CancellationToken);
                paymentLink = session.Url;
            }

            await OrderManager.CreateOrderAsync(npgsql, orderId, callerId, InitiatedBy, this.WorkspaceId, paymentLink, new[]{Product});
            await trx.CommitAsync(context.CancellationToken);

            if (!string.IsNullOrWhiteSpace(paymentLink))
            {
                context.AddData("paymentLink", paymentLink);
            }

            context.AddData("orderId", orderId);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            await trx.RollbackAsync(context.CancellationToken);
            throw;
        }
    }
}

