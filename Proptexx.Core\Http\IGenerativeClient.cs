using System.Net.Http.Json;
using System.Text.Json;
using Microsoft.Extensions.Logging;

namespace Proptexx.Core.Http;

public interface IGenerativeClient
{
    Task<JsonDocument> SendAsync(string endpoint, Dictionary<string, object?> payload, CancellationToken cancellationToken, Dictionary<string, string>? headers = null);
}

public sealed class GenerativeHttpClient(HttpClient httpClient, ILogger<GenerativeHttpClient> logger) 
    : BaseHttpClient(httpClient, logger), IGenerativeClient
{
    public async Task<JsonDocument> SendAsync(string endpoint, Dictionary<string, object?> payload, CancellationToken cancellationToken, Dictionary<string, string>? headers = null)
    {
        using var content = JsonContent.Create(payload);
        var doc = await PostJsonAsync(endpoint, content, cancellationToken, headers);
        return doc;
    }
}