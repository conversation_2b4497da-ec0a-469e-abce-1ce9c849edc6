using System.ComponentModel.DataAnnotations;
using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Attributes;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.System;

public sealed class GetAccount : IQuery
{
    [Required, GuidNotEmpty] public required Guid Id { get; init; }
    
    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        context.User.EnsureRootAccess();
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var account = await npgsql.QueryFirstOrDefaultAsync<AccountModel>(AccountModel.Sql, new { _account_id = Id });
        return new { account };
    }

    private sealed class AccountModel
    {
        public required Guid Id { get; init; }

        public required string FirstName { get; init; }

        public required string FamilyName { get; init; }

        public DateTime? DateOfBirth { get; init; }

        public int? Gender { get; init; }

        public DateTime CreatedAt { get; init; }

        public static string Sql => @"
            select a.id,
                   a.first_name,
                   a.family_name,
                   a.date_of_birth,
                   a.gender,
                   a.created_at
            from core.account a
            where a.id = :_account_id;
        ";
    }
}
