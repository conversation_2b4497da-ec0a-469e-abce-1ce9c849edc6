namespace Proptexx.Core.Cqrs.Command;

public class CommandConfig
{
    private readonly Dictionary<string, ICommandHandler> _handlers = new();

    internal CommandConfig() { }

    public void AddHandler(ICommandHandler handler)
    {
        if (!_handlers.TryAdd(handler.Identifier, handler))
        {
            throw new CommandException("Command handler already defined");
        }
    }

    internal ICommandHandler? GetHandler(string identifier) 
        => _handlers.GetValueOrDefault(identifier);
}