﻿using Proptexx.Core.Entities;

namespace Proptexx.Core.Constants
{

    public static class SubscriptionPlans
    {
        public const string DefaultPlanFamilyId = "y9qxG2QA";
        public const string FreePlanId = "pWrMKa9n";
        public const string StarterPlanId = "A93o8590";
        public const string GrowthPlanId = "nmDyA2Qy";
        public const string ProPlanId = "BWzwLNmE";
        public const string PayAsYouGoPlanId = "amRbo3mJ";

        public static readonly Dictionary<string, SubscriptionPlan> Plans = new()
        {
            ["pWrMKa9n"] = new SubscriptionPlan
            {
                Uid = "pWrMKa9n",
                Code = "FREE_TEST_DRIVE",
                Name = "Free - Test Drive",
                Description = "<ul>\n<li><span class=\"_fadeIn_m1hgl_8\">Includes 1</span><span class=\"_fadeIn_m1hgl_8\">0 </span><span class=\"_fadeIn_m1hgl_8\">free </span><span class=\"_fadeIn_m1hgl_8\">images on a product page.</span></li>\n<li><span class=\"_fadeIn_m1hgl_8\">Remains on your e-commerce website forever and is activated either automatically or manually.</span></li>\n<li><span class=\"_fadeIn_m1hgl_8\">Perfect </span><span class=\"_fadeIn_m1hgl_8\">for&nbsp;</span><span class=\"_fadeIn_m1hgl_8\">testing&nbsp;</span><span class=\"_fadeIn_m1hgl_8\">the </span><span class=\"_fadeIn_m1hgl_8\">widget </span><span class=\"_fadeIn_m1hgl_8\">before upgrading.</span></li>\n</ul>",
                PlanFamilyName = "Default",
                PlanFamilyUid = "y9qxG2QA",
                IsQuantityEditable = false,
                MonthlyRate = 0.00m,
                AnnualRate = 0.00m,
                QuarterlyRate = 0.00m,
                OneTimeRate = 0.00m,
                SetupFee = 0.00m,
                IsActive = true,
                IsPerUser = false,
                TrialPeriodDays = 0,
                TrialUntilDate = null,
                UnitOfMeasure = "",
                PlanAddOns = new List<string>(),
                NumberOfSubscriptions = 0,
                Quota = 10,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = null
            },
            ["A93o8590"] = new SubscriptionPlan
            {
                Uid = "A93o8590",
                Code = "STARTER",
                Name = "Starter",
                Description = "<p class=\"p1\">&bull; 100 images/month<br />&bull; $0.50/image overage<br />&bull; email support</p>\n<p class=\"p1\"><strong>Perfect For:</strong></p>\n<ul>\n<li>\n<p class=\"p1\">Niche DTC stores with moderate but steady traffic</p>\n</li>\n<li>\n<p class=\"p1\">Selling 10&ndash;50 items per month</p>\n</li>\n<li>\n<p class=\"p1\">Want basic automation and professional image previews</p>\n</li>\n</ul>\n<p class=\"p3\">&nbsp;</p>\n<p class=\"p1\"><strong>Example Store:</strong></p>\n<p class=\"p4\">A rug store with 5&ndash;10 collections and 5,000&ndash;10,000 monthly visitors.</p>",
                PlanFamilyName = "Default",
                PlanFamilyUid = "y9qxG2QA",
                IsQuantityEditable = false,
                MonthlyRate = 39.00m,
                AnnualRate = 390.00m,
                QuarterlyRate = 0.00m,
                OneTimeRate = 0.00m,
                SetupFee = 0.00m,
                IsActive = true,
                IsPerUser = false,
                TrialPeriodDays = 0,
                TrialUntilDate = null,
                UnitOfMeasure = "",
                PlanAddOns = new List<string>(),
                NumberOfSubscriptions = 0,
                Quota = 100,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = null
            },
            ["nmDyA2Qy"] = new SubscriptionPlan
            {
                Uid = "nmDyA2Qy",
                Code = "GROWTH",
                Name = "Growth",
                Description = "<p class=\"p1\">&bull; 300 images/month<br />&bull; Dashboard access<br />&bull; $0.35/image overage<br /><br /></p>\n<p class=\"p1\"><strong>Perfect For:</strong></p>\n<ul>\n<li>\n<p class=\"p1\">Mid-sized e-commerce brands</p>\n</li>\n<li>\n<p class=\"p1\">Multi-category SKUs and regular marketing</p>\n</li>\n<li>\n<p class=\"p1\">Use AI visuals to boost conversion and AOV</p>\n</li>\n</ul>\n<p class=\"p3\">&nbsp;</p>\n<p class=\"p1\"><strong>Example Store:</strong></p>\n<p class=\"p4\">An office furniture retailer with multiple collections (desks, chairs, accessories) and 20,000&ndash;50,000 monthly visitors.</p>",
                PlanFamilyName = "Default",
                PlanFamilyUid = "y9qxG2QA",
                IsQuantityEditable = false,
                MonthlyRate = 99.00m,
                AnnualRate = 990.00m,
                QuarterlyRate = 0.00m,
                OneTimeRate = 0.00m,
                SetupFee = 0.00m,
                IsActive = true,
                IsPerUser = false,
                TrialPeriodDays = 0,
                TrialUntilDate = null,
                UnitOfMeasure = "",
                PlanAddOns = new List<string>(),
                NumberOfSubscriptions = 0,
                Quota = 300,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = null
            },
            ["BWzwLNmE"] = new SubscriptionPlan
            {
                Uid = "BWzwLNmE",
                Code = "PRO",
                Name = "Pro",
                Description = "<p class=\"p1\">&bull; 750 images/month<br />&bull; Dashboard access<br />&bull; $0.25/image overage<br />&bull; Real-time chat support<br />&bull; Integrations</p>\n<p class=\"p1\"><strong>Perfect For:</strong></p>\n<ul>\n<li>\n<p class=\"p1\">High-volume e-commerce stores</p>\n</li>\n<li>\n<p class=\"p1\">Large catalogs and extensive marketing campaigns</p>\n</li>\n<li>\n<p class=\"p1\">Need premium support and advanced features</p>\n</li>\n</ul>\n<p class=\"p3\">&nbsp;</p>\n<p class=\"p1\"><strong>Example Store:</strong></p>\n<p class=\"p4\">A furniture retailer with thousands of SKUs and 100,000+ monthly visitors.</p>",
                PlanFamilyName = "Default",
                PlanFamilyUid = "y9qxG2QA",
                IsQuantityEditable = false,
                MonthlyRate = 199.00m,
                AnnualRate = 1990.00m,
                QuarterlyRate = 0.00m,
                OneTimeRate = 0.00m,
                SetupFee = 0.00m,
                IsActive = true,
                IsPerUser = false,
                TrialPeriodDays = 0,
                TrialUntilDate = null,
                UnitOfMeasure = "",
                PlanAddOns = new List<string>(),
                NumberOfSubscriptions = 0,
                Quota = 750,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = null
            },
            ["amRbo3mJ"] = new SubscriptionPlan
            {
                Uid = "amRbo3mJ",
                Code = "PAY_AS_YOU_GO",
                Name = "Pay as you Go",
                Description = "<p>&bull; $0.99/image<br />&bull; No monthly commitment<br />&bull; Dashboard access<br />&bull; email support</p>\n<p><strong>Perfect For:</strong></p>\n<ul>\n<li>Testing seasonal or campaign-specific products</li>\n<li>Low-volume stores with irregular needs</li>\n<li>Supplement existing plans during peak seasons</li>\n</ul>\n<p>&nbsp;</p>\n<p><strong>Example Use:</strong></p>\n<p>A seasonal retailer testing 20-30 holiday-specific products before committing to a monthly plan.</p>",
                PlanFamilyName = "Default",
                PlanFamilyUid = "y9qxG2QA",
                IsQuantityEditable = true,
                MonthlyRate = 0.99m,
                AnnualRate = 0.00m,
                QuarterlyRate = 0.00m,
                OneTimeRate = 0.00m,
                SetupFee = 0.00m,
                IsActive = false,
                IsPerUser = false,
                TrialPeriodDays = 0,
                TrialUntilDate = null,
                UnitOfMeasure = "images",
                PlanAddOns = new List<string>(),
                NumberOfSubscriptions = 0,
                Quota = 10000,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = null
            }
        };
    }
}
