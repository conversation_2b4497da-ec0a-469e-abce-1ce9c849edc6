using System.Data;
using Dapper;
using Proptexx.Core.Entities;
using Proptexx.Core.Postgresql.Builder;

namespace Proptexx.Core.Stores;

public class ClusterStore
{
    private readonly IDbConnection _connection;

    public ClusterStore(IDbConnection connection) => _connection = connection;
    
    public async Task<Guid> Create(Guid workspaceId, string clusterName, Guid clusterTypeId)
    {
        var cluster = new Cluster
        {
            WorkspaceId = workspaceId,
            ClusterTypeId = clusterTypeId,
            Name = clusterName
        };

        await _connection.InsertAsync(cluster);
        return cluster.Id;
    }

    public async Task<Guid?> GetClusterIdByClusterTypeName(Guid workspaceId, string clusterName)
    {
        var clusterId = await _connection.QueryFirstOrDefaultAsync<Guid?>(@"
                    select c.id
                    from core.cluster c
                    join core.cluster_tpl_type ctt on c.cluster_type_id = ctt.id
                    where c.workspace_id = :_workspace_id and c.name = :_cluster_name
                ", new { _workspace_id = workspaceId, _cluster_name = clusterName });

        return clusterId;
    }
}