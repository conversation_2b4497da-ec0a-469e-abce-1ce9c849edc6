using Microsoft.AspNetCore.Http;

namespace Proptexx.Web.Webhook.Middleware;

/// <summary>
/// Middleware to extract API key from query parameters and add to Authorization header
/// This supports webhook callback URLs from external services like Outseta
/// </summary>
public class ApiKeyFromQueryMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ApiKeyFromQueryMiddleware> _logger;

    public ApiKeyFromQueryMiddleware(RequestDelegate next, ILogger<ApiKeyFromQueryMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        _logger.LogDebug("ApiKeyFromQueryMiddleware processing path: {Path}", context.Request.Path);
        
        // Only process webhook endpoints
        if (context.Request.Path.StartsWithSegments("/webhook"))
        {
            _logger.LogDebug("Processing webhook endpoint: {Path}", context.Request.Path);
            
            var hasAuthHeader = HasAuthorizationHeader(context);
            var hasApiKeyQuery = context.Request.Query.TryGetValue("apiKey", out var apiKey);
            
            _logger.LogInformation("Auth header present: {HasAuthHeader}, API key in query: {HasApiKeyQuery}", 
                hasAuthHeader, hasApiKeyQuery);
            
            if (hasApiKeyQuery)
            {
                _logger.LogInformation("API key found in query: {ApiKey}", 
                    string.IsNullOrEmpty(apiKey) ? "empty" : "present");
            }
            
            // Check if Authorization header is missing but apiKey query parameter exists
            if (!hasAuthHeader && hasApiKeyQuery && !string.IsNullOrEmpty(apiKey))
            {
                // Add API key to Authorization header for authentication middleware
                context.Request.Headers["Authorization"] = $"ApiKey {apiKey}";
                
                _logger.LogInformation("API key extracted from query parameter and added to Authorization header for webhook endpoint: {Path}", 
                    context.Request.Path);
            }
            else if (hasAuthHeader)
            {
                _logger.LogDebug("Authorization header already present, skipping query parameter extraction");
            }
            else if (!hasApiKeyQuery)
            {
                _logger.LogWarning("No Authorization header and no apiKey query parameter found for webhook endpoint: {Path}", 
                    context.Request.Path);
            }
        }

        await _next(context);
    }

    private static bool HasAuthorizationHeader(HttpContext context)
    {
        return context.Request.Headers.ContainsKey("Authorization") && 
               !string.IsNullOrEmpty(context.Request.Headers["Authorization"]);
    }
} 