using Google.Cloud.BigQuery.V2;
using Google;
using Microsoft.Extensions.Logging;

namespace Proptexx.Core.BigQuery;

public static class BigQuerySchemas
{
    public static async Task EnsureTablesExistAsync(BigQueryClient client, string datasetId, ILogger? logger = null)
    {
        var dataset = await client.GetOrCreateDatasetAsync(datasetId);
        logger?.LogInformation("Ensuring BigQuery tables exist in dataset {DatasetId}", datasetId);

        // Create batches table
        await EnsureTableExistsAsync(client, datasetId, "batches", GetBatchesTableSchema(), logger);
        
        // Create batch_tasks table
        await EnsureTableExistsAsync(client, datasetId, "batch_tasks", GetBatchTasksTableSchema(), logger);
        
        // Create batch_results table
        await EnsureTableExistsAsync(client, datasetId, "batch_results", GetBatchResultsTableSchema(), logger);
        
        // Create webhook_feedback table
        await EnsureTableExistsAsync(client, datasetId, "webhook_feedback", GetWebhookFeedbackTableSchema(), logger);
        
        logger?.LogInformation("All BigQuery tables ensured for dataset {DatasetId}", datasetId);
    }

    private static string GetBatchesTableSchema() =>
        @"(
            id STRING NOT NULL,
            workspace_id STRING NOT NULL,
            account_id STRING,
            session STRING,
            created_at TIMESTAMP NOT NULL,
            callback_url STRING,
            is_sync BOOL NOT NULL
        )";

    private static string GetBatchTasksTableSchema() =>
        @"(
            id STRING NOT NULL,
            batch_id STRING NOT NULL,
            created_at TIMESTAMP NOT NULL,
            model STRING NOT NULL,
            config STRING NOT NULL
        )";

    private static string GetBatchResultsTableSchema() =>
        @"(
            id STRING NOT NULL,
            task_id STRING NOT NULL,
            status INT64 NOT NULL,
            output STRING,
            error_message STRING,
            started_at TIMESTAMP NOT NULL,
            completed_at TIMESTAMP,
            exception STRING
        )";

    private static string GetWebhookFeedbackTableSchema() =>
        @"(
            id STRING NOT NULL,
            batch_id STRING NOT NULL,
            callback_url STRING NOT NULL,
            sent_at TIMESTAMP NOT NULL,
            retry_attempt INT64 NOT NULL,
            is_success BOOL NOT NULL,
            status_code INT64,
            response_text STRING
        )";

    private static async Task EnsureTableExistsAsync(BigQueryClient client, string datasetId, string tableName, string schemaSql, ILogger? logger = null)
    {
        try
        {
            var dataset = client.GetDataset(datasetId);
            await dataset.GetTableAsync(tableName);
            // Table exists, no need to create
            logger?.LogDebug("Table {TableName} already exists in dataset {DatasetId}", tableName, datasetId);
        }
        catch (GoogleApiException ex) when (ex.HttpStatusCode == System.Net.HttpStatusCode.NotFound)
        {
            // Table doesn't exist, create it using DDL
            logger?.LogInformation("Creating table {TableName} in dataset {DatasetId}", tableName, datasetId);
            await CreateTableWithDdlAsync(client, datasetId, tableName, schemaSql, logger);
        }
        catch (Exception ex) when (ex.Message.Contains("notFound") || ex.Message.Contains("Not found") || ex.Message.Contains("Table not found"))
        {
            // Fallback for other "not found" exceptions
            logger?.LogInformation("Creating table {TableName} in dataset {DatasetId} (fallback)", tableName, datasetId);
            await CreateTableWithDdlAsync(client, datasetId, tableName, schemaSql, logger);
        }
    }

    private static async Task CreateTableWithDdlAsync(BigQueryClient client, string datasetId, string tableName, string schemaSql, ILogger? logger = null)
    {
        var createTableSql = $"CREATE TABLE IF NOT EXISTS `{client.ProjectId}.{datasetId}.{tableName}` {schemaSql}";
        logger?.LogDebug("Executing DDL: {Sql}", createTableSql);
        var job = await client.CreateQueryJobAsync(createTableSql, null);
        await job.GetQueryResultsAsync();
        logger?.LogInformation("Successfully created table {TableName} in dataset {DatasetId}", tableName, datasetId);
    }
}
