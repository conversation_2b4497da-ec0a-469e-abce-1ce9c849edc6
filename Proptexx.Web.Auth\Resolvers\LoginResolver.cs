using Dapper;
using Npgsql;
using Proptexx.Core;
using Proptexx.Core.Services;
using Proptexx.Web.Auth.Auth;

namespace Proptexx.Web.Auth.Resolvers;

public sealed class LoginResolver : IScopeResolver<LoginPayload>
{
    private readonly NpgsqlDataSource _dataSource;

    public LoginResolver(NpgsqlDataSource dataSource)
    {
        _dataSource = dataSource;
    }

    public async Task ResolveAsync(ScopeContext context, LoginPayload payload, CancellationToken cancellationToken)
    {
        var clientId = context.Identity.GetClientGuid();
        var password = payload.Password ?? throw new UnauthorizedAccessException();

        await using var npgsql = await _dataSource.OpenConnectionAsync(cancellationToken);

        var param = new { _email = payload.Email };
        var account = await npgsql.QueryFirstOrDefaultAsync<LoginModel>(LoginModel.LoginEmailSql, param)
                      ?? throw new UnauthorizedAccessException();

        if (!PasswordService.VerifyPassword(password, account.Hash, account.Salt))
        {
            throw new UnauthorizedAccessException();
        }

        await LoginHelper.SetAccountClaimsAsync(context, account.Id, $"{account.FirstName} {account.FamilyName}", payload.Email);
        await LoginHelper.SetWorkspaceClaimsAsync(context, npgsql, account.Id);
        await LoginHelper.SetSystemClaimsAsync(context, account.IsRoot);
    }

    private class LoginModel
    {
        public required Guid Id { get; init; }

        public required string FirstName { get; init; }

        public required string FamilyName { get; init; }

        public required string Hash { get; init; }

        public required string Salt { get; init; }
        
        public bool IsRoot { get; init; }

        public static string LoginEmailSql => @"
            select a.id,
                   a.first_name,
                   a.family_name,
                   a.is_root,
                   acs.hash,
                   acs.salt
            from core.account a
            join core.account_secret acs on a.id = acs.account_id and acs.expired_at is null
            join core.account_email ace on a.id = ace.account_id
            where ace.email = :_email and a.cancelled_at is null
        ";
    }
}
    

public class LoginSecretModel
{
    public required Guid Id { get; init; }

    public required string FirstName { get; init; }

    public required string FamilyName { get; init; }

    public bool IsRoot { get; init; }

    public bool RegisteredInWorkspace { get; init; }

    public static string Sql => @"
            select a.id,
                   a.first_name,
                   a.family_name,
                   a.is_root,
                   (count(c.workspace_id) > 0) as registered_in_workspace
            from core.account a
            left outer join core.cluster_account_binding cab on a.id = cab.account_id
            left outer join core.cluster c on cab.cluster_id = c.id and c.workspace_id = :_workspace_id and lower(c.name) = 'lead'
            where a.id::text = :_username and a.cancelled_at is null
            group by a.id, a.first_name, a.family_name";
}

public sealed class LoginPayload
{
    public required string Email { get; set; }

    public required string Password { get; set; }
}