using Proptexx.Core.Http;
using Proptexx.Core.Storage;

namespace Proptexx.AI.Models.Gen;

public sealed class SkyReplacement : ImageEnhancementModel
{
    public SkyReplacement(IImageEnhancementClient imageEnhancementClient, IStorageService storageService)  
        : base(imageEnhancementClient, storageService)
    {
    }

    protected override string GetModelEndpoint(string workspaceId)
        => "http://preprocessing.prod.ai.proptexx.com/image-enhancement-sky-replacement/predict";
}
