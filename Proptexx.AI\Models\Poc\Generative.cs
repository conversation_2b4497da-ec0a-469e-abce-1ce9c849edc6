using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using Google.Cloud.AIPlatform.V1;
using Proptexx.Core.AI;
using Proptexx.Core.Http;
using Proptexx.Core.Json;
using Proptexx.Core.Services;
using Proptexx.Core.Storage;

namespace Proptexx.AI.Models.Poc;

public sealed class Generative : IModel
{
    private readonly string _projectId = "capable-fuze-448611-n0";
    private readonly string _location = "us-central1";
    private readonly string _authToken = "*************************************************************************************************************************************************************************************************************************************";

    private readonly IImageAssessmentClient _assessmentClient;
    private readonly IGenerativeClient _generativeClient;
    private readonly IStorageService _storageService;
    private readonly PredictionServiceClient _serviceClient;

    public Generative(IImageAssessmentClient assessmentClient, IGenerativeClient generativeClient, IStorageService storageService, PredictionServiceClient serviceClient)
    {
        _assessmentClient = assessmentClient;
        _generativeClient = generativeClient;
        _storageService = storageService;
        _serviceClient = serviceClient;
    }

    public async Task<ModelResponse> InferAsync(ModelContext context)
    {
        if (context.Payload is null) throw new ApplicationException("Payload not provided");

        try
        {
            var imageUrl = PayloadService.GetRequiredString(context.Payload, "imageUrl");

            var ext = ".jpg"; // GetFileExtensionFromUrl(imageUrl);
            var assessment = await _assessmentClient.InspectImageAsync(imageUrl);

            // var text = """
            //            Analyze this interior photo and generate a detailed natural language prompt that can be used with the Imagen model to virtually stage the space. The prompt should describe the room’s architectural features, lighting, materials, and window layout. It should remove any existing furniture and suggest a warm Scandinavian staging style using light grey tones and natural materials. Ensure all structural elements like walls, windows, doors, fireplace, floors, and ceiling angles are preserved. The generated prompt must be coherent, descriptive, and tailored to the room’s actual layout and perspective.
            //            """;
            //
            // var prompt = await GetPromptAsync(_authToken, text, assessment);
            var prompt = """
                         An empty rectangular room with soft grey walls, a light marble or stone floor, and a flat white ceiling with four recessed spotlights. The left side of the room features full-height black-framed glass windows, partially covered with elegant grey curtains. Natural daylight enters the space gently from the window, casting subtle shadows.
                         Virtually stage the room as a warm Scandinavian-style living room using light grey and natural wood tones. Add a cozy L-shaped corner sofa in light grey textured fabric, positioned against the back wall, with plush cushions and a light throw. Place a low-profile rectangular wood coffee table in front of the sofa with a small ceramic bowl, candle, and stacked books. Include a soft neutral wool or jute rug under the seating area to add comfort without covering the whole floor.
                         Place a potted green plant near the window and a slim matte black floor lamp in the opposite corner. Style the scene minimally to maintain an airy, calm aesthetic.
                         Do not modify the floor, windows, curtains, ceiling, spotlights, or any structural features. Ensure the original camera perspective, light direction, and architectural layout remain fully intact in the final image.
                         """;

            var outputUrls = new Dictionary<string, string>();
            var models = new[] { "imagen-3.0-generate-001", "imagen-3.0-capability-001" };
            
            try
            {
                var base64Output = await Imagen30Generate001(_authToken, assessment, prompt);
                var output = await UploadPhotoAsync(base64Output, ext);
                outputUrls.Add("generate", output);
            }
            catch (Exception e)
            {
                outputUrls.Add("generate", e.Message);
            }

            try
            {
                var base64Output = await Imagen30Capability001(_authToken, assessment, prompt);
                var output = await UploadPhotoAsync(base64Output, ext);
                outputUrls.Add("capability", output);
            }
            catch (Exception e)
            {
                outputUrls.Add("capability", e.Message);
            }

            var document = CreateDocument(imageUrl, prompt, outputUrls);

            return new ModelResponse
            {
                Document = document
            };
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    private async Task<string> Imagen30Generate001(string authToken, ImageAssessment assessment, string prompt)
    {
        var payload = new Dictionary<string, object?>()
        {
            ["instances"] = new[]
            {
                new {
                    prompt,
                    context_image = new {
                        mimeType = assessment.MimeType,
                        bytesBase64Encoded = assessment.Base64String
                    }
                }
            },
            ["parameters"] = new {
                guidance_scale = 8,
                num_images = 1
            }
        };

        var endpoint = $"https://{_location}-aiplatform.googleapis.com/v1" +
                       $"/projects/{_projectId}" +
                       $"/locations/{_location}" +
                       $"/publishers/google" +
                       $"/models/imagen-3.0-generate-001:predict";

        using var client = new HttpClient();
        client.DefaultRequestHeaders.Add("Authorization", $"Bearer {authToken}");
        var response = await client.PostAsJsonAsync(endpoint, payload);
        var content = await response.Content.ReadFromJsonAsync<PredictionResponse>(JsonDefaults.JsonSerializerOptions);

        var prediction = content?.Predictions.FirstOrDefault();
        if (prediction?.BytesBase64Encoded is null)
        {
            throw new ApplicationException("Unable to transform model result");
        }

        return prediction.BytesBase64Encoded;
    }

    private async Task<string> Imagen30Capability001(string authToken, ImageAssessment assessment, string prompt)
    {
        var payload = new Dictionary<string, object?>()
        {
            ["instances"] = new[]
            {
                new {
                    prompt,
                    referenceImages = new []
                    {
                        new
                        {
                            referenceId = 1,
                            referenceType = "",
                            referenceImage = new
                            {
                                mimeType = assessment.MimeType,
                                bytesBase64Encoded = assessment.Base64String
                            }
                        }
                    }
                }
            },
            ["parameters"] = new {
                guidance_scale = 8,
                num_images = 1
            }
        };

        var endpoint = $"https://{_location}-aiplatform.googleapis.com/v1" +
                       $"/projects/{_projectId}" +
                       $"/locations/{_location}" +
                       $"/publishers/google" +
                       $"/models/imagen-3.0-capability-001:predict";

        using var client = new HttpClient();
        client.DefaultRequestHeaders.Add("Authorization", $"Bearer {authToken}");
        var response = await client.PostAsJsonAsync(endpoint, payload);
        var content = await response.Content.ReadFromJsonAsync<PredictionResponse>(JsonDefaults.JsonSerializerOptions);

        var prediction = content?.Predictions.FirstOrDefault();
        if (prediction?.BytesBase64Encoded is null)
        {
            throw new ApplicationException("Unable to transform model result");
        }

        return prediction.BytesBase64Encoded;
    }

    private async Task<string> GetPromptAsync(string accessToken, string text, ImageAssessment assessment)
    {
        string modelId = "gemini-2.0-flash-001";
        string endpoint = $"https://{_location}-aiplatform.googleapis.com/v1" +
                          $"/projects/{_projectId}" +
                          $"/locations/global" +
                          $"/publishers/google" +
                          $"/models/{modelId}:generateContent";

        var parts = new List<object>
        {
            new
            {
                inlineData = new
                {
                    mimeType = assessment.MimeType,
                    data = assessment.Base64String
                }
            },
            new { text }
        };

        var payload = new
        {
            contents = new[]
            {
                new {
                    role = "user",
                    parts = parts.ToArray()
                }
            },
            generationConfig = new
            {
                temperature = 1,
                topP = 0.95,
                responseMimeType = "application/json",
                responseSchema = new
                {
                     type = "OBJECT",
                     required = new[] { "prompt" },
                     properties = new
                     {
                         prompt = new
                         {
                             type = "STRING"
                         }
                     }
                }
            },
            safetySettings = new[]
            {
                new { category = "HARM_CATEGORY_HATE_SPEECH", threshold = "OFF" },
                new { category = "HARM_CATEGORY_DANGEROUS_CONTENT", threshold = "OFF" },
                new { category = "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold = "OFF" },
                new { category = "HARM_CATEGORY_HARASSMENT", threshold = "OFF" }
            }
        };

        using var client = new HttpClient();
        var request = new HttpRequestMessage(HttpMethod.Post, endpoint)
        {
            Content = new StringContent(JsonSerializer.Serialize(payload), Encoding.UTF8, "application/json")
        };

        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

        var response = await client.SendAsync(request);
        if (!response.IsSuccessStatusCode)
        {
            var error = await response.Content.ReadAsStringAsync();
            throw new Exception($"Gemini API error: {error}");
        }

        var json = await response.Content.ReadAsStringAsync();
        using var doc = JsonDocument.Parse(json);
        var prompt = ParsePrompt(doc);
        return prompt;
    }

    private static string ParsePrompt(JsonDocument doc)
    {
        var textPart = doc.RootElement
            .GetProperty("candidates")[0]
            .GetProperty("content")
            .GetProperty("parts")[0]
            .GetProperty("text");

        string? prompt = null;
        if (textPart.ValueKind == JsonValueKind.Object)
        {
            prompt = textPart.GetProperty("prompt").GetString();
        }
        else if (textPart.ValueKind == JsonValueKind.String)
        {
            var jsonLiteral = textPart.GetRawText();
            var json = JsonSerializer.Deserialize<string>(jsonLiteral, JsonDefaults.JsonSerializerOptions);

            if (!string.IsNullOrWhiteSpace(json))
            {
                var obj = JsonSerializer.Deserialize<PromptResponse>(json, JsonDefaults.JsonSerializerOptions);
                prompt = obj?.Prompt;
            }
        }

        return prompt ?? throw new Exception("Unable to parse prompt");
    }

    private static JsonDocument CreateDocument(string inputUrl, string prompt, Dictionary<string, string> outputs)
    {
        var obj = new { inputUrl, prompt, outputs };
        var str = JsonSerializer.Serialize(obj);
        return JsonDocument.Parse(str);
    }

    private async Task<string> UploadPhotoAsync(string base64Output, string extension)
    {
        var containerName = "forward-outputs";
        extension = extension.StartsWith('.') ? extension[1..] : extension;
        var fileName = $"{Guid.NewGuid().ToString()}.{extension}";
        return await _storageService.UploadImageAsync(containerName, fileName, base64Output, "image/jpeg");
    }

    private static string GetFileExtensionFromUrl(string url)
    {
        var uri = new Uri(url);

        // Option 1: Try to get from filename
        string fileName = Path.GetFileName(uri.AbsolutePath); // Might be something like "1106x830>/format/jpg"
        string ext = Path.GetExtension(fileName);

        // If not valid extension, try to get it from the last segment
        if (string.IsNullOrEmpty(ext) || ext.Length <= 1)
        {
            string[] segments = uri.Segments;
            string lastSegment = segments[^1].Trim('/').ToLower(); // Get the last part of the path
            if (lastSegment == "jpg" || lastSegment == "png" || lastSegment == "jpeg" || lastSegment == "webp") // extend this list
            {
                return "." + lastSegment;
            }
        }

        return ext; // might be empty if no extension found
    }
}

internal class PromptResponse
{
    public string? Prompt { get; set; }
}

public class PredictionResponse
{
    public List<Prediction> Predictions { get; set; }
}

public class Prediction
{
    public string BytesBase64Encoded { get; set; }
}

public class GeminiResponse
{
    public List<Candidate> Candidates { get; set; } = new();
}

public class Candidate
{
    public Content Content { get; set; }
    public string FinishReason { get; set; }
    public List<SafetyRating>? SafetyRatings { get; set; }
}

public class Content
{
    public string Role { get; set; }
    public List<Part> Parts { get; set; } = new();
}

public class Part
{
    public string Text { get; set; }
}

public class SafetyRating
{
    public string Category { get; set; }
    public string Probability { get; set; }
}
