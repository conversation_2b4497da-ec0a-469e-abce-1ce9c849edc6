using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Proptexx.Core.Auth;
using Proptexx.Core.Configuration;
using Proptexx.Core.Consumers;
using Proptexx.Core.Extensions;
using Proptexx.Core.HealthCheck;
using Proptexx.Core.Http;
using Proptexx.Core.Interface;
using Proptexx.Core.Json;
using Proptexx.Core.Middleware;
using Proptexx.Core.Options;
using Proptexx.Core.Postgresql;
using Proptexx.Core.Redis;
using Proptexx.Core.Services;
using Proptexx.Core.Telemetry;
using Proptexx.Web.ClientServices.Interfaces;
using Proptexx.Web.Services;

namespace Proptexx.Web;

public static class ProptexxWebBuilder
{
    public static WebApplicationBuilder AddProptexxWeb(this WebApplicationBuilder builder)
    {
        builder.AddProptexxConfiguration();
        builder.AddProptexxLogging();
        builder.AddProptexxHealthChecks();

        // Config options
        var proptexxSection = builder.Configuration.GetSection("proptexx");
        var proptexxOptions = proptexxSection.Get<ProptexxOptions>()
            ?? throw new NullReferenceException("Config section Proptexx");

        builder.Services.Configure<ProptexxOptions>(proptexxSection);
        
        // Config authentication middleware
        builder.Services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.TokenValidationParameters = AuthService.GetTokenValidationParams(proptexxOptions);
            });

        builder.Services.AddCors(cors =>
        {
            cors.AddDefaultPolicy(p => p
                .SetIsOriginAllowed(_ => true)
                .AllowAnyHeader()
                .AllowAnyMethod()
                .AllowCredentials()
                .Build());
        });
        builder.Services.AddJsonConverters();
        builder.Services.AddPostgresql(builder.Configuration);
        builder.Services.AddRedis(builder.Configuration);
        builder.Services.AddHttpContextAccessor();
        builder.Services.AddProptexxHttpClients();
        builder.Services.AddScoped<AuthenticationMiddleware>();
        builder.Services.AddSubscriptionManagementServices();
        builder.Services.AddSingleton<WorkspaceDataHashStore>();
        builder.Services.AddHttpClient<IOutsetaApiClient, OutsetaApiClient>();
        builder.Services.AddScoped<IOutsetaUserCreationService, OutsetaUserCreationService>();
        builder.Services.AddHostedService<OutsetaEventConsumerService>();

        builder.Services.Configure<OutsetaOptions>(builder.Configuration.GetSection(OutsetaOptions.SectionName));
        return builder;
    }

    public static void UseProptexxWeb(this WebApplication app, bool withAuthMiddleware = true, bool enableApiKeyUsageValidation = false)
    {
        app.UseRouting();
        app.MapHealthChecks("/health");
        app.UseCors();

        if (withAuthMiddleware)
        {
            app.UseMiddleware<AuthenticationMiddleware>();
        }

        // Add API key validation middleware if requested
        if (enableApiKeyUsageValidation)
        {
            app.UseApiKeyUsageValidation();
        }

        // app.UseMiddleware<TelemetryMiddleware>();
    }

    /// <summary>
    /// Alternative extension method to configure Proptexx web with API key validation enabled by default
    /// This is useful for API services that require API key validation
    /// </summary>
    public static void UseProptexxWebWithApiValidation(this WebApplication app, bool withAuthMiddleware = true)
    {
        app.UseProptexxWeb(withAuthMiddleware, enableApiKeyUsageValidation: true);
    }

    /// <summary>
    /// Configure Proptexx web with customizable API key validation
    /// </summary>
    public static void UseProptexxWebWithApiValidation(this WebApplication app, Action<RequestValidationOptions> configure, bool withAuthMiddleware = true)
    {
        app.UseProptexxWeb(withAuthMiddleware, enableApiKeyUsageValidation: false); // Don't use default validation
        app.UseApiKeyValidation(configure); // Use custom validation configuration
    }
}