namespace Proptexx.Web.Command.Widget;

// public sealed class AddFurniture : ICommand
// {
//     [Required]
//     public required string ImageUrl { get; init; }
//
//     [Required]
//     public required string RoomType { get; init; }
//
//     [Required]
//     public required string ArchitectureStyle { get; init; }
//
//     public string? Url { get; init; }
//
//     public async Task ExecuteAsync(CommandContext context)
//     {
//         var strBld = new StringBuilder($"Call to command {nameof(AddFurniture)}\nListing: {this.Url}\n");
//
//         try
//         {
//             var clientId = context.User.GetClientIdentifier();
//             strBld.AppendLine($"Client ID: {clientId}");
//
//             var redis = context.GetService<IConnectionMultiplexer>().GetDatabase();
//
//             var fieldsToGet = new RedisValue[] { "workspaceId" };
//             var values = await redis.HashGetAsync($"client_service:{clientId}", fieldsToGet);
//
//             if (values[0].IsNullOrEmpty || !Guid.TryParse(values[0], out var workspaceId))
//             {
//                 throw new UnauthorizedAccessException();
//             }
//
//             strBld.AppendLine($"Workspace ID: {workspaceId}");
//             strBld.AppendLine($"Input image: {this.ImageUrl}");
//             strBld.AppendLine($"Room Type: {this.RoomType}");
//             strBld.AppendLine($"Architecture Style: {this.ArchitectureStyle}");
//
//             var model = context.Services.GetModel<GenerativeVirtualStaging>();
//
//             var payload = context.Definition.Payload is null
//                 ? JsonDocument.Parse("{}")
//                 : JsonDocument.Parse(context.Definition.Payload.ToJsonString());
//
//             var ctx = new GenerativeModelContext
//             {
//                 ImageUrl = this.ImageUrl,
//                 RoomType = this.RoomType,
//                 ArchitectureStyle = this.ArchitectureStyle,
//                 ItemId = Guid.NewGuid().ToString(),
//                 WorkspaceId = workspaceId.ToString(),
//                 CancellationToken = context.CancellationToken,
//                 Payload = payload
//             };
//
//             var response = await model.InferAsync(ctx);
//
//             if (response.Document is null ||
//                 !response.Document.RootElement.TryGetProperty("imageUrl", out var imageUrlElement))
//             {
//                 throw new ApplicationException("Model returned empty result");
//             }
//
//             var imageUrl = imageUrlElement.GetString()!;
//             if (!string.IsNullOrWhiteSpace(imageUrl))
//             {
//                 context.AddData("responseUrl", imageUrl);
//                 strBld.AppendLine($"Output Image: {imageUrl}");
//             }
//             else
//             {
//                 strBld.AppendLine("Output Image: empty response");
//             }
//         }
//         catch (ApplicationException e)
//         {
//             throw new ProptexxException(e.Message, e);
//         }
//         catch (Exception e)
//         {
//             strBld.AppendLine(e.ToString());
//             throw;
//         }
//         finally
//         {
//             Console.WriteLine(strBld.ToString());
//         }
//     }
// }
