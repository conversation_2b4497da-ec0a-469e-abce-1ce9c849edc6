﻿using Dapper;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;
using System.Threading.Tasks;

namespace Proptexx.Web.Query.Dashboard
{
    public sealed class GetTopRenderedProperties : BaseFilter, IQuery
    {
        public async Task<object?> ExecuteAsync(QueryContext context)
        {
            await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);

            var result = await npgsql.QueryAsync<RenderPropertyStat>(Sql, GetParameters());
            return result;
        }

        public static string Sql => @"
            SELECT
                render_url    AS RenderUrl,
                SUM(request_count) AS RenderCount
            FROM telemetry.mv_widget_renders_summary
            WHERE
                day BETWEEN @StartDate::date AND @EndDate::date
                AND (NULLIF(@ids, ARRAY[]::UUID[]) IS NULL OR workspace_id = ANY(@ids))
            GROUP BY render_url
            ORDER BY RenderCount DESC
            LIMIT 5;
        ";
    }

    // Result model mapping for the rendered properties
    public sealed class RenderPropertyStat
    {
        public required string RenderUrl { get; init; }
        public required int RenderCount { get; init; }
    }
}
