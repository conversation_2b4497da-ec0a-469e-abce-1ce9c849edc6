using System.Net.Http.Json;
using System.Text.Json;
using Microsoft.Extensions.Logging;

namespace Proptexx.Core.Http;

public interface IImageEnhancementClient
{
    Task<JsonDocument> EnhanceAsync(string endpoint, Dictionary<string, object?> payload, CancellationToken cancellationToken);
}

public sealed class ImageEnhancementHttpClient(HttpClient httpClient, ILogger<ImageEnhancementHttpClient> logger) 
    : BaseHttpClient(httpClient, logger), IImageEnhancementClient
{
    public async Task<JsonDocument> EnhanceAsync(string endpoint, Dictionary<string, object?> payload, CancellationToken cancellationToken)
    {
        using var content = JsonContent.Create(new Dictionary<string, object?>
        {
            ["instances"] = new List<object> { payload }
        });

        return await PostJsonAsync(endpoint, content, cancellationToken);
    }
}
