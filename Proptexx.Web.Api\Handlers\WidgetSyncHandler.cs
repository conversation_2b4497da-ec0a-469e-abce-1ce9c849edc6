using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Web;
using Dapper;
using Npgsql;
using Proptexx.AI;
using Proptexx.AI.Models.CV;
using Proptexx.Core;
using Proptexx.Core.AI;
using Proptexx.Core.Entities;
using Proptexx.Core.Extensions;
using Proptexx.Core.Http;
using Proptexx.Core.Json;
using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Resilience;
using Proptexx.Core.Services;
using Proptexx.Web.Api.Helpers;
using Proptexx.Web.Api.Responses;

namespace Proptexx.Web.Api.Handlers;

public sealed class WidgetSyncHandler
{
    private ILogger<WidgetSyncHandler> _logger;
    private readonly NpgsqlDataSource _dataSource;
    private readonly IImageAssessmentClient _imageAssessmentService;
     private const int RetryCount = 3;
    private const int DelaySeconds = 1;
    public WidgetSyncHandler(
        ILogger<WidgetSyncHandler> logger,
        NpgsqlDataSource dataSource,
        IImageAssessmentClient imageAssessmentService)
    {
        _logger = logger;
        _dataSource = dataSource;
        _imageAssessmentService = imageAssessmentService;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var id = Guid.NewGuid();
        var response = new WidgetApiResponse();

        try
        {
            var payload = await JsonDocument.ParseAsync(context.Request.Body);
            var listingUrl = PayloadService.GetRequiredString(payload, "url");
            listingUrl = NormalizeUrl(listingUrl);

            var images = PayloadService.GetRequiredElement(payload, "images");

            if (images.ValueKind != JsonValueKind.Array)
            {
                throw new ApplicationException("Expected an images property of type array");
            }

            var workspaceId = context.User.GetWorkspaceGuid();

            await using var npgsql = await _dataSource.OpenConnectionAsync(context.RequestAborted);
            var imageIds = ParseImageUrls(images);
            var param = new { _image_ids = imageIds.Keys.ToArray(), _listing_url = listingUrl, _workspace_id = workspaceId };
            var enumerable = await npgsql.QueryAsync<WidgetImageModel>(WidgetImageModel.Sql, param);
            var list = enumerable.ToList();

            // Figure out whats going to be persisted
            var (processList, listingList) = FilterForProcessing(workspaceId, listingUrl, imageIds, list);

            // If received any items to process, then call CV assessment of these
            if (processList.Count > 0)
            {
                await AssessImagesAsync(workspaceId.ToString(), context.RequestServices, processList, context.RequestAborted);
            }

            // If received any items to process or listing to register
            if (processList.Count + listingList.Count > 0)
            {
                await PersistAsync(npgsql, processList, listingList, context.RequestAborted);
            }
            
            // Return the complete result here
            var result = list
                .Merge(processList, image => image.Id)
                .Select(x => new
                {
                    Url = x.OriginalUrl ?? x.ImageUrl,
                    x.Status,
                    x.RoomType,
                    x.IsEmptyRoom,
                    x.ErrorMessage
                });

            var document = await result.ToJsonDocumentAsync(JsonDefaults.JsonSerializerOptions);

            _logger.LogInformation(
                "Listing: {ListingUrl}\nTotal Images: {TotalCount}\nProcessed and appended: {AppendedCount}", 
                listingUrl, result.Count(), processList.Count);

            response.Success(id, document);
            context.Response.StatusCode = StatusCodes.Status200OK;
        }
        catch (ApplicationException e)
        {
            context.Response.StatusCode = StatusCodes.Status400BadRequest;
            response.Fail(e.Message);
        }
        catch (UnauthorizedAccessException e)
        {
            context.Response.StatusCode = StatusCodes.Status401Unauthorized;
            response.Fail(e.Message);
        }
        catch (JsonException)
        {
            context.Response.StatusCode = StatusCodes.Status400BadRequest;
            response.Fail("Unable to parse the request payload");
        }
        catch (Exception e)
        {
            _logger.LogError(e, "System exception");
            context.Response.StatusCode = StatusCodes.Status500InternalServerError;
            response.Fail("The operation exited with an error. We have been notified and are looking into it");
        }
        finally
        {
            await context.Response.WriteAsJsonAsync(response, JsonDefaults.JsonSerializerOptions);
        }
    }

    private static (List<WidgetImage> processList, List<WidgetListing> listingList) FilterForProcessing(
        Guid workspaceId, string listingUrl, Dictionary<string, string> imageIds, IList<WidgetImageModel> list)
    {
        var processList = new List<WidgetImage>();
        var listingList = new List<WidgetListing>();
         
        foreach (var imageId in imageIds)
        {
            var imageModel = list.FirstOrDefault(x => x.Id.Equals(imageId.Key));

            if (imageModel?.InListing is not true)
            {
                listingList.Add(new WidgetListing
                {
                    ImageId = imageId.Key,
                    ListingUrl = listingUrl,
                    WorkspaceId = workspaceId
                });
            }

            if (imageModel is not null) continue;

            processList.Add(new WidgetImage
            {
                Id = imageId.Key,
                ImageUrl = imageId.Value,
                OriginalUrl = null,
                Status = ImageAssessmentStatus.Processing
            });
            
        }
        return (processList, listingList);
    }

    private static async Task PersistAsync(NpgsqlConnection npgsql, IEnumerable<WidgetImage> processList, IList<WidgetListing> listingList, CancellationToken cancellationToken)
    {
        await using var transaction = await npgsql.BeginTransactionAsync(cancellationToken);

        try
        {
            var conflictHandlerForAssessment = new IgnoreOnConflictHandler(ConflictType.Column);
            await npgsql.InsertAsync(processList, conflictHandlerForAssessment);

            if (listingList.Any())
            {
                var conflictHandlerForListing = new IgnoreOnConflictHandler(ConflictType.Constraint, "ux_listing");
                await npgsql.InsertAsync(listingList, conflictHandlerForListing);
            }

            await transaction.CommitAsync(cancellationToken);
        }
        catch (Exception)
        {
            await transaction.RollbackAsync(cancellationToken);
            throw;
        }
    }

    private static Dictionary<string, string> ParseImageUrls(JsonElement images)
    {
        var result = new Dictionary<string, string>();
        foreach (var imageEl in images.EnumerateArray())
        {
            var imageUrl = imageEl.GetString();
            if (string.IsNullOrWhiteSpace(imageUrl)) continue;

            var imageUrls = NestedUrlParser.GetNestedUrls(imageUrl);
            for (var i = 0; i < imageUrls.Count; i++)
            {
                var imageId = GenerateUrlHash(imageUrls[i]);
                result.TryAdd(imageId, imageUrls[i]);
            }
        }

        return result;
    }

    private async Task AssessImagesAsync(string workspaceId, IServiceProvider services, IEnumerable<WidgetImage> processList, CancellationToken cancellationToken)
    {
        var semaphore = new SemaphoreSlim(20);
        var tasks = processList.Select(async image =>
        {
            await semaphore.WaitAsync(cancellationToken);

            try
            {
                var assessment = await _imageAssessmentService.InspectImageAsync(image.ImageUrl, cancellationToken);
                image.FileHash = assessment.FileHash;
                image.MimeType = assessment.MimeType;
                image.Width = assessment.Width;
                image.Height = assessment.Height;
                
                if (image.Width is not 0 and < 300 || image.Height is not 0 and < 300)
                {
                    throw new ApplicationException("Image dimensions is less than the required 300x300px");
                }

                (image.RoomType, image.IsEmptyRoom) = await RunPreprocessForRoomAsync(workspaceId, services, assessment, cancellationToken);
                await RunPreprocessForObjectsAsync(workspaceId, services, assessment, cancellationToken);
                image.Status = ImageAssessmentStatus.Success;
            }
            catch (Exception e)
            {
                image.Status = ImageAssessmentStatus.Error;
                image.ErrorMessage = e.Message;
            }
            finally
            {
                semaphore.Release();
            }
        });

        await Task.WhenAll(tasks);
    }

    private static string NormalizeUrl(string url)
    {
        if (!Uri.TryCreate(url, UriKind.Absolute, out var uri))
        {
            throw new ApplicationException("Property `url` must be an absolute url");
        }
        
        var queryParams = HttpUtility.ParseQueryString(uri.Query);
        var sortedQuery = string.Join("&", queryParams.AllKeys.OrderBy(k => k).Select(k => $"{k}={queryParams[k]}"));

        var normalizedUrl = $"{uri.Scheme}://{uri.Host.ToLowerInvariant()}{uri.AbsolutePath}";

        if (!string.IsNullOrWhiteSpace(sortedQuery))
        {
            normalizedUrl += $"?{sortedQuery}";
        }

        return normalizedUrl;
    }
    
    private static string GenerateUrlHash(string normalizedUrl)
    {
        var hashBytes = SHA256.HashData(Encoding.UTF8.GetBytes(normalizedUrl));
        return BitConverter.ToString(hashBytes).Replace("-", "").ToLowerInvariant();
    }

    private static async Task<(string roomType, bool isEmptyRoom)> RunPreprocessForRoomAsync(string workspaceId, IServiceProvider services, ImageAssessment assessment, CancellationToken cancellationToken)
    {
        var model = services.GetModel<PreProcessorForRoomScene>();
        var logger = services.GetRequiredService<ILogger<PreProcessorForRoomScene>>();

        var response = await PollyRetryExtensions.GetDefaultRetryPolicy(logger, "PreProcessorForRoomScene - RunPreprocessForRoomAsync", RetryCount, DelaySeconds).ExecuteAsync(() =>
        {
            return model.InferAsync(workspaceId, assessment, cancellationToken);
        });

        var result = response.GetRequiredResult<PreProcessorForRoomScene.Result>();

        if (result is null) throw new ApplicationException($"No prediction from pre-processing room model");

        var sceneType = result.SceneTypes.FirstOrDefault();
        if (sceneType is null) throw new ApplicationException("SceneType returned null");

        if (!sceneType.Equals("indoor", StringComparison.OrdinalIgnoreCase))
        {
            throw new ApplicationException("Not of an indoor scene");
        }
        
        string[] acceptedRoomTypes = ["bedroom", "living room", "kitchen", "bathroom"];
    
        string? roomType = null;
        foreach (var (key, _) in result.RoomTypes)
        {
            if (acceptedRoomTypes.Contains(key, StringComparer.OrdinalIgnoreCase))
            {
                roomType = key.ToLowerInvariant();
            }
        }

        if (roomType is null)
        {
            var errorMessage = result.RoomTypes.Keys.Count > 1
                ? $"Room types {string.Join(',', result.RoomTypes.Keys)} are not supported"
                : $"Room type {string.Join(',', result.RoomTypes.Keys)} is not supported";

            throw new ApplicationException(errorMessage);
        }

        return (roomType, result.IsEmptyRoom);
    }

    private static async Task RunPreprocessForObjectsAsync(string workspaceId, IServiceProvider services, ImageAssessment assessment, CancellationToken cancellationToken)
    {
        var model = services.GetModel<PreProcessorForObjects>();
        var logger = services.GetRequiredService<ILogger<PreProcessorForObjects>>();
        var response = await PollyRetryExtensions.GetDefaultRetryPolicy(logger, "PreProcessorForObjects - RunPreprocessForObjectsAsync", RetryCount, DelaySeconds).ExecuteAsync(() =>
        {
            return model.InferAsync(workspaceId, assessment, cancellationToken);
        });
        var result = response.GetRequiredResult<PreProcessorForObjects.Result>();

        if (result is null) throw new ApplicationException($"No prediction from pre-processing object model");
        if (result.HasPerson) throw new ApplicationException("Image contains a person");
        if (result.HasCloseUpObject) throw new ApplicationException("Image contains a dominant close up object");
        if (result.HasRealAnimal) throw new ApplicationException("Image contains an animal");
        if (result.HasFood) throw new ApplicationException("Image contains food");
        if (result.HasMultipleImages) throw new ApplicationException("Two or more frames detected within the image");
    }

    private static Dictionary<string, float>? MergeInputRoomTypesScores(string[]? roomTypes, float[]? confidenceScores)
    {
        if (roomTypes is null || confidenceScores is null) return null;

        var minLength = Math.Min(roomTypes.Length, confidenceScores.Length);

        var dictionary = new Dictionary<string, float>();
        for (var i = 0; i < minLength; i++)
        {
            dictionary[roomTypes[i]] = confidenceScores[i];
        }

        return dictionary;
    }
}

public class WidgetImageModel : WidgetImage
{
    public bool? InListing { get; init; }

    public static string Sql = @"
        select i.*,
               (case when l.listing_url = :_listing_url then true else false end) as in_listing
        from widget.image i
        left outer join widget.listing l on l.image_id = i.id
        where i.id = any(:_image_ids) or (l.workspace_id = :_workspace_id and l.listing_url = :_listing_url)
    ";
}

public sealed class WidgetApiResponse : ApiResponse
{
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public JsonDocument? Result { get; private set; }

    public void Success(Guid id, JsonDocument? result)
    {
        base.Success(id);
        this.Result = result;
    }
}