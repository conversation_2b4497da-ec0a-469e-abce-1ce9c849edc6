using Proptexx.Core.Entities;

namespace Proptexx.Core.Services;

/// <summary>
/// Interface for API key registration service
/// Follows Single Responsibility Principle
/// </summary>
public interface IApiKeyRegistrationService
{
    ///// <summary>
    ///// Register an existing API key in the unified system with explicit product service type
    ///// </summary>
    ///// <param name="apiKey">API key created with existing ApiKeyService</param>
    ///// <param name="productService">Product service type (must be specified explicitly)</param>
    ///// <param name="clientName">Client name</param>
    ///// <param name="contactName">Contact name</param>
    ///// <param name="contactEmail">Contact email</param>
    ///// <param name="environment">Environment</param>
    ///// <param name="subscriptionType">Subscription type (Monthly, Yearly, OneTime, PayAsYouGo)</param>
    ///// <param name="rateLimitMonthly">Rate limit per month</param>
    ///// <param name="workspaceId">Optional workspace ID</param>
    ///// <param name="accountId">Optional account ID</param>
    ///// <param name="planId">Optional plan ID</param>
    ///// <param name="domain">Optional domain</param>
    ///// <param name="allowedPaths">Optional allowed paths</param>
    ///// <param name="outsetaId">Optional Outseta ID for integration</param>
    ///// <param name="notes">Optional notes</param>
    ///// <param name="cancellationToken">Cancellation token</param>
    ///// <returns>The registered Subscription</returns>
    //Task<Subscription> RegisterApiKeyAsync(
    //    string apiKey,
    //    string productService,
    //    string clientName,
    //    string contactName,
    //    string contactEmail,
    //    string environment = "Prod",
    //    string subscriptionType = "Monthly",
    //    int rateLimitMonthly = 1000,
    //    Guid? workspaceId = null,
    //    Guid? accountId = null,
    //    string? planId = null,
    //    string? domain = null,
    //    List<string>? allowedPaths = null,
    //    string? outsetaId = null,
    //    string? notes = null,
    //    CancellationToken cancellationToken = default);

    ///// <summary>
    ///// Check if an API key is already registered in the unified system
    ///// </summary>
    ///// <param name="apiKey">API key to check</param>
    ///// <param name="cancellationToken">Cancellation token</param>
    ///// <returns>True if already registered</returns>
    //Task<bool> IsApiKeyRegisteredAsync(string apiKey, CancellationToken cancellationToken = default);

    ///// <summary>
    ///// Get all available product service types
    ///// </summary>
    ///// <returns>Array of available product service types</returns>
    //string[] GetAvailableProductServices();
} 