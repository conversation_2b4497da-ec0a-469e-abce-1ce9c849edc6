using System.ComponentModel.DataAnnotations;
using System.Security.Authentication;
using System.Text.Json;
using Dapper;
using Npgsql;
using Proptexx.Core;
using Proptexx.Core.Json;
using Proptexx.Core.Messaging;
using Proptexx.Core.Services;
using Proptexx.Core.Stores;
using Proptexx.Core.Utils;
using Proptexx.Stripe;

namespace Proptexx.Web.Partner.Handlers;

public sealed class OnboardingHandler
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<OnboardingHandler> _logger;
    private readonly NpgsqlDataSource _dataSource;

    public OnboardingHandler(IConfiguration configuration, ILogger<OnboardingHandler> logger, NpgsqlDataSource dataSource)
    {
        _configuration = configuration;
        _logger = logger;
        _dataSource = dataSource;
    }
    
    internal static Task InvokeAsync(OnboardingHandler handler, HttpContext httpContext) => handler.InvokeAsync(httpContext);
    
    private async Task InvokeAsync(HttpContext httpContext)
    {
        object? output = null;

        try
        {
            using var doc = await JsonDocument.ParseAsync(httpContext.Request.Body);

            OnboardAffiliatePayload payload;

            // this handles legacy payload structure
            if (PayloadService.GetOptionalString(doc, "emailAddress") is not null)
            {
                var onboardingPayload = doc.RootElement.Deserialize<LegacyWorkspaceOnboardingPayload>(JsonDefaults.JsonSerializerOptions)
                                        ?? throw new NullReferenceException("Unable to parse JSON payload");

                payload = new OnboardAffiliatePayload
                {
                    Identifier = onboardingPayload.TenantIdentifier,
                    Name = onboardingPayload.TenantName,
                    ContactName = $"{onboardingPayload.FirstName} {onboardingPayload.FamilyName}",
                    ContactEmail = onboardingPayload.EmailAddress,
                    Urls = onboardingPayload.Domains
                };
            }
            else
            {
                payload = doc.RootElement.Deserialize<OnboardAffiliatePayload>(JsonDefaults.JsonSerializerOptions)
                          ?? throw new NullReferenceException("Unable to parse JSON payload");
            }

            output = await OnboardAffiliateAsync(httpContext, payload);
        }
        catch (ApplicationException e)
        {
            _logger.LogError(e, "Application Exception during partner onboarding");
            httpContext.Response.StatusCode = StatusCodes.Status400BadRequest;
            output = new { errorMessage = e.Message };
        }
        catch (AuthenticationException)
        {
            httpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
            output = new { errorMessage = "Unauthorized" };
        }
        catch (Exception e)
        {
            _logger.LogError(e, "System Exception during partner onboarding");
            httpContext.Response.StatusCode = StatusCodes.Status500InternalServerError;
            output = new { errorMessage = "Attempting to onboard on invalid workspace. Check access key" };
        }
        finally
        {
            await httpContext.Response.WriteAsJsonAsync(output, JsonDefaults.JsonSerializerOptions);
        }
    }

    private async Task<object?> OnboardAffiliateAsync(HttpContext httpContext, OnboardAffiliatePayload payload)
    {
        await using var npgsql = await _dataSource.OpenConnectionAsync();

        var workspaceId = httpContext.User.GetWorkspaceGuid();
        var workspaceName = httpContext.User.GetWorkspaceName();

        _logger.LogInformation("Onboarding to workspace '{workspaceName}' ('{workspaceId}')", workspaceName, workspaceId);

        var workspace = await npgsql.Workspace()
            .GetByData("partnerId", payload.Identifier, null, workspaceId);

        if (workspace is not null)
        {
            var apiKey = await GetApiKeyAsync(npgsql, workspace.Id, "widget");
            return new { apiKey, createdAt = DateTime.UtcNow };
        }

        return await OnNewAffiliate(
            npgsql,
            workspaceId,
            workspaceName,
            payload.Identifier,
            payload.Name,
            payload.ContactName,
            payload.ContactEmail,
            payload.Urls,
            httpContext.RequestAborted);
    }
    
    private async Task<object> OnNewAffiliate(
        NpgsqlConnection npgsql,
        Guid parentWorkspaceId,
        string parentWorkspaceName,
        string identifier,
        string name,
        string contactName,
        string contactEmail,
        IEnumerable<string> urls,
        CancellationToken cancellationToken)
    {
        await using var trx = await npgsql.BeginTransactionAsync(cancellationToken);

        try
        {
            var account = await npgsql.Account()
                .GetAccountIdByEmailAsync(contactEmail);

            string? password = null;
            if (account is null)
            {
                password = PasswordService.GeneratePassword(8);
                account = await OnboardHelper.OnboardAccount(
                    npgsql, contactName, contactEmail, true, password, null);
            }

            var domains = ResolveUrls(urls);
            var workspace = await OnboardHelper.CreateWorkspace(
                npgsql,
                account,
                name,
                parentWorkspaceId,
                domains);

            await npgsql.Workspace().SetData(workspace.Id, "partnerId", identifier);
            var apiKey = await GetApiKeyAsync(npgsql, workspace.Id, "widget");

            string? stripePaymentLink = null;
            if (parentWorkspaceName.Equals("exit", StringComparison.OrdinalIgnoreCase))
            {
                var productId = Guid.Parse("9c5ab746-26f6-49ab-bc53-d1b7733c3595");
                var product = await npgsql.QueryFirstAsync<ProductModel>(ProductModel.Sql, new { _product_id = productId });
                var orderId = await OrderManager.CreateOrderAsync(npgsql, account.Id, workspace.Id, productId);
                var stripeSession = await StripeManager.CreateCheckoutSessionAsync(_configuration, product, orderId, cancellationToken);
                stripePaymentLink = stripeSession.Url;
            }
            else if (parentWorkspaceName.Equals("elm-street", StringComparison.OrdinalIgnoreCase))
            {
                var reqService = new RequestedService
                {
                    WorkspaceId = workspace.Id,
                    ServiceId = "widget-access"
                };

                await OrderManager.EnableServicesAsync(npgsql, workspace.Id, [reqService]);
            }

            var (subject, body) = EmailTemplate
                .OnPartnerAccountRegistration(parentWorkspaceName, password, true, stripePaymentLink);

            var opt = new MessageCreationOptions()
                .SetBody(body, true)
                .AddRecipient(new EmailRecipientOption(contactEmail, account.Id));

            await MessageService.CreateAndSendAsync(npgsql, subject, opt);

            await trx.CommitAsync(cancellationToken);
            return new { apiKey, createdAt = DateTime.UtcNow };
        }
        catch (Exception)
        {
            await trx.RollbackAsync(cancellationToken);
            throw;
        }
    }

    private static async Task<string> GetApiKeyAsync(NpgsqlConnection npgsql, Guid workspaceId, string clientName)
    {
        var clientSecret = await npgsql.Client().GetSecret(workspaceId, clientName);

        if (clientSecret is null)
        {
            var scopes = clientName switch
            {
                "widget" => "widget login otp",
                "store" => "store",
                _ => throw new NullReferenceException($"Unsupported clientName '{clientName}'")
            };

            var widgetClientId = await npgsql.Client().Create(workspaceId, clientName, scopes, false);
            clientSecret = await npgsql.Client().AddSecret(widgetClientId);
        }

        return ApiKeyService.CreateApiKey(clientSecret.Id);
    }

    private static List<UrlInfo> ResolveUrls(IEnumerable<string> urls)
    {
        List<UrlInfo> result;
        try
        {
            result = urls.Select(UrlInfo.Parse).ToList();
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            result = [];
        }

        return result;
    }
}

public class LegacyWorkspaceOnboardingPayload
{
    [Required, StringLength(100, MinimumLength = 2)]
    public required string TenantIdentifier { get; init; }
    
    [Required, StringLength(100, MinimumLength = 2)]
    public required string TenantName { get; init; }

    [Required, StringLength(100, MinimumLength = 2)]
    public required string FirstName { get; init; }
    
    [Required, StringLength(100, MinimumLength = 2)]
    public required string FamilyName { get; init; }
    
    [Required, EmailAddress, StringLength(200)]
    public required string EmailAddress { get; init; }

    public IEnumerable<string> Domains { get; init; } = [];
}
    
public class OnboardAffiliatePayload
{
    [Required, StringLength(100, MinimumLength = 2)]
    public required string Identifier { get; init; }
    
    [Required, StringLength(100, MinimumLength = 2)]
    public required string Name { get; init; }

    [Required, StringLength(100, MinimumLength = 2)]
    public required string ContactName { get; init; }
    
    [Required, EmailAddress, StringLength(200)]
    public required string ContactEmail { get; init; }

    public IEnumerable<string> Urls { get; init; } = [];
}