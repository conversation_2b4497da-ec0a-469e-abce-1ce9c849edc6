using System.Text.Json;
using System.Text.Json.Serialization;

namespace Proptexx.Web.Partner.Store.Tenants;

public class GryptechIListTenant : ITenantService
{
    public Task SignupAsync(StoreSignupPayload payload)
    {
        return Task.CompletedTask;
    }

    public Task<StoreSession?> InitSessionAsync(JsonDocument payload)
    {
        var data = payload.RootElement.Deserialize<GryptechIListReceiveRequest>();
        return Task.FromResult<StoreSession?>(null);
    }
}

public sealed class GryptechIListReceiveRequest
{
    [JsonPropertyName("partner_token")]
    public required string PartnerToken { get; init; }
    
    [JsonPropertyName("user_ref")]
    public required string UserRef { get; init; }
    
    [JsonPropertyName("module")]
    public required string Module { get; init; }
    
    [JsonPropertyName("property_ref")]
    public required string PropertyRef { get; init; }

    [JsonPropertyName("images")]
    public required List<RequestImage> Images { get; init; }

    public sealed class RequestImage
    {
        [JsonPropertyName("imageRef")]
        public required string ImageRef { get; set; }

        [JsonPropertyName("imageURL")]
        public required string ImageUrl { get; set; }
    }
}
