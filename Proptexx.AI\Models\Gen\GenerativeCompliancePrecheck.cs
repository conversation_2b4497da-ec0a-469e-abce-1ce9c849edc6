using System.Net.Http.Json;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Proptexx.AI;
using Proptexx.Core.AI;
using Proptexx.Core.Http;
using Proptexx.Core.Services;
using Proptexx.Core.Storage;

namespace Proptexx.AI.Models.Gen;

public sealed class GenerativeCompliancePrecheck : BaseModel
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly string _cvModelEndpoint;

    public GenerativeCompliancePrecheck(IHttpClientFactory httpClientFactory,
        IConfiguration configuration)
    {
        _httpClientFactory = httpClientFactory;

        _cvModelEndpoint = configuration.GetValue<string>("cv_model_dns")
                         ?? throw new NullReferenceException("cv_model_dns");
    }

    public override async Task<ModelResponse> InferAsync(ModelContext context)
    {
        if (context.Payload is null)
            throw new NullReferenceException(nameof(context.Payload));
        var imageUrl = PayloadService.GetRequiredString(context.Payload, "imageUrl");
        var doc = await RunAsync(imageUrl, context.CancellationToken);
        return new ModelResponse { Document = doc };
    }

    public async Task<JsonDocument> RunAsync(string imageUrl, CancellationToken cancellationToken = default)
    {
        var httpClient = _httpClientFactory.CreateClient();
        var reqBase64Img = await ImageService.DownloadImageAsBase64Async(_httpClientFactory, imageUrl, cancellationToken);

        // 1. PreProcessorForObjectsV4
        var objectsPayload = new { image = reqBase64Img, processor_name = "PreProcessorForObjectsV4" };
        var objectsResp = await httpClient.PostAsJsonAsync($"{_cvModelEndpoint}/info/", objectsPayload, cancellationToken);
        objectsResp.EnsureSuccessStatusCode();
        var objectsJson = await objectsResp.Content.ReadFromJsonAsync<JsonElement>();

        // 2. PreProcessorForRoomSceneV4
        var roomScenePayload = new { image = reqBase64Img, processor_name = "PreProcessorForRoomSceneV4" };
        var roomSceneResp = await httpClient.PostAsJsonAsync($"{_cvModelEndpoint}/info/", roomScenePayload, cancellationToken);
        roomSceneResp.EnsureSuccessStatusCode();
        var roomSceneJson = await roomSceneResp.Content.ReadFromJsonAsync<JsonElement>();

        // Compose output
        var result = new Dictionary<string, object?>
        {
            ["filters"] = objectsJson,
            ["room_scene_detectors"] = roomSceneJson
        };
        return JsonDocument.Parse(JsonSerializer.Serialize(result));
    }
}
