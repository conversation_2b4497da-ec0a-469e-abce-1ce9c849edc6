### Authenticate
POST https://auth.dev.local/_auth
Content-Type: application/json
Authorization: A<PERSON><PERSON><PERSON> NmJjYWIzYTktYWZhMy00ZDdhLWIwNjQtYzAzZjFiOGVkOWNlfDAxMDc1ZDA5LTJiODgtNGYzNS1iZTE2LTg1ZjdjODYxNmI2MQ

{}

> {% client.global.set("accessToken", response.body.$accessToken) %}

### Call CV Model with accessToken from previous request
POST http://localhost:5158/excel/e53edf40-909c-4ead-b1d3-0b97ec697b00
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{}
