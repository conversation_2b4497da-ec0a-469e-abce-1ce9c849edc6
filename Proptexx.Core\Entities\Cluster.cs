using Proptexx.Core.Postgresql.Builder;
using Proptexx.Core.Postgresql.Builder.Attribute;

namespace Proptexx.Core.Entities;

public class Cluster : IDbTable
{
    [IgnoreOnUpdate] public Guid Id { get; } = Guid.NewGuid();
    
    public Guid? ParentId { get; init; }

    public required Guid ClusterTypeId { get; init; }
    
    public required Guid WorkspaceId { get; init; }

    public required string Name { get; set; }

    [IgnoreOnUpdate] public DateTime CreatedAt { get; } = DateTime.UtcNow;

    public DateTime? UpdatedAt { get; } = null!;

    public string GetDbRef() => "core.cluster";
}