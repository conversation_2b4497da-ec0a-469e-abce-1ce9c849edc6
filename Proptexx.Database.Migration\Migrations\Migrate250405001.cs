using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations;

[Migration(*********)]
public class Migrate********* : FluentMigrator.Migration
{
    public override void Up()
    {
        Execute.Sql("""
                    SET session_replication_role = replica;
                    ALTER FUNCTION crm.generate_api_key(uuid) SET SCHEMA core;
                    ALTER FUNCTION crm.parse_api_key(text) SET SCHEMA core;
                    
                    -- Base tables
                    ALTER TABLE crm.account SET SCHEMA core;
                    ALTER TABLE crm.workspace SET SCHEMA core;
                    ALTER TABLE crm.cluster_tpl SET SCHEMA core;
                    ALTER TABLE crm.form SET SCHEMA core;
                    ALTER TABLE crm.message SET SCHEMA core;
                    ALTER TABLE crm.client SET SCHEMA core;
                    
                    -- Dependent tables
                    ALTER TABLE crm.account_data SET SCHEMA core;
                    ALTER TABLE crm.account_email SET SCHEMA core;
                    ALTER TABLE crm.account_id_lookup SET SCHEMA core;
                    ALTER TABLE crm.account_phone SET SCHEMA core;
                    ALTER TABLE crm.account_secret SET SCHEMA core;
                    
                    ALTER TABLE crm.cluster_tpl_type SET SCHEMA core;
                    ALTER TABLE crm.cluster_tpl_hierarchy SET SCHEMA core;
                    
                    ALTER TABLE crm.form_entry SET SCHEMA core;
                    
                    ALTER TABLE crm.message_recipient SET SCHEMA core;
                    
                    ALTER TABLE crm.client_secret SET SCHEMA core;
                    
                    ALTER TABLE crm.cluster SET SCHEMA core;
                    ALTER TABLE crm.cluster_account_binding SET SCHEMA core;
                    
                    ALTER TABLE crm.workspace_data SET SCHEMA core;
                    SET session_replication_role = origin;
                    
                    """);

    }

    public override void Down()
    {
        Execute.Sql("""
                    SET session_replication_role = replica;
                    ALTER FUNCTION core.generate_api_key(uuid) SET SCHEMA crm;
                    ALTER FUNCTION core.parse_api_key(text) SET SCHEMA crm;

                    -- Base tables
                    ALTER TABLE core.account SET SCHEMA crm;
                    ALTER TABLE core.workspace SET SCHEMA crm;
                    ALTER TABLE core.cluster_tpl SET SCHEMA crm;
                    ALTER TABLE core.form SET SCHEMA crm;
                    ALTER TABLE core.message SET SCHEMA crm;
                    ALTER TABLE core.client SET SCHEMA crm;

                    -- Dependent tables
                    ALTER TABLE core.account_data SET SCHEMA crm;
                    ALTER TABLE core.account_email SET SCHEMA crm;
                    ALTER TABLE core.account_id_lookup SET SCHEMA crm;
                    ALTER TABLE core.account_phone SET SCHEMA crm;
                    ALTER TABLE core.account_secret SET SCHEMA crm;

                    ALTER TABLE core.cluster_tpl_type SET SCHEMA crm;
                    ALTER TABLE core.cluster_tpl_hierarchy SET SCHEMA crm;

                    ALTER TABLE core.form_entry SET SCHEMA crm;

                    ALTER TABLE core.message_recipient SET SCHEMA crm;

                    ALTER TABLE core.client_secret SET SCHEMA crm;

                    ALTER TABLE core.cluster SET SCHEMA crm;
                    ALTER TABLE core.cluster_account_binding SET SCHEMA crm;

                    ALTER TABLE core.workspace_data SET SCHEMA crm;
                    SET session_replication_role = origin;
                    """);
    }
}