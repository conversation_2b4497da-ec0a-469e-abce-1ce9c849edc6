namespace Proptexx.Core.Constants;

/// <summary>
/// Constants related to widget operations and AI model commands
/// </summary>
public static class WidgetConstants
{
     
    /// Unified widget command identifiers and operations for AI model processing
    /// These commands consume AI model resources and should be tracked for quota management
    /// </summary>
    public static readonly HashSet<string> ExpensiveRequestTargets = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
    {
        "widget.submitimages",
        "widget.indoorstagingorrefurnishing",              // AI staging and refurnishing
        "widget.stagingorrefurnishingv2command",           // Advanced AI staging v2
        "widget.processrefurnishingproductreplacement",
        "widget.addfurniture",                             // AI furniture addition
        "widget.redecorate",                               // AI redecoration and styling
        "widget.redesign"
    };

    /// <summary>
    /// Widget handler types for request classification
    /// </summary>
    public static class HandlerTypes
    {
        public const string WidgetHandler = "WidgetHandler";
        public const string WidgetSyncHandler = "WidgetSyncHandler";
        public const string CommandHandler = "CommandHandler";
        public const string ModelHandler = "ModelHandler";
        public const string BatchHandler = "BatchHandler";
        public const string StatusHandler = "StatusHandler";
        public const string SequentialHandler = "SequentialHandler";
    }

    /// <summary>
    /// Widget operation sub-types
    /// </summary>
    public static class SubTypes
    {
        public const string Widget = "Widget";
        public const string WidgetRender = "WidgetRender";
        public const string Batch = "Batch";
        public const string Status = "Status";
        public const string Sequential = "Sequential";
        public const string Model = "Model";
    }
    public static class ServiceNames
    {
        public const string Widget = "widget";
        public const string WidgetAccess = "widget-access";
        public const string RealEstateWidget = "real-estate-widget";      
        public const string EcommerceWidget = "ecommerce-widget";    
    }
    /// <summary>
    /// Quota thresholds for different operation types
    /// </summary>
    public static class QuotaThresholds
    {
        public const double HighUsageWidgetThreshold = 90.0;  // 90% for high-usage widget operations
        public const double HighResourceOperationThreshold = 95.0;  // 95% for resource-intensive operations
        public const double StandardOperationThreshold = 100.0;  // 100% for standard operations
    }
} 