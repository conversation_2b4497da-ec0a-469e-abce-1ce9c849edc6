using Proptexx.AI.Services;
using Proptexx.Core.Auth;
using Proptexx.Core.Cqrs.Command;

namespace Proptexx.Web.Command.Widget;

public sealed class SendChat : ICommand
{
    public string? Message { get; init; }

    public async Task ExecuteAsync(CommandContext context)
    {
        using var httpClient = context.GetService<HttpClient>();
        var chatService = context.GetService<IChatService>();

        var userId = context.User.GetCallerId().Replace("-", "");
        var sessionId = context.User.GetSessionId().Replace("-", "");
        var language = "english";
        var message = this.Message ?? "";
        var response = await chatService.SendMessageAsync(httpClient, userId, sessionId, language, message, context.CancellationToken);
        context.AddData("contents", response.Contents);
    }
}
