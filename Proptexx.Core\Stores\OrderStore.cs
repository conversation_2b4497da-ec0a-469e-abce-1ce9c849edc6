using System.Data;
using Dapper;
using Proptexx.Core.Entities;

namespace Proptexx.Core.Stores;

public class OrderStore
{
    private readonly IDbConnection _connection;

    public OrderStore(IDbConnection connection) => _connection = connection;

    public Task<Order> GetOrder(Guid orderId)
    {
        const string sql = @"select o.* from core.order o where o.id = :_order_id";
        return _connection.QueryFirstAsync<Order>(sql, new { _order_id = orderId });
    }
}
