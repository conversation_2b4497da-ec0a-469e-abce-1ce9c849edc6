using FluentMigrator;

namespace Proptexx.Database.Migration.Migrations
{
    [Migration(250625001)]
    public class CreateSubscriptionPlanAndUpdateWidgetClients : FluentMigrator.Migration
    {
        public override void Up()
        {
            // 1. Create SubscriptionPlan table
            Create.Table("subscription_plan").InSchema("core")
                .WithColumn("uid").AsString(50).PrimaryKey()
                .WithColumn("code").AsString(100).NotNullable().Unique()
                .WithColumn("name").AsString(255).NotNullable()
                .WithColumn("description").AsCustom("TEXT").Nullable()
                .WithColumn("plan_family_name").AsString(255).NotNullable()
                .WithColumn("plan_family_uid").AsString(50).NotNullable()
                .WithColumn("is_quantity_editable").AsBoolean().NotNullable().WithDefaultValue(false)
                .WithColumn("monthly_rate").AsDecimal(10, 2).NotNullable().WithDefaultValue(0.00)
                .WithColumn("annual_rate").AsDecimal(10, 2).NotNullable().WithDefaultValue(0.00)
                .WithColumn("quarterly_rate").AsDecimal(10, 2).NotNullable().WithDefaultValue(0.00)
                .WithColumn("one_time_rate").AsDecimal(10, 2).NotNullable().WithDefaultValue(0.00)
                .WithColumn("setup_fee").AsDecimal(10, 2).NotNullable().WithDefaultValue(0.00)
                .WithColumn("is_active").AsBoolean().NotNullable().WithDefaultValue(true)
                .WithColumn("is_per_user").AsBoolean().NotNullable().WithDefaultValue(false)
                .WithColumn("trial_period_days").AsInt32().NotNullable().WithDefaultValue(0)
                .WithColumn("trial_until_date").AsDateTime().Nullable()
                .WithColumn("unit_of_measure").AsString(50).NotNullable().WithDefaultValue("")
                .WithColumn("plan_add_ons").AsCustom("JSONB").NotNullable().WithDefaultValue("[]")
                .WithColumn("number_of_subscriptions").AsInt32().NotNullable().WithDefaultValue(0)
                .WithColumn("quota").AsInt32().NotNullable().WithDefaultValue(0)
                .WithColumn("created_at").AsDateTime().NotNullable().WithDefault(SystemMethods.CurrentUTCDateTime)
                .WithColumn("updated_at").AsDateTime().Nullable();

            // 2. Seed data for SubscriptionPlan based on SubscriptionPlans.cs
            
            // Free - Test Drive Plan
            Insert.IntoTable("subscription_plan").InSchema("core")
                .Row(new
                {
                    uid = "pWrMKa9n",
                    code = "FREE_TEST_DRIVE",
                    name = "Free - Test Drive",
                    description = "<ul>\n<li><span class=\"_fadeIn_m1hgl_8\">Includes 1</span><span class=\"_fadeIn_m1hgl_8\">0 </span><span class=\"_fadeIn_m1hgl_8\">free </span><span class=\"_fadeIn_m1hgl_8\">images on a product page.</span></li>\n<li><span class=\"_fadeIn_m1hgl_8\">Remains on your e-commerce website forever and is activated either automatically or manually.</span></li>\n<li><span class=\"_fadeIn_m1hgl_8\">Perfect </span><span class=\"_fadeIn_m1hgl_8\">for&nbsp;</span><span class=\"_fadeIn_m1hgl_8\">testing&nbsp;</span><span class=\"_fadeIn_m1hgl_8\">the </span><span class=\"_fadeIn_m1hgl_8\">widget </span><span class=\"_fadeIn_m1hgl_8\">before upgrading.</span></li>\n</ul>",
                    plan_family_name = "Default",
                    plan_family_uid = "y9qxG2QA",
                    is_quantity_editable = false,
                    monthly_rate = 0.00,
                    annual_rate = 0.00,
                    quarterly_rate = 0.00,
                    one_time_rate = 0.00,
                    setup_fee = 0.00,
                    is_active = true,
                    is_per_user = false,
                    trial_period_days = 0,
                    trial_until_date = (DateTime?)null,
                    unit_of_measure = "",
                    plan_add_ons = "[]",
                    number_of_subscriptions = 0,
                    quota = 10
                });

            // Starter Plan
            Insert.IntoTable("subscription_plan").InSchema("core")
                .Row(new
                {
                    uid = "A93o8590",
                    code = "STARTER",
                    name = "Starter",
                    description = "<p class=\"p1\">&bull; 100 images/month<br />&bull; $0.50/image overage<br />&bull; email support</p>\n<p class=\"p1\"><strong>Perfect For:</strong></p>\n<ul>\n<li>\n<p class=\"p1\">Niche DTC stores with moderate but steady traffic</p>\n</li>\n<li>\n<p class=\"p1\">Selling 10&ndash;50 items per month</p>\n</li>\n<li>\n<p class=\"p1\">Want basic automation and professional image previews</p>\n</li>\n</ul>\n<p class=\"p3\">&nbsp;</p>\n<p class=\"p1\"><strong>Example Store:</strong></p>\n<p class=\"p4\">A rug store with 5&ndash;10 collections and 5,000&ndash;10,000 monthly visitors.</p>",
                    plan_family_name = "Default",
                    plan_family_uid = "y9qxG2QA",
                    is_quantity_editable = false,
                    monthly_rate = 39.00,
                    annual_rate = 390.00,
                    quarterly_rate = 0.00,
                    one_time_rate = 0.00,
                    setup_fee = 0.00,
                    is_active = true,
                    is_per_user = false,
                    trial_period_days = 0,
                    trial_until_date = (DateTime?)null,
                    unit_of_measure = "",
                    plan_add_ons = "[]",
                    number_of_subscriptions = 0,
                    quota = 100
                });

            // Growth Plan
            Insert.IntoTable("subscription_plan").InSchema("core")
                .Row(new
                {
                    uid = "nmDyA2Qy",
                    code = "GROWTH",
                    name = "Growth",
                    description = "<p class=\"p1\">&bull; 300 images/month<br />&bull; Dashboard access<br />&bull; $0.35/image overage<br /><br /></p>\n<p class=\"p1\"><strong>Perfect For:</strong></p>\n<ul>\n<li>\n<p class=\"p1\">Mid-sized e-commerce brands</p>\n</li>\n<li>\n<p class=\"p1\">Multi-category SKUs and regular marketing</p>\n</li>\n<li>\n<p class=\"p1\">Use AI visuals to boost conversion and AOV</p>\n</li>\n</ul>\n<p class=\"p3\">&nbsp;</p>\n<p class=\"p1\"><strong>Example Store:</strong></p>\n<p class=\"p4\">An office furniture retailer with multiple collections (desks, chairs, accessories) and 20,000&ndash;50,000 monthly visitors.</p>",
                    plan_family_name = "Default",
                    plan_family_uid = "y9qxG2QA",
                    is_quantity_editable = false,
                    monthly_rate = 99.00,
                    annual_rate = 990.00,
                    quarterly_rate = 0.00,
                    one_time_rate = 0.00,
                    setup_fee = 0.00,
                    is_active = true,
                    is_per_user = false,
                    trial_period_days = 0,
                    trial_until_date = (DateTime?)null,
                    unit_of_measure = "",
                    plan_add_ons = "[]",
                    number_of_subscriptions = 0,
                    quota = 300
                });

            // Pro Plan
            Insert.IntoTable("subscription_plan").InSchema("core")
                .Row(new
                {
                    uid = "BWzwLNmE",
                    code = "PRO",
                    name = "Pro",
                    description = "<p class=\"p1\">&bull; 750 images/month<br />&bull; Team collaboration<br />&bull; Slack/email priority support<br />&bull; Early access to features<br />&bull;<span class=\"Apple-converted-space\">&nbsp;</span>$0.25/image overage<br /><br /></p>\n<p class=\"p1\"><strong>Perfect For:</strong></p>\n<ul>\n<li>\n<p class=\"p1\">High-growth e-commerce brands with teams</p>\n</li>\n<li>\n<p class=\"p1\">Running paid ads, influencer campaigns, and needing collaboration</p>\n</li>\n<li>\n<p class=\"p1\">Expecting 100,000+ monthly visits or multi-store setup</p>\n</li>\n</ul>\n<p class=\"p3\">&nbsp;</p>\n<p class=\"p1\"><strong>Example Store:</strong></p>\n<p class=\"p4\">A DTC interior design brand that sells furniture, lighting, and decor, looking to dominate visual selling.</p>",
                    plan_family_name = "Default",
                    plan_family_uid = "y9qxG2QA",
                    is_quantity_editable = false,
                    monthly_rate = 199.00,
                    annual_rate = 1990.00,
                    quarterly_rate = 0.00,
                    one_time_rate = 0.00,
                    setup_fee = 0.00,
                    is_active = true,
                    is_per_user = false,
                    trial_period_days = 0,
                    trial_until_date = (DateTime?)null,
                    unit_of_measure = "",
                    plan_add_ons = "[]",
                    number_of_subscriptions = 0,
                    quota = 750
                });

            // Pay as you Go Plan
            Insert.IntoTable("subscription_plan").InSchema("core")
                .Row(new
                {
                    uid = "amRbo3mJ",
                    code = "PAY_AS_YOU_GO",
                    name = "Pay as you Go",
                    description = "<p class=\"p1\">&bull;<span class=\"Apple-converted-space\">&nbsp;</span>Save card on first purchase<br />&bull;<span class=\"Apple-converted-space\">&nbsp;</span>Auto top-up (e.g., refill 10 credits at $9.90)<br /><span style=\"letter-spacing: -0.01em;\">&bull;&nbsp;</span><span style=\"letter-spacing: -0.01em;\">Instant purchase with stored card<br /><br /></span></p>\n<p class=\"p1\"><strong>Perfect For:</strong></p>\n<ul>\n<li>\n<p class=\"p1\">Seasonal sellers or single-product stores</p>\n</li>\n<li>\n<p class=\"p1\">Campaign-specific use (e.g., only want the feature for a Black Friday or summer push)</p>\n</li>\n<li>\n<p class=\"p1\">Brands who want flexibility without a subscription</p>\n</li>\n</ul>\n<p class=\"p3\">&nbsp;</p>\n<p class=\"p1\"><strong>Example Store:</strong></p>\n<p class=\"p4\">WooCommerce store selling garden lamps or bean bags, wanting to offer virtual placement during peak season.</p>\n<p class=\"p1\"><span style=\"letter-spacing: -0.01em;\">&nbsp;</span></p>",
                    plan_family_name = "Default",
                    plan_family_uid = "y9qxG2QA",
                    is_quantity_editable = true,
                    monthly_rate = 0.99,
                    annual_rate = 0.00,
                    quarterly_rate = 0.00,
                    one_time_rate = 0.00,
                    setup_fee = 0.00,
                    is_active = false,
                    is_per_user = false,
                    trial_period_days = 0,
                    trial_until_date = (DateTime?)null,
                    unit_of_measure = "images",
                    plan_add_ons = "[]",
                    number_of_subscriptions = 0,
                    quota = 10000
                });

            // 3. Add columns to widget_clients table

            // Add workspace_id column with foreign key to core.workspace
            Alter.Table("widget_clients").InSchema("core")
                .AddColumn("workspace_id").AsGuid().Nullable()
                .ForeignKey("fk_widget_clients_workspace_id", "core", "workspace", "id");

            // Add account_id column with foreign key to core.account
            Alter.Table("widget_clients").InSchema("core")
                .AddColumn("account_id").AsGuid().Nullable()
                .ForeignKey("fk_widget_clients_account_id", "core", "account", "id");

            // Add plan_id column as nullable string with foreign key to core.subscription_plan
            Alter.Table("widget_clients").InSchema("core")
                .AddColumn("plan_id").AsString(50).Nullable()
                .ForeignKey("fk_widget_clients_plan_id", "core", "subscription_plan", "uid");

            // Add start_date and expired_date columns
            Alter.Table("widget_clients").InSchema("core")
                .AddColumn("start_date").AsDateTime().Nullable();

            Alter.Table("widget_clients").InSchema("core")
                .AddColumn("expired_date").AsDateTime().Nullable();
        }

        public override void Down()
        {
            // Remove columns from widget_clients in reverse order
            Delete.Column("expired_date").FromTable("widget_clients").InSchema("core");
            Delete.Column("start_date").FromTable("widget_clients").InSchema("core");
            Delete.Column("plan_id").FromTable("widget_clients").InSchema("core");
            Delete.Column("account_id").FromTable("widget_clients").InSchema("core");
            Delete.Column("workspace_id").FromTable("widget_clients").InSchema("core");

            // Drop the subscription_plan table
            Delete.Table("subscription_plan").InSchema("core");
        }
    }
} 