using Proptexx.Core.Utils;
using Proptexx.Core.Services;
using Proptexx.Core;
using Npgsql;
using Proptexx.Core.Stores;

namespace Proptexx.Web.Api.Handlers
{
    public class StoreHandler
    {
        private readonly ILogger<StoreHandler> _logger;
        private readonly NpgsqlDataSource _dataSource;

        public StoreHandler(ILogger<StoreHandler> logger, NpgsqlDataSource dataSource)
        {
            _logger = logger;
            _dataSource = dataSource;
        }

        public class CreateAccountWorkspaceRequest
        {
            [System.Text.Json.Serialization.JsonPropertyName("email")]
            public string Email { get; set; } = string.Empty;

            [System.Text.Json.Serialization.JsonPropertyName("fullName")]
            public string FullName { get; set; } = string.Empty;

            [System.Text.Json.Serialization.JsonPropertyName("companyName")]
            public string CompanyName { get; set; } = string.Empty;

            [System.Text.Json.Serialization.JsonPropertyName("stripeCustomerId")]
            public string StripeCustomerId { get; set; } = string.Empty;
        }

        public async Task GetApiKeyByWorkspaceTitle(HttpContext context)
        {
            try
            {
                var title = context.Request.Query["partner"].ToString();
                if (string.IsNullOrWhiteSpace(title))
                {
                    context.Response.StatusCode = StatusCodes.Status400BadRequest;
                    await context.Response.WriteAsJsonAsync(new { success = false, message = "Missing or empty partner parameter" });
                    return;
                }

                await using var connection = await _dataSource.OpenConnectionAsync();
                var workspaceStore = new WorkspaceStore(connection);
                var workspace = await workspaceStore.GetByTitle(title);
                if (workspace == null)
                {
                    context.Response.StatusCode = StatusCodes.Status404NotFound;
                    await context.Response.WriteAsJsonAsync(new { success = false, message = $"Workspace not found for partner: {title}" });
                    return;
                }

                var apiKey = await GetApiKeyAsync(workspace.Id);
                await context.Response.WriteAsJsonAsync(new { success = true, api_key = apiKey });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get API key for workspace with partner {Partner}", context.Request.Query["partner"]!);
                context.Response.StatusCode = StatusCodes.Status500InternalServerError;
                await context.Response.WriteAsJsonAsync(new { success = false, message = $"Error retrieving API key: {ex.Message}" });
            }
        }

        public async Task CheckAccountExistsAsync(HttpContext context)
        {
            var email = context.Request.Query["email"].ToString();
            if (string.IsNullOrWhiteSpace(email))
            {
                context.Response.StatusCode = StatusCodes.Status400BadRequest;
                await context.Response.WriteAsJsonAsync(new { success = false, message = "Missing or empty email parameter" });
                return;
            }

            try
            {
                await using var connection = await _dataSource.OpenConnectionAsync();
                var accountStore = new AccountStore(connection);
                var accountId = await accountStore.GetAccountIdByEmailAsync(email);
                if (accountId != null && accountId.WorkspaceId.HasValue)
                {
                    var workspace = await connection.Workspace().GetById(accountId.WorkspaceId!.Value);
                    // var prefix = workspace!.Title.Equals("ilist", StringComparison.OrdinalIgnoreCase) ? "ilist" : $"rep-{workspace.Title.ToLowerInvariant()}";
                    var prefix = workspace!.Title.ToLowerInvariant();

                    var userRef = $"{prefix}-{accountId.Id}";
                    var apiKey = await GetApiKeyAsync(accountId.WorkspaceId ?? Guid.Empty);
                    await context.Response.WriteAsJsonAsync(new
                    {
                        success = true,
                        user_ref = userRef,
                        email,
                        api_key = apiKey
                    });
                }
                else
                {
                    context.Response.StatusCode = StatusCodes.Status404NotFound;
                    await context.Response.WriteAsJsonAsync(new { success = false, message = $"No account exists with the provided email: {email}" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to check account existence for {Email}", email);
                context.Response.StatusCode = StatusCodes.Status500InternalServerError;
                await context.Response.WriteAsJsonAsync(new { success = false, message = $"Error checking account: {ex.Message}" });
            }
        }

        public async Task InvokeAsync(HttpContext context)
        {
            using var reader = new StreamReader(context.Request.Body);
            var body = await reader.ReadToEndAsync();
            if (string.IsNullOrWhiteSpace(body))
            {
                context.Response.StatusCode = StatusCodes.Status400BadRequest;
                await context.Response.WriteAsJsonAsync(new { success = false, message = "Missing request body" });
                return;
            }

            var accountWorkspaceRequest = System.Text.Json.JsonSerializer.Deserialize<CreateAccountWorkspaceRequest>(body);
            var (isValid, errorMessage) = ValidateAccountWorkspaceRequest(accountWorkspaceRequest);
            if (!isValid)
            {
                context.Response.StatusCode = StatusCodes.Status400BadRequest;
                await context.Response.WriteAsJsonAsync(new { success = false, message = errorMessage });
                return;
            }

            try
            {
                var (accountId, workspaceId, workspace_title) = await CreateAccountWorkspaceAsync(accountWorkspaceRequest!);
                await AddAccountData(accountId, "StripeCustomerId", accountWorkspaceRequest!.StripeCustomerId);
                var (clientId, apiKey) = await CreateClientApiKeyAsync(workspaceId.ToString());

                var userRef = $"{workspace_title}-{accountId}";
                await context.Response.WriteAsJsonAsync(new
                {
                    success = true,
                    user_ref = userRef,
                    email = accountWorkspaceRequest.Email,
                    api_key = apiKey,
                    message = "user created successfully"
                });
            }
            catch (InvalidOperationException ex)
            {
                context.Response.StatusCode = StatusCodes.Status400BadRequest;
                await context.Response.WriteAsJsonAsync(new { success = false, message = ex.Message });
            }
            catch (ArgumentException ex)
            {
                context.Response.StatusCode = StatusCodes.Status400BadRequest;
                await context.Response.WriteAsJsonAsync(new { success = false, message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in signup flow");
                context.Response.StatusCode = StatusCodes.Status500InternalServerError;
                await context.Response.WriteAsJsonAsync(new { success = false, message = "Internal server error" });
            }
        }

        private static (bool isValid, string errorMessage) ValidateAccountWorkspaceRequest(CreateAccountWorkspaceRequest? request)
        {
            if (request == null)
                return (false, "Invalid or missing account workspace fields");
            if (string.IsNullOrWhiteSpace(request.Email))
                return (false, "Email is required");
            if (string.IsNullOrWhiteSpace(request.FullName))
                return (false, "FullName is required");
            if (string.IsNullOrWhiteSpace(request.CompanyName))
                return (false, "CompanyName is required");
            if (string.IsNullOrWhiteSpace(request.StripeCustomerId))
                return (false, "StripeCustomerId is required");
            return (true, string.Empty);
        }

        private async Task<(Guid accountId, Guid workspaceId, string workspace_name)> CreateAccountWorkspaceAsync(CreateAccountWorkspaceRequest request)
        {
            await using var connection = await _dataSource.OpenConnectionAsync();
            var accountStore = new AccountStore(connection);

            // Step 1: Check if account already exists
            var existingAccount = await accountStore.GetAccountIdByEmailAsync(request.Email);
            if (existingAccount != null)
            {
                throw new InvalidOperationException($"Account already exists for email: {request.Email}");
            }

            // Step 2: Create account using OnboardHelper
            var password = PasswordService.GeneratePassword(8);
            var account = await OnboardHelper.OnboardAccount(connection, request.FullName, request.Email, true, password, null, true);

            // Step 3: Create workspace using OnboardHelper
            var existingWorkspace = await connection.Workspace().GetByTitle(request.CompanyName);
            var workspace = existingWorkspace ?? await OnboardHelper.CreateWorkspace(connection, account, request.CompanyName, null, null);

            // Step 4: Set workspace ID for the account
            await accountStore.SetWorkspaceId(account.Id, workspace.Id);

            _logger.LogInformation("Created account {AccountId} and workspace {WorkspaceId} for {Email}", account.Id, workspace.Id, request.Email);
            return (account.Id, workspace.Id, workspace.Title.ToLowerInvariant());
        }


        private async Task<(Guid clientId, string apiKey)> CreateClientApiKeyAsync(string workspaceId)
        {
            if (string.IsNullOrWhiteSpace(workspaceId))
                throw new ArgumentException("WorkspaceId is required");

            await using var connection = await _dataSource.OpenConnectionAsync();
            var clientStore = new ClientStore(connection);
            const string clientName = "api";
            const string scopes = "api";
            var workspaceGuid = Guid.Parse(workspaceId);
            var clientId = await clientStore.Create(workspaceGuid, clientName, scopes, false);
            var clientSecret = await clientStore.AddSecret(clientId);

            var apiKey = ApiKeyService.CreateApiKey(clientSecret.Id);

            _logger.LogInformation("Created client {ClientId} and API key for workspace {WorkspaceId}", clientId, workspaceId);
            return (clientId, apiKey);
        }

        private async Task<string> GetApiKeyAsync(Guid workspaceId)
        {
            await using var connection = await _dataSource.OpenConnectionAsync();
            var accountStore = new ClientStore(connection);
            var secret = await accountStore.GetSecret(workspaceId, "api")
                ?? throw new InvalidOperationException($"API key not found for workspace: {workspaceId}");

            return ApiKeyService.CreateApiKey(secret.Id);
        }

        private async Task<bool> AddAccountData(Guid accountId, string dataKey, string dataValue)
        {
            await using var connection = await _dataSource.OpenConnectionAsync();
            var accountStore = new AccountStore(connection);
            await accountStore.AddData(accountId, dataKey, dataValue);

            return true;
        }
    }
}
