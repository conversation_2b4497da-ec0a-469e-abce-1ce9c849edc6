using Proptexx.Core.Entities;
using Proptexx.Core.DTOs;

namespace Proptexx.Core.Services;

/// <summary>
/// Interface for Subscription repository operations
/// Follows Interface Segregation Principle
/// </summary>
public interface ISubscriptionService
{
    /// <summary>
    /// Create a new subscription
    /// </summary>
    Task<ClientSubscription> CreateAsync(ClientSubscription subscription, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get subscription by API key
    /// </summary>
    Task<ClientSubscription?> GetByApiKeyAsync(string apiKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get subscription by ID
    /// </summary>
    Task<ClientSubscription?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all subscriptions with filtering and pagination
    /// </summary>
    Task<(IEnumerable<ClientSubscription> Subscriptions, int TotalCount)> GetAllAsync(
        string? status = null,
        string? subscriptionType = null,
        Guid? clientSecretId = null,
        int page = 1,
        int pageSize = 50,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update an existing subscription
    /// </summary>
    Task<bool> UpdateAsync(ClientSubscription subscription, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete a subscription
    /// </summary>
    Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if API key exists
    /// </summary>
    Task<bool> ApiKeyExistsAsync(string apiKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Manually create a PayAsYouGo subscription for a valid API key
    /// This method can be used for administrative purposes or migration
    /// </summary>
    Task<ClientSubscription?> CreatePayAsYouGoSubscriptionAsync(
        string apiKey, 
        string? clientName = null,
        string? contactName = null,
        string? contactEmail = null,
        List<string>? productServices = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if API key exists in subscription table
    /// </summary>
    Task<bool> ApiKeyExistsInSubscriptionAsync(string apiKey, CancellationToken cancellationToken = default);

 
    // New methods for database functions and views

    /// <summary>
    /// Increment usage for both client_secret and client_subscription by client_secret_id
    /// </summary>
    Task<bool> IncrementUsageAsync(Guid clientSecretId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Increment usage for both client_secret and client_subscription by API key
    /// </summary>
    Task<bool> IncrementUsageByApiKeyAsync(string apiKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Increment usage for client_secret only by client_secret_id
    /// </summary>
    Task<bool> IncrementClientSecretUsageAsync(Guid clientSecretId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Increment usage for client_secret only by API key
    /// </summary>
    Task<bool> IncrementClientSecretUsageByApiKeyAsync(string apiKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Increment usage for subscription only by client_secret_id
    /// </summary>
    Task<bool> IncrementSubscriptionUsageAsync(Guid clientSecretId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Increment usage for subscription only by API key
    /// </summary>
    Task<bool> IncrementSubscriptionUsageByApiKeyAsync(string apiKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get total usage by client_id
    /// </summary>
    Task<ClientUsageSummary?> GetClientTotalUsageAsync(Guid clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get total usage by client_id as JSON
    /// </summary>
    Task<string> GetClientTotalUsageJsonAsync(Guid clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all clients usage summary
    /// </summary>
    Task<IEnumerable<ClientUsageSummary>> GetAllClientsUsageAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get top clients by usage
    /// </summary>
    Task<IEnumerable<ClientUsageSummary>> GetTopClientsByUsageAsync(int limit = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate API key from client_secret_id
    /// </summary>
    Task<string?> GetApiKeyFromClientSecretIdAsync(Guid clientSecretId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get client_secret_id from API key using ApiKeyService
    /// </summary>
    Task<Guid?> GetClientSecretIdFromApiKeyAsync(string apiKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get client usage summary from view
    /// </summary>
    Task<IEnumerable<ClientUsageSummary>> GetClientUsageSummaryFromViewAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get client usage summary by client_id from view
    /// </summary>
    Task<ClientUsageSummary?> GetClientUsageSummaryByClientIdAsync(Guid clientId, CancellationToken cancellationToken = default);

    // Usage Check Methods

    /// <summary>
    /// Check usage for Period subscription type
    /// </summary>
    Task<UsageCheckResult> CheckPeriodUsageAsync(string apiKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check usage for OneTime subscription type
    /// </summary>
    Task<UsageCheckResult> CheckOneTimeUsageAsync(string apiKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check usage for PayAsYouGo subscription type
    /// </summary>
    Task<UsageCheckResult> CheckPayAsYouGoUsageAsync(string apiKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check usage for any subscription type (auto-detect)
    /// </summary>
    Task<UsageCheckResult> CheckUsageAsync(string apiKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check usage by client_secret_id for any subscription type
    /// </summary>
    Task<UsageCheckResult> CheckUsageByClientSecretIdAsync(Guid clientSecretId, CancellationToken cancellationToken = default);

    Task<ClientSubscription?> EnsureClientSubscriptionExists(
        string apiKey,
        string? clientName = null,
        string? contactName = null,
        string? contactEmail = null,
        List<string>? productServices = null,
        CancellationToken cancellationToken = default);
    
}
 