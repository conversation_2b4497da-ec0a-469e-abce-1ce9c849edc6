using System.Diagnostics;

namespace Proptexx.Core.Cqrs.Query;

public interface IQueryHandler
{
    string Identifier { get; }

    Task<bool> HasAccessAsync(QueryContext ctx);

    Task<QueryHandlerResult> ExecuteAsync(QueryContext ctx);
}

public abstract class QueryHandlerResult
{
    private readonly Stopwatch _timer = Stopwatch.StartNew();

    public string? ErrorMessage { get; private set; }

    public long ExecutionTime => _timer.ElapsedMilliseconds;

    public bool IsSuccess { get; private set; }

    public object? Result { get; private set; }

    private void Completed()
    {
        _timer.Stop();
    }

    public void Success(object result)
    {
        Result = result;
        IsSuccess = true;
        Completed();
    }

    public void Error(string errorMessage)
    {
        ErrorMessage = errorMessage;
        Completed();
    }
}