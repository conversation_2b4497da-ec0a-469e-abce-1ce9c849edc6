using Proptexx.Core;
using Proptexx.Core.Cqrs.Command;

namespace Proptexx.Web.Command.Account;

public class DeleteAccount : ICommand
{
    public Guid? AccountId { get; init; }
    
    public Task ExecuteAsync(CommandContext context)
    {
        var accountId = context.User.GetCallerGuid();
        var workspaceId = context.User.GetWorkspaceGuid();
        
        throw new NotImplementedException();
    }
}