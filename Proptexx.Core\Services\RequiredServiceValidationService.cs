using Proptexx.Core.Attributes;
using Proptexx.Core.Auth;
using Proptexx.Core.Redis;
using System.Security.Claims;

namespace Proptexx.Core.Services;

/// <summary>
/// Service for validating required services based on RequiredServiceAttribute
/// </summary>
public class RequiredServiceValidationService
{
    private readonly WidgetStore _widgetStore;

    public RequiredServiceValidationService(WidgetStore widgetStore)
    {
        _widgetStore = widgetStore;
    }

    /// <summary>
    /// Validates if the current user has access to the required services for a command
    /// </summary>
    /// <param name="commandType">The command type to validate</param>
    /// <param name="user">The current user claims principal</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if access is granted, false otherwise</returns>
    public async Task<bool> ValidateAccessAsync(Type commandType, ClaimsPrincipal user, CancellationToken cancellationToken = default)
    {
        // Check if the command has the RequiredServiceAttribute
        var attribute = commandType.GetCustomAttributes(typeof(RequiredServiceAttribute), false)
            .FirstOrDefault() as RequiredServiceAttribute;

        if (attribute == null)
        {
            // No attribute means no specific service requirements
            return true;
        }

        // Get the client identifier from the user claims
        var clientIdentifier = user.GetClientIdentifier();
        if (string.IsNullOrEmpty(clientIdentifier))
        {
            return false;
        }

        // Parse the client identifier and create API key
        if (!Guid.TryParse(clientIdentifier, out var parsedId))
        {
            return false;
        }

        var apiKey = ApiKeyService.CreateApiKey(parsedId);
        
        // Get widget model from store
        var widgetModel = await _widgetStore.GetEntryAsync(apiKey);
        if (widgetModel?.Services == null)
        {
            return false;
        }

        // Validate services based on attribute configuration
        return ValidateServices(widgetModel.Services, attribute);
    }

    /// <summary>
    /// Validates if the current user has access to the required services for a command and throws an exception if not
    /// </summary>
    /// <param name="commandType">The command type to validate</param>
    /// <param name="user">The current user claims principal</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <exception cref="UnauthorizedAccessException">Thrown when access is denied</exception>
    public async Task ValidateAccessOrThrowAsync(Type commandType, ClaimsPrincipal user, CancellationToken cancellationToken = default)
    {
        var hasAccess = await ValidateAccessAsync(commandType, user, cancellationToken);
        if (!hasAccess)
        {
            var attribute = commandType.GetCustomAttributes(typeof(RequiredServiceAttribute), false)
                .FirstOrDefault() as RequiredServiceAttribute;

            var errorMessage = attribute?.ErrorMessage ?? 
                $"Access denied. Required services: {string.Join(", ", attribute?.RequiredServices ?? [])}";

            throw new UnauthorizedAccessException(errorMessage);
        }
    }

    /// <summary>
    /// Validates services against the required service attribute
    /// </summary>
    /// <param name="availableServices">Available services string</param>
    /// <param name="attribute">The required service attribute</param>
    /// <returns>True if validation passes</returns>
    private static bool ValidateServices(string availableServices, RequiredServiceAttribute attribute)
    {
        if (string.IsNullOrEmpty(availableServices))
        {
            return false;
        }

        var services = availableServices.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        
        if (attribute.RequireAllServices)
        {
            // ALL required services must be present
            return attribute.RequiredServices.All(required => 
                services.Contains(required, StringComparer.OrdinalIgnoreCase));
        }
        else
        {
            // ANY of the required services must be present
            return attribute.RequiredServices.Any(required => 
                services.Contains(required, StringComparer.OrdinalIgnoreCase));
        }
    }
} 