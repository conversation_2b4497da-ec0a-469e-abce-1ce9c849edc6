﻿using System.Text.Json;
using Dapper;
using Npgsql;
using Proptexx.Core.Entities;
using StackExchange.Redis;
using Microsoft.Extensions.Logging;

namespace Proptexx.Worker.Telemetry
{
    public class TelemetryProcessor : BackgroundService
    {
        private readonly ILogger<TelemetryProcessor> _logger;
        private readonly NpgsqlDataSource _dataSource;
        private readonly IDatabase _redisDb;

        public TelemetryProcessor(ILogger<TelemetryProcessor> logger, NpgsqlDataSource dataSource, IConnectionMultiplexer redis)
        {
            _logger = logger;
            _dataSource = dataSource;
            _redisDb = redis.GetDatabase();
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // Retrieve a batch of log entries from Redis
                    var logs = await _redisDb.ListLeftPopAsync("telemetry_queue", 200);
                    if (logs is null || logs.Length <= 0)
                    {
                        await Task.Delay(5000, stoppingToken);
                        continue;
                    }

                    await using var connection = await _dataSource.OpenConnectionAsync(stoppingToken);

                    foreach (var log in logs)
                    {
                        if (log.IsNullOrEmpty)
                            continue;

                        var json = log.ToString();
                        if (string.IsNullOrWhiteSpace(json))
                            continue;

                        var logEntry = JsonSerializer.Deserialize<TelemetryEntry>(json);
                        if (logEntry == null)
                            continue;

                        // Enrich the telemetry entry: set flags, compute endpoints, extract IP, etc.
                        logEntry.SetFlags();

                        // Asynchronously enrich data from secret (if available)
                        await EnrichDataFromSecretIdAsync(connection, logEntry);

                        // Save the enriched telemetry entry to PostgreSQL.
                        await SaveLogToPostgresAsync(connection, logEntry);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing telemetry logs");
                    await Task.Delay(5000, stoppingToken); // Delay on error.
                }
                finally
                {
                    await Task.Delay(5000, stoppingToken); // Avoid excessive CPU usage.
                }
            }
        }

        private async Task EnrichDataFromSecretIdAsync(NpgsqlConnection connection, TelemetryEntry logEntry)
        {
            if (logEntry == null || !logEntry.SecretId.HasValue)
                return;

            const string sql = @"
                SELECT 
                    c.id AS Client_Id, 
                    c.workspace_id AS Workspace_Id
                FROM core.client_secret cs
                INNER JOIN core.client c ON c.id = cs.client_id
                WHERE cs.id = @SecretId";

            try
            {
                var result = await connection.QueryFirstOrDefaultAsync<ClientInfoDto>(
                    sql, new { SecretId = logEntry.SecretId.Value });
                if (result != null)
                {
                    logEntry.ClientId = result.Client_Id;
                    logEntry.WorkspaceId = result.Workspace_Id;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error enriching data for SecretId {SecretId}", logEntry.SecretId);
            }
        }

        private async Task SaveLogToPostgresAsync(NpgsqlConnection connection, TelemetryEntry logEntry)
        {
            const string sql = @"
                INSERT INTO telemetry.api_logs 
                (
                    timestamp, 
                    endpoint, 
                    headers, 
                    request_body, 
                    workspace_id, 
                    session_id, 
                    client_version, 
                    account_id, 
                    http_method, 
                    client_id, 
                    duration_ms,
                    is_batch, 
                    is_api_request, 
                    is_ai_request, 
                    response_code, 
                    identifier,
                    clean_endpoint, 
                    processed_endpoint, 
                    is_widget, 
                    ip
                ) 
                VALUES 
                (
                    @Timestamp, 
                    @Endpoint, 
                    @Headers::jsonb, 
                    @Body::jsonb, 
                    @WorkspaceId, 
                    @SessionId, 
                    @ClientVersion, 
                    @AccountId, 
                    @HttpMethod, 
                    @ClientId, 
                    @DurationMs,
                    @IsBatch, 
                    @IsApiRequest, 
                    @IsAIRequest, 
                    @ResponseCode, 
                    @Identifier,
                    @CleanEndpoint, 
                    @ProcessedEndpoint, 
                    @IsWidget, 
                    @IP
                );
            ";

            var param = new
            {
                logEntry.Timestamp,
                logEntry.Endpoint,
                Headers = logEntry.Headers,
                Body = logEntry.Body,
                logEntry.WorkspaceId,
                logEntry.SessionId,
                logEntry.ClientVersion,
                logEntry.AccountId,
                logEntry.HttpMethod,
                logEntry.ClientId,
                logEntry.DurationMs,
                logEntry.IsBatch,
                logEntry.IsApiRequest,
                logEntry.IsAIRequest,
                logEntry.ResponseCode,
                logEntry.Identifier,
                logEntry.CleanEndpoint,
                logEntry.ProcessedEndpoint,
                logEntry.IsWidget,
                logEntry.IP
            };

            try
            {
                await connection.ExecuteAsync(sql, param);
            }
            catch (PostgresException pgEx)
            {
                _logger.LogError("[Postgres ERROR] {SqlState}: {MessageText}", pgEx.SqlState, pgEx.MessageText);
                _logger.LogError("Query values: {ParamJson}", JsonSerializer.Serialize(param));
            }
            catch (Exception ex)
            {
                _logger.LogError("[General ERROR] {ExceptionType}: {Message}", ex.GetType().Name, ex.Message);
            }
        }
    }
    public class ClientInfoDto
    {
        public Guid Client_Id { get; set; }
        public Guid Workspace_Id { get; set; }
    }
}