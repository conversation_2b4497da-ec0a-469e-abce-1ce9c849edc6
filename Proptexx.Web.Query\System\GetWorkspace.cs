using System.ComponentModel.DataAnnotations;
using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Attributes;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.System;

public sealed class GetWorkspace : IQuery
{
    [Required, GuidNotEmpty] public required Guid Id { get; init; }

    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        context.User.EnsureRootAccess();

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var result = await npgsql.QueryFirstOrDefaultAsync<WorkspaceModel>(WorkspaceModel.Sql, new { _workspace_id = this.Id });
        return result;
    }

    private sealed class WorkspaceModel
    {
        public required Guid Id { get; init; }
        
        public required string Name { get; init; }
        
        public string? Description { get; init; }

        public DateTime CreatedAt { get; init; }
        
        public bool IsTestMode { get; init; }
        
        public Guid? FounderId { get; init; }
        
        public string? FounderName { get; init; }
        
        public Guid? ParentId { get; init; }
        
        public string? ParentName { get; init; }

        public static string Sql => @"
            select ws.id,
                   ws.name,
                   ws.created_at,
                   ws.is_test_mode,
                   ws.founder_id,
                   concat_ws(' ', a.first_name, a.family_name) as founder_name,
                   ws.parent_id,
                   p.name as parent_name
            from core.workspace ws
            left outer join core.account a on a.id = ws.founder_id
            left outer join core.workspace p on p.id = ws.parent_id
            where ws.id = :_workspace_id;
        ";
    }
}