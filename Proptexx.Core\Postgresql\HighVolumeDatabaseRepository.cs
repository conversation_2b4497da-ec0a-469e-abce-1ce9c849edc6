using Npgsql;

namespace Proptexx.Core.Postgresql;

public interface IHighVolumeDatabaseRepository
{
    Task BulkInsertDataAsync(IEnumerable<object> data, CancellationToken cancellationToken);
}

public sealed class HighVolumeDatabaseRepository : IHighVolumeDatabaseRepository
{
    private readonly NpgsqlDataSource _dataSource;

    public HighVolumeDatabaseRepository(NpgsqlDataSource dataSource)
    {
        _dataSource = dataSource;
    }

    public async Task BulkInsertDataAsync(IEnumerable<object> data, CancellationToken cancellationToken)
    {
        await using var conn = await _dataSource
            .OpenConnectionAsync(cancellationToken);

        // await using var writer = await conn.BeginTextImportAsync("COPY MyTable (Id, Name, Age) FROM STDIN (FORMAT CSV)", cancellationToken: cancellationToken);
        // foreach (var item in data)
        // {
        //     // var csvRow = $"{item.Id},{item.Name},{item.Age}";
        //     // await writer.WriteLineAsync(csvRow);
        // }
    }
}