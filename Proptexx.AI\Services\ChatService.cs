using System.Net.Http.Json;
using System.Text.Json.Serialization;
using Proptexx.Core.Json;

namespace Proptexx.AI.Services;

public sealed class ChatService : IChatService
{
    private const string ChatEndpointUrl = "http://40.66.40.123/proptexx/chat";
    private const string HistoryEndpointUrl = "http://40.66.40.123/proptexx/history";

    public async Task<ChatResponse> SendMessageAsync(
        HttpClient httpClient, string userId, string sessionId, string language, string message, CancellationToken cancellationToken)
    {
        var contents = new[]
        {
            new
            {
                content = message,
                type = "text"
            }
        };

        using var content = JsonContent.Create(new
        {
            user_id = userId,
            session_id = sessionId,
            language,
            contents
        });

        using var response = await httpClient.PostAsync(ChatEndpointUrl, content, cancellationToken);
        response.EnsureSuccessStatusCode();

        var body = await response.Content.ReadFromJsonAsync<ChatResponse>(cancellationToken);

        if (body is null) throw new NullReferenceException(nameof(body));
        if (body.UserId != userId) throw new FormatException("User ID mismatch between sender and receiver");
        if (body.SessionId != sessionId) throw new FormatException("Session ID mismatch between sender and receiver");

        return body;
    }

    public async Task<ChatHistory?> GetChatHistoryAsync(HttpClient httpClient, string userId, string sessionId, CancellationToken cancellationToken)
    {
        using var content = JsonContent.Create(new
        {
            user_id = userId,
            session_id = sessionId,
        });

        using var response = await httpClient.PostAsync(HistoryEndpointUrl, content, cancellationToken);
        response.EnsureSuccessStatusCode();

        var body = await response.Content.ReadFromJsonAsync<ChatHistory>(JsonDefaults.JsonSerializerOptions, cancellationToken);
        return body;
    }
}

public interface IChatService
{
    Task<ChatResponse> SendMessageAsync(
        HttpClient httpClient, string userId, string sessionId, string language, string message, CancellationToken cancellationToken);

    Task<ChatHistory?> GetChatHistoryAsync(HttpClient httpClient, string userId, string sessionId,
        CancellationToken cancellationToken);
}

public sealed class ChatResponse
{
    [JsonPropertyName("user_id")]
    public required string UserId { get; init; }
    
    [JsonPropertyName("session_id")]
    public required string SessionId { get; init; }
    
    [JsonPropertyName("language")]
    public required string Language { get; init; }

    [JsonPropertyName("contents")]
    public IEnumerable<ChatContent> Contents { get; init; } = [];
}

public sealed class ChatHistory
{
    [JsonPropertyName("user_id")]
    public required string UserId { get; init; }

    [JsonPropertyName("session_id")]
    public required string SessionId { get; init; }

    [JsonPropertyName("conversation")]
    public required List<ChatConversation> Conversation { get; init; }
}

public sealed class ChatConversation
{
    [JsonPropertyName("input")]
    public List<ChatContent> Input { get; init; } = [];

    [JsonPropertyName("output")]
    public List<ChatContent> Output { get; init; } = [];
}

public sealed class ChatContent
{
    [JsonPropertyName("content")]
    public object? Content { get; init; }
    
    [JsonPropertyName("type")]
    public required string Type { get; init; }
}
