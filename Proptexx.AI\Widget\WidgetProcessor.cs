using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Proptexx.AI.Models.CV;
using Proptexx.Core.AI;
using Proptexx.Core.Extensions;
using Proptexx.Core.Http;
using Proptexx.Core.Resilience;
using Proptexx.Core.Services;
using StackExchange.Redis;

namespace Proptexx.AI.Widget;

public class WidgetService
{
    private readonly string[] _roomTypes = ["bedroom", "living room", "bathroom", "kitchen"];
    private readonly string[] _mimeTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
    private readonly IServiceProvider _services;
    private readonly IDatabase _redis;
    private readonly IImageAssessmentClient _imageAssessmentService;
    private readonly ILogger<WidgetService> _logger;
    private const int RetryCount = 3;
    private const int DelaySeconds = 1;
    public WidgetService(IServiceProvider services, IConnectionMultiplexer connectionMultiplexer, IImageAssessmentClient imageAssessmentService, ILogger<WidgetService> logger)
    {
        _services = services;
        _redis = connectionMultiplexer.GetDatabase();
        _imageAssessmentService = imageAssessmentService;
        _logger = logger;
    }

    private async Task<WidgetListingAssessment> ProcessImage(string workspaceName, string imageUrl, CancellationToken cancellationToken)
    {
        var result = new WidgetListingAssessment
        {
            Url = imageUrl,
            ProcessedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
        };

        try
        {
            if (!imageUrl.TrimStart().StartsWith("http"))
            {
                throw new ApplicationException("Invalid iamge URL");
            }

            var assessment = await _imageAssessmentService.InspectImageAsync(imageUrl, cancellationToken);
            result.Width = assessment.Width;
            result.Height = assessment.Height;
            result.MimeType = assessment.MimeType;

            if (assessment.Width <= 300 || assessment.Height <= 300)
            {
                throw new ApplicationException("Invalid image size");
            }

            if (!_mimeTypes.Contains(result.MimeType))
            {
                throw new ApplicationException("Invalid mime type");
            }

            var model = _services.GetModel<PreProcessorForRoomScene>();
            var response = await  PollyRetryExtensions.GetDefaultRetryPolicy(_logger, $"ProcessImage", RetryCount, DelaySeconds).ExecuteAsync(() =>
            {
                return model.InferAsync(
                workspaceName,
                assessment,
                cancellationToken,
                false);
            });
            var cvResult = response.GetRequiredResult<PreProcessorForRoomScene.Result>();
            result.Type = cvResult.SceneTypes.First();
            result.RoomType = cvResult.RoomTypes.Keys.First();

            if (!result.Type.Equals("Indoor", StringComparison.OrdinalIgnoreCase))
            {
                throw new ApplicationException($"Invalid scene type: '{result.Type}'");
            }

            if (!_roomTypes.Contains(result.RoomType, StringComparer.OrdinalIgnoreCase))
            {
                throw new ApplicationException($"Invalid room type '{result.RoomType}'");
            }

            result.Status = 2;
        }
        catch (ApplicationException e)
        {
            result.Status = -1;
            result.Error = e.Message;
        }
        catch (Exception e)
        {
            result.Status = -1;
            result.Exception = e.Message;
        }

        return result;
    }

    public async Task<WidgetListingEntry> ProcessImages(string workspaceName, string identifier, IEnumerable<string> imageUrls, CancellationToken cancellationToken)
    {
        var isUpdated = false;
        var entry = await this.GetEntry(workspaceName, identifier);
        
        if (entry is null)
        {
            entry = new WidgetListingEntry();
            await SetEntry(workspaceName, identifier, entry);
        }

        var entryUrls = entry.GetImageUrls();
        foreach (var imageUrl in imageUrls)
        {
            if (entryUrls.Contains(imageUrl)) continue;
            isUpdated = true;

            var assessment = await ProcessImage(workspaceName, imageUrl, cancellationToken);
            entry.Images.Add(assessment);
        }

        if (isUpdated)
        {
            await this.SetEntry(workspaceName, identifier, entry);
        }

        return entry;
    }

    public Task<WidgetListingEntry?> GetEntry(string workspaceName, string identifier)
    {
        return _redis.HashGetObjectAsync<WidgetListingEntry>($"listings:{workspaceName}", identifier);
    }

    public async Task SetEntry(string workspaceName, string identifier, WidgetListingEntry entry)
    {
        await _redis.HashSetObjectAsync($"listings:{workspaceName}", identifier, entry);
    }
}