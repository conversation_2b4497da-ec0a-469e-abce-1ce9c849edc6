using System.Net.Http.Json;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Proptexx.Core.AI;
using Proptexx.Core.Http;
using Proptexx.Core.Services;
using Proptexx.Core.Storage;

namespace Proptexx.AI.Models.Gen;

public sealed class Decluttering : ImageEnhancementModel
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IStorageService _storageService;
    private readonly string _cvModelEndpoint;
    private readonly string _aiModelEndpoint;

    public Decluttering(IImageEnhancementClient imageEnhancementClient,
        IStorageService storageService,
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration)
        : base(imageEnhancementClient, storageService)
    {
        _httpClientFactory = httpClientFactory;
        _storageService = storageService;

        _cvModelEndpoint = configuration.GetValue<string>("cv_model_dns")
                         ?? throw new NullReferenceException("cv_model_dns");
        _aiModelEndpoint = configuration.GetValue<string>("ai_model_dns")
                          ?? throw new NullReferenceException("ai_model_dns");
    }

    protected override string GetModelEndpoint(string workspaceId)
        => "http://preprocessing.prod.ai.proptexx.com/image-enhancement-declutering/predict";

    public override async Task<ModelResponse> InferAsync(ModelContext context)
    {
        if (context.Payload is null)
        {
            throw new NullReferenceException(nameof(context.Payload));
        }

        var imageUrl = PayloadService.GetRequiredString(context.Payload, "imageUrl");
        var httpClient = _httpClientFactory.CreateClient();
        var reqBase64Img = await ImageService.DownloadImageAsBase64Async(
                _httpClientFactory,
                imageUrl,
                context.CancellationToken
            );

        // 1. RoomSceneProcessorV3
        var scenePayload = new
        {
            image = reqBase64Img,
            processor_name = "RoomSceneProcessorV3"
        };
        var sceneResp = await httpClient.PostAsJsonAsync($"{_cvModelEndpoint}/info/", scenePayload, context.CancellationToken);
        sceneResp.EnsureSuccessStatusCode();
        var sceneJson = await sceneResp.Content.ReadFromJsonAsync<JsonElement>();
        var scene = sceneJson.GetProperty("sceneTypes").GetProperty("sceneType").GetString()?.ToLowerInvariant();

        if (scene == "floor plan" || scene == "irrelevant" || scene == "outdoor" ||
            !sceneJson.TryGetProperty("isEmpty", out var isEmpty) ||
            isEmpty.GetBoolean())
            throw new Exception("Scene is not suitable for decluttering.");

        // 2. Call model endpoint
        var modelPayload = new
        {
            image = reqBase64Img
        };
        var modelResp = await httpClient.PostAsJsonAsync($"{_aiModelEndpoint}/generative-image-decluttering/predict", modelPayload, context.CancellationToken);
        var modelJson = await modelResp.Content.ReadFromJsonAsync<JsonElement>();

        // upload image to storage
        const string contentType = "image/jpeg";
        var filename = $"{context.ItemId}_{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}.jpg";
        var imgUrl = string.Empty;

        if (modelJson.TryGetProperty("data", out var dataElem) &&
            dataElem.TryGetProperty("image", out var resBase64Img))
        {
            imgUrl = await _storageService.UploadImageAsync(context.WorkspaceId, filename, resBase64Img.ToString(), contentType);
        }

        return new GenerativeModelResponse
        {
            Base64Image = reqBase64Img,
            MimeType = "image/jpeg",
            RequestParams = new Dictionary<string, object?>
            {
                ["payload"] = modelPayload
            },
            OutputImageUrl = imgUrl,
            Document = JsonDocument.Parse($$"""{"imageUrl": "{{imgUrl}}"}""")
        };
    }
}