using System.Text.Json;
using Proptexx.Core.Entities;
using Proptexx.Core.Json;
using Proptexx.Core.Stores;

namespace Proptexx.Core.Redis;

public sealed class BatchMessage
{
    public required Guid WorkspaceId { get; init; }

    public required Guid BatchId { get; init; }

    public required IList<BatchTask> Tasks { get; init; }
    
    public required string? CallbackUrl { get; init; }
}


public sealed class CallbackResponse
{
    public Guid? Id { get; private set; }

    public int TotalCount { get; private set; }
    
    public int PendingCount { get; private set; }
    
    public int ErrorCount { get; private set; }
    
    public object? Results { get; private set; }
    
    public static CallbackResponse Success(BatchMessage batchMessage, List<BatchResult> batchResults)
    {
        var response = new CallbackResponse { Id = batchMessage.BatchId };
        
        var items = new List<object>();
        foreach (var task in batchMessage.Tasks)
        {
            var batchResult = batchResults.First(x => x.TaskId == task.Id);

            response.TotalCount++;

            switch (batchResult.Status)
            {
                case BatchResultStatus.Pending:
                case BatchResultStatus.Processing:
                    response.PendingCount++;
                    break;
                case BatchResultStatus.Error:
                    response.ErrorCount++;
                    break;
            }

            items.Add(new
            {
                status = BatchResultModel.GetStatusText(batchResult.Status),
                model = task.Model,
                config = task.Config.RootElement,
                result = batchResult.Output?.RootElement,
                error = batchResult.ErrorMessage
            });
        }

        response.Results = items;
        return response;
    }
    
    public static CallbackResponse Success(Guid batchId, IEnumerable<BatchResultModel> results)
    {
        var response = new CallbackResponse
        {
            Id = batchId
        };

        var items = new List<object>();
        foreach (var x in results)
        {
            response.TotalCount++;

            switch (x.Status)
            {
                case 0:
                case 1:
                    response.PendingCount++;
                    break;
                case -1:
                    response.ErrorCount++;
                    break;
            }

            items.Add(new
            {
                status = BatchResultModel.GetStatusText(x.Status),
                model = x.Model,
                config = x.Config.RootElement,
                result = x.Result?.RootElement,
                error = x.ErrorMessage
            });
        }

        response.Results = items;
        return response;
    }

    public override string ToString() => JsonSerializer.Serialize(this, JsonDefaults.CompactOptions);
}

public sealed class RedisBatchItem
{
    public required Guid WorkspaceId { get; init; }

    public required Guid BatchId { get; init; }

    public required Guid TaskId { get; init; }

    public required string ModelName { get; init; }
    
    public required JsonDocument ModelConfig { get; init; }

    public required int TotalTasks { get; init; }
}