using System.ComponentModel.DataAnnotations;
using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.System;

public sealed class GetService : IQuery
{
    [Required] public required string Id { get; init; }

    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        context.User.EnsureRootAccess();

        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var query = string.Concat(ServiceModel.Sql);
        await using var grid = await npgsql.QueryMultipleAsync(query, new { _service_id = this.Id });

        var service = await grid.ReadFirstOrDefaultAsync<ServiceModel>();
        var usages = new List<object>();

        return new {
            service, usages
        };
    }

    private sealed class ServiceModel
    {
        public required string Id { get; init; }
        
        public required string Title { get; init; }
        
        public string? Description { get; init; }

        public decimal CreditCost { get; init; }

        public required string ServiceType { get; init; }

        public static string Sql => @"
            select s.id,
                   s.title,
                   s.description,
                   s.service_type,
                   s.credit_cost
            from core.service s
            where s.id = :_service_id
            order by s.title;
        ";
    }
}