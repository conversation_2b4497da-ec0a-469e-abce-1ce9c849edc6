﻿using System.Reflection;
using System.Text;
using System.Text.Json;
using Google.Cloud.AIPlatform.V1;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Proptexx.AI.Services;
using Proptexx.Core.AI;
using Proptexx.Core.Storage;
using Type = System.Type;

namespace Proptexx.AI;

public static class Extensions
{
    public static bool HasModel(this IServiceProvider serviceProvider, string modelName)
    {
        return ModelFactory.Has(modelName);
    }

    public static IModel GetModel(this IServiceProvider serviceProvider, string modelName)
    {
        var modelType = ModelFactory.Get(modelName);
        return (IModel)serviceProvider.GetRequiredService(modelType);
    }

    public static TModel GetModel<TModel>(this IServiceProvider serviceProvider) where TModel : class, IModel
    {
        return serviceProvider.GetRequiredService<TModel>();
    }

    public static IServiceCollection AddChatServices(this IServiceCollection services)
    {
        services.AddSingleton<IChatService, ChatService>();
        return services;
    }

    public static IServiceCollection AddModels(this IServiceCollection services)
    {
        services.AddSingleton<IStorageService, AzureStorageService>();

        services.AddSingleton<PredictionServiceClient>(serviceProvider =>
        {
            var configuration = serviceProvider.GetRequiredService<IConfiguration>();
            var base64JsonCredentials = configuration.GetValue<string>("GoogleG:Base64EncodedJsonCredentials");
            var location = configuration.GetValue<string>("GoogleG:Location", "europe-west3");

            if (string.IsNullOrWhiteSpace(base64JsonCredentials))
            {
                throw new NullReferenceException("Unable to retrieve certificate");
            }

            var byteCredentials = Convert.FromBase64String(base64JsonCredentials);
            var jsonCredentials = Encoding.UTF8.GetString(byteCredentials);

            return new PredictionServiceClientBuilder
            {
                JsonCredentials = jsonCredentials,
                Endpoint = $"{location}-aiplatform.googleapis.com"
            }.Build();
        });

        var assembly = Assembly.GetExecutingAssembly();
        var modelsNamespace = $"{assembly.GetName().Name}.Models";

        // Find all types in the assembly that implement IModel and are within the specified namespace
        var modelTypes = assembly
            .GetTypes()
            .Where(t => typeof(IModel).IsAssignableFrom(t) && t.Namespace != null && t.Namespace.StartsWith(modelsNamespace))
            .ToList();

        foreach (var modelType in modelTypes)
        {
            // Convert the namespace and type name to the desired string path format
            var paths = GetRelativePath(modelType, modelsNamespace);
            ModelFactory.Add(paths.Item1, modelType);
            ModelFactory.Add(paths.Item2, modelType);

            // Register the model type with the service collection as transient
            services.AddTransient(modelType);
        }

        // Alias alternative FloorPlan paths to FloorPlanAnalysis for backwards compatibility
        ModelFactory.Add("cv/floorplan-analyzer", typeof(Proptexx.AI.Models.CV.FloorPlanAnalysis));
        ModelFactory.Add("cv/FloorplanAnalyzer", typeof(Proptexx.AI.Models.CV.FloorPlanAnalysis));
        ModelFactory.Add("cv/floorplan-analysis", typeof(Proptexx.AI.Models.CV.FloorPlanAnalysis));
        ModelFactory.Add("cv/FloorplanAnalysis", typeof(Proptexx.AI.Models.CV.FloorPlanAnalysis));
        return services;
    }

    private static (string, string) GetRelativePath(Type type, string rootNamespace)
    {
        // Remove the root namespace and convert to lower case with hyphens for slashes
        if (string.IsNullOrWhiteSpace(type.Namespace)) throw new NullReferenceException(nameof(type.Namespace));
        var relativeNamespace = type.Namespace[(rootNamespace.Length + 1)..].ToLowerInvariant(); // Remove root namespace
        var kebabCased = relativeNamespace.Replace('.', '/') + "/" + ToKebabCase(type.Name);
        var normalCased = relativeNamespace.Replace('.', '/') + "/" + type.Name;
        return (kebabCased, normalCased);
    }

    private static string ToKebabCase(string input)
    {
        // Convert PascalCase to kebab-case
        return string.Concat(input.Select((x, i) =>
            i > 0 && char.IsUpper(x) ? "-" + char.ToLower(x) : char.ToLower(x).ToString()));
    }
}

public static class JsonExtensions
{
    public static JsonElement? GetPropertyOrDefault(this JsonElement element, string propertyName, int? arrayIndex = null)
    {
        var property = element.TryGetProperty(propertyName, out var value) ? value : (JsonElement?)null;

        if (property.HasValue && arrayIndex.HasValue)
        {
            if (property.Value.ValueKind != JsonValueKind.Array) return null;
            if (property.Value.GetArrayLength() <= 0) return null;
            return property.Value[arrayIndex.Value];
        }

        return property;
    }

    public static JsonDocument? GetPropertiesToJsonDocument(
        this JsonElement element,
        IDictionary<string, string> propertyMapping)
    {
        using var stream = new MemoryStream();
        using (var jsonWriter = new Utf8JsonWriter(stream))
        {
            jsonWriter.WriteStartObject();

            foreach (var pm in propertyMapping)
            {
                if (!element.TryGetProperty(pm.Key, out var propertyValue)) continue;
                jsonWriter.WritePropertyName(pm.Value);
                propertyValue.WriteTo(jsonWriter);
            }

            jsonWriter.WriteEndObject();
            jsonWriter.Flush();
        }

        stream.Position = 0;
        return JsonDocument.Parse(stream);
    }
}