using System.Data;
using FluentMigrator;
using FluentMigrator.Postgres;

namespace Proptexx.Database.Migration.Migrations;

[Migration(250405002)]
public class Migrate250405002 : FluentMigrator.Migration
{
    public override void Up()
    {
        Create.Table("website").InSchema("core")
            .WithColumn("id").AsGuid().NotNullable().WithDefault(SystemMethods.NewGuid).PrimaryKey("pk_website")
            .WithColumn("slug").AsCustom("citext").NotNullable().Unique("ux_website_name")
            .WithColumn("title").AsString().NotNullable()
            .WithColumn("description").AsString().Nullable()
            .WithColumn("created_at").AsDateTime().NotNullable().WithDefault(SystemMethods.CurrentUTCDateTime);
        
        Create.Table("document").InSchema("core")
            .WithColumn("id").AsGuid().NotNullable().WithDefault(SystemMethods.NewGuid).PrimaryKey("pk_document")
            .WithColumn("website_id").AsGuid().NotNullable().ForeignKey("fk_document__website", "core", "website", "id").OnDelete(Rule.Cascade)
            .WithColumn("language").AsCustom("citext").NotNullable()
            .WithColumn("path").AsCustom("citext").NotNullable()
            .WithColumn("title").AsString().NotNullable()
            .WithColumn("description").AsString().Nullable();

        Create.UniqueConstraint("ux_document")
            .OnTable("document").WithSchema("core")
            .Columns("website_id", "language", "path");
        
        Create.Table("document_revision").InSchema("core")
            .WithColumn("id").AsGuid().NotNullable().WithDefault(SystemMethods.NewGuid).PrimaryKey("pk_document_revision")
            .WithColumn("document_id").AsGuid().NotNullable().ForeignKey("fk_document_revision__document", "core", "document", "id").OnDelete(Rule.Cascade)
            .WithColumn("content").AsCustom("jsonb").NotNullable()
            .WithColumn("created_at").AsDateTime().NotNullable().WithDefault(SystemMethods.CurrentUTCDateTime)
            .WithColumn("published_at").AsDateTime().Nullable();

        Create.Index("ux_document_revision__document_id__published")
            .OnTable("document_revision").InSchema("core")
            .OnColumn("document_id").Ascending().WithOptions().Filter("published_at IS NOT NULL").Unique();
    }

    public override void Down()
    {
        // Drop the unique index first
        Delete.Index("ux_document_revision__document_id__published").OnTable("document_revision").InSchema("core");

        // Drop dependent tables first
        Delete.Table("document_revision").InSchema("core");

        // Drop unique constraint before dropping document table
        Delete.UniqueConstraint("ux_document").FromTable("document").InSchema("core");
        Delete.Table("document").InSchema("core");

        // Finally, drop the base table
        Delete.Table("website").InSchema("core");
    }
}