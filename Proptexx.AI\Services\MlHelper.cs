using System.Text.Json;
using Microsoft.Extensions.Logging;
using Proptexx.AI.Interfaces;
using Proptexx.Core.AI;
using Proptexx.Core.Entities;
using Proptexx.Core.Resilience;

namespace Proptexx.AI.Services;

public static class MlHelper
{
    private const int RetryCount = 3;
    private const int DelaySeconds = 1;
    public static async Task<BatchResult> ProcessItemAsync(
        IServiceProvider services, ILogger logger, 
        Guid workspaceId, Guid taskId, string modelName, JsonDocument modelPayload, CancellationToken cancellationToken)
    {
        var batchResult = new BatchResult
        {
            Id = Guid.NewGuid(),
            TaskId = taskId,
            StartedAt = DateTime.UtcNow,
            Status = BatchResultStatus.Pending
        };

        ModelResponse? inferResult = null;

        try
        {
            var model = services.GetModel(modelName);

            var ctx = new ModelContext
            {
                ItemId = taskId.ToString(),
                WorkspaceId = workspaceId.ToString(),
                Payload = modelPayload,
                CancellationToken = cancellationToken,
            };

            var retryPolicy = PollyRetryExtensions.GetDefaultRetryPolicy(logger, $"InferAsync for TaskId: {taskId}", RetryCount, DelaySeconds);

            batchResult.Status = BatchResultStatus.Processing;
            
            inferResult = await retryPolicy.ExecuteAsync(async () => 
            {
                // Check for cancellation before each attempt
                cancellationToken.ThrowIfCancellationRequested();
                return await model.InferAsync(ctx);
            });

            if (inferResult is IModelRequestResponseParams reqResParams)
            {
                batchResult.RequestParams = reqResParams.RequestParams;
                batchResult.ResponseParams = reqResParams.ResponseParams;
            }
            
            batchResult.Output = inferResult.Document;
            batchResult.Status = batchResult.Output is null ? BatchResultStatus.Error : BatchResultStatus.Success;
        }
        catch (ApplicationException e)
        {
            batchResult.Output = inferResult?.Document;
            batchResult.Status = BatchResultStatus.Error;
            batchResult.ErrorMessage = e.Message;
            batchResult.Exception = e.InnerException?.Message;
        }
        catch (Exception e)
        {
            batchResult.Output = inferResult?.Document;
            batchResult.Status = BatchResultStatus.Error;
            batchResult.ErrorMessage = "An exception was thrown. We are looking into it";
            batchResult.Exception = e.Message;
            logger.LogError(e, "Exception on {TaskId}", taskId);
        }
        finally
        {
            batchResult.CompletedAt = DateTime.UtcNow;
        }
        
        return batchResult;
    }
}
