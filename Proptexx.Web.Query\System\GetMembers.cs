using System.ComponentModel.DataAnnotations;
using Dapper;
using Proptexx.Core;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Attributes;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.System;

public sealed class GetMembers : IQuery
{
    [Required, GuidNotEmpty] public Guid WorkspaceId { get; init; }

    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        context.User.EnsureRootAccess();
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        var result = await npgsql.QueryAsync<MemberModel>(MemberModel.Sql, new { _workspace_id = WorkspaceId });
        return result;
    }

    private sealed class MemberModel
    {
        public required Guid Id { get; init; }

        public required string FirstName { get; init; }

        public required string FamilyName { get; init; }

        public DateTime MemberSince { get; init; }

        public string? Emails { get; init; }

        public string? Clusters { get; init; }

        public static string Sql => @"
            select a.id,
                   a.first_name,
                   a.family_name,
                   a.created_at as member_since,
                   string_agg(c.name, ',') as clusters,
                   string_agg(ae.email, ',') as emails
            from core.account a
            join core.cluster_account_binding cab on a.id = cab.account_id
            join core.cluster c on cab.cluster_id = c.id
            left outer join core.account_email ae on a.id = ae.account_id
            where c.workspace_id = :_workspace_id
            group by a.id, a.first_name, a.family_name, a.created_at
            order by a.first_name, a.family_name;
        ";
    }
}