﻿using System.Diagnostics;
using System.Text.Json.Serialization;

namespace Proptexx.Web.Api.Responses;

public class ApiResponseStatus
{
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public Guid? Id { get; protected set; }

    public string? Error { get; private set; }

    public DateTime? ReceivedAt { get; set; }

    public string? TimeTaken { get; set; }

    public virtual void Success(Guid id)
    {
        this.Id = id;
    }

    public void Fail(string errorMessage)
    {
        Id = null;
        Error = errorMessage;
    }
}
