using Microsoft.Extensions.Configuration;
using Proptexx.Core.Entities;
using Stripe.Checkout;

namespace Proptexx.Stripe;

public sealed class ProductModel : StripeManager.ICreateCheckoutPayload
{
    public Guid Id { get; init; }

    public required string Title { get; init; }
        
    public string? Description { get; init; }
        
    public decimal PriceAmount { get; init; }
        
    public required string Currency { get; init; }
        
    public required string PaymentType { get; init; }
        
    public required ProductConfig Config { get; init; }

    public static string Sql => @"
            select p.id,
                   p.title,
                   p.description,
                   p.price_amount,
                   p.currency,
                   p.payment_type,
                   p.config
            from core.product p
            where p.id = :_product_id;
        ";
}

public static class StripeManager
{
    public static async Task<Session> CreateCheckoutSessionAsync(IConfiguration configuration, ICreateCheckoutPayload product, Guid orderId, CancellationToken cancellationToken)
    {
        var mode = "payment";
        var priceData = new SessionLineItemPriceDataOptions
        {
            ProductData = new SessionLineItemPriceDataProductDataOptions
            {
                Name = product.Title,
                Description = product.Description
            },
            Currency = product.Currency,
            UnitAmountDecimal = product.PriceAmount * 100,
        };

        if (product.PaymentType.Equals("recurring") && product.Config is ProductWidgetAccessConfig widgetAccessConfig)
        {
            var interval = widgetAccessConfig.Interval?.Split(":");
            if (interval is not null && interval.Length == 2 && int.TryParse(interval[0], out var intervalCount))
            {
                priceData.Recurring = new SessionLineItemPriceDataRecurringOptions
                {
                    Interval = interval[1],
                    IntervalCount = intervalCount
                };

                mode = "subscription";
            }
        }

        var lineItem = new SessionLineItemOptions
        {
            PriceData = priceData,
            Quantity = 1
        };

        var stripeSuccessUrl = configuration.GetValue<string>("Stripe:SuccessUrl")
                               ?? throw new NullReferenceException("Missing env var for Stripe:SuccessUrl");

        var stripedCanceledUrl = configuration.GetValue<string>("Stripe:CanceledUrl")
                                 ?? throw new NullReferenceException("Missing env var for Stripe:ErrorUrl");

        var sessionOptions = new SessionCreateOptions
        {
            Mode = mode,
            LineItems = [lineItem],
            // Discounts = [
            //     new SessionDiscountOptions
            //     {
            //         PromotionCode = "exitpromo"
            //     }
            // ],
            // SubscriptionData = new SessionSubscriptionDataOptions
            // {
            //     TrialPeriodDays = 60 // 2 months trial period
            // },
            SuccessUrl = stripeSuccessUrl,
            CancelUrl = stripedCanceledUrl,
            Metadata = new Dictionary<string, string>
            {
                ["order_id"] = orderId.ToString()
            }
        };

        var sessionService = new SessionService();
        var session = await sessionService.CreateAsync(sessionOptions, null, cancellationToken);
        return session;
    }


    public interface ICreateCheckoutPayload
    {
        string Title { get; }

        string? Description { get; }
    
        decimal PriceAmount { get; }
    
        ProductConfig Config { get; }
        
        string Currency { get; }
        
        string PaymentType { get; }
    }
}