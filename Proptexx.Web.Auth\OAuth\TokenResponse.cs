using System.Text.Json.Serialization;

namespace Proptexx.Web.Auth.OAuth;

public sealed class TokenResponse
{
    [Json<PERSON>ropertyName("access_token")]
    public required string AccessToken { get; init; }

    [JsonPropertyName("expires_in")]
    public required int ExpiresIn { get; init; }

    [JsonPropertyName("scope")]
    public required string Scope { get; init; }

    [JsonPropertyName("token_type")]
    public required string TokenType { get; init; }

    [JsonPropertyName("id_token")]
    public required string IdToken { get; init; }
}

/*
 * Microsoft
 * {
     "token_type": "Bearer",
     "scope": "email openid profile User.Read",
     "expires_in": 4806,
     "ext_expires_in": 4806,
     "access_token": "eyJ0eXAiOiJKV1QiLCJub25jZSI6Ilo3LXJ2VWlnd19GV0lrdlJNTFpjZjFPaXpXMVVzWndybGNBTjZ0M19xVEEiLCJhbGciOiJSUzI1NiIsIng1dCI6Inp4ZWcyV09OcFRrd041R21lWWN1VGR0QzZKMCIsImtpZCI6Inp4ZWcyV09OcFRrd041R21lWWN1VGR0QzZKMCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.X3W5R4JV4CqlIa3jmOec3byXnL9emDeXkticXUGpy_QhtPCR2NTiwdkoYQyGxh6hDyOA_Bso1l0ZXD0_eHmA17KnKkhTrK1mci_lkLVIkuJqY6jt0IiQy2ZpnoyjGB0l3ER1RDWdX4vqWlnvodJvG_kxTXYFTg41pmf9XUSBFP1FaHNtas90albrrpHINptRPKInf7jRGrHgREeLXC-nCtAOUsKwU9mi2GAqBeDfQKi5Fo03-1fm8E-Pb_tCP3WD4ZiXW5ea64cNyNNq-M3EkFtht2Tf4b5KRdIwIQpAhER6nmfAEqo4LnlxyquZQNPhC_DEtJ8m7Jgbw286xQLCiA",
     "id_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6Inp4ZWcyV09OcFRrd041R21lWWN1VGR0QzZKMCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mkX-KyE56TLQfrirpq5x9l-V8NhSAl1np33Wv7A9O06uK_SPMPEFwMBf4VYjOaOC46voeEOGbl-yAuu3uTBQBQZLM_MA2KBjeQSe3wwM33O9viurLSOWyCFaL8G4C9crRyNaQkrTI7V5VHaD6dGg8AA1ZnrDHmZ4u8gdZTKQC21BpP-8b2UPIHj076YqWDHJohaymRe8YCQoVyp_P4mRM_iNZFMVovXMCWJebcAyENbo6J8eT9QFk5D_jaIyT-kzQOp-DednVvGZVI1pj7gMUwhbSBPNKjuAB5tOuD6vW4w5jmn64ugKX4awDjDmPUJoZZtVL6GzoFICtoxTumbJoQ"
   }
 */