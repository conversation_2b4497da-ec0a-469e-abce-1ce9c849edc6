using StackExchange.Redis;

namespace Proptexx.Core.Redis;

public class PhoneHashStore : RedisHashStore<PhoneHashStore.PhoneModel>
{
    public PhoneHashStore(IConnectionMultiplexer connectionMultiplexer) 
        : base(connectionMultiplexer, "phones")
    {
    }

    protected override string ResolveKey(PhoneModel entry) => entry.Number.ToLowerInvariant();
    
    public class PhoneModel
    {
        public Guid AccountId { get; init; }

        public required string Number { get; init; }
        
        public DateTime? VerifiedAt { get; init; }
    }
}