using System.Net.Http.Headers;
using System.Text;

namespace Proptexx.Worker.DevTools;

public static class OpenAi
{
    private const string apiKey = "********************************************************";

    public static async Task ExecuteAsync(CancellationToken cancellationToken)
    {
        const string prompt = @"Apply furniture, such as a sofa, to the image.";
        var imagePath = "https://d36xftgacqn2p.cloudfront.net/listingphotos6/3182842-3.jpg?v=1712622544";
        var apiUrl = "https://api.openai.com/v1/images/generations"; // Replace with the appropriate API endpoint

        // Read image file
        var imageBytes = await GetImageAsync(imagePath, cancellationToken);
        var base64Image = Convert.ToBase64String(imageBytes);

        // Create JSON payload
        var jsonPayload = new Dictionary<string, object>
        {
            { "prompt", prompt },
            { "image", base64Image },
            { "size", "1024x1024" } // You can adjust the size as needed
        };

        var strPayload = jsonPayload.ToString() ?? throw new NullReferenceException(nameof(jsonPayload));

        using var httpClient = new HttpClient();
        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);

        using var content = new StringContent(strPayload, Encoding.UTF8, "application/json");

        // Send POST request
        var response = await httpClient.PostAsync(apiUrl, content, cancellationToken);

        if (response.IsSuccessStatusCode)
        {
            var responseBody = await response.Content.ReadAsStringAsync(cancellationToken);
            Console.WriteLine("Response: " + responseBody);
        }
        else
        {
            Console.WriteLine("Error: " + response.StatusCode);
        }
    }

    private static async Task<byte[]> GetImageAsync(string imagePath, CancellationToken cancellationToken)
    {
        using var httpClient = new HttpClient();
        using var response = await httpClient.GetAsync(imagePath, cancellationToken);
        response.EnsureSuccessStatusCode();
        var result = await response.Content.ReadAsByteArrayAsync(cancellationToken);
        return result;
    }
}