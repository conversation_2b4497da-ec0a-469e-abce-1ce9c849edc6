using System.ComponentModel.DataAnnotations;
using Dapper;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Attributes;
using Proptexx.Core.Entities;
using Proptexx.Core.Postgresql;

namespace Proptexx.Web.Query.Ecommerce;

public sealed class GetOrder : IQuery
{
    [Required, GuidNotEmpty]  public required Guid Id { get; init; }

    public async Task<object?> ExecuteAsync(QueryContext context)
    {
        await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
        await using var grid = await npgsql.QueryMultipleAsync(OrderModel.Sql + OrderLineModel.Sql, new { _order_id = Id });
        var order = await grid.ReadFirstAsync<OrderModel>();
        var orderLines = await grid.ReadAsync<OrderLineModel>();
        return new { order, orderLines };
    }
}

public sealed class OrderModel : Order
{
    public static string Sql => @"
        select o.* from core.order o where o.id = :_order_id;
    ";
}

public sealed class OrderLineModel
{
    public Guid Id { get; init; }
    
    public Guid WorkspaceId { get; init; }

    public Guid ProductId { get; init; }

    public Guid OrderId { get; init; }

    public DateTime CreatedAt { get; init; }

    public required string Title { get; init; }

    public required string PaymentType { get; init; }

    public decimal PriceAmount { get; init; }

    public required string Currency { get; init; }

    public static string Sql => @"
        select ol.id,
               ol.workspace_id,
               ol.product_id,
               ol.order_id,
               ol.created_at,
               p.title,
               p.payment_type,
               p.price_amount,
               p.currency
        from core.order_line ol
        join core.product p on ol.product_id = p.id
        where ol.order_id = :_order_id;
    ";
}