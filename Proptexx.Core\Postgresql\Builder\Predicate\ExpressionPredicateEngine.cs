using System.Linq.Expressions;
using System.Reflection;
using System.Text;

namespace Proptexx.Core.Postgresql.Builder.Predicate;

internal class ExpressionPredicateEngine : ExpressionVisitor
{
    private readonly PredicateBuilderConfig _config;
    private readonly StringBuilder _sqlBuilder;
    private int _paramCount;

    internal ExpressionPredicateEngine(PredicateBuilderConfig config)
    {
        _config = config;
        _sqlBuilder = new StringBuilder();
    }

    internal Dictionary<string, object?> Parameters { get; } = new();

    protected override Expression VisitMember(MemberExpression node)
    {
        if (node.Expression == null)
        {
            return base.VisitMember(node);
        }

        // Stack to keep track of nested properties
        var members = new Stack<MemberExpression>();
        Expression? expression = node;

        // Unwrap and stack nested properties
        while (expression is MemberExpression memberExpression)
        {
            members.Push(memberExpression);
            expression = memberExpression.Expression;
        }

        // Retrieve the final value
        object? finalValue = null;

        if (expression is ConstantExpression constantExpression)
        {
            finalValue = constantExpression.Value;

            // Traverse through the members and get the final value
            while (members.Count > 0)
            {
                MemberInfo memberInfo = members.Pop().Member;

                if (memberInfo is FieldInfo fieldInfo)
                {
                    finalValue = fieldInfo.GetValue(finalValue);
                }
                else if (memberInfo is PropertyInfo propertyInfo)
                {
                    finalValue = propertyInfo.GetValue(finalValue);
                }
            }
        }
        else if (expression is ParameterExpression)
        {
            var memberName = node.Member.Name;
            var str = PredicateBuilderConfig.ColumnNameRender(memberName);
            Write(str);
        }

        // Append the final value to the SQL builder
        if (finalValue != null)
        {
            WriteValue(finalValue);
        }

        return node;
    }

    protected override Expression VisitMethodCall(MethodCallExpression node)
    {
        if (node.Method.DeclaringType is null) return node;

        var handler = _config.Handlers
            .FirstOrDefault(x => 
                x.CanHandle(node.Method.DeclaringType));

        if (handler is null) return node;

        handler.Handle(new MethodCallExpressionContext
        {
            Write = Write,
            Visit = Visit,
            Expression = node
        });

        return node;
    }

    protected override Expression VisitConstant(ConstantExpression node)
    {
        WriteValue(node.Value);
        return node;
    }

    protected override Expression VisitBinary(BinaryExpression node)
    {
        var needParentheses = node.Right is BinaryExpression && 
                              node.Right.NodeType is ExpressionType.AndAlso or ExpressionType.OrElse;

        switch (node.NodeType)
        {
            case ExpressionType.Equal:
                Visit(node.Left);

                if (IsConstantNull(node.Right))
                {
                    Write(" is null");
                }
                else
                {
                    Write(" = ");
                    Visit(node.Right);
                }

                break;
            
            case ExpressionType.NotEqual:
                Visit(node.Left);

                if (IsConstantNull(node.Right))
                {
                    Write(" is not null");
                }
                else
                {
                    Write(" != ");
                    Visit(node.Right);
                }

                break;
            
            case ExpressionType.GreaterThan:
                Visit(node.Left);
                Write(" > ");
                Visit(node.Right);
                break;
            
            case ExpressionType.GreaterThanOrEqual:
                Visit(node.Left);
                Write(" >= ");
                Visit(node.Right);
                break;
            
            case ExpressionType.LessThan:
                Visit(node.Left);
                Write(" < ");
                Visit(node.Right);
                break;
            
            case ExpressionType.LessThanOrEqual:
                Visit(node.Left);
                Write(" <= ");
                Visit(node.Right);
                break;
            
            case ExpressionType.Add:
                Visit(node.Left);
                Write(" + ");
                Visit(node.Right);
                break;
            
            case ExpressionType.Subtract:
                Visit(node.Left);
                Write(" - ");
                Visit(node.Right);
                break;
            
            case ExpressionType.Multiply:
                Visit(node.Left);
                Write(" * ");
                Visit(node.Right);
                break;
            
            case ExpressionType.Divide:
                Visit(node.Left);
                Write(" / ");
                Visit(node.Right);
                break;
            
            case ExpressionType.And:
            case ExpressionType.AndAlso:
                Visit(node.Left);
                Write(" and ");
                if (needParentheses) Write("(");
                Visit(node.Right);
                if (needParentheses) Write(")");
                break;

            case ExpressionType.Or:
            case ExpressionType.OrElse:
                Visit(node.Left);
                Write(" or ");
                if (needParentheses) Write("(");
                Visit(node.Right);
                if (needParentheses) Write(")");
                break;

            default: throw new ArgumentException($"Operator '{node.NodeType}' is not supported.");
        }

        return node;
    }

    protected override Expression VisitUnary(UnaryExpression node)
    {
        if (node.NodeType == ExpressionType.Not)
        {
            if (node.Operand is MethodCallExpression)
            {
                Write(" NOT");
            }
            else
            {
                Write(" NOT "); // Or this.Write("!");
            }

            Visit(node.Operand);
        }
        else
        {
            // Keep other unary expressions unchanged
            return base.VisitUnary(node);
        }
    
        return node;
    }

    private static bool IsConstantNull(Expression expression)
    {
        return expression is ConstantExpression { Value: null };
    }

    private void WriteValue(object? val)
    {
        var paramName = $"{_config.ParameterPrefix}{_paramCount++}";
        Parameters.Add(paramName, val);
        Write(paramName);
    }

    private void Write(object val)
    {
        _sqlBuilder.Append(val);
    }

    public override string ToString() => _sqlBuilder.ToString();
}