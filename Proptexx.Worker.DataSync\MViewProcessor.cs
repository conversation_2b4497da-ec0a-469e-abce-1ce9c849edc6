﻿using Dapper;
using Npgsql;

namespace Proptexx.Worker.DataSync
{
    public static class MViewProcessor
    {
        private static bool? _isMidnightRun = null;

        private static string[] _continuousViews =
        [
            "telemetry.mv_dashboard_summary_today",
            "telemetry.mv_dashboard_summary",
            "telemetry.mv_ai_requests_today",
            "telemetry.mv_ai_requests_summary",
            "telemetry.mv_batch_requests_today",
            "telemetry.mv_batch_requests_summary",
            "telemetry.mv_api_requests_today",
            "telemetry.mv_api_requests_summary",
            "telemetry.mv_dashboard_summary_today",
            "telemetry.mv_dashboard_summary",
            "telemetry.mv_api_requests_timeline_minute_today",
            "telemetry.mv_api_requests_timeline_hourly_today",
            "telemetry.mv_api_requests_timeline_daily_today",
            "telemetry.mv_api_requests_timeline_monthly_today",
            "telemetry.mv_api_requests_timeline_minute_summary",
            "telemetry.mv_api_requests_timeline_hourly_summary",
            "telemetry.mv_api_requests_timeline_daily_summary",
            "telemetry.mv_api_requests_timeline_monthly_summary",
            "telemetry.mv_widget_renders_today",
            "telemetry.mv_widget_renders_summary",
            "telemetry.mv_widget_metrics_today",
            "telemetry.mv_widget_metrics_summary"
        ];

        // Views that should be refreshed once per day (at midnight).
        private static readonly string[] _midnightViews =
        [
            "telemetry.mv_batch_requests_past",
            "telemetry.mv_api_requests_past",
            "telemetry.mv_ai_requests_past",
            "telemetry.mv_dashboard_summary_past",
            "telemetry.mv_api_requests_timeline_minute_past",
            "telemetry.mv_api_requests_timeline_hourly_past",
            "telemetry.mv_api_requests_timeline_daily_past",
            "telemetry.mv_api_requests_timeline_monthly_past",
            "telemetry.mv_widget_renders_past",
            "telemetry.mv_widget_metrics_past"
        ];

        public static async Task SyncAsync(IServiceProvider services, CancellationToken cancellationToken)
        {
            var dataSource = services.GetRequiredService<NpgsqlDataSource>();

            // Commented out as the order of views seems important, and therefor I've hardcoded it above.
            // Optionally we could allow for both, where we merge the list above (takes precedence) with whatever is set in the env. var,
            // which would allow us to add new views dynamically. // Mikal
            // var views = configuration.GetValue<string>("MaterializedViews", "telemetry.mv_dashboard_summary")?.Split(',');
            var now = DateTime.UtcNow;
            await using var connection = await dataSource.OpenConnectionAsync(cancellationToken);
            
            // If it's midnight, refresh the additional midnight views
            if (_isMidnightRun is null or true)
            {
                foreach (var view in _midnightViews)
                {
                    await RefreshViewAsync(connection, view, 120);
                }
            }

            _isMidnightRun = (now - now.Date).TotalMinutes <= 0.5;
            
            // Always refresh continuous views
            foreach (var view in _continuousViews)
            {
                await RefreshViewAsync(connection, view, 120);
            }
        }

        private static Task<int> RefreshViewAsync(NpgsqlConnection connection, string viewName, int timeoutSec)
        {
            var sql = $"""
                       DO $$
                           BEGIN
                               BEGIN
                                   REFRESH MATERIALIZED VIEW CONCURRENTLY {viewName};
                               EXCEPTION WHEN others THEN
                                   -- fallback if the view is not populated
                                   PERFORM pg_sleep(0);  -- no-op to keep the BEGIN block happy
                                   REFRESH MATERIALIZED VIEW {viewName};
                               END;
                           END
                       $$;
                       """;

            return connection.ExecuteAsync(sql, commandTimeout: timeoutSec);
        }
    }
}
