using Proptexx.Stripe;
using Proptexx.Web;
using Proptexx.Web.Partner.Handlers;

var builder = WebApplication.CreateBuilder(args);
builder.AddProptexxWeb();
builder.RegisterStripeApiKey();
builder.Services.AddScoped<OnboardingHandler>();
builder.Services.AddScoped<StoreApiKeyHandler>();

builder.Services.AddCors(cors =>
{
    cors.AddDefaultPolicy(p => p
        .SetIsOriginAllowed(_ => true)
        .AllowAnyHeader()
        .AllowAnyMethod()
        .AllowCredentials()
        .Build());
});

var app = builder.Build();
app.UseProptexxWeb();
app.MapGet("/", () => "Proptexx | Partner");
app.MapPost("/onboarding/tenant", OnboardingHandler.InvokeAsync); // Used by EXIT - legacy
app.MapPost("/onboarding/affiliate", OnboardingHandler.InvokeAsync); // Used by some others (Elmstreet?)
app.MapPost("/onboarding/store-api-key", StoreApiKeyHandler.InvokeAsync);
app.Run();
