using Microsoft.Playwright;

namespace Proptexx.Web.ImageProxy;

public sealed class PlaywrightBrowser : IAsyncDisposable
{
    public IPlaywright? Instance { get; private set; }

    public IBrowser? Browser { get; private set; }

    public async Task InitializeAsync()
    {
        var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));

        try
        {
            Instance = await Playwright.CreateAsync().WaitAsync(cts.Token);
            Console.WriteLine("Playwright created.");

            Browser = await Instance.Chromium.LaunchAsync(new BrowserTypeLaunchOptions
            {
                Headless = true,
                Timeout = 5000
            });

            Console.WriteLine("Browser launched.");
        }
        catch (OperationCanceledException)
        {
            Console.WriteLine("Timeout: Playwright.CreateAsync() did not complete.");
            throw;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Playwright init failed: " + ex);
            throw;
        }
        // Browser = await Instance.Chromium.LaunchAsync(new BrowserTypeLaunchOptions
        // {
        //     Headless = true,
        //     Timeout = 5000
        // });
    }

    public async ValueTask DisposeAsync()
    {
        if (<PERSON><PERSON><PERSON> is not null)
        {
            await Browser.DisposeAsync();
        }

        Instance?.Dispose();
    }
}